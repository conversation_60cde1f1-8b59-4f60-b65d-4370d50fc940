// AI Site Submitter - 奶芙导航 规则配置
// 自动生成于: 2025/7/19 16:36:28
// 域名: www.navs.cc

export const SITE_RULE = {
  "domain": "www.navs.cc",
  "siteName": "奶芙导航",
  "lastUpdated": "2025-07-19T08:36:28.348Z",
  "fieldMappings": {
    "siteName": {
      "selectors": [
        "input[name='author']",
        "#author",
        ".comment-respond input[name='author']"
      ],
      "type": "input",
      "fillMethod": "value",
      "required": false,
      "validation": "required"
    },
    "contactEmail": {
      "selectors": [
        "input[name='email']",
        "#email",
        ".comment-respond input[name='email']"
      ],
      "type": "input",
      "fillMethod": "value",
      "required": false,
      "validation": "required"
    },
    "siteDescription": {
      "selectors": [
        "textarea[name='comment']",
        "#comment",
        ".comment-respond textarea[name='comment']"
      ],
      "type": "input",
      "fillMethod": "value",
      "required": false,
      "validation": "required"
    },
    "submitterRole": {
      "selectors": [
        "input[name='author']",
        "#author",
        ".comment-respond input[name='author']"
      ],
      "type": "input",
      "fillMethod": "value",
      "required": false,
      "validation": "optional"
    }
  },
  "formInfo": {
    "description": "奶芙导航关于我们页面的评论提交表单",
    "submitSelector": "#submit, .comment-respond button[type='submit']",
    "totalFields": 3,
    "notes": [
      "这是一个WordPress评论表单，主要包含昵称、邮箱和评论内容",
      "表单通过AJAX提交，action为ajax_comment",
      "隐藏字段包含_wpnonce、comment_post_ID等WordPress特有字段",
      "该表单主要用于用户反馈和建议，可适配为网站提交表单"
    ]
  },
  "metadata": {
    "generatedBy": "AI",
    "generatedAt": "2025-07-19T08:36:28.348Z",
    "version": "3.0.0",
    "aiModel": "moonshotai/Kimi-K2-Instruct"
  }
};

// 自定义处理函数 (可选)
export function handleWwwNavsCcSubmission(data, rule) {
  console.log('Processing 奶芙导航 form submission...');
  
  const processedData = { ...data };
  
  // 在这里添加特殊处理逻辑
  // 例如：URL格式化、字段验证、默认值设置等
  
  return processedData;
}

// 自定义元素填写函数 (可选)
export async function customFillElement(element, value, config) {
  console.log('🔧 奶芙导航 自定义填写函数被调用:', element, value);
  
  // 在这里添加特殊的元素填写逻辑
  // 例如：处理特殊的UI组件、异步操作等
  
  return false; // 返回 false 使用默认填写方法
}