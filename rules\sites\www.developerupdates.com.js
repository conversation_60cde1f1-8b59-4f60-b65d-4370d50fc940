// DeveloperUpdates.com 网站规则配置
// 网站: https://www.developerupdates.com/directory/submit
// 最后更新: 2025-07-24

export const SITE_RULE = {
  domain: 'www.developerupdates.com',
  siteName: 'Developer Updates',
  priority: 1,
  lastUpdated: '2025-07-24',

  fieldMappings: {
    siteUrl: {
      selectors: [
        'input[name="website_url"]',
        'input[type="url"]',
        'input[placeholder="example.com or store.example.com"]'
      ],
      method: 'value',
      validation: 'required|url',
      notes: '网站URL'
    },

    siteName: {
      selectors: [
        'input[name="title"]',
        'input[placeholder="Enter your product name"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '产品名称'
    },

    tagline: {
      selectors: [
        'input[name="slogan"]',
        'input[placeholder="A catchy slogan for your product"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '产品标语'
    },

    siteDescription: {
      selectors: [
        'textarea[name="short_description"]',
        'textarea[placeholder="Brief description of your product"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '简短描述，最多160字符'
    },

    detailedIntro: {
      selectors: [
        '.ql-editor',
        '[data-placeholder*="Enter your product description"]',
        '.ql-editor.ql-blank'
      ],
      method: 'innerHTML',
      validation: 'optional',
      notes: 'Quill富文本编辑器详细描述'
    }
  },

  submitConfig: {
    submitButton: 'button[type="submit"], #submit_button',
    submitMethod: 'click',
    successIndicators: ['.success-message'],
    errorIndicators: ['.error-message']
  },

  specialHandling: {
    requiresLogin: false,
    hasCaptcha: false,
    hasFileUpload: false,
    customScript: 'handleDeveloperUpdatesSubmission',
    formValidation: {
      requiredFields: ['siteUrl', 'siteName', 'tagline', 'siteDescription'],
      emailValidation: false,
      urlValidation: true
    },
    notes: [
      '有Auto-fetch按钮自动获取信息',
      '描述有160字符限制',
      '使用Bootstrap样式',
      '有字符计数器',
      '支持域名或子域名',
      '现代化的表单设计'
    ]
  }
};

export function handleDeveloperUpdatesSubmission(data, rule) {
  const processedData = { ...data };

  if (processedData.siteUrl && !processedData.siteUrl.startsWith('http')) {
    processedData.siteUrl = 'https://' + processedData.siteUrl;
  }

  return processedData;
}

export async function customFillElement(element, value, config) {
  // 处理Quill富文本编辑器
  if (element.classList.contains('ql-editor')) {
    element.innerHTML = `<p>${value}</p>`;
    element.dispatchEvent(new Event('input', { bubbles: true }));
    // 触发Quill的文本变化事件
    const quillContainer = element.closest('.ql-container');
    if (quillContainer && window.Quill) {
      const quill = window.Quill.find(quillContainer);
      if (quill) {
        quill.setText(value);
      }
    }
    return true;
  }

  if (element.tagName === 'INPUT' || element.tagName === 'TEXTAREA') {
    element.value = value;
    element.dispatchEvent(new Event('input', { bubbles: true }));
    element.dispatchEvent(new Event('change', { bubbles: true }));
    return true;
  }

  return false;
}