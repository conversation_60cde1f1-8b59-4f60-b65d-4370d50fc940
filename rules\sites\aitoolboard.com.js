// AIToolBoard.com 网站规则配置
// 网站: https://aitoolboard.com/list
// 最后更新: 2025-07-24

export const SITE_RULE = {
  domain: 'aitoolboard.com',
  siteName: 'AI Tool Board',
  priority: 1,
  lastUpdated: '2025-07-24',

  fieldMappings: {
    siteName: {
      selectors: [
        '#toolname',
        'input[name="toolname"]',
        'input[placeholder="e.g Midjourney"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '工具名称'
    },

    category: {
      selectors: [
        '#category',
        'select[name="category"]'
      ],
      method: 'select',
      validation: 'required',
      defaultValue: '5',
      notes: '分类选择，默认选择Productivity'
    },

    pricing: {
      selectors: [
        '#pricing',
        'select[name="pricing"]'
      ],
      method: 'select',
      validation: 'required',
      defaultValue: 'Free',
      notes: '定价类型'
    },

    siteUrl: {
      selectors: [
        '#link',
        'input[name="link"]',
        'input[placeholder="e.g https://aitoolboard.com"]'
      ],
      method: 'value',
      validation: 'required|url',
      notes: 'AI工具URL'
    },

    siteDescription: {
      selectors: [
        '#shortdescription',
        'textarea[name="shortdescription"]',
        'textarea[maxlength="150"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '简短描述，8-12个词，最多150字符'
    },

    detailedIntro: {
      selectors: [
        '.ck-editor__editable',
        '.ck-content[contenteditable="true"]',
        '[role="textbox"][aria-label*="Editor editing area"]'
      ],
      method: 'innerHTML',
      validation: 'optional',
      notes: 'CKEditor富文本编辑器详细描述'
    },

    contactEmail: {
      selectors: [
        '#submitteremail',
        'input[name="submitteremail"]',
        'label:contains("Your Email") + input'
      ],
      method: 'value',
      validation: 'optional|email',
      notes: '提交者邮箱'
    }
  },

  submitConfig: {
    submitButton: 'button[type="submit"], .btn.btn-success',
    submitMethod: 'click',
    successIndicators: ['.success-message'],
    errorIndicators: ['.error-message']
  },

  specialHandling: {
    requiresLogin: false,
    hasCaptcha: false,
    hasFileUpload: true,
    customScript: 'handleAIToolBoardSubmission',
    formValidation: {
      requiredFields: ['siteName', 'category', 'pricing', 'siteUrl', 'siteDescription'],
      emailValidation: true,
      urlValidation: true
    },
    notes: [
      '需要上传图片文件',
      '描述要求8-12个词',
      '有多个可选URL字段',
      '使用Laravel框架',
      '有CSRF token保护',
      '强调描述必须原创'
    ]
  }
};

export function handleAIToolBoardSubmission(data, rule) {
  const processedData = { ...data };

  if (processedData.siteUrl && !processedData.siteUrl.startsWith('http')) {
    processedData.siteUrl = 'https://' + processedData.siteUrl;
  }

  return processedData;
}

export async function customFillElement(element, value, config) {
  // 处理CKEditor富文本编辑器
  if (element.classList.contains('ck-editor__editable')) {
    element.innerHTML = `<p>${value}</p>`;
    element.dispatchEvent(new Event('input', { bubbles: true }));
    // 触发CKEditor的数据变化事件
    const editorContainer = element.closest('.ck-editor');
    if (editorContainer && window.CKEDITOR) {
      // 尝试获取CKEditor实例并设置数据
      const editorId = editorContainer.id;
      if (editorId && window.CKEDITOR.instances[editorId]) {
        window.CKEDITOR.instances[editorId].setData(`<p>${value}</p>`);
      }
    }
    return true;
  }

  if (element.tagName === 'SELECT') {
    const options = element.querySelectorAll('option');
    let option;

    if (element.name === 'category') {
      option = Array.from(options).find(opt => opt.value === '5' || opt.textContent.includes('Productivity'));
    } else if (element.name === 'pricing') {
      option = Array.from(options).find(opt => opt.value === 'Free');
    } else {
      option = Array.from(options).find(opt => opt.value === value);
    }

    if (option) {
      element.value = option.value;
      element.dispatchEvent(new Event('change', { bubbles: true }));
      return true;
    }
  }

  if (element.tagName === 'INPUT' || element.tagName === 'TEXTAREA') {
    element.value = value;
    element.dispatchEvent(new Event('input', { bubbles: true }));
    return true;
  }

  return false;
}