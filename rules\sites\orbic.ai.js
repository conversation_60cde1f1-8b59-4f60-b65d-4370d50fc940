// Orbic.ai 网站规则配置
// 网站: https://orbic.ai/submit/tools
// 最后更新: 2025-07-24

export const SITE_RULE = {
  domain: 'orbic.ai',
  siteName: 'Orbic AI',
  priority: 1,
  lastUpdated: '2025-07-24',

  fieldMappings: {
    siteName: {
      selectors: [
        'input[name="title"]',
        'input[placeholder="Enter the title of the product"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '工具标题'
    },

    tagline: {
      selectors: [
        'input[name="tagline"]',
        'input[placeholder="Enter the description of the tool"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '工具标语'
    },

    siteDescription: {
      selectors: [
        '.bn-editor .ProseMirror',
        '.bn-block-content h1',
        '[contenteditable="true"]'
      ],
      method: 'innerHTML',
      validation: 'required',
      notes: 'Markdown内容编辑器'
    },

    siteUrl: {
      selectors: [
        'input[name="url"]',
        '#url',
        'input[type="url"]'
      ],
      method: 'value',
      validation: 'required|url',
      notes: '工具网站链接'
    },

    tags: {
      selectors: [
        'input[placeholder="Enter the topics"]',
        '[cmdk-input]',
        '.flex-1.bg-transparent'
      ],
      method: 'value',
      validation: 'required',
      notes: '主题标签'
    },

    pricing: {
      selectors: [
        'select[name="priceType"]',
        '#priceType'
      ],
      method: 'select',
      validation: 'required',
      defaultValue: 'freemium',
      notes: '定价类型'
    },

    price: {
      selectors: [
        'input[name="priceValue"]',
        '#priceValue',
        'input[type="number"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '价格数值'
    },

    priceDuration: {
      selectors: [
        'select[name="priceDuration"]',
        '#priceDuration'
      ],
      method: 'select',
      validation: 'required',
      defaultValue: 'month',
      notes: '价格周期'
    }
  },

  submitConfig: {
    submitButton: 'button[type="submit"], .btn.btn-primary:last-of-type',
    submitMethod: 'click',
    successIndicators: ['.success-message'],
    errorIndicators: ['.error-message']
  },

  specialHandling: {
    requiresLogin: false,
    hasCaptcha: false,
    hasFileUpload: true,
    customScript: 'handleOrbicSubmission',
    formValidation: {
      requiredFields: ['siteName', 'tagline', 'siteDescription', 'siteUrl', 'tags', 'pricing', 'price', 'priceDuration'],
      emailValidation: false,
      urlValidation: true
    },
    notes: [
      '需要上传Logo和截图',
      '使用Markdown编辑器',
      '有复杂的定价选项',
      '支持社交链接和视频',
      '使用现代UI组件',
      '有主题标签输入'
    ]
  }
};

export function handleOrbicSubmission(data, rule) {
  const processedData = { ...data };

  if (processedData.siteUrl && !processedData.siteUrl.startsWith('http')) {
    processedData.siteUrl = 'https://' + processedData.siteUrl;
  }

  if (processedData.pricing) {
    const pricingMap = {
      'Free': 'free',
      'Paid': 'paid',
      'Freemium': 'freemium',
      'Open Source': 'open_source',
      'Free Trial': 'free_trial',
      'Contact for Pricing': 'contact_for_pricing'
    };
    processedData.pricing = pricingMap[processedData.pricing] || 'freemium';
  }

  if (processedData.priceDuration) {
    const durationMap = {
      'One Time': 'one_time',
      'Monthly': 'month',
      'Yearly': 'year',
      'Daily': 'day',
      'Weekly': 'week'
    };
    processedData.priceDuration = durationMap[processedData.priceDuration] || 'month';
  }

  return processedData;
}

export async function customFillElement(element, value, config) {
  if (element.tagName === 'SELECT') {
    const options = element.querySelectorAll('option');
    const option = Array.from(options).find(opt =>
      opt.value === value || opt.textContent.toLowerCase().includes(value.toLowerCase())
    );
    if (option) {
      element.value = option.value;
      element.dispatchEvent(new Event('change', { bubbles: true }));
      return true;
    }
  }

  if (element.classList.contains('ProseMirror')) {
    element.innerHTML = `<div class="bn-block-group"><div class="bn-block-outer"><div class="bn-block"><div class="bn-block-content"><p class="bn-inline-content">${value}</p></div></div></div></div>`;
    element.dispatchEvent(new Event('input', { bubbles: true }));
    return true;
  }

  if (element.tagName === 'INPUT') {
    element.value = value;
    element.dispatchEvent(new Event('input', { bubbles: true }));
    return true;
  }

  return false;
}