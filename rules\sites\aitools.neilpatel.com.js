// aitools.neilpatel.com 网站规则配置
// 网站: https://aitools.neilpatel.com/submit/
// 表单技术: WordPress表单
// 最后更新: 2025-07-07

export const SITE_RULE = {
  // 基本信息
  domain: 'aitools.neilpatel.com',
  siteName: '<PERSON> AI Tools',
  priority: 1,
  lastUpdated: '2025-07-07',
  
  // 字段映射规则
  fieldMappings: {
    // 提交者姓名 -> Name
    fullName: {
      selectors: [
        'input[name*="name"]:not([name*="tool"])',
        'input[placeholder*="Name"]',
        'input[type="text"]:first-of-type',
        '#name',
        '.name-field'
      ],
      method: 'value',
      validation: 'required',
      notes: '提交者姓名，使用website-info.js中的fullName字段'
    },
    
    // 邮箱地址 -> Email address
    contactEmail: {
      selectors: [
        'input[name*="email"]',
        'input[type="email"]',
        'input[placeholder*="Email address"]',
        '#email',
        '.email-field'
      ],
      method: 'value',
      validation: 'required|email',
      notes: '联系邮箱，使用website-info.js中的contactEmail字段'
    },
    
    // 工具名称 -> Tool name
    siteName: {
      selectors: [
        'input[name*="tool"][name*="name"]',
        'input[placeholder*="Tool name"]',
        '#tool-name',
        '.tool-name-field'
      ],
      method: 'value',
      validation: 'required',
      notes: '工具名称，使用website-info.js中的siteName字段'
    },
    
    // 工具URL -> Tool URL
    siteUrl: {
      selectors: [
        'input[name*="tool"][name*="url"]',
        'input[placeholder*="Tool URL"]',
        'input[type="url"]',
        '#tool-url',
        '.tool-url-field'
      ],
      method: 'value',
      validation: 'required|url',
      notes: '工具URL，使用website-info.js中的siteUrl字段'
    },
    
    // 工具标签 -> Tool Tags
    keywords: {
      selectors: [
        'input[name*="tag"]',
        'input[placeholder*="Tool Tags"]',
        'textarea[name*="tag"]',
        '#tool-tags',
        '.tool-tags-field'
      ],
      method: 'value',
      validation: 'required',
      notes: '工具标签，使用website-info.js中的keywords字段'
    },
    
    // 定价模式 -> Tool pricing model
    pricing: {
      selectors: [
        'select[name*="pricing"]',
        'select:contains("Please choose an option")',
        '#tool-pricing',
        '.pricing-select'
      ],
      method: 'select',
      validation: 'required',
      defaultValue: 'Free',
      notes: '定价模式，从下拉列表中选择，默认选择Free'
    },
    
    // 工具描述 -> Tool description
    siteDescription: {
      selectors: [
        'textarea[name*="description"]',
        'textarea[placeholder*="Tool description"]',
        '#tool-description',
        '.tool-description-field'
      ],
      method: 'value',
      validation: 'required',
      notes: '工具描述，使用website-info.js中的siteDescription字段'
    }
  },
  
  // 自定义填写函数
  customFillElement: async function(element, value, config) {
    console.log(`Neil Patel AI Tools自定义填写: ${element.name || element.placeholder}, 方法: ${config.method}`);
    
    switch (config.method) {
      case 'value':
        // 标准输入框和文本域处理
        element.focus();
        await new Promise(resolve => setTimeout(resolve, 100));
        
        // 清空现有内容
        element.value = '';
        await new Promise(resolve => setTimeout(resolve, 50));
        
        // 设置新值
        element.value = value;
        
        // 触发事件
        element.dispatchEvent(new Event('input', { bubbles: true }));
        element.dispatchEvent(new Event('change', { bubbles: true }));
        element.dispatchEvent(new Event('blur', { bubbles: true }));
        
        console.log(`✓ 填写字段: ${element.placeholder || element.name} = "${value.substring(0, 50)}..."`);
        break;
        
      case 'select':
        // 下拉选择框处理
        if (element.tagName.toLowerCase() === 'select') {
          // 定价模式映射
          const pricingMapping = {
            'Free': 'Free',
            'Freemium': 'Freemium',
            'Open Source': 'Source Open',
            'Paid': 'Paid'
          };
          
          // 尝试映射定价模式
          let targetValue = pricingMapping[value] || config.defaultValue;
          
          // 查找匹配的选项
          const option = Array.from(element.options).find(opt => 
            opt.value === targetValue || 
            opt.text === targetValue ||
            opt.text.toLowerCase().includes(targetValue.toLowerCase()) ||
            targetValue.toLowerCase().includes(opt.text.toLowerCase())
          );
          
          if (option) {
            element.value = option.value;
            element.dispatchEvent(new Event('change', { bubbles: true }));
            console.log(`✓ 选择定价模式: ${option.text}`);
          } else {
            // 使用默认值
            const defaultOption = Array.from(element.options).find(opt => 
              opt.text === config.defaultValue || opt.value === config.defaultValue
            );
            if (defaultOption) {
              element.value = defaultOption.value;
              element.dispatchEvent(new Event('change', { bubbles: true }));
              console.log(`✓ 使用默认定价模式: ${defaultOption.text}`);
            }
          }
        }
        break;
        
      default:
        element.value = value;
        element.dispatchEvent(new Event('input', { bubbles: true }));
        element.dispatchEvent(new Event('change', { bubbles: true }));
    }
  },

  // 提交流程配置
  submitConfig: {
    submitButton: [
      'button[type="submit"]',
      'input[type="submit"]',
      'button:contains("Submit")',
      '.submit-button',
      '.submit-tool-button'
    ],
    submitMethod: 'click',
    waitAfterFill: 2000,
    waitAfterSubmit: 5000,
    successIndicators: [
      '.success-message',
      '.thank-you',
      '.confirmation',
      '[class*="success"]',
      'text:contains("submitted")',
      'text:contains("received")'
    ],
    errorIndicators: [
      '.error-message',
      '.validation-error',
      '[class*="error"]'
    ]
  },

  // 特殊处理
  specialHandling: {
    requiresLogin: false,
    hasCaptcha: false,
    hasFileUpload: true, // 有工具特色图片上传
    isNeilPatelSite: true, // Neil Patel的网站

    // 表单验证规则
    formValidation: {
      requiredFields: ['fullName', 'contactEmail', 'siteName', 'siteUrl', 'keywords', 'pricing', 'siteDescription'],
      emailValidation: true,
      urlValidation: true
    },

    // 特殊注意事项
    notes: [
      '这是Neil Patel的AI工具目录网站',
      '表单包含7个字段，全部必填',
      '有文件上传功能（工具特色图片），需要手动处理',
      '定价模式选项：Free, Freemium, Source Open, Paid',
      '工具标签字段可以输入多个标签',
      '网站专注于AI工具收集和推荐',
      '由知名营销专家Neil Patel运营',
      '表单提交后需要审核'
    ]
  }
};

// 自定义处理函数
export function handleNeilPatelAIToolsSubmission(data, _rule) {
  console.log('Processing Neil Patel AI Tools form submission...');

  const processedData = { ...data };

  // 确保URL格式正确
  if (processedData.siteUrl && !processedData.siteUrl.startsWith('http')) {
    processedData.siteUrl = 'https://' + processedData.siteUrl;
  }

  // 处理定价模式映射
  if (processedData.pricing) {
    const pricingMapping = {
      'Free': 'Free',
      'Freemium': 'Freemium',
      'Open Source': 'Source Open',
      'Paid': 'Paid'
    };

    processedData.pricing = pricingMapping[processedData.pricing] || 'Free';
  }

  // 处理工具标签格式
  if (processedData.keywords) {
    // 如果keywords是数组，转换为逗号分隔的字符串
    if (Array.isArray(processedData.keywords)) {
      processedData.keywords = processedData.keywords.join(', ');
    }
  }

  return processedData;
}
