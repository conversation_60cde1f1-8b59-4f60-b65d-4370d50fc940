// best-ai-tools.org 网站规则配置
// 网站: https://www.best-ai-tools.org/submit-tool
// 表单技术: Modern React Components with Tailwind CSS
// 最后更新: 2025-07-07

export const SITE_RULE = {
  // 基本信息
  domain: 'best-ai-tools.org',
  siteName: 'Best AI Tools',
  priority: 1,
  lastUpdated: '2025-07-07',
  
  // 字段映射规则
  fieldMappings: {
    // 工具名称 -> Tool Name
    siteName: {
      selectors: [
        'input[name="name"]',
        'input[placeholder*="Awesome AI Writer"]',
        'input[id*="form-item"]:first-of-type'
      ],
      method: 'value',
      validation: 'required',
      notes: '工具名称，使用website-info.js中的siteName字段'
    },
    
    // 网站URL -> Website URL
    siteUrl: {
      selectors: [
        'input[name="websiteUrl"]',
        'input[placeholder="https://example.com"]',
        'input[id*="form-item"][placeholder*="https://"]'
      ],
      method: 'value',
      validation: 'required|url',
      notes: '网站URL，使用website-info.js中的siteUrl字段'
    },
    
    // 标语 -> Slogan (Optional)
    slogan: {
      selectors: [
        'input[name="slogan"]',
        'input[placeholder*="future of AI innovation"]',
        'input[id*="form-item"][placeholder*="innovation"]'
      ],
      method: 'value',
      validation: 'optional',
      defaultValue: 'Innovative AI Tool for Enhanced Productivity',
      notes: '工具标语，使用默认值'
    },
    
    // 分类 -> Category
    category: {
      selectors: [
        'input[name="category"]',
        'input[placeholder*="Image Generation"]',
        'input[id*="form-item"][placeholder*="Generation"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '工具分类，使用website-info.js中的category字段'
    },
    
    // 描述 -> Description
    siteDescription: {
      selectors: [
        'textarea[name="description"]',
        'textarea[placeholder*="Briefly describe"]',
        'textarea[class*="min-h-[100px]"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '工具描述，使用website-info.js中的siteDescription字段，最少20字符'
    },
    
    // 定价模式 -> Pricing Model
    pricing: {
      selectors: [
        'button[role="combobox"]',
        'button[aria-controls*="radix"]',
        'select[aria-hidden="true"]'
      ],
      method: 'combobox',
      validation: 'required',
      defaultValue: 'Free',
      notes: '定价模式，使用Combobox组件，默认选择Free'
    },
    
    // 标签 -> Tags (Optional)
    keywords: {
      selectors: [
        'input[name="tags"]',
        'input[placeholder*="ai art, text-to-image"]',
        'input[id*="form-item"][placeholder*="creative"]'
      ],
      method: 'value',
      validation: 'optional',
      notes: '工具标签，使用website-info.js中的keywords字段，逗号分隔'
    },
    
    // 提交者邮箱 -> Your Email (Optional)
    contactEmail: {
      selectors: [
        'input[name="submitterEmail"]',
        'input[type="email"]',
        'input[placeholder*="<EMAIL>"]'
      ],
      method: 'value',
      validation: 'optional|email',
      notes: '提交者邮箱，使用website-info.js中的contactEmail字段'
    },
    
    // 附加说明 -> Additional Notes (Optional)
    additionalNotes: {
      selectors: [
        'textarea[name="notes"]',
        'textarea[placeholder*="Any other information"]',
        'textarea[class*="min-h-[80px]"]'
      ],
      method: 'value',
      validation: 'optional',
      defaultValue: 'This is an innovative AI tool that provides excellent features and user experience.',
      notes: '附加说明，使用默认值'
    }
  },
  
  // 自定义填写函数
  customFillElement: async function(element, value, config) {
    console.log(`Best AI Tools自定义填写: ${element.name || element.placeholder}, 方法: ${config.method}`);
    
    switch (config.method) {
      case 'value':
        // 标准输入框和文本域处理
        element.focus();
        await new Promise(resolve => setTimeout(resolve, 100));
        
        // 清空现有内容
        element.value = '';
        await new Promise(resolve => setTimeout(resolve, 50));
        
        // 处理特殊字段
        let finalValue = value;
        if (element.name === 'slogan') {
          // 标语使用默认值
          finalValue = config.defaultValue;
        } else if (element.name === 'description') {
          // 描述确保至少20字符
          if (value.length < 20) {
            finalValue = value + '. This AI tool provides innovative solutions with advanced features and capabilities.';
          }
        } else if (element.name === 'tags' && Array.isArray(value)) {
          // 标签转换为逗号分隔的字符串
          finalValue = value.join(', ');
        } else if (element.name === 'notes') {
          // 附加说明使用默认值
          finalValue = config.defaultValue;
        }
        
        // 设置新值
        element.value = finalValue;
        
        // 触发事件
        element.dispatchEvent(new Event('input', { bubbles: true }));
        element.dispatchEvent(new Event('change', { bubbles: true }));
        element.dispatchEvent(new Event('blur', { bubbles: true }));
        
        console.log(`✓ 填写字段: ${element.name} = "${finalValue.substring(0, 50)}..."`);
        break;
        
      case 'combobox':
        // Combobox组件处理 - 定价模式
        try {
          // 点击combobox按钮打开选项
          element.click();
          console.log('点击打开Combobox组件');
          
          // 等待选项加载
          await new Promise(resolve => setTimeout(resolve, 800));
          
          // 查找Free选项
          const options = document.querySelectorAll('[role="option"], .radix-select-item, [data-value]');
          let targetOption = null;
          
          // 定价映射
          const pricingMapping = {
            'Free': 'Free',
            'Freemium': 'Freemium',
            'Paid': 'Paid',
            'Subscription': 'Subscription'
          };
          
          const targetValue = pricingMapping[value] || config.defaultValue;
          
          for (const option of options) {
            const text = (option.textContent || option.innerText || '').trim();
            if (text === targetValue || text === config.defaultValue) {
              targetOption = option;
              break;
            }
          }
          
          if (targetOption) {
            targetOption.click();
            console.log(`✓ 选择定价模式: ${targetOption.textContent}`);
          } else {
            // 如果没找到，尝试通过隐藏的select元素
            const hiddenSelect = document.querySelector('select[aria-hidden="true"]');
            if (hiddenSelect) {
              const option = Array.from(hiddenSelect.options).find(opt => 
                opt.value === config.defaultValue || opt.text === config.defaultValue
              );
              if (option) {
                hiddenSelect.value = option.value;
                hiddenSelect.dispatchEvent(new Event('change', { bubbles: true }));
                console.log(`✓ 通过隐藏select选择: ${option.text}`);
              }
            }
          }
        } catch (error) {
          console.error('Combobox处理出错:', error);
        }
        break;
        
      default:
        element.value = value;
        element.dispatchEvent(new Event('input', { bubbles: true }));
        element.dispatchEvent(new Event('change', { bubbles: true }));
    }
  },
  
  // 提交流程配置
  submitConfig: {
    submitButton: [
      'button[type="submit"]',
      'button:contains("Submit Tool")',
      '.bg-primary[type="submit"]'
    ],
    submitMethod: 'click',
    waitAfterFill: 2000,
    waitAfterSubmit: 5000,
    successIndicators: [
      '.success-message',
      '.alert-success',
      '[class*="success"]',
      'text:contains("submitted")'
    ],
    errorIndicators: [
      '.error-message',
      '.alert-error',
      '[class*="error"]'
    ]
  },
  
  // 特殊处理
  specialHandling: {
    requiresLogin: false,
    hasCaptcha: false,
    hasFileUpload: false,
    isModernReact: true, // 使用现代React组件
    hasTailwindCSS: true, // 使用Tailwind CSS
    isDemoForm: true, // 演示表单，不保存数据
    
    // 表单验证规则
    formValidation: {
      requiredFields: ['siteName', 'siteUrl', 'category', 'siteDescription', 'pricing'],
      emailValidation: true,
      urlValidation: true,
      characterLimits: {
        siteDescription: { min: 20 }
      }
    },
    
    // 特殊注意事项
    notes: [
      '这是现代React网站，使用Tailwind CSS',
      '表单包含9个字段，5个必填，4个可选',
      '这是演示表单，数据不会保存到数据库',
      '描述字段要求最少20字符',
      '定价模式使用Combobox组件',
      '标签字段支持逗号分隔的关键词',
      '字段ID包含随机字符串',
      '使用现代化的UI组件和动画效果',
      '提交后显示成功消息但不保存数据'
    ]
  }
};

// 自定义处理函数
export function handleBestAIToolsSubmission(data, _rule) {
  console.log('Processing Best AI Tools form submission...');

  const processedData = { ...data };

  // 确保URL格式正确
  if (processedData.siteUrl && !processedData.siteUrl.startsWith('http')) {
    processedData.siteUrl = 'https://' + processedData.siteUrl;
  }

  // 处理描述长度要求
  if (processedData.siteDescription && processedData.siteDescription.length < 20) {
    processedData.siteDescription += '. This AI tool provides innovative solutions with advanced features and capabilities.';
  }

  // 处理标签格式
  if (processedData.keywords) {
    if (Array.isArray(processedData.keywords)) {
      processedData.keywords = processedData.keywords.join(', ');
    }
  }

  // 设置默认值
  processedData.slogan = 'Innovative AI Tool for Enhanced Productivity';
  processedData.pricing = 'Free';
  processedData.additionalNotes = 'This is an innovative AI tool that provides excellent features and user experience.';

  return processedData;
}
