// worldweb-directory.com 网站规则配置
// 网站: https://www.worldweb-directory.com/add.php
// 表单技术: PHP Form with Modern UI
// 最后更新: 2025-07-08

export const SITE_RULE = {
  // 基本信息
  domain: 'worldweb-directory.com',
  siteName: 'World Web Directory',
  priority: 1,
  lastUpdated: '2025-07-08',
  
  // 字段映射规则
  fieldMappings: {
    // 定价选项 -> Choose a Submission Plan
    linkType: {
      selectors: [
        'input[name="LINK_TYPE"][value="normal"]',
        'input[type="radio"][value="normal"]',
        'input[value="normal"]'
      ],
      method: 'radio',
      validation: 'required',
      defaultValue: 'normal',
      availableOptions: ['featured', 'normal'],
      notes: '定价选项，默认选择normal (Regular links，免费)'
    },
    
    // 网站标题 -> Website Title
    siteName: {
      selectors: [
        'input[name="TITLE"]',
        'input[placeholder="Website Title"]',
        'input.text:first-of-type',
        'input[size="60"]:first-of-type'
      ],
      method: 'value',
      validation: 'required',
      notes: '网站标题，使用website-info.js中的siteName字段'
    },
    
    // 网站URL -> Website Url
    siteUrl: {
      selectors: [
        'input[name="URL"]',
        'input[placeholder="Website Url"]',
        'input[maxlength="255"]',
        'input[size="60"]:nth-of-type(2)'
      ],
      method: 'value',
      validation: 'required|url',
      notes: '网站URL，使用website-info.js中的siteUrl字段'
    },
    
    // 描述 -> Description
    detailedIntro: {
      selectors: [
        'textarea[name="DESCRIPTION"]',
        'textarea[placeholder="Description"]',
        'textarea[rows="5"]',
        'textarea[cols="61"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '网站描述，使用website-info.js中的detailedIntro字段'
    },
    
    // 您的姓名 -> Your Name
    fullName: {
      selectors: [
        'input[name="OWNER_NAME"]',
        'input[placeholder="Your Name"]',
        'input[size="60"]:nth-of-type(3)',
        'input.text:nth-of-type(3)'
      ],
      method: 'value',
      validation: 'required',
      notes: '您的姓名，使用website-info.js中的fullName字段'
    },
    
    // 您的邮箱 -> Your Email Address
    contactEmail: {
      selectors: [
        'input[name="OWNER_EMAIL"]',
        'input[placeholder="Your Email Address"]',
        'input[size="60"]:nth-of-type(4)',
        'input.text:nth-of-type(4)'
      ],
      method: 'value',
      validation: 'required|email',
      notes: '您的邮箱，使用website-info.js中的contactEmail字段'
    }
  },
  
  // 自定义填写函数
  customFillElement: async function(element, value, config) {
    console.log(`World Web Directory自定义填写: ${element.name || element.type}, 方法: ${config.method}`);
    
    switch (config.method) {
      case 'value':
        // 标准输入框和文本域处理
        element.focus();
        await new Promise(resolve => setTimeout(resolve, 100));
        
        // 清空现有内容
        element.value = '';
        await new Promise(resolve => setTimeout(resolve, 50));
        
        // 设置新值
        element.value = value;
        
        // 触发事件
        element.dispatchEvent(new Event('input', { bubbles: true }));
        element.dispatchEvent(new Event('change', { bubbles: true }));
        element.dispatchEvent(new Event('blur', { bubbles: true }));
        
        console.log(`✓ 填写字段: ${element.name} = "${value.substring(0, 50)}..."`);
        break;
        
      case 'radio':
        // 单选按钮处理
        console.log(`处理定价选项，目标值: ${config.defaultValue}`);
        
        // 查找所有同名单选按钮
        const radioButtons = document.querySelectorAll('input[name="LINK_TYPE"]');
        
        // 先取消所有选择
        radioButtons.forEach(rb => {
          rb.checked = false;
        });
        
        // 选择目标选项
        const targetRadio = Array.from(radioButtons).find(rb => 
          rb.value === config.defaultValue
        );
        
        if (targetRadio) {
          targetRadio.checked = true;
          targetRadio.dispatchEvent(new Event('change', { bubbles: true }));
          console.log(`✓ 选择定价选项: Regular links (${config.defaultValue})`);
        } else {
          console.log(`⚠️ 未找到定价选项: ${config.defaultValue}`);
        }
        break;
        
      default:
        element.value = value;
        element.dispatchEvent(new Event('input', { bubbles: true }));
        element.dispatchEvent(new Event('change', { bubbles: true }));
    }
  },
  
  // 提交流程配置
  submitConfig: {
    submitButton: [
      'button[type="submit"]',
      'input[type="submit"]',
      'button:contains("Submit")',
      'input[value*="Submit"]'
    ],
    submitMethod: 'click',
    waitAfterFill: 2000,
    waitAfterSubmit: 5000,
    successIndicators: [
      'text:contains("submitted")',
      'text:contains("thank you")',
      'text:contains("success")',
      'text:contains("approved")'
    ],
    errorIndicators: [
      'text:contains("error")',
      'text:contains("required")',
      'text:contains("invalid")',
      'text:contains("captcha")'
    ]
  },
  
  // 特殊处理
  specialHandling: {
    requiresLogin: false,
    hasCaptcha: false, // 无验证码
    hasFileUpload: false,
    isPHPForm: true, // PHP表单
    isWorldDirectory: true, // 世界目录
    hasModernUI: true, // 现代化界面
    isSimplifiedForm: true, // 简化表单
    hasPlaceholders: true, // 有占位符
    hasMonthlyPricing: true, // 按月付费
    
    // 表单验证规则
    formValidation: {
      requiredFields: ['linkType', 'siteName', 'siteUrl', 'detailedIntro', 'fullName', 'contactEmail'],
      optionalFields: [],
      emailValidation: true,
      urlValidation: true,
      radioGroups: ['linkType']
    },
    
    // 特殊注意事项
    notes: [
      '这是World Web Directory的网站提交表单',
      '表单包含6个字段，全部必填',
      '世界网站目录，全球范围收录',
      '无验证码保护，提交便捷',
      '默认选择Regular links（免费）',
      '有付费选项：Featured links $7/月（按月付费）',
      '简化表单，只有核心字段',
      '现代化界面设计',
      '使用占位符提示',
      '使用实际字段名：LINK_TYPE, TITLE, URL, DESCRIPTION, OWNER_NAME, OWNER_EMAIL',
      '专注于全球网站收录',
      '按月付费模式，灵活性高',
      '表单简洁，用户体验好'
    ]
  }
};

// 自定义处理函数
export function handleWorldWebDirectorySubmission(data, _rule) {
  console.log('Processing World Web Directory form submission...');

  const processedData = { ...data };

  // 确保URL格式正确
  if (processedData.siteUrl && !processedData.siteUrl.startsWith('http')) {
    processedData.siteUrl = 'https://' + processedData.siteUrl;
  }

  // 设置默认值
  processedData.linkType = 'normal'; // Regular links

  return processedData;
}

// World Web Directory信息提醒
export function showWorldWebDirectoryInfo() {
  console.log('🌍 World Web Directory 信息:');
  console.log('');
  console.log('平台特色:');
  console.log('- 世界网站目录');
  console.log('- 全球范围收录');
  console.log('- 现代化界面设计');
  console.log('- 简化的表单结构');
  console.log('- 无验证码，提交便捷');
  console.log('');
  console.log('提交选项 (2种):');
  console.log('1. Featured links - $7/月');
  console.log('   - 按月付费');
  console.log('   - 灵活性高');
  console.log('   - 可随时取消');
  console.log('');
  console.log('2. Regular links - 免费 ✅ 默认选择');
  console.log('   - 完全免费');
  console.log('   - 标准收录');
  console.log('   - 无时间限制');
  console.log('');
  console.log('表单特点:');
  console.log('- 只有6个字段，全部必填');
  console.log('- 使用占位符提示');
  console.log('- 现代化设计');
  console.log('- 无META字段');
  console.log('- 无分类选择');
  console.log('');
  console.log('World Web Directory - 全球网站收录平台！');
}

// 按月付费模式分析
export function showMonthlyPricingAnalysis() {
  console.log('📅 按月付费模式分析:');
  console.log('');
  console.log('World Web Directory特色:');
  console.log('- 价格: $7/月');
  console.log('- 付费模式: 按月订阅');
  console.log('- 灵活性: 可随时取消');
  console.log('');
  console.log('与年费模式对比:');
  console.log('- Quality Internet: $5.79/年 = $0.48/月');
  console.log('- All States USA: $6.79/年 = $0.57/月');
  console.log('- Free Internet: $6.97/年 = $0.58/月');
  console.log('- UK Internet: $7.97/年 = $0.66/月');
  console.log('- World Web: $7/月 = $84/年 ⚠️ 最贵');
  console.log('');
  console.log('适用场景:');
  console.log('- 短期推广需求');
  console.log('- 测试付费效果');
  console.log('- 预算灵活调整');
  console.log('- 避免长期承诺');
  console.log('');
  console.log('注意事项:');
  console.log('- 长期使用成本高');
  console.log('- 需要定期续费');
  console.log('- 适合短期项目');
  console.log('- 灵活性是主要优势');
}

// 简化表单优势
export function showSimplifiedFormAdvantages() {
  console.log('✨ 简化表单优势分析:');
  console.log('');
  console.log('World Web Directory表单特点:');
  console.log('- 只有6个字段');
  console.log('- 全部必填，无可选');
  console.log('- 无META字段');
  console.log('- 无分类选择');
  console.log('- 无验证码');
  console.log('');
  console.log('与其他目录对比:');
  console.log('- Australia Web: 7个字段');
  console.log('- All States USA: 8个字段');
  console.log('- Free PR Web: 8个字段');
  console.log('- Free Internet: 8个字段');
  console.log('- Quality Internet: 8个字段');
  console.log('- UK Internet: 8个字段');
  console.log('- USA Websites: 8个字段');
  console.log('- Info Listings: 6个字段');
  console.log('- World Web: 6个字段 ✅ 最简单');
  console.log('');
  console.log('用户体验优势:');
  console.log('- 填写时间最短');
  console.log('- 降低用户流失率');
  console.log('- 提高完成率');
  console.log('- 减少错误概率');
  console.log('- 现代化界面');
}

// 现代化界面特点
export function showModernUIFeatures() {
  console.log('🎨 现代化界面特点:');
  console.log('');
  console.log('设计特色:');
  console.log('- 使用占位符提示');
  console.log('- 清晰的字段标识');
  console.log('- 现代化布局');
  console.log('- 响应式设计');
  console.log('');
  console.log('用户体验:');
  console.log('- 直观的操作流程');
  console.log('- 友好的提示信息');
  console.log('- 减少认知负担');
  console.log('- 提高填写效率');
  console.log('');
  console.log('技术优势:');
  console.log('- 无验证码干扰');
  console.log('- 快速提交');
  console.log('- 移动端友好');
  console.log('- 加载速度快');
  console.log('');
  console.log('占位符示例:');
  console.log('- "Website Title"');
  console.log('- "Website Url"');
  console.log('- "Description"');
  console.log('- "Your Name"');
  console.log('- "Your Email Address"');
}

// 表单验证
export function validateWorldWebDirectoryForm() {
  console.log('验证World Web Directory表单...');

  const requiredFields = [
    { selector: 'input[name="TITLE"]', label: '网站标题' },
    { selector: 'input[name="URL"]', label: '网站URL' },
    { selector: 'textarea[name="DESCRIPTION"]', label: '网站描述' },
    { selector: 'input[name="OWNER_NAME"]', label: '您的姓名' },
    { selector: 'input[name="OWNER_EMAIL"]', label: '您的邮箱' }
  ];

  let isValid = true;

  requiredFields.forEach(field => {
    const element = document.querySelector(field.selector);
    if (!element || !element.value.trim()) {
      console.log(`⚠️ 必填字段为空: ${field.label}`);
      isValid = false;
    }
  });

  // 检查定价选项
  const radioButtons = document.querySelectorAll('input[name="LINK_TYPE"]:checked');
  if (radioButtons.length === 0) {
    console.log('⚠️ 请选择定价选项');
    isValid = false;
  }

  // 检查URL格式
  const urlField = document.querySelector('input[name="URL"]');
  if (urlField && urlField.value && !urlField.value.match(/^https?:\/\//)) {
    console.log('⚠️ URL格式可能不正确，建议包含http://或https://');
  }

  if (isValid) {
    console.log('✓ 表单验证通过');
  }

  return isValid;
}

// 全球目录优势
export function showGlobalDirectoryAdvantages() {
  console.log('🌍 全球目录优势:');
  console.log('');
  console.log('覆盖范围:');
  console.log('- 全球网站收录');
  console.log('- 无地理限制');
  console.log('- 多语言支持');
  console.log('- 国际化视野');
  console.log('');
  console.log('SEO价值:');
  console.log('- 全球链接权重');
  console.log('- 国际化信号');
  console.log('- 多地区流量');
  console.log('- 品牌国际化');
  console.log('');
  console.log('商业价值:');
  console.log('- 扩大市场覆盖');
  console.log('- 提高国际知名度');
  console.log('- 增加商业机会');
  console.log('- 建立全球形象');
  console.log('');
  console.log('适合网站类型:');
  console.log('- 国际化企业');
  console.log('- 跨境电商');
  console.log('- 全球服务商');
  console.log('- 多语言网站');
}

// 目录网站最终统计
export function showFinalDirectoryStatistics() {
  console.log('📊 目录网站最终统计 (9个):');
  console.log('');
  console.log('地理分布:');
  console.log('🇦🇺 澳大利亚: 1个');
  console.log('🇺🇸 美国: 2个');
  console.log('🇬🇧 英国: 1个');
  console.log('🌍 全球: 5个');
  console.log('');
  console.log('付费模式分布:');
  console.log('💰 年费: 7个');
  console.log('💰 月费: 1个 (World Web)');
  console.log('💰 终身: 1个 (Info Listings)');
  console.log('');
  console.log('表单复杂度:');
  console.log('📝 6字段: 2个 (最简单)');
  console.log('📝 7字段: 1个');
  console.log('📝 8字段: 6个 (最复杂)');
  console.log('');
  console.log('特色功能统计:');
  console.log('🔗 互惠链接: 4个');
  console.log('📝 META字段: 6个');
  console.log('🎨 现代界面: 1个 (World Web)');
  console.log('🤖 无验证码: 1个 (World Web)');
  console.log('');
  console.log('价格范围:');
  console.log('💰 $5-7: 5个');
  console.log('💰 $7-10: 2个');
  console.log('💰 $10+: 1个');
  console.log('💰 特殊: 1个 (月费模式)');
}
