// ToolHunter.ai 网站规则配置
// 网站: https://www.toolhunter.ai/submit-a-tool
// 最后更新: 2025-07-24

export const SITE_RULE = {
  domain: 'www.toolhunter.ai',
  siteName: 'Tool Hunter AI',
  priority: 1,
  lastUpdated: '2025-07-24',

  fieldMappings: {
    siteName: {
      selectors: [
        '#Tool-Name',
        'input[name="Tool-Name"]',
        'input[placeholder="Tool Name"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '工具名称'
    },

    siteUrl: {
      selectors: [
        '#Website',
        'input[name="Website"]',
        'input[placeholder="yourwebsite.com"]'
      ],
      method: 'value',
      validation: 'required|url',
      notes: '工具网站'
    },

    category: {
      selectors: [
        '#field-2',
        'select[name="field-2"]',
        '.contact_text_field.select'
      ],
      method: 'select',
      validation: 'required',
      defaultValue: 'Productivity',
      notes: '工具分类，默认Productivity'
    },

    siteDescription: {
      selectors: [
        '.contact_text_area',
        'textarea[placeholder*="Comprehensive description"]',
        'textarea[rows="6"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '产品详细描述'
    },

    instagramUrl: {
      selectors: [
        '#Instagram',
        'input[name="Instagram"]',
        'input[placeholder="instagram.com/yourtool"]'
      ],
      method: 'value',
      validation: 'optional|url',
      notes: 'Instagram链接'
    },

    facebookUrl: {
      selectors: [
        '#Facebook',
        'input[name="Facebook"]',
        'input[placeholder="facebook.com/yourtool"]'
      ],
      method: 'value',
      validation: 'optional|url',
      notes: 'Facebook链接'
    },

    linkedinUrl: {
      selectors: [
        '#Linkedin',
        'input[name="Linkedin"]',
        'input[placeholder="linkedin.com/in/yourtool"]'
      ],
      method: 'value',
      validation: 'optional|url',
      notes: 'LinkedIn链接'
    },

    twitterUrl: {
      selectors: [
        '#Twiiter',
        'input[name="Twiiter"]',
        'input[placeholder="twitter.com/yourtool"]'
      ],
      method: 'value',
      validation: 'optional|url',
      notes: 'Twitter链接'
    },

    contactEmail: {
      selectors: [
        '#Email',
        'input[name="Email"]',
        'input[type="email"]'
      ],
      method: 'value',
      validation: 'required|email',
      notes: '联系邮箱'
    },

    foundedYear: {
      selectors: [
        '#Date',
        'input[name="Date"]',
        'input[placeholder="date founded dd/mm/yyyy"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '成立日期，格式dd/mm/yyyy'
    }
  },

  submitConfig: {
    submitButton: 'input[type="submit"], .submit-button-copy-2',
    submitMethod: 'click',
    successIndicators: ['.w-form-done'],
    errorIndicators: ['.w-form-fail']
  },

  specialHandling: {
    requiresLogin: false,
    hasCaptcha: false,
    hasFileUpload: false,
    customScript: 'handleToolHunterSubmission',
    formValidation: {
      requiredFields: ['siteName', 'siteUrl', 'category', 'siteDescription', 'contactEmail', 'foundedYear'],
      emailValidation: true,
      urlValidation: true
    },
    notes: [
      'AI工具目录提交平台',
      'Webflow表单系统',
      '多个社交媒体链接',
      '动态分类选择',
      '日期格式dd/mm/yyyy',
      'GET方法提交'
    ]
  }
};

export function handleToolHunterSubmission(data, rule) {
  const processedData = { ...data };

  // URL格式化
  const urlFields = ['siteUrl', 'instagramUrl', 'facebookUrl', 'linkedinUrl', 'twitterUrl'];
  urlFields.forEach(field => {
    if (processedData[field] && !processedData[field].startsWith('http')) {
      processedData[field] = 'https://' + processedData[field];
    }
  });

  // 日期格式化为dd/mm/yyyy
  if (processedData.foundedYear && !processedData.foundedYear.includes('/')) {
    const year = processedData.foundedYear;
    processedData.foundedYear = `01/01/${year}`;
  }

  // 自动选择默认分类
  const categorySelect = document.querySelector('#field-2');
  if (categorySelect) {
    categorySelect.value = 'Productivity';
  }

  return processedData;
}

export async function customFillElement(element, value, config) {
  // 处理下拉选择框
  if (element.tagName === 'SELECT') {
    const options = element.querySelectorAll('option');
    const option = Array.from(options).find(opt => opt.value === 'Productivity');
    if (option) {
      element.value = option.value;
      element.dispatchEvent(new Event('change', { bubbles: true }));
      return true;
    }
  }

  // 处理日期字段
  if (element.name === 'Date') {
    let dateValue = value;
    if (dateValue && !dateValue.includes('/')) {
      dateValue = `01/01/${dateValue}`;
    }
    element.value = dateValue;
    element.dispatchEvent(new Event('input', { bubbles: true }));
    element.dispatchEvent(new Event('change', { bubbles: true }));
    return true;
  }

  if (element.tagName === 'INPUT' || element.tagName === 'TEXTAREA') {
    element.value = value;
    element.dispatchEvent(new Event('input', { bubbles: true }));
    element.dispatchEvent(new Event('change', { bubbles: true }));
    return true;
  }

  return false;
}