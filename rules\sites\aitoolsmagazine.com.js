// AiToolsMagazine.com 网站规则配置
// 网站: https://aitoolsmagazine.com/submit-listing/details/
// 最后更新: 2025-07-09

export const SITE_RULE = {
  // 基本信息
  domain: 'aitoolsmagazine.com',
  siteName: 'AiToolsMagazine',
  priority: 1,
  lastUpdated: '2025-07-09',
  
  // 字段映射规则
  fieldMappings: {
    // 分类 -> Category (选择AI PRODUCTIVITY TOOLS)
    category: {
      selectors: [
        'select[name="categories"]',
        '.select2-selection--single'
      ],
      method: 'select',
      validation: 'required',
      targetValue: '278',
      defaultValue: 'AI PRODUCTIVITY TOOLS',
      notes: '产品分类，选择AI PRODUCTIVITY TOOLS'
    },
    
    // 标题 -> Title
    siteName: {
      selectors: [
        'input[name="title"]',
        '.hp-field--text[name="title"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '工具标题'
    },
    
    // 摘要 -> Excerpt
    siteDescription: {
      selectors: [
        'input[name="excerpt"]',
        'input[placeholder="One Line Explanation"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '一行解释说明'
    },
    
    // 定价模式 -> Pricing Model (选择Free)
    pricing: {
      selectors: [
        'select[name="pricingtype[]"]',
        '.select2-selection--multiple:first-of-type'
      ],
      method: 'select',
      validation: 'required',
      targetValue: '223',
      defaultValue: 'Free',
      notes: '定价模式，选择Free'
    },
    
    // 集成 -> Integrations
    integrations: {
      selectors: [
        'input[name="integration"]',
        'input[placeholder="Integration with other Platforms"]'
      ],
      method: 'value',
      validation: 'optional',
      notes: '与其他平台的集成'
    },
    
    // 信用卡要求 -> Credit Card Required (选择No)
    creditCardRequired: {
      selectors: [
        'select[name="creditcard[]"]',
        '.select2-selection--multiple:nth-of-type(2)'
      ],
      method: 'select',
      validation: 'required',
      targetValue: '227',
      defaultValue: 'No',
      notes: '是否需要信用卡，选择No'
    },
    
    // AI工具URL -> URL of AI Tool
    siteUrl: {
      selectors: [
        'input[name="websitelink"]',
        'input[placeholder="URL of the Website of AI Tool"]',
        'input[type="url"]'
      ],
      method: 'value',
      validation: 'required|url',
      notes: 'AI工具网站URL'
    },
    
    // 支持平台 -> Supported Platforms (选择Web Browser)
    supportedPlatforms: {
      selectors: [
        'select[name="platform[]"]',
        '.select2-selection--multiple:nth-of-type(3)'
      ],
      method: 'select',
      validation: 'required',
      targetValue: '237',
      defaultValue: 'Web Browser',
      notes: '支持的平台，选择Web Browser'
    },
    
    // API可用性 -> API Available (选择No)
    apiAvailable: {
      selectors: [
        'select[name="api[]"]',
        '.select2-selection--multiple:nth-of-type(4)'
      ],
      method: 'select',
      validation: 'required',
      targetValue: '249',
      defaultValue: 'No',
      notes: 'API是否可用，选择No'
    },
    
    // 标签 -> Tags (选择AI PRODUCTIVITY TOOL)
    tags: {
      selectors: [
        'select[name="tags[]"]',
        '.select2-selection--multiple:last-of-type'
      ],
      method: 'select',
      validation: 'optional',
      targetValue: '311',
      defaultValue: 'AI PRODUCTIVITY TOOL',
      notes: '产品标签'
    },
    
    // 描述 -> Description
    detailedIntro: {
      selectors: [
        'textarea[name="description"]',
        '.hp-field--textarea'
      ],
      method: 'value',
      validation: 'required',
      notes: '详细描述'
    }
  },
  
  // 提交流程配置
  submitConfig: {
    submitButton: 'button[type="submit"], .hp-form__button',
    submitMethod: 'click',
    waitAfterFill: 3000, // 填写后等待3秒
    waitAfterSubmit: 5000, // 提交后等待5秒
    successIndicators: [
      '.hp-form__messages--success',
      '.success-message',
      '.alert-success'
    ],
    errorIndicators: [
      '.hp-form__messages--error',
      '.error-message',
      '.alert-error'
    ]
  },
  
  // 特殊处理
  specialHandling: {
    requiresLogin: false,
    hasCaptcha: false,
    hasFileUpload: true, // 有图片上传
    
    // 表单验证规则
    formValidation: {
      requiredFields: ['category', 'siteName', 'siteDescription', 'pricing', 'creditCardRequired', 'siteUrl', 'supportedPlatforms', 'apiAvailable', 'detailedIntro'],
      emailValidation: false,
      urlValidation: true
    },
    
    // 特殊注意事项
    notes: [
      '使用HivePress构建的复杂表单',
      '大量使用Select2多选下拉组件',
      '表单包含11个字段：分类、标题、摘要、定价、集成、信用卡、URL、平台、API、标签、描述',
      '有图片上传功能（可选）',
      '多个必填的多选字段',
      '需要处理Select2组件的特殊选择逻辑'
    ]
  }
};

// 自定义处理函数
export function handleAiToolsMagazineSubmission(data, rule) {
  console.log('Processing AiToolsMagazine.com submission...');
  
  // 特殊处理逻辑
  const processedData = { ...data };
  
  // 确保URL格式正确
  if (processedData.siteUrl && !processedData.siteUrl.startsWith('http')) {
    processedData.siteUrl = 'https://' + processedData.siteUrl;
  }
  
  // 设置默认值
  processedData.category = '278'; // AI PRODUCTIVITY TOOLS
  processedData.pricing = '223'; // Free
  processedData.creditCardRequired = '227'; // No
  processedData.supportedPlatforms = '237'; // Web Browser
  processedData.apiAvailable = '249'; // No
  processedData.tags = '311'; // AI PRODUCTIVITY TOOL
  
  // 设置默认集成信息
  if (!processedData.integrations) {
    processedData.integrations = 'Web-based platform';
  }
  
  return processedData;
}

// 自定义元素填写函数，专门处理Select2组件
export async function customFillElement(element, value, config) {
  console.log('🔧 AiToolsMagazine自定义填写函数被调用:', element, value);

  // 处理Select2下拉选择组件
  if (element.classList.contains('select2-hidden-accessible')) {
    try {
      // 设置原始select的值
      element.value = config.targetValue;

      // 触发Select2更新
      if (window.jQuery && jQuery(element).data('select2')) {
        jQuery(element).val(config.targetValue).trigger('change');
        console.log('✅ 使用jQuery Select2设置值:', config.defaultValue);
        return true;
      }

      // 备用方法：直接操作DOM
      element.dispatchEvent(new Event('change', { bubbles: true }));
      console.log('✅ 使用DOM事件设置Select2值:', config.defaultValue);
      return true;
    } catch (error) {
      console.warn('Select2组件处理失败:', error);
    }
  }

  // 处理Select2的可视化元素
  if (element.classList.contains('select2-selection')) {
    try {
      // 点击打开下拉菜单
      element.click();

      // 等待下拉菜单出现
      await new Promise(resolve => setTimeout(resolve, 500));

      // 查找并点击对应选项
      const option = document.querySelector(`[data-select2-id="${config.targetValue}"]`);
      if (option) {
        option.click();
        console.log('✅ 点击Select2选项:', config.defaultValue);
        return true;
      }

      // 备用方法：查找包含文本的选项
      const textOption = Array.from(document.querySelectorAll('.select2-results__option')).find(
        opt => opt.textContent.includes(config.defaultValue)
      );
      if (textOption) {
        textOption.click();
        console.log('✅ 通过文本匹配点击Select2选项:', config.defaultValue);
        return true;
      }
    } catch (error) {
      console.warn('Select2可视化元素处理失败:', error);
    }
  }

  // 处理HivePress表单字段
  if (element.classList.contains('hp-field')) {
    try {
      element.value = value;
      element.dispatchEvent(new Event('input', { bubbles: true }));
      element.dispatchEvent(new Event('change', { bubbles: true }));
      console.log('✅ 使用HivePress字段填写:', value);
      return true;
    } catch (error) {
      console.warn('HivePress字段处理失败:', error);
    }
  }

  // 默认处理
  return false;
}
