// aitoolbee.com 网站规则配置
// 网站: https://aitoolbee.com/submit/
// 表单技术: WPForms with Choices.js
// 最后更新: 2025-07-08

export const SITE_RULE = {
  // 基本信息
  domain: 'aitoolbee.com',
  siteName: 'AI Tool Bee',
  priority: 1,
  lastUpdated: '2025-07-08',
  
  // 字段映射规则
  fieldMappings: {
    // 姓名（名字） -> Name (First)
    firstName: {
      selectors: [
        'input[id="wpforms-4932-field_0"]',
        'input[name="wpforms[fields][0][first]"]',
        '.wpforms-field-name-first'
      ],
      method: 'value',
      validation: 'required',
      defaultValue: 'Zephyr',
      notes: '名字，固定值：Zephyr'
    },

    // 姓名（姓氏） -> Name (Last)
    lastName: {
      selectors: [
        'input[id="wpforms-4932-field_0-last"]',
        'input[name="wpforms[fields][0][last]"]',
        '.wpforms-field-name-last'
      ],
      method: 'value',
      validation: 'required',
      defaultValue: 'Nightwhisper',
      notes: '姓氏，固定值：Nightwhisper'
    },
    
    // 邮箱地址 -> Email
    contactEmail: {
      selectors: [
        'input[id="wpforms-4932-field_1"]',
        'input[name="wpforms[fields][1]"]',
        'input[type="email"]'
      ],
      method: 'value',
      validation: 'required|email',
      notes: '邮箱地址，使用website-info.js中的contactEmail字段'
    },
    
    // 定价模式 -> Please select pricing modal
    pricing: {
      selectors: [
        'select[id="wpforms-4932-field_4"]',
        '.choices[data-type="select-one"]',
        'select[name="wpforms[fields][4]"]'
      ],
      method: 'choices-select',
      validation: 'required',
      defaultValue: 'Free',
      notes: '定价模式，使用Choices.js组件，默认选择Free'
    },
    
    // 工具名称 -> Name of Tool you would like us to add?
    siteName: {
      selectors: [
        'input[id="wpforms-4932-field_3"]',
        'input[name="wpforms[fields][3]"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '工具名称，使用website-info.js中的siteName字段'
    },
    
    // 工具URL -> Website URL of Tool
    siteUrl: {
      selectors: [
        'input[id="wpforms-4932-field_5"]',
        'input[name="wpforms[fields][5]"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '工具URL，使用website-info.js中的siteUrl字段'
    },
    
    // 视频演示URL -> Video Demo URL of Tool if any....
    videoUrl: {
      selectors: [
        'input[id="wpforms-4932-field_7"]',
        'input[name="wpforms[fields][7]"]'
      ],
      method: 'value',
      validation: 'optional',
      defaultValue: '',
      notes: '视频演示URL，可选字段，留空'
    },
    
    // 工具描述 -> Please explain how you'd like this Tool?
    siteDescription: {
      selectors: [
        'textarea[id="wpforms-4932-field_2"]',
        'textarea[name="wpforms[fields][2]"]'
      ],
      method: 'value',
      validation: 'optional',
      notes: '工具描述，使用website-info.js中的siteDescription字段'
    }
  },
  
  // 自定义填写函数
  customFillElement: async function(element, value, config) {
    console.log(`AI Tool Bee自定义填写: ${element.id || element.name}, 方法: ${config.method}`);
    
    switch (config.method) {
      case 'value':
        // 标准输入框和文本域处理
        element.focus();
        await new Promise(resolve => setTimeout(resolve, 100));
        
        // 清空现有内容
        element.value = '';
        await new Promise(resolve => setTimeout(resolve, 50));
        
        // 处理特殊字段
        let finalValue = value;
        if (element.id === 'wpforms-4932-field_0') {
          // First Name 固定值
          finalValue = config.defaultValue || 'Zephyr';
        } else if (element.id === 'wpforms-4932-field_0-last') {
          // Last Name 固定值
          finalValue = config.defaultValue || 'Nightwhisper';
        } else if (element.id === 'wpforms-4932-field_7') {
          // 视频URL留空
          finalValue = '';
        }
        
        // 设置新值
        element.value = finalValue;
        
        // 触发事件
        element.dispatchEvent(new Event('input', { bubbles: true }));
        element.dispatchEvent(new Event('change', { bubbles: true }));
        element.dispatchEvent(new Event('blur', { bubbles: true }));
        
        console.log(`✓ 填写字段: ${element.id} = "${finalValue.substring(0, 50)}..."`);
        break;
        
      case 'choices-select':
        // Choices.js下拉选择处理
        try {
          // 查找Choices.js容器
          const choicesContainer = element.closest('.choices') || 
                                 document.querySelector('.choices[data-type="select-one"]');
          
          if (choicesContainer) {
            // 点击打开下拉菜单
            choicesContainer.click();
            console.log('点击打开Choices.js下拉菜单');
            
            // 等待菜单展开
            await new Promise(resolve => setTimeout(resolve, 500));
            
            // 查找目标选项
            const targetValue = config.defaultValue || 'Free';

            // 查找具有指定data-value的选项
            let option = document.querySelector(`[data-value="${targetValue}"]`);

            // 如果没找到，尝试查找包含文本的选项
            if (!option) {
              const allOptions = document.querySelectorAll('.choices__item--choice');
              option = Array.from(allOptions).find(opt =>
                opt.textContent.trim() === targetValue
              );
            }

            if (option) {
              option.click();
              console.log(`✓ 选择选项: ${targetValue}`);
            } else {
              // 尝试通过原生select
              const nativeSelect = element;
              if (nativeSelect.tagName.toLowerCase() === 'select') {
                const optionElement = Array.from(nativeSelect.options).find(opt => 
                  opt.value === targetValue || opt.text === targetValue
                );
                if (optionElement) {
                  nativeSelect.value = optionElement.value;
                  nativeSelect.dispatchEvent(new Event('change', { bubbles: true }));
                  console.log(`✓ 通过原生select选择: ${targetValue}`);
                }
              }
            }
          }
        } catch (error) {
          console.error('Choices.js处理出错:', error);
        }
        break;
        
      default:
        element.value = value;
        element.dispatchEvent(new Event('input', { bubbles: true }));
        element.dispatchEvent(new Event('change', { bubbles: true }));
    }
  },
  
  // 提交流程配置
  submitConfig: {
    submitButton: [
      'button[id="wpforms-submit-4932"]',
      'button[type="submit"]',
      '.wpforms-submit',
      'button[name="wpforms[submit]"]'
    ],
    submitMethod: 'click',
    waitAfterFill: 2000,
    waitAfterSubmit: 5000,
    successIndicators: [
      '.wpforms-confirmation-container',
      '.wpforms-confirmation-message',
      'text:contains("Thank you")',
      'text:contains("submitted")'
    ],
    errorIndicators: [
      '.wpforms-error',
      '.wpforms-field-error',
      '[class*="error"]'
    ]
  },
  
  // 特殊处理
  specialHandling: {
    requiresLogin: false,
    hasCaptcha: false,
    hasFileUpload: false,
    isWPForms: true, // 使用WPForms
    hasChoicesJS: true, // 使用Choices.js
    hasNameSplit: true, // 需要分割姓名
    
    // 表单验证规则
    formValidation: {
      requiredFields: ['firstName', 'lastName', 'contactEmail', 'pricing', 'siteName', 'siteUrl'],
      emailValidation: true,
      urlValidation: true,
      choicesValidation: true
    },
    
    // 特殊注意事项
    notes: [
      '这是WordPress网站，使用WPForms插件',
      '表单包含8个字段，6个必填，2个可选',
      '姓名字段分为First和Last两部分',
      '定价模式使用Choices.js组件',
      '视频URL字段为可选，可以留空',
      '表单ID: wpforms-form-4932',
      '字段名格式: wpforms[fields][id]',
      '有AJAX提交功能',
      '定价选项: Free, Freemium, Paid, Unknown, Free Trial, Mobile APP',
      '专注于AI工具目录服务'
    ]
  }
};

// 自定义处理函数
export function handleAIToolBeeSubmission(data, _rule) {
  console.log('Processing AI Tool Bee form submission...');

  const processedData = { ...data };

  // 确保URL格式正确
  if (processedData.siteUrl && !processedData.siteUrl.startsWith('http')) {
    processedData.siteUrl = 'https://' + processedData.siteUrl;
  }

  // 设置超级独特的固定姓名
  processedData.firstName = 'Zephyr';
  processedData.lastName = 'Nightwhisper';

  // 设置默认值
  processedData.pricing = 'Free';
  processedData.videoUrl = '';

  return processedData;
}

// Choices.js处理函数
export async function handleChoicesJS() {
  console.log('处理Choices.js组件...');
  
  // 等待Choices.js初始化
  await new Promise(resolve => setTimeout(resolve, 1000));
  
  // 检查Choices.js是否存在
  const choicesElements = document.querySelectorAll('.choices');
  if (choicesElements.length > 0) {
    console.log(`检测到 ${choicesElements.length} 个Choices.js组件`);
    
    choicesElements.forEach((element, index) => {
      console.log(`Choices.js组件 ${index + 1}: ${element.getAttribute('data-type')}`);
    });
  }
}

// 姓名分割处理
export function splitFullName(fullName) {
  if (!fullName) return { firstName: '', lastName: '' };
  
  const nameParts = fullName.trim().split(' ');
  return {
    firstName: nameParts[0] || '',
    lastName: nameParts.slice(1).join(' ') || nameParts[0] || ''
  };
}
