// PostFreeDirectory.com 网站规则配置
// 网站: https://www.postfreedirectory.com/submit
// 最后更新: 2025-07-24

export const SITE_RULE = {
  domain: 'www.postfreedirectory.com',
  siteName: 'Post Free Directory',
  priority: 1,
  lastUpdated: '2025-07-24',

  fieldMappings: {
    siteName: {
      selectors: [
        'input[name="title"]',
        'input[placeholder="Title"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '网站标题'
    },

    siteUrl: {
      selectors: [
        'input[name="website"]',
        'input[placeholder="URL"]'
      ],
      method: 'value',
      validation: 'required|url',
      notes: '网站URL'
    },

    siteDescription: {
      selectors: [
        'textarea[name="description"]',
        'textarea[placeholder="Description"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '网站描述，不支持HTML标签'
    },

    tags: {
      selectors: [
        'input[name="keywords"]',
        'input[placeholder="Meta Keywords"]'
      ],
      method: 'value',
      validation: 'optional',
      notes: 'META关键词，逗号分隔'
    },

    detailedIntro: {
      selectors: [
        'input[name="smalldesc"]',
        'input[placeholder="Meta Description"]'
      ],
      method: 'value',
      validation: 'optional',
      notes: 'META描述'
    },

    category: {
      selectors: [
        'select[name="category"]',
        '.contact_input3:first-of-type'
      ],
      method: 'select',
      validation: 'required',
      defaultValue: 'computers',
      notes: '主分类选择'
    },

    subCategory: {
      selectors: [
        'select[name="subcat"]',
        '.contact_input3:nth-of-type(2)'
      ],
      method: 'select',
      validation: 'optional',
      notes: '子分类选择'
    },

    fullName: {
      selectors: [
        'input[name="cname"]',
        'input[placeholder="Contact person"]'
      ],
      method: 'value',
      validation: 'optional',
      notes: '联系人姓名'
    },

    contactEmail: {
      selectors: [
        'input[name="email"]',
        'input[placeholder="Contact Email"]'
      ],
      method: 'value',
      validation: 'required|email',
      notes: '联系邮箱，必须有效'
    }
  },

  submitConfig: {
    submitButton: 'button[type="submit"], .itg-btn.subcribes',
    submitMethod: 'click',
    successIndicators: ['.success-message'],
    errorIndicators: ['.error-message']
  },

  specialHandling: {
    requiresLogin: false,
    hasCaptcha: true,
    hasFileUpload: false,
    customScript: 'handlePostFreeDirectorySubmission',
    formValidation: {
      requiredFields: ['siteName', 'siteUrl', 'siteDescription', 'category', 'contactEmail'],
      emailValidation: true,
      urlValidation: true
    },
    notes: [
      '有付费和免费选项',
      '默认选择免费Regular Listing',
      '有验证码验证',
      '有分类和子分类选择',
      '不支持HTML标签',
      '关键词用逗号分隔'
    ]
  }
};

export function handlePostFreeDirectorySubmission(data, rule) {
  const processedData = { ...data };

  if (processedData.siteUrl && !processedData.siteUrl.startsWith('http')) {
    processedData.siteUrl = 'https://' + processedData.siteUrl;
  }

  // 自动选择免费Regular Listing选项
  const regularRadio = document.querySelector('input[name="met"][value="0"]');
  if (regularRadio) {
    regularRadio.checked = true;
  }

  return processedData;
}

export async function customFillElement(element, value, config) {
  if (element.tagName === 'SELECT') {
    const options = element.querySelectorAll('option');
    let option;

    if (element.name === 'category') {
      option = Array.from(options).find(opt => opt.value === 'computers');
    } else {
      option = Array.from(options).find(opt => opt.value === value);
    }

    if (option) {
      element.value = option.value;
      element.dispatchEvent(new Event('change', { bubbles: true }));
      return true;
    }
  }

  if (element.tagName === 'INPUT' || element.tagName === 'TEXTAREA') {
    element.value = value;
    element.dispatchEvent(new Event('input', { bubbles: true }));
    return true;
  }

  return false;
}