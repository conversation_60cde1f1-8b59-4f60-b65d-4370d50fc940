// appliedai.tools 网站规则配置
// 网站: https://appliedai.tools/submit-ai-tool/
// 表单技术: Jetpack Contact Form with WordPress Interactive API
// 最后更新: 2025-07-07

export const SITE_RULE = {
  // 基本信息
  domain: 'appliedai.tools',
  siteName: 'Applied AI Tools',
  priority: 1,
  lastUpdated: '2025-07-07',
  
  // 字段映射规则
  fieldMappings: {
    // 提交者姓名 -> Name
    fullName: {
      selectors: [
        'input[id="g1993-name"]',
        'input[name="g1993-name"]',
        '.grunion-field-name-wrap input',
        'input.name.grunion-field'
      ],
      method: 'value',
      validation: 'required',
      notes: '提交者姓名，使用website-info.js中的fullName字段'
    },
    
    // 邮箱地址 -> Email
    contactEmail: {
      selectors: [
        'input[id="g1993-email"]',
        'input[name="g1993-email"]',
        'input[type="email"]',
        '.grunion-field-email-wrap input',
        'input.email.grunion-field'
      ],
      method: 'value',
      validation: 'required|email',
      notes: '邮箱地址，使用website-info.js中的contactEmail字段'
    },
    
    // AI工具名称 -> AI tool name
    siteName: {
      selectors: [
        'input[id="g1993-aitoolname"]',
        'input[name="g1993-aitoolname"]',
        'input[type="tel"]:first-of-type',
        '.grunion-field-telephone-wrap input:first-of-type'
      ],
      method: 'value',
      validation: 'optional',
      notes: 'AI工具名称，使用website-info.js中的siteName字段'
    },
    
    // 网站 -> Website
    siteUrl: {
      selectors: [
        'input[id="g1993-website"]',
        'input[name="g1993-website"]',
        'input[type="tel"]:last-of-type',
        '.grunion-field-telephone-wrap input:last-of-type'
      ],
      method: 'value',
      validation: 'optional|url',
      notes: '网站URL，使用website-info.js中的siteUrl字段'
    },
    
    // 其他详情或消息 -> Other Details or message (optional)
    additionalMessage: {
      selectors: [
        'textarea[id="contact-form-comment-g1993-otherdetailsormessageoptional"]',
        'textarea[name="g1993-otherdetailsormessageoptional"]',
        '.grunion-field-textarea-wrap textarea',
        'textarea.textarea.grunion-field'
      ],
      method: 'value',
      validation: 'optional',
      defaultValue: 'This is an innovative AI tool that provides excellent features and user experience. We would like to be listed in your directory.',
      notes: '其他详情或消息，可选字段，使用默认值'
    }
  },
  
  // 自定义填写函数
  customFillElement: async function(element, value, config) {
    console.log(`Applied AI Tools自定义填写: ${element.id || element.name}, 方法: ${config.method}`);
    
    switch (config.method) {
      case 'value':
        // 标准输入框和文本域处理
        element.focus();
        await new Promise(resolve => setTimeout(resolve, 100));
        
        // 清空现有内容
        element.value = '';
        await new Promise(resolve => setTimeout(resolve, 50));
        
        // 处理特殊字段
        let finalValue = value;
        if (element.id === 'contact-form-comment-g1993-otherdetailsormessageoptional') {
          // 附加消息使用默认值
          finalValue = config.defaultValue;
        }
        
        // 设置新值
        element.value = finalValue;
        
        // 触发WordPress Interactive API事件
        element.dispatchEvent(new Event('input', { bubbles: true }));
        element.dispatchEvent(new Event('change', { bubbles: true }));
        element.dispatchEvent(new Event('blur', { bubbles: true }));
        
        // 特殊处理：触发WordPress Interactive API的自定义事件
        if (element.hasAttribute('data-wp-on--input')) {
          // 模拟WordPress Interactive API的输入事件
          const inputEvent = new CustomEvent('wp:input', {
            bubbles: true,
            detail: { value: finalValue }
          });
          element.dispatchEvent(inputEvent);
        }
        
        console.log(`✓ 填写字段: ${element.id} = "${finalValue.substring(0, 50)}..."`);
        break;
        
      default:
        element.value = value;
        element.dispatchEvent(new Event('input', { bubbles: true }));
        element.dispatchEvent(new Event('change', { bubbles: true }));
    }
  },
  
  // 提交流程配置
  submitConfig: {
    submitButton: [
      'button.wp-block-button__link',
      'button[data-id-attr="placeholder"]',
      'button:contains("Send")',
      '.wp-block-jetpack-button button'
    ],
    submitMethod: 'click',
    waitAfterFill: 2000,
    waitAfterSubmit: 5000,
    successIndicators: [
      '.contact-form__success',
      '.success-message',
      '.thank-you',
      '[class*="success"]',
      'text:contains("submitted")'
    ],
    errorIndicators: [
      '.contact-form__error',
      '.contact-form__input-error',
      '[class*="error"]',
      '.grunion-field-wrap[data-wp-class--has-errors="true"]'
    ]
  },
  
  // 特殊处理
  specialHandling: {
    requiresLogin: false,
    hasCaptcha: false,
    hasFileUpload: false,
    isJetpackForm: true, // 使用Jetpack联系表单
    hasWordPressInteractive: true, // 使用WordPress Interactive API
    isBusinessContactForm: true, // 商业联系表单
    
    // 表单验证规则
    formValidation: {
      requiredFields: ['fullName', 'contactEmail'],
      emailValidation: true,
      urlValidation: true,
      realTimeValidation: true // 实时验证
    },
    
    // 特殊注意事项
    notes: [
      '这是WordPress网站，使用Jetpack联系表单',
      '表单包含5个字段，2个必填，3个可选',
      '使用WordPress Interactive API进行实时验证',
      '表单会在1-2个工作日内回复',
      '字段ID格式：g1993-{fieldname}',
      '使用Grunion表单系统（Jetpack的一部分）',
      'AI工具名称和网站字段使用tel类型（可能是配置错误）',
      '附加消息字段有20行高度',
      '提交按钮显示加载动画',
      '实时错误验证和显示'
    ]
  }
};

// 自定义处理函数
export function handleAppliedAIToolsSubmission(data, _rule) {
  console.log('Processing Applied AI Tools form submission...');

  const processedData = { ...data };

  // 确保URL格式正确
  if (processedData.siteUrl && !processedData.siteUrl.startsWith('http')) {
    processedData.siteUrl = 'https://' + processedData.siteUrl;
  }

  // 设置默认值
  processedData.additionalMessage = 'This is an innovative AI tool that provides excellent features and user experience. We would like to be listed in your directory.';

  return processedData;
}

// WordPress Interactive API兼容处理
export async function handleWordPressInteractiveForm() {
  console.log('处理WordPress Interactive API表单...');
  
  // 等待WordPress Interactive API初始化
  await new Promise(resolve => setTimeout(resolve, 1000));
  
  // 检查是否有WordPress Interactive API
  if (window.wp && window.wp.interactivity) {
    console.log('检测到WordPress Interactive API');
    
    // 获取表单上下文
    const formElements = document.querySelectorAll('[data-wp-interactive="jetpack/form"]');
    
    for (const element of formElements) {
      // 触发WordPress Interactive API的初始化
      if (element.hasAttribute('data-wp-init')) {
        const initEvent = new CustomEvent('wp:init', { bubbles: true });
        element.dispatchEvent(initEvent);
      }
    }
  }
}

// 实时验证处理
export async function handleRealTimeValidation(element) {
  console.log('处理实时验证...');
  
  // 触发blur事件进行验证
  element.dispatchEvent(new Event('blur', { bubbles: true }));
  
  // 等待验证完成
  await new Promise(resolve => setTimeout(resolve, 300));
  
  // 检查是否有错误
  const errorElement = element.parentElement.querySelector('.contact-form__input-error');
  if (errorElement && errorElement.classList.contains('has-errors')) {
    console.log('⚠️ 字段验证失败:', element.id);
    return false;
  }
  
  console.log('✓ 字段验证通过:', element.id);
  return true;
}
