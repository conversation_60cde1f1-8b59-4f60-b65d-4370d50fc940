// Infrabase.ai 网站规则配置
// 网站: https://infrabase.ai/submit
// 最后更新: 2025-07-24

export const SITE_RULE = {
  domain: 'infrabase.ai',
  siteName: 'Infrabase AI',
  priority: 1,
  lastUpdated: '2025-07-24',

  fieldMappings: {
    siteName: {
      selectors: [
        '#product_name',
        'input[name="product_name"]',
        'input[required]:nth-of-type(1)'
      ],
      method: 'value',
      validation: 'required',
      notes: '产品/服务/工具名称'
    },

    siteUrl: {
      selectors: [
        '#url',
        'input[name="url"]',
        'input[type="url"]'
      ],
      method: 'value',
      validation: 'required|url',
      notes: '工具网站链接'
    },

    contactEmail: {
      selectors: [
        '#email',
        'input[name="email"]',
        'input[type="email"]'
      ],
      method: 'value',
      validation: 'required|email',
      notes: '联系邮箱'
    }
  },

  submitConfig: {
    submitButton: 'input[name="commit"], input[type="submit"]',
    submitMethod: 'click',
    successIndicators: ['.success-message'],
    errorIndicators: ['.error-message']
  },

  specialHandling: {
    requiresLogin: false,
    hasCaptcha: false,
    hasFileUpload: false,
    customScript: 'handleInfrabaseSubmission',
    formValidation: {
      requiredFields: ['siteName', 'siteUrl', 'contactEmail'],
      emailValidation: true,
      urlValidation: true
    },
    notes: [
      '表单结构简洁',
      '只有3个必填字段',
      '有隐藏的name字段但不需要填写',
      '使用Rails框架'
    ]
  }
};

export function handleInfrabaseSubmission(data, rule) {
  const processedData = { ...data };

  if (processedData.siteUrl && !processedData.siteUrl.startsWith('http')) {
    processedData.siteUrl = 'https://' + processedData.siteUrl;
  }

  return processedData;
}

export async function customFillElement(element, value, config) {
  if (element.tagName === 'INPUT') {
    element.value = value;
    element.dispatchEvent(new Event('input', { bubbles: true }));
    element.dispatchEvent(new Event('change', { bubbles: true }));
    return true;
  }

  return false;
}