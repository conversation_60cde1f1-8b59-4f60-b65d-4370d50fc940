// WebSquash.com 网站规则配置
// 网站: https://websquash.com/listings.php
// 最后更新: 2025-07-24

export const SITE_RULE = {
  domain: 'websquash.com',
  siteName: 'WebSquash',
  priority: 1,
  lastUpdated: '2025-07-24',
  
  fieldMappings: {
    firstName: {
      selectors: [
        'input[name="first_name"]',
        '#name',
        'input[type="text"]:first-of-type'
      ],
      method: 'value',
      validation: 'required',
      notes: '提交者名字'
    },

    lastName: {
      selectors: [
        'input[name="last_name"]',
        'input[type="text"]:nth-of-type(2)'
      ],
      method: 'value',
      validation: 'required',
      notes: '提交者姓氏'
    },

    siteUrl: {
      selectors: [
        'input[name="title"]',
        '#title',
        'input[placeholder="Enter URL"]'
      ],
      method: 'value',
      validation: 'required|url',
      notes: '网站URL'
    },

    contactEmail: {
      selectors: [
        'input[name="email"]',
        '#email',
        'input[type="hidden"][name="email"]'
      ],
      method: 'value',
      validation: 'required|email',
      notes: '联系邮箱，隐藏字段'
    }
  },
  
  submitConfig: {
    submitButton: 'button[name="submit"], #myButton',
    submitMethod: 'click',
    successIndicators: ['.success-message'],
    errorIndicators: ['.error-message']
  },
  
  specialHandling: {
    requiresLogin: false,
    hasCaptcha: false,
    hasFileUpload: false,
    customScript: 'handleWebSquashSubmission',
    formValidation: {
      requiredFields: ['firstName', 'lastName', 'siteUrl', 'contactEmail'],
      emailValidation: true,
      urlValidation: true
    },
    notes: [
      '有JavaScript表单验证',
      '邮箱字段为隐藏字段',
      '有加载状态按钮',
      '提交到CGI脚本',
      '简单的确认列表表单',
      '使用Bootstrap样式'
    ]
  }
};

export function handleWebSquashSubmission(data, rule) {
  const processedData = { ...data };
  
  if (processedData.siteUrl && !processedData.siteUrl.startsWith('http')) {
    processedData.siteUrl = 'https://' + processedData.siteUrl;
  }
  
  // 组合全名到隐藏字段
  if (processedData.firstName && processedData.lastName) {
    const fullNameField = document.querySelector('#full_name');
    if (fullNameField) {
      fullNameField.value = `${processedData.firstName} ${processedData.lastName}`;
    }
  }
  
  return processedData;
}

export async function customFillElement(element, value, config) {
  if (element.tagName === 'INPUT') {
    element.value = value;
    element.dispatchEvent(new Event('input', { bubbles: true }));
    element.dispatchEvent(new Event('change', { bubbles: true }));
    return true;
  }
  
  return false;
}
