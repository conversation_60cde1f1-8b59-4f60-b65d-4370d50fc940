// LazarShishmanov.com 网站规则配置
// 网站: https://lazarshishmanov.com/en/best-free-tools/submit-tool/
// 最后更新: 2025-07-24

export const SITE_RULE = {
  domain: 'lazarshishmanov.com',
  siteName: '<PERSON><PERSON>',
  priority: 1,
  lastUpdated: '2025-07-24',

  fieldMappings: {
    contactEmail: {
      selectors: [
        '#et_pb_contact_email_0',
        'input[placeholder="Work Email *"]',
        'input[data-original_id="email"]'
      ],
      method: 'value',
      validation: 'required|email',
      notes: '工作邮箱'
    },

    firstName: {
      selectors: [
        '#et_pb_contact_first_name_0',
        'input[placeholder="First Name *"]',
        'input[data-original_id="first_name"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '名字'
    },

    lastName: {
      selectors: [
        '#et_pb_contact_last_name_0',
        'input[placeholder="Last Name *"]',
        'input[data-original_id="last_name"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '姓氏'
    },

    companyName: {
      selectors: [
        '#et_pb_contact_company_name_0',
        'input[placeholder="Company Name *"]',
        'input[data-original_id="company_name"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '公司名称'
    },

    orgSize: {
      selectors: [
        '#et_pb_contact_orgsize_0',
        'select[data-original_id="orgsize"]',
        '.et_pb_contact_select'
      ],
      method: 'select',
      validation: 'required',
      defaultValue: '0-10 employees',
      notes: '组织规模'
    },

    siteName: {
      selectors: [
        '#et_pb_contact_tool_0',
        'input[placeholder="Tool Name"]',
        'input[data-original_id="tool"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '工具名称'
    },

    siteUrl: {
      selectors: [
        '#et_pb_contact_url_0',
        'input[placeholder="URL (where can I find the tool?)"]',
        'input[data-original_id="url"]'
      ],
      method: 'value',
      validation: 'required|url',
      notes: '工具URL'
    },

    siteDescription: {
      selectors: [
        '#et_pb_contact_message_0',
        'textarea[placeholder="Tell me about your tool"]',
        'textarea[data-original_id="message"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '工具描述'
    }
  },

  submitConfig: {
    submitButton: 'button[name="et_builder_submit_button"], .et_pb_contact_submit',
    submitMethod: 'click',
    successIndicators: ['.et-pb-contact-message'],
    errorIndicators: ['.et_pb_contact_error']
  },

  specialHandling: {
    requiresLogin: false,
    hasCaptcha: true,
    hasFileUpload: false,
    customScript: 'handleLazarShishmanSubmission',
    formValidation: {
      requiredFields: ['contactEmail', 'firstName', 'lastName', 'companyName', 'orgSize', 'siteName', 'siteUrl', 'siteDescription'],
      emailValidation: true,
      urlValidation: true
    },
    notes: [
      '使用Divi主题构建',
      '有reCAPTCHA验证',
      '需要完整的联系信息',
      '有组织规模选择',
      '所有字段都是必填',
      '专业的工具提交表单'
    ]
  }
};

export function handleLazarShishmanSubmission(data, rule) {
  const processedData = { ...data };

  if (processedData.siteUrl && !processedData.siteUrl.startsWith('http')) {
    processedData.siteUrl = 'https://' + processedData.siteUrl;
  }

  return processedData;
}

export async function customFillElement(element, value, config) {
  if (element.tagName === 'SELECT') {
    const options = element.querySelectorAll('option');
    const option = Array.from(options).find(opt =>
      opt.value === '0-10 employees' || opt.textContent.includes('0-10')
    );
    if (option) {
      element.value = option.value;
      element.dispatchEvent(new Event('change', { bubbles: true }));
      return true;
    }
  }

  if (element.tagName === 'INPUT' || element.tagName === 'TEXTAREA') {
    element.value = value;
    element.dispatchEvent(new Event('input', { bubbles: true }));
    element.dispatchEvent(new Event('change', { bubbles: true }));
    return true;
  }

  return false;
}