// AiToolsCorner.com 网站规则配置
// 网站: https://aitoolscorner.com/dashboard/add-product
// 最后更新: 2025-07-09

export const SITE_RULE = {
  // 基本信息
  domain: 'aitoolscorner.com',
  siteName: 'AiToolsCorner',
  priority: 1,
  lastUpdated: '2025-07-09',
  
  // 字段映射规则
  fieldMappings: {
    // 分类 -> Category (选择AI Design Generator)
    category: {
      selectors: [
        'select[name="category_id[]"]',
        '#categories',
        'select.subcategory-select'
      ],
      method: 'select',
      validation: 'required',
      targetValue: '149',
      defaultValue: '149',
      notes: '产品分类，选择AI Design Generator'
    },
    
    // 工具名称 -> Tool Name
    siteName: {
      selectors: [
        'input[name="title_1"]',
        'input[placeholder="Title"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '工具名称'
    },
    
    // 工具URL -> Tool URL
    siteUrl: {
      selectors: [
        'input[name="url"]',
        'input[placeholder="https://example.com"]'
      ],
      method: 'value',
      validation: 'required|url',
      notes: '工具URL地址'
    },
    
    // 简短描述 -> Short Description
    siteDescription: {
      selectors: [
        'input[name="short_description_1"]',
        'input[placeholder="Short Description"]'
      ],
      method: 'value',
      validation: 'optional',
      notes: '简短描述'
    },
    
    // 关键词 -> Keywords
    keywords: {
      selectors: [
        'input[name="keywords_1"]',
        'input[placeholder="Keywords"]'
      ],
      method: 'value',
      validation: 'optional',
      notes: '关键词，用逗号分隔'
    },
    
    // 详细描述 -> Rich Text Editor
    detailedIntro: {
      selectors: [
        'iframe#editor_1_ifr',
        '.tox-edit-area__iframe',
        'iframe[title="Rich Text Area"]'
      ],
      method: 'tinymce',
      validation: 'optional',
      notes: '详细描述，使用TinyMCE富文本编辑器'
    }
  },
  
  // 提交流程配置
  submitConfig: {
    submitButton: 'button[type="submit"], input[type="submit"], .btn-submit',
    submitMethod: 'click',
    waitAfterFill: 2000, // 填写后等待2秒
    waitAfterSubmit: 3000, // 提交后等待3秒
    successIndicators: [
      '.success-message',
      '.alert-success',
      '.notification-success',
      '[class*="success"]'
    ],
    errorIndicators: [
      '.error-message',
      '.alert-error',
      '.alert-danger',
      '[class*="error"]'
    ]
  },
  
  // 特殊处理
  specialHandling: {
    requiresLogin: true, // 需要登录到dashboard
    hasCaptcha: false,
    hasFileUpload: false,
    
    // 表单验证规则
    formValidation: {
      requiredFields: ['category', 'siteName', 'siteUrl'],
      emailValidation: false,
      urlValidation: true
    },
    
    // 特殊注意事项
    notes: [
      '需要登录到dashboard才能访问',
      '表单包含6个字段：分类、工具名称、URL、描述、关键词、详细描述',
      '必填字段：分类、工具名称、URL',
      '分类自动选择AI Design Generator（value="149"）',
      '使用Select2下拉选择组件',
      '使用TinyMCE富文本编辑器',
      '关键词需要用逗号分隔'
    ]
  }
};

// 自定义处理函数
export function handleAiToolsCornerSubmission(data, rule) {
  console.log('Processing AiToolsCorner.com submission...');
  
  // 特殊处理逻辑
  const processedData = { ...data };
  
  // 确保URL格式正确
  if (processedData.siteUrl && !processedData.siteUrl.startsWith('http')) {
    processedData.siteUrl = 'https://' + processedData.siteUrl;
  }
  
  // 确保分类设置为AI Design Generator
  processedData.category = '149';
  
  // 处理关键词格式
  if (processedData.keywords) {
    // 如果是数组，转换为逗号分隔的字符串
    if (Array.isArray(processedData.keywords)) {
      processedData.keywords = processedData.keywords.join(', ');
    }
    processedData.keywords = processedData.keywords.trim();
  }
  
  return processedData;
}

// 自定义元素填写函数，专门处理Select2和TinyMCE
export async function customFillElement(element, value, config) {
  console.log('🔧 AiToolsCorner自定义填写函数被调用:', element, value);
  
  // 处理Select2下拉选择
  if (element.classList.contains('select2') || element.classList.contains('subcategory-select')) {
    try {
      // 设置选中值
      element.value = config.targetValue || '149';
      
      // 触发Select2事件
      $(element).val(config.targetValue || '149').trigger('change');
      
      console.log('✅ 使用Select2选择分类:', config.targetValue || '149');
      return true;
    } catch (error) {
      console.warn('Select2选择失败:', error);
    }
  }
  
  // 处理TinyMCE富文本编辑器
  if (element.tagName === 'IFRAME' && element.id.includes('editor')) {
    try {
      // 等待TinyMCE初始化
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // 方法1: 使用TinyMCE API
      if (window.tinymce) {
        const editorId = element.id.replace('_ifr', '');
        const editor = tinymce.get(editorId);
        if (editor) {
          editor.setContent(value);
          console.log('✅ 使用TinyMCE API填写:', value);
          return true;
        }
      }
      
      // 方法2: 直接操作iframe内容
      const iframeDoc = element.contentDocument || element.contentWindow.document;
      if (iframeDoc && iframeDoc.body) {
        iframeDoc.body.innerHTML = value;
        console.log('✅ 使用iframe直接填写:', value);
        return true;
      }
    } catch (error) {
      console.warn('TinyMCE编辑器填写失败:', error);
    }
  }
  
  // 默认处理
  return false;
}
