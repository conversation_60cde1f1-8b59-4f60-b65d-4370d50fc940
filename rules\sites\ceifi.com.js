// Ceifi.com 网站规则配置
// 网站: https://ceifi.com/panel/create/
// 最后更新: 2025-07-24

export const SITE_RULE = {
  // 基本信息
  domain: 'ceifi.com',
  siteName: 'Ceifi',
  priority: 1,
  lastUpdated: '2025-07-24',
  
  // 字段映射规则
  fieldMappings: {
    // 名称 -> siteName
    siteName: {
      selectors: [
        'input[name="name"]',
        'input#name',
        'label:contains("名称") + div input'
      ],
      method: 'value',
      validation: 'required',
      notes: '工具名称'
    },

    // 分类 -> category
    category: {
      selectors: [
        '.listivo-select-v2',
        'label:contains("分类") + div .listivo-select-v2'
      ],
      method: 'select',
      validation: 'required',
      notes: '产品分类选择'
    },

    // 查看网站 -> siteUrl
    siteUrl: {
      selectors: [
        'input[name="listivo_8674"]',
        'input#listivo_8674',
        'label:contains("查看网站") + div input'
      ],
      method: 'value',
      validation: 'required|url',
      notes: '网站URL'
    },

    // 价格每月(USD) -> priceAmount
    priceMonthlyUSD: {
      selectors: [
        'input[name="listivo_8764_listivo_13"]',
        'input#listivo_8764_listivo_13'
      ],
      method: 'value',
      validation: 'optional',
      notes: '月付价格美元'
    },

    // 价格每月(人民币) -> 使用priceAmount转换
    priceMonthlyRMB: {
      selectors: [
        'input[name="listivo_8764_listivo_608"]',
        'input#listivo_8764_listivo_608'
      ],
      method: 'value',
      validation: 'optional',
      notes: '月付价格人民币'
    },

    // 价格一次性(USD)
    priceOnetimeUSD: {
      selectors: [
        'input[name="listivo_9297_listivo_13"]',
        'input#listivo_9297_listivo_13'
      ],
      method: 'value',
      validation: 'optional',
      notes: '一次性价格美元'
    },

    // 价格一次性(人民币)
    priceOnetimeRMB: {
      selectors: [
        'input[name="listivo_9297_listivo_608"]',
        'input#listivo_9297_listivo_608'
      ],
      method: 'value',
      validation: 'optional',
      notes: '一次性价格人民币'
    },

    // 价格一次(USD)
    priceOnceUSD: {
      selectors: [
        'input[name="listivo_9460_listivo_13"]',
        'input#listivo_9460_listivo_13'
      ],
      method: 'value',
      validation: 'optional',
      notes: '单次价格美元'
    },

    // 价格一次(人民币)
    priceOnceRMB: {
      selectors: [
        'input[name="listivo_9460_listivo_608"]',
        'input#listivo_9460_listivo_608'
      ],
      method: 'value',
      validation: 'optional',
      notes: '单次价格人民币'
    },

    // 工具标签 -> keywords
    keywords: {
      selectors: [
        'input[name="listivo_11645"]',
        'input#listivo_11645',
        'label:contains("工具标签") + div input'
      ],
      method: 'value',
      validation: 'optional',
      notes: '工具标签关键词'
    },

    // 特点 -> features (复选框)
    features: {
      selectors: [
        '.listivo-panel-form__checkbox-option .listivo-checkbox',
        '.listivo-panel-form__multiple-options .listivo-checkbox'
      ],
      method: 'checkbox',
      validation: 'optional',
      notes: '产品特点复选框'
    },

    // 付费类型 -> pricing (复选框)
    pricing: {
      selectors: [
        '.listivo-panel-form__checkbox-option .listivo-checkbox',
        'span:contains("免费增值") ~ .listivo-checkbox',
        'span:contains("免费试用") ~ .listivo-checkbox'
      ],
      method: 'checkbox',
      validation: 'optional',
      notes: '付费类型选择'
    },

    // 描述 -> detailedIntro (富文本编辑器)
    detailedIntro: {
      selectors: [
        'textarea[name="listivo_description"]',
        '#listivo_description',
        'iframe#listivo_description_ifr'
      ],
      method: 'richtext',
      validation: 'required',
      notes: '产品详细描述，WordPress富文本编辑器'
    }
  },

  // 提交流程配置
  submitConfig: {
    submitButton: '.listivo-panel-form__submit-button, button[type="submit"]',
    submitMethod: 'click',
    waitAfterFill: 2000,
    waitAfterSubmit: 5000,
    successIndicators: ['.success-message'],
    errorIndicators: ['.error-message', '.listivo-input-v2__error']
  },

  // 特殊处理
  specialHandling: {
    requiresLogin: true,
    hasCaptcha: false,
    hasFileUpload: false,
    customScript: 'handleCeifiSubmission',
    formValidation: {
      requiredFields: ['siteName', 'category', 'siteUrl', 'detailedIntro'],
      emailValidation: false,
      urlValidation: true
    },
    notes: [
      'AI工具目录提交平台',
      '需要登录账户',
      '支持多种价格类型',
      '有复选框特点选择',
      '中文界面',
      'Listivo主题框架'
    ]
  }
};

// 自定义处理函数
export function handleCeifiSubmission(data) {
  const processedData = { ...data };

  // 确保URL格式正确
  if (processedData.siteUrl && !processedData.siteUrl.startsWith('http')) {
    processedData.siteUrl = 'https://' + processedData.siteUrl;
  }

  // 处理价格字段 - 价格字段留空，不填写
  // 价格字段在website-info.js中没有对应的数值，保持空白

  return processedData;
}

// 自定义元素填写函数
export async function customFillElement(element, value, config) {
  // 处理Listivo下拉选择框
  if (element.classList.contains('listivo-select-v2')) {
    element.click();
    
    // 等待下拉选项出现
    setTimeout(() => {
      const options = document.querySelectorAll('.listivo-select-v2__option');
      const targetOption = Array.from(options).find(opt => 
        opt.textContent.includes('AI') || 
        opt.textContent.includes('工具') ||
        opt.textContent.includes('技术')
      );
      
      if (targetOption) {
        targetOption.click();
      } else if (options.length > 0) {
        options[0].click();
      }
    }, 300);
    
    return true;
  }

  // 处理复选框
  if (element.classList.contains('listivo-checkbox')) {
    element.click();
    return true;
  }

  // 处理WordPress富文本编辑器
  if (element.tagName === 'IFRAME' && element.id === 'listivo_description_ifr') {
    try {
      const iframeDoc = element.contentDocument || element.contentWindow.document;
      const body = iframeDoc.body;
      if (body) {
        body.innerHTML = value;
        // 触发WordPress编辑器更新
        const textarea = document.getElementById('listivo_description');
        if (textarea) {
          textarea.value = value;
          textarea.dispatchEvent(new Event('change', { bubbles: true }));
        }
        return true;
      }
    } catch (e) {
      console.warn('无法访问iframe内容，尝试直接设置textarea');
    }
  }

  // 处理隐藏的textarea（富文本编辑器后端）
  if (element.tagName === 'TEXTAREA' && element.name === 'listivo_description') {
    element.value = value;
    element.dispatchEvent(new Event('input', { bubbles: true }));
    element.dispatchEvent(new Event('change', { bubbles: true }));

    // 尝试更新TinyMCE编辑器
    if (window.tinymce && window.tinymce.get('listivo_description')) {
      window.tinymce.get('listivo_description').setContent(value);
    }
    return true;
  }

  // 处理标准输入框
  if (element.tagName === 'INPUT') {
    element.value = value;
    element.dispatchEvent(new Event('input', { bubbles: true }));
    element.dispatchEvent(new Event('change', { bubbles: true }));
    return true;
  }

  return false;
}
