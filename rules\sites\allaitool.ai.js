// allaitool.ai 网站规则配置
// 网站: https://allaitool.ai/submit-ai-tool
// 表单技术: Bootstrap Forms
// 最后更新: 2025-07-07

export const SITE_RULE = {
  // 基本信息
  domain: 'allaitool.ai',
  siteName: 'Allaitool',
  priority: 1,
  lastUpdated: '2025-07-07',
  
  // 字段映射规则
  fieldMappings: {
    // 工具名称 -> Tool Name
    siteName: {
      selectors: [
        'input[id="tname"]',
        'input[name="tool"]',
        'input[placeholder="Enter your tool name"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '工具名称，使用website-info.js中的siteName字段'
    },
    
    // 官方邮箱 -> Official Email
    contactEmail: {
      selectors: [
        'input[id="tx-email"]',
        'input[name="email"]',
        'input[type="email"]',
        'input[placeholder="Email"]'
      ],
      method: 'value',
      validation: 'required|email',
      notes: '官方邮箱，使用website-info.js中的contactEmail字段'
    },
    
    // 工具分类 -> Tool Category
    category: {
      selectors: [
        'input[id="category"]',
        'input[name="category"]',
        'input[placeholder="Category"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '工具分类，使用website-info.js中的category字段'
    },
    
    // 工具网站 -> Tool Website
    siteUrl: {
      selectors: [
        'input[id="tx-web"]',
        'input[name="website"]',
        'input[placeholder="website.com"]'
      ],
      method: 'value',
      validation: 'required|url',
      notes: '工具网站，使用website-info.js中的siteUrl字段'
    },
    
    // 联盟链接 -> Affiliate Link
    affiliateLink: {
      selectors: [
        'input[id="affiliate_link"]',
        'input[name="affiliate_link"]',
        'input[placeholder="Enter affiliate link"]'
      ],
      method: 'value',
      validation: 'required',
      defaultValue: 'No affiliate program available',
      notes: '联盟链接，使用默认值或website-info.js中的相关字段'
    },
    
    // 营销服务 -> Marketing Services
    marketingServices: {
      selectors: [
        'select[id="marketing_services"]',
        'select[name="marketing_services"]'
      ],
      method: 'select',
      validation: 'required',
      defaultValue: 'no',
      notes: '营销服务，默认选择No'
    },
    
    // 工具描述 -> Tool Description
    siteDescription: {
      selectors: [
        'textarea[id="tx-sdesc"]',
        'textarea[name="sdes"]',
        'textarea[placeholder="Describe your tool"]',
        'textarea[maxlength="180"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '工具描述，使用website-info.js中的siteDescription字段，限制180字符'
    }
  },
  
  // 自定义填写函数
  customFillElement: async function(element, value, config) {
    console.log(`All AI Tool自定义填写: ${element.id || element.name}, 方法: ${config.method}`);
    
    switch (config.method) {
      case 'value':
        // 标准输入框和文本域处理
        element.focus();
        await new Promise(resolve => setTimeout(resolve, 100));
        
        // 清空现有内容
        element.value = '';
        await new Promise(resolve => setTimeout(resolve, 50));
        
        // 处理特殊字段
        let finalValue = value;
        if (element.id === 'tx-sdesc' && element.maxLength === 180) {
          // 工具描述限制180字符
          finalValue = value.substring(0, 180);
        } else if (element.id === 'affiliate_link') {
          // 联盟链接使用默认值
          finalValue = config.defaultValue;
        } else if (element.id === 'tx-web') {
          // 网站URL处理，移除协议前缀
          if (value.startsWith('https://')) {
            finalValue = value.replace('https://', '');
          } else if (value.startsWith('http://')) {
            finalValue = value.replace('http://', '');
          }
        }
        
        // 设置新值
        element.value = finalValue;
        
        // 触发事件
        element.dispatchEvent(new Event('input', { bubbles: true }));
        element.dispatchEvent(new Event('change', { bubbles: true }));
        element.dispatchEvent(new Event('blur', { bubbles: true }));
        
        console.log(`✓ 填写字段: ${element.id} = "${finalValue.substring(0, 50)}..."`);
        break;
        
      case 'select':
        // 下拉选择框处理
        if (element.tagName.toLowerCase() === 'select') {
          // 营销服务选择
          let targetValue = config.defaultValue; // 默认选择 'no'
          
          // 查找匹配的选项
          const option = Array.from(element.options).find(opt => 
            opt.value === targetValue || 
            opt.text === targetValue ||
            opt.text.toLowerCase().includes('no')
          );
          
          if (option) {
            element.value = option.value;
            element.dispatchEvent(new Event('change', { bubbles: true }));
            console.log(`✓ 选择营销服务: ${option.text}`);
          } else {
            // 如果没找到，选择第二个选项（通常是No）
            if (element.options.length > 1) {
              element.value = element.options[1].value;
              element.dispatchEvent(new Event('change', { bubbles: true }));
              console.log(`✓ 选择默认选项: ${element.options[1].text}`);
            }
          }
        }
        break;
        
      default:
        element.value = value;
        element.dispatchEvent(new Event('input', { bubbles: true }));
        element.dispatchEvent(new Event('change', { bubbles: true }));
    }
  },
  
  // 提交流程配置
  submitConfig: {
    submitButton: [
      'button[name="submit_your_tool"]',
      'button[type="submit"]',
      '.visit-btn',
      'button:contains("Submit And Pay")'
    ],
    submitMethod: 'click',
    waitAfterFill: 2000,
    waitAfterSubmit: 5000,
    successIndicators: [
      '.success-message',
      '.thank-you',
      '[class*="success"]',
      'text:contains("submitted")'
    ],
    errorIndicators: [
      '.error-message',
      '.alert-danger',
      '[class*="error"]'
    ]
  },
  
  // 特殊处理
  specialHandling: {
    requiresLogin: false,
    hasCaptcha: false,
    hasFileUpload: false,
    isBootstrapForm: true, // 使用Bootstrap表单
    hasPayment: true, // 需要付费提交
    hasAffiliateProgram: true, // 支持联盟计划
    
    // 表单验证规则
    formValidation: {
      requiredFields: ['siteName', 'contactEmail', 'category', 'siteUrl', 'affiliateLink', 'marketingServices', 'siteDescription'],
      emailValidation: true,
      urlValidation: true,
      characterLimits: {
        siteDescription: 180
      }
    },
    
    // 特殊注意事项
    notes: [
      '这是付费提交的AI工具目录网站',
      '表单包含7个字段，全部必填',
      '工具描述限制180字符',
      '网站URL需要去掉协议前缀',
      '联盟链接是必填字段，使用默认值',
      '营销服务默认选择No',
      '提交按钮显示"Submit And Pay"',
      '使用Bootstrap表单样式',
      '字段布局为响应式网格'
    ]
  }
};

// 自定义处理函数
export function handleAllAIToolSubmission(data, _rule) {
  console.log('Processing All AI Tool form submission...');

  const processedData = { ...data };

  // 处理网站URL，移除协议前缀
  if (processedData.siteUrl) {
    let url = processedData.siteUrl;
    if (url.startsWith('https://')) {
      url = url.replace('https://', '');
    } else if (url.startsWith('http://')) {
      url = url.replace('http://', '');
    }
    processedData.siteUrl = url;
  }

  // 处理工具描述长度限制
  if (processedData.siteDescription && processedData.siteDescription.length > 180) {
    processedData.siteDescription = processedData.siteDescription.substring(0, 180);
  }

  // 设置默认值
  processedData.affiliateLink = 'No affiliate program available';
  processedData.marketingServices = 'no';

  return processedData;
}
