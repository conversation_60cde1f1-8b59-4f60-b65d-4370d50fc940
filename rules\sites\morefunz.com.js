// MoreFunz.com 网站规则配置
// 网站: https://morefunz.com/submit-url
// 最后更新: 2025-07-24

export const SITE_RULE = {
  domain: 'morefunz.com',
  siteName: 'MoreFunz Directory',
  priority: 1,
  lastUpdated: '2025-07-24',

  fieldMappings: {
    siteName: {
      selectors: [
        '#sitenm',
        'input[name="sitenm"]',
        'input[placeholder="website/company name"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '网站名称，最少6个字符'
    },

    siteUrl: {
      selectors: [
        '#siteurl',
        'input[name="urlsite"]',
        'input[placeholder="http:// or https:// "]'
      ],
      method: 'value',
      validation: 'required|url',
      notes: '网站URL'
    },

    detailedIntro: {
      selectors: [
        '#descarea',
        'textarea[name="description"]',
        'textarea[rows="7"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '网站描述，最少200个字符'
    },

    category: {
      selectors: [
        '#cat1',
        'select[name="cat1"]'
      ],
      method: 'select',
      validation: 'required',
      defaultValue: '/computers/',
      notes: '主分类选择'
    },

    email: {
      selectors: [
        '#siteml',
        'input[name="siteml"]',
        'input[placeholder="info@yourdomain"]'
      ],
      method: 'value',
      validation: 'optional|email',
      notes: '网站联系邮箱'
    },

    phone: {
      selectors: [
        '#phone',
        'input[name="phone"]',
        'input[placeholder="****** 1234, ****** 5678"]'
      ],
      method: 'value',
      validation: 'optional',
      notes: '联系电话'
    },

    address: {
      selectors: [
        '#address',
        'textarea[name="address"]'
      ],
      method: 'value',
      validation: 'optional',
      notes: '联系地址'
    },

    fullName: {
      selectors: [
        '#name',
        'input[name="name"]',
        'input[placeholder="Your name"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '提交者姓名'
    },

    contactEmail: {
      selectors: [
        '#email',
        'input[name="email"]',
        'input[placeholder="Your email"]'
      ],
      method: 'value',
      validation: 'required|email',
      notes: '提交者邮箱'
    }
  },

  submitConfig: {
    submitButton: '#submit, input[name="submit"]',
    submitMethod: 'click',
    successIndicators: ['.success-message'],
    errorIndicators: ['.error-message']
  },

  specialHandling: {
    requiresLogin: false,
    hasCaptcha: false,
    hasFileUpload: false,
    customScript: 'handleMoreFunzSubmission',
    formValidation: {
      requiredFields: ['siteName', 'siteUrl', 'detailedIntro', 'category', 'fullName', 'contactEmail'],
      emailValidation: true,
      urlValidation: true
    },
    notes: [
      '需要同意隐私条款',
      '有付费快速审核选项',
      '描述最少200字符',
      '网站名称最少6字符',
      '有三级分类选择',
      '提交按钮初始禁用'
    ]
  }
};

export function handleMoreFunzSubmission(data, rule) {
  const processedData = { ...data };

  if (processedData.siteUrl && !processedData.siteUrl.startsWith('http')) {
    processedData.siteUrl = 'https://' + processedData.siteUrl;
  }

  // 自动勾选隐私条款
  const privacyCheckbox = document.querySelector('#priv');
  if (privacyCheckbox) {
    privacyCheckbox.checked = true;
    privacyCheckbox.dispatchEvent(new Event('change', { bubbles: true }));
  }

  return processedData;
}

export async function customFillElement(element, value, config) {
  if (element.tagName === 'SELECT') {
    const options = element.querySelectorAll('option');
    const option = Array.from(options).find(opt =>
      opt.value === value || opt.textContent.toLowerCase().includes('computers')
    );
    if (option) {
      element.value = option.value;
      element.dispatchEvent(new Event('change', { bubbles: true }));
      return true;
    }
  }

  if (element.tagName === 'INPUT' || element.tagName === 'TEXTAREA') {
    element.value = value;
    element.dispatchEvent(new Event('input', { bubbles: true }));
    return true;
  }

  return false;
}