// AutoAIs.com 网站规则配置
// 网站: https://autoais.com/?U=Add
// 最后更新: 2025-07-24

export const SITE_RULE = {
  domain: 'autoais.com',
  siteName: 'AutoAIs',
  priority: 1,
  lastUpdated: '2025-07-24',

  fieldMappings: {
    siteName: {
      selectors: [
        '#AdduserInput1',
        'input[name="Title"]',
        'input[maxlength="30"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '网站名称，最多30字符'
    },

    siteUrl: {
      selectors: [
        '#AdduserInput2',
        'input[name="WebURL"]',
        'input[placeholder="https://example.com"]'
      ],
      method: 'value',
      validation: 'required|url',
      notes: '网站URL，必须以https://开头'
    },

    siteDescription: {
      selectors: [
        '#AdduserInput3',
        'textarea[name="Description"]',
        'textarea[maxlength="190"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '网站描述，最多190字符'
    },

    category: {
      selectors: [
        '#AdduserInput4',
        'select[name="Tab1"]',
        '.select-MainTab'
      ],
      method: 'select',
      validation: 'required',
      defaultValue: 'Productivity',
      notes: '主分类选择'
    },

    tags: {
      selectors: [
        '#AdduserInput5',
        'select[name="Tab2"]',
        '.select-Tab:first-of-type'
      ],
      method: 'select',
      validation: 'optional',
      defaultValue: 'SEO',
      notes: '标签2选择'
    },

    additionalTags: {
      selectors: [
        '#AdduserInput6',
        'select[name="Tab3"]',
        '.select-Tab:nth-of-type(2)'
      ],
      method: 'select',
      validation: 'optional',
      defaultValue: 'Chat',
      notes: '标签3选择'
    }
  },

  submitConfig: {
    submitButton: 'button[name="T"], input[type="submit"]',
    submitMethod: 'click',
    successIndicators: ['.success-message'],
    errorIndicators: ['.error-message']
  },

  specialHandling: {
    requiresLogin: false,
    hasCaptcha: false,
    hasFileUpload: false,
    customScript: 'handleAutoAIsSubmission',
    formValidation: {
      requiredFields: ['siteName', 'siteUrl', 'siteDescription', 'category'],
      emailValidation: false,
      urlValidation: true
    },
    notes: [
      '有字符计数器',
      '多级标签选择',
      'URL必须https://开头',
      '有5个标签选择器',
      '描述最多190字符',
      '网站名称最多30字符'
    ]
  }
};

export function handleAutoAIsSubmission(data, rule) {
  const processedData = { ...data };

  if (processedData.siteUrl && !processedData.siteUrl.startsWith('https://')) {
    processedData.siteUrl = 'https://' + processedData.siteUrl.replace(/^https?:\/\//, '');
  }

  return processedData;
}

export async function customFillElement(element, value, config) {
  if (element.tagName === 'SELECT') {
    const options = element.querySelectorAll('option');
    let option;

    if (element.name === 'Tab1') {
      option = Array.from(options).find(opt => opt.value === 'Productivity');
    } else if (element.name === 'Tab2') {
      option = Array.from(options).find(opt => opt.value === 'SEO');
    } else if (element.name === 'Tab3') {
      option = Array.from(options).find(opt => opt.value === 'Chat');
    } else {
      option = Array.from(options).find(opt => opt.value === value);
    }

    if (option) {
      element.value = option.value;
      element.dispatchEvent(new Event('change', { bubbles: true }));
      return true;
    }
  }

  if (element.tagName === 'INPUT' || element.tagName === 'TEXTAREA') {
    element.value = value;
    element.dispatchEvent(new Event('input', { bubbles: true }));
    return true;
  }

  return false;
}