// AiNavHub.com 网站规则配置
// 网站: https://ainavhub.com/submit
// 最后更新: 2025-07-24

export const SITE_RULE = {
  // 基本信息
  domain: 'ainavhub.com',
  siteName: 'AI NavHub',
  priority: 1,
  lastUpdated: '2025-07-24',

  // 字段映射规则
  fieldMappings: {
    // 网站名称 -> Website Name
    siteName: {
      selectors: [
        'input[name="website"]',
        'input[placeholder="AI NavHub Tools Directory"]',
        'label:contains("Website Name") + input'
      ],
      method: 'value',
      validation: 'required',
      notes: '网站名称'
    },

    // 网站URL -> Website URL
    siteUrl: {
      selectors: [
        'input[name="url"]',
        'input[placeholder="https://ainavhub.com/"]',
        'label:contains("Website URL") + input'
      ],
      method: 'value',
      validation: 'required|url',
      notes: '网站URL地址'
    },

    // 联系邮箱 -> Email
    contactEmail: {
      selectors: [
        'input[name="email"]',
        'input[type="email"]',
        'label:contains("Email") + input'
      ],
      method: 'value',
      validation: 'required|email',
      notes: '联系邮箱'
    }
  },

  // 提交流程配置
  submitConfig: {
    submitButton: 'button[type="submit"], button:contains("Submit")',
    submitMethod: 'manual', // 手动提交
    waitAfterFill: 0,
    waitAfterSubmit: 0,
    successIndicators: ['.success-message'],
    errorIndicators: ['.error-message']
  },

  // 特殊处理
  specialHandling: {
    requiresLogin: false,
    hasCaptcha: false,
    hasFileUpload: false,
    customScript: 'handleAiNavHubSubmission',
    formValidation: {
      requiredFields: ['siteName', 'siteUrl', 'contactEmail'],
      emailValidation: true,
      urlValidation: true
    },
    notes: [
      'AI NavHub AI工具导航提交平台',
      '简洁的三字段表单',
      '白色主题界面',
      '提供付费快速提交服务（Buy me a coffee）',
      '付费后24小时内处理，网站显示在首页',
      '联系邮箱：<EMAIL>',
      '手动提交模式'
    ]
  }
};

// 自定义处理函数
export function handleAiNavHubSubmission(data) {
  console.log('Processing AI NavHub form submission...');

  const processedData = { ...data };

  // 确保URL格式正确
  if (processedData.siteUrl && !processedData.siteUrl.startsWith('http')) {
    processedData.siteUrl = 'https://' + processedData.siteUrl;
  }

  return processedData;
}

// 自定义元素填写函数
export async function customFillElement(element, value, config) {
  console.log(`AI NavHub自定义填写: ${element.tagName}, 方法: ${config.method}`);

  switch (config.method) {
    case 'value':
      // 标准输入框处理
      element.focus();
      await new Promise(resolve => setTimeout(resolve, 200));

      element.value = value;
      element.dispatchEvent(new Event('input', { bubbles: true }));
      element.dispatchEvent(new Event('change', { bubbles: true }));
      element.dispatchEvent(new Event('blur', { bubbles: true }));

      console.log(`✓ 填写字段: ${element.name} = "${value}"`);
      return true;

    default:
      element.value = value;
      element.dispatchEvent(new Event('input', { bubbles: true }));
      return true;
  }

  return false;
}