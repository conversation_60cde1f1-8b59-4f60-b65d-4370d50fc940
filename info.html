<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Website Info Viewer - AI Site Submitter</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
            color: #333;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 16px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 40px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            font-weight: 700;
        }

        .header p {
            font-size: 1.1rem;
            opacity: 0.9;
        }

        .stats {
            background: #f8f9fa;
            padding: 20px;
            text-align: center;
            border-bottom: 1px solid #e9ecef;
        }

        .stats-item {
            display: inline-block;
            margin: 0 20px;
            padding: 15px 25px;
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }

        .stats-number {
            font-size: 1.8rem;
            font-weight: 700;
            color: #4facfe;
        }

        .stats-label {
            font-size: 0.9rem;
            color: #6c757d;
            margin-top: 5px;
        }

        .content {
            padding: 40px;
        }

        .search-box {
            margin-bottom: 30px;
            position: relative;
        }

        .search-input {
            width: 100%;
            padding: 15px 50px 15px 20px;
            border: 2px solid #e9ecef;
            border-radius: 12px;
            font-size: 16px;
            transition: all 0.3s ease;
        }

        .search-input:focus {
            outline: none;
            border-color: #4facfe;
            box-shadow: 0 0 0 3px rgba(79, 172, 254, 0.1);
        }

        .search-icon {
            position: absolute;
            right: 20px;
            top: 50%;
            transform: translateY(-50%);
            color: #6c757d;
            font-size: 18px;
        }

        .field-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 20px;
        }

        .field-card {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 20px;
            border: 1px solid #e9ecef;
            transition: all 0.3s ease;
        }

        .field-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        }

        .field-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 15px;
        }

        .field-name {
            font-size: 1.1rem;
            font-weight: 600;
            color: #495057;
        }

        .field-type {
            background: #4facfe;
            color: white;
            padding: 4px 8px;
            border-radius: 6px;
            font-size: 0.75rem;
            font-weight: 500;
        }

        .field-comment {
            font-size: 0.9rem;
            color: #6c757d;
            margin-bottom: 12px;
            font-style: italic;
        }

        .field-value {
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 12px;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            line-height: 1.5;
            word-break: break-word;
            position: relative;
            min-height: 50px;
        }

        .copy-btn {
            position: absolute;
            top: 8px;
            right: 8px;
            background: #28a745;
            color: white;
            border: none;
            padding: 6px 10px;
            border-radius: 6px;
            font-size: 0.75rem;
            cursor: pointer;
            transition: all 0.3s ease;
            opacity: 0.8;
        }

        .copy-btn:hover {
            opacity: 1;
            transform: scale(1.05);
        }

        .copy-btn.copied {
            background: #17a2b8;
        }

        .field-card.hidden {
            display: none;
        }

        /* 分类样式 */
        .category-header {
            grid-column: 1 / -1;
            margin: 30px 0 15px 0;
            padding: 15px 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }

        .category-header h3 {
            margin: 0;
            color: white;
            font-size: 1.4em;
            font-weight: 600;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }

        .category-container {
            grid-column: 1 / -1;
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }

        .category-stats {
            grid-column: 1 / -1;
            text-align: center;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 8px;
            color: #6c757d;
            font-weight: 500;
            margin-top: 10px;
        }

        .footer {
            background: #343a40;
            color: white;
            text-align: center;
            padding: 30px;
            font-size: 0.9rem;
        }

        .footer a {
            color: #4facfe;
            text-decoration: none;
        }

        .footer a:hover {
            text-decoration: underline;
        }

        .hidden {
            display: none;
        }

        @media (max-width: 768px) {
            .field-grid {
                grid-template-columns: 1fr;
            }
            
            .header h1 {
                font-size: 2rem;
            }
            
            .content {
                padding: 20px;
            }
            
            .stats-item {
                margin: 10px;
                display: block;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <header class="header">
            <h1>📊 Website Info Viewer</h1>
            <p>AI Site Submitter - 网站信息字段管理器</p>
        </header>

        <div class="stats">
            <div class="stats-item">
                <div class="stats-number" id="total-fields">0</div>
                <div class="stats-label">总字段数</div>
            </div>
            <div class="stats-item">
                <div class="stats-number" id="visible-fields">0</div>
                <div class="stats-label">显示字段</div>
            </div>
            <div class="stats-item">
                <div class="stats-number" id="copied-count">0</div>
                <div class="stats-label">已复制</div>
            </div>
        </div>

        <div class="content">
            <div class="search-box">
                <input type="text" class="search-input" id="search-input" placeholder="搜索字段名称或值...">
                <span class="search-icon">🔍</span>
            </div>

            <div class="field-grid" id="field-grid">
                <!-- 字段卡片将通过JavaScript动态生成 -->
            </div>
        </div>

        <footer class="footer">
            <p>Created by 二词真君 | <a href="https://catchideas.com/" target="_blank">CatchIdeas</a></p>
            <p>AI Site Submitter v3.0.0 AI识别版 - Website Information Manager</p>
        </footer>
    </div>

    <script>
        // 网站信息数据（从website-info.js复制）
        const WEBSITE_INFO = {
            // 基本信息字段
            siteName: 'HumanWhisper',
            siteTitle: 'HumanWhisper - AI That Explains Complex Topics Simply',
            siteUrl: 'https://humanwhisper.com/',
            siteDescription: 'AI that explains complex topics in simple, human-friendly language. Get clear explanations without jargon, using everyday analogies and examples.',
            tagline: 'The AI That Whispers Wisdom in Your Language',
            detailedIntro: 'HumanWhisper is an AI simplification tool that transforms complex topics into crystal clear understanding. Unlike other AI assistants that use technical jargon, HumanWhisper specializes in plain language explanations with real-world analogies. Perfect for students, professionals, and curious minds who want to understand complex concepts without the complexity.',
            contactEmail: '<EMAIL>',
            faviconUrl: 'https://humanwhisper.com/favicon.ico',
            thumbnailUrl: 'https://humanwhisper.com/og-image.png',

            // 分类和标签
            category: 'Ai Tools',
            keywords: 'AI simplification, complex topics explained, plain language AI, educational AI, learning assistant, AI tutor, concept explanation, knowledge simplification',
            tags: 'AI, Education, Learning, Simplification, Explanation, Knowledge, Tutor, Assistant, Plain Language, Complex Topics',
            seoKeywords: 'AI simplification tool, complex topics explained simply, plain language AI, educational AI assistant, concept explanation AI, learning helper',

            // 功能特性
            features: 'Plain language explanations, Real-time simplification, Customizable complexity levels, Multi-language support, Everyday analogies, No jargon approach, Streaming responses, Privacy protection',
            uniqueSellingPoints: 'Pre-optimized for simplification, No complex prompts needed, Instant clear explanations, Advanced NLP technology, Context understanding, Semantic analysis, Human-friendly AI',

            // 技术和商业信息
            techStack: 'Next.js, React, Advanced NLP, AI/ML, JavaScript, Streaming Technology, Responsive Design',
            pricing: 'Freemium',
            priceAmount: '$29.99/month',
            isOpenSource: false,
            sourceCodeUrl: '',
            demoUrl: 'https://humanwhisper.com',

            // 公司和联系信息
            companyName: 'HumanWhisper Technologies',
            fullName: 'Sarah Chen',
            firstName: 'Sarah',
            lastName: 'Chen',
            loginUrl: 'https://humanwhisper.com/chat',
            username: 'sarahc.tech',
            password: 'Whisper2024#Tech',
            submitterRole: 'Product Manager',
            phone: '******-892-7341',
            country: 'United States',
            streetAddress: '2847 Pine Street, Suite 201',
            city: 'San Francisco',
            state: 'California',
            zipCode: '94115',

            // 社交媒体链接
            socialLinks: 'https://twitter.com/humanwhisper, https://linkedin.com/company/humanwhisper',
            videoUrl: 'https://www.youtube.com/watch?v=humanwhisper-demo',
            twitterUrl: 'https://twitter.com/humanwhisper',
            twitterUsername: 'humanwhisper',
            linkedinUrl: 'https://www.linkedin.com/company/humanwhisper',
            instagramUsername: 'humanwhisper',
            githubUsername: 'humanwhisper',
            facebookUrl: 'https://www.facebook.com/humanwhisper',
            message: 'HumanWhisper is an innovative AI tool that makes complex topics accessible to everyone. Thank you for considering our submission.',
            useCases: 'Learning new skills, Understanding everyday questions, Work and career topics, Academic research, Technical concept explanation, Business terminology, Scientific concepts, Financial topics',
            secondUseCase: 'Professional development and workplace learning',
            thirdUseCase: 'Academic study and research assistance',

            // 其他信息
            launchDate: '2025-07-24',
            releaseYear: '2025',
            websiteStatus: 'Active',
            hasAiFeatures: true,
            targetAudience: 'Students, Professionals, Curious minds, Researchers, Educators, Lifelong learners, Non-technical users',
            userCases: 'Students understanding complex academic concepts, Professionals learning new industry terms, Researchers simplifying technical papers, Educators creating accessible content, Curious individuals exploring new topics',
            howToUse: 'Visit website, Ask any complex question naturally, Receive instant plain-language explanation, Customize complexity level if needed, Get analogies and examples, Follow up with related questions',
            installMethod: 'Web-based platform, No installation required, Access via any web browser, Mobile-friendly responsive design, Instant access',
            faqs: 'Is it free? Yes, with 100 free credits. Do I need special prompts? No, ask naturally. What topics can it explain? Any complex topic. Is my data private? Yes, fully encrypted and secure.',
            userRating: '5.0',
            alternatives: 'ChatGPT, Claude, Perplexity, Explain Like I\'m 5, Simple Wikipedia'
        };

        // 字段分类和注释映射
        const fieldCategories = {
            '基本网站信息': {
                siteName: '网站名称 - 显示在各个提交网站上的产品名称',
                siteTitle: '网站标题 - 更详细的标题描述',
                siteUrl: '网站URL - 完整的网站地址',
                siteDescription: '网站描述 - 简短的网站介绍（通常50-160字符）',
                tagline: '标语口号 - 一句话的品牌标语/口号',
                detailedIntro: '详细介绍 - 更完整的网站功能描述（通常200-500字符）',
                faviconUrl: '网站图标URL - Favicon地址',
                thumbnailUrl: '缩略图URL - 网站预览图片'
            },
            '分类和标签': {
                category: '网站分类 - 主要分类（固定，不要为了适配提交网站而修改）',
                keywords: '关键词 - SEO关键词，用逗号分隔',
                tags: '标签 - 相关标签',
                seoKeywords: 'SEO关键词 - 针对搜索引擎优化的关键词'
            },
            '功能特性': {
                features: '功能特性 - 网站主要功能列表',
                uniqueSellingPoints: '产品亮点 - 独特卖点',
                useCases: '使用场景 - 具体的使用案例',
                secondUseCase: '第二个使用案例 - 工具的次要用途',
                thirdUseCase: '第三个使用案例 - 工具的其他用途'
            },
            '技术和商业信息': {
                techStack: '技术栈 - 使用的主要技术',
                pricing: '定价模式 - 收费方式',
                priceAmount: '价格金额 - 具体价格（如果是付费的话）',
                isOpenSource: '是否开源 - 是否为开源项目',
                sourceCodeUrl: '源代码URL - 如果开源的话',
                demoUrl: '演示URL - 在线演示地址',
                hasFreePlan: '是否有免费计划 - 是否提供免费版本',
                hasFreeTrial: '是否有免费试用 - 是否提供免费试用期',
                hasAffiliateProgram: '是否有联盟计划 - 是否提供联盟营销计划',
                integrations: '集成信息 - 与其他平台的集成',
                creditCardRequired: '是否需要信用卡 - 注册是否需要信用卡',
                supportedPlatforms: '支持的平台 - 工具支持的操作系统/平台',
                apiAvailable: 'API可用性 - 是否提供API接口',
                affiliateUrl: '联盟计划URL - 联盟营销计划链接',
                features: '产品功能 - 独特功能列表',
                pros: '产品优点 - 主要优势',
                cons: '产品缺点 - 主要劣势',
                seoTitle: 'SEO标题 - 搜索引擎优化标题',
                seoKeywords: 'SEO关键词 - 搜索引擎关键词',
                seoDescription: 'SEO描述 - 搜索引擎描述',
                accessModel: '访问模式 - 工具的访问方式',
                industry: '行业分类 - 适用的行业领域',
                documentationUrl: '文档URL - 产品文档链接',
                useCases: '使用案例 - 产品使用场景',
                discordUrl: 'Discord URL - Discord社区链接',
                telegramUrl: 'Telegram URL - Telegram群组链接',
                faviconUrl: 'Favicon URL - 产品图标链接',
                pricingDetails: '定价详情 - 详细定价信息',
                keywords: '关键词 - 产品关键词标签',
                price: '价格 - 产品价格'
            },
            '联系信息': {
                contactEmail: '联系邮箱 - 网站联系邮箱',
                companyName: '公司名称 - 开发公司或团队名称',
                fullName: '联系人姓名 - 提交者姓名',
                firstName: '名字 - 独特的真实名字',
                lastName: '姓氏 - 独特的真实姓氏',
                submitterRole: '提交者角色 - 在公司中的角色',
                phone: '联系电话 - 联系电话号码',
                country: '国家地区 - 网站服务的主要地区'
            },
            '账号信息': {
                loginUrl: '登录URL - 网站登录页面地址',
                username: '用户名 - 随机生成的用户名',
                password: '密码 - 随机生成的密码'
            },
            '社交媒体': {
                socialLinks: '社交媒体链接 - 相关社交媒体账号',
                videoUrl: 'YouTube视频链接 - 产品演示或介绍视频',
                twitterUrl: 'Twitter账号链接 - 官方Twitter账号',
                twitterUsername: 'Twitter用户名 - 不包含@符号的用户名',
                linkedinUrl: 'LinkedIn账号链接 - 官方LinkedIn页面',
                instagramUsername: 'Instagram用户名 - Instagram账号用户名',
                githubUsername: 'GitHub用户名 - GitHub账号用户名'
            },
            '其他信息': {
                message: '提交备注信息 - 给站长的留言',
                launchDate: '上线时间 - 网站发布日期',
                releaseYear: '发布年份 - 网站发布年份',
                websiteStatus: '网站状态 - 当前运营状态',
                hasAiFeatures: 'AI功能标识 - 是否包含AI功能',
                targetAudience: '目标用户 - 主要用户群体',
                userCases: '用户案例 - 成功案例或用户反馈',
                howToUse: '使用方式 - 如何使用网站',
                installMethod: '安装方式 - 如何访问或安装',
                faqs: '常见问题 - FAQ内容',
                userRating: '用户评分 - 用户满意度评分',
                alternatives: '竞争对手 - 类似的工具或网站'
            }
        };

        // 扁平化字段注释映射（向后兼容）
        const fieldComments = {};
        Object.values(fieldCategories).forEach(category => {
            Object.assign(fieldComments, category);
        });

        let copiedCount = 0;

        // 获取字段类型
        function getFieldType(value) {
            if (typeof value === 'boolean') return 'Boolean';
            if (typeof value === 'number') return 'Number';
            if (typeof value === 'string') {
                if (value.startsWith('http')) return 'URL';
                if (value.includes('@')) return 'Email';
                if (value.length > 100) return 'Text';
                return 'String';
            }
            return 'Unknown';
        }

        // 复制到剪贴板
        async function copyToClipboard(text, button) {
            try {
                await navigator.clipboard.writeText(text);
                button.textContent = '已复制!';
                button.classList.add('copied');
                copiedCount++;
                document.getElementById('copied-count').textContent = copiedCount;
                
                setTimeout(() => {
                    button.textContent = '复制';
                    button.classList.remove('copied');
                }, 2000);
            } catch (err) {
                console.error('复制失败:', err);
                button.textContent = '复制失败';
                setTimeout(() => {
                    button.textContent = '复制';
                }, 2000);
            }
        }

        // 创建字段卡片
        function createFieldCard(key, value) {
            const card = document.createElement('div');
            card.className = 'field-card';
            card.dataset.fieldName = key.toLowerCase();
            card.dataset.fieldValue = String(value).toLowerCase();

            const comment = fieldComments[key] || '暂无说明';
            const type = getFieldType(value);

            // 创建复制按钮
            const copyBtn = document.createElement('button');
            copyBtn.className = 'copy-btn';
            copyBtn.textContent = '复制';
            copyBtn.addEventListener('click', () => copyToClipboard(String(value), copyBtn));

            // 创建字段值容器
            const fieldValueDiv = document.createElement('div');
            fieldValueDiv.className = 'field-value';
            fieldValueDiv.textContent = String(value);
            fieldValueDiv.appendChild(copyBtn);

            // 创建卡片内容
            card.innerHTML = `
                <div class="field-header">
                    <span class="field-name">${key}</span>
                    <span class="field-type">${type}</span>
                </div>
                <div class="field-comment">${comment}</div>
            `;

            card.appendChild(fieldValueDiv);
            return card;
        }

        // 渲染所有字段（按分类）
        function renderFields() {
            const fieldGrid = document.getElementById('field-grid');
            fieldGrid.innerHTML = '';

            let totalFields = 0;

            // 按分类渲染字段
            Object.entries(fieldCategories).forEach(([categoryName, categoryFields]) => {
                // 创建分类标题
                const categoryHeader = document.createElement('div');
                categoryHeader.className = 'category-header';
                categoryHeader.innerHTML = `<h3>${categoryName}</h3>`;
                fieldGrid.appendChild(categoryHeader);

                // 创建分类容器
                const categoryContainer = document.createElement('div');
                categoryContainer.className = 'category-container';

                let categoryCount = 0;
                Object.keys(categoryFields).forEach(key => {
                    if (WEBSITE_INFO.hasOwnProperty(key)) {
                        const card = createFieldCard(key, WEBSITE_INFO[key]);
                        categoryContainer.appendChild(card);
                        totalFields++;
                        categoryCount++;
                    }
                });

                // 添加分类统计
                const categoryStats = document.createElement('div');
                categoryStats.className = 'category-stats';
                categoryStats.textContent = `本分类: ${categoryCount} 个字段`;
                categoryContainer.appendChild(categoryStats);

                fieldGrid.appendChild(categoryContainer);
            });

            // 更新统计
            document.getElementById('total-fields').textContent = totalFields;
            document.getElementById('visible-fields').textContent = totalFields;
        }

        // 搜索功能
        function setupSearch() {
            const searchInput = document.getElementById('search-input');
            
            searchInput.addEventListener('input', (e) => {
                const searchTerm = e.target.value.toLowerCase();
                const cards = document.querySelectorAll('.field-card');
                let visibleCount = 0;

                cards.forEach(card => {
                    const fieldName = card.dataset.fieldName;
                    const fieldValue = card.dataset.fieldValue;
                    
                    if (fieldName.includes(searchTerm) || fieldValue.includes(searchTerm)) {
                        card.classList.remove('hidden');
                        visibleCount++;
                    } else {
                        card.classList.add('hidden');
                    }
                });

                document.getElementById('visible-fields').textContent = visibleCount;
            });
        }

        // 初始化
        document.addEventListener('DOMContentLoaded', () => {
            renderFields();
            setupSearch();
        });
    </script>
</body>
</html>
