// Firsto.co 网站规则配置
// 网站: https://firsto.co/projects/submit
// 最后更新: 2025-08-01

export const SITE_RULE = {
  // 基本信息
  domain: 'firsto.co',
  siteName: 'Firsto',
  priority: 1,
  lastUpdated: '2025-08-01',
  
  // 字段映射规则
  fieldMappings: {
    // 项目名称 -> Project Name
    siteName: {
      selectors: [
        'input[name="name"]',
        'input#name',
        'input[placeholder*="Enter project name"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '项目名称，必填字段'
    },

    // 网站URL -> Website URL
    siteUrl: {
      selectors: [
        'input[name="websiteUrl"]',
        'input#websiteUrl',
        'input[placeholder*="https://example.com"]'
      ],
      method: 'value',
      validation: 'required|url',
      notes: '网站URL地址，必填字段'
    },

    // 项目描述 -> Short Description
    siteDescription: {
      selectors: [
        'textarea[name="description"]',
        'textarea#description',
        'textarea[placeholder*="Enter a short description"]'
      ],
      method: 'value',
      validation: 'required',
      maxLength: 120,
      notes: '项目简短描述，最大120字符，必填字段'
    }
  },
  
  // 提交流程配置
  submitConfig: {
    submitButton: 'button[type="submit"], .submit-button, button:contains("Submit")',
    submitMethod: 'click',
    waitAfterFill: 1000,
    waitAfterSubmit: 3000,
    successIndicators: [
      '.success-message',
      '.thank-you',
      '.submission-success'
    ],
    errorIndicators: [
      '.error-message',
      '.validation-error',
      '.form-error'
    ]
  },
  
  // 特殊处理
  specialHandling: {
    requiresLogin: false,
    hasCaptcha: false,
    hasFileUpload: false,
    
    // 表单验证规则
    formValidation: {
      requiredFields: ['siteName', 'siteUrl', 'siteDescription'],
      emailValidation: false,
      urlValidation: true,
      maxLengthValidation: true
    },
    
    // 特殊注意事项
    notes: [
      '简洁的三字段表单',
      '描述字段有120字符限制',
      '所有字段都是必填项',
      '使用现代化的UI设计'
    ]
  }
};

// 自定义处理函数
export function handleFirstoSubmission(data, rule) {
  console.log('Processing Firsto.co submission...');
  
  const processedData = { ...data };
  
  // 确保URL格式正确
  if (processedData.siteUrl && !processedData.siteUrl.startsWith('http')) {
    processedData.siteUrl = 'https://' + processedData.siteUrl;
  }
  
  // 处理描述长度限制
  if (processedData.siteDescription) {
    processedData.siteDescription = processedData.siteDescription.trim();
    if (processedData.siteDescription.length > 120) {
      processedData.siteDescription = processedData.siteDescription.substring(0, 117) + '...';
    }
  }
  
  return processedData;
}
