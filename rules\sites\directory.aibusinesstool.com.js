// AIBusinessTool Directory 网站规则配置
// 网站: https://directory.aibusinesstool.com/submit-your-ai-tool-directory
// 最后更新: 2025-07-24

export const SITE_RULE = {
  // 基本信息
  domain: 'directory.aibusinesstool.com',
  siteName: 'AI Business Tool Directory',
  priority: 1,
  lastUpdated: '2025-07-24',

  // 字段映射规则
  fieldMappings: {
    // 姓名 -> Your Name
    fullName: {
      selectors: [
        'input[placeholder="Your Name"]',
        'input[name="a7647ca9"]',
        '#form3-YourName-1545733626'
      ],
      method: 'value',
      validation: 'optional',
      notes: '提交者姓名'
    },

    // AI工具名称 -> Ai Tool Name
    siteName: {
      selectors: [
        'input[placeholder="Ai Tool Name"]',
        'input[name="bda76fe9"]',
        '#form3-AiToolName--678167719'
      ],
      method: 'value',
      validation: 'required',
      notes: 'AI工具名称'
    },

    // 邮箱地址 -> Your Email Address
    contactEmail: {
      selectors: [
        'input[placeholder="Your Email Address"]',
        'input[name="2f4c1306"]',
        'input[type="email"]'
      ],
      method: 'value',
      validation: 'required|email',
      notes: '联系邮箱地址'
    },

    // 网站URL -> Website URL
    siteUrl: {
      selectors: [
        'input[placeholder="Website URL"]',
        'input[name="82ef41d0"]',
        '#form3-WebsiteURL--462305460'
      ],
      method: 'value',
      validation: 'required|url',
      notes: '网站URL地址'
    },

    // 列表类型 -> Listing Type
    category: {
      selectors: [
        'input[placeholder="Listing Type"]',
        'input[name="935536b7"]',
        '#form3-ListingType-1004614836'
      ],
      method: 'value',
      validation: 'optional',
      notes: '列表类型',
      defaultValue: 'AI Tool'
    },

    // 域名权威度 -> Domain Authority
    domainAuthority: {
      selectors: [
        'input[placeholder="Domain Authority"]',
        'input[name="4f682236"]',
        '#form3-DomainAuthority-80753925'
      ],
      method: 'value',
      validation: 'optional',
      notes: '域名权威度分数',
      defaultValue: '45'
    },

    // Similarweb流量 -> Similarweb Traffic
    traffic: {
      selectors: [
        'input[placeholder="Similarweb Traffic"]',
        'input[name="8467e544"]',
        '#form3-SimilarwebTraffic-1805584644'
      ],
      method: 'value',
      validation: 'optional',
      notes: 'Similarweb流量数据',
      defaultValue: '50K'
    },

    // 域名年龄 -> Domain Age
    domainAge: {
      selectors: [
        'input[placeholder="Domain Age"]',
        'input[name="8a4dbd91"]',
        '#form3-DomainAge--637820863'
      ],
      method: 'value',
      validation: 'optional',
      notes: '域名年龄',
      defaultValue: '3 years'
    }
  },
  // 提交流程配置
  submitConfig: {
    submitButton: 'button[type="submit"], .MuiButton-contained',
    submitMethod: 'manual', // 手动提交
    waitAfterFill: 0,
    waitAfterSubmit: 0,
    successIndicators: ['.success-message'],
    errorIndicators: ['.error-message']
  },

  // 特殊处理
  specialHandling: {
    requiresLogin: false,
    hasCaptcha: false,
    hasFileUpload: false,
    customScript: 'handleAiBusinessToolDirectorySubmission',
    formValidation: {
      requiredFields: ['siteName', 'contactEmail', 'siteUrl'],
      emailValidation: true,
      urlValidation: true
    },
    notes: [
      'AI Business Tool Directory AI工具目录平台',
      '基于Material-UI构建',
      '包含详细的SEO指标字段',
      '支持域名权威度、流量数据等专业指标',
      '现代化的React界面',
      '手动提交模式'
    ]
  }
};

// 自定义处理函数
export function handleAiBusinessToolDirectorySubmission(data) {
  console.log('Processing AI Business Tool Directory form submission...');

  const processedData = { ...data };

  // 确保URL格式正确
  if (processedData.siteUrl && !processedData.siteUrl.startsWith('http')) {
    processedData.siteUrl = 'https://' + processedData.siteUrl;
  }

  // 设置默认值
  if (!processedData.listingType) {
    processedData.listingType = 'AI Tool';
  }

  if (!processedData.domainAuthority) {
    processedData.domainAuthority = '45';
  }

  if (!processedData.traffic) {
    processedData.traffic = '50K';
  }

  if (!processedData.domainAge) {
    processedData.domainAge = '3 years';
  }

  return processedData;
}

// 自定义元素填写函数
export async function customFillElement(element, value, config) {
  console.log(`AI Business Tool Directory自定义填写: ${element.tagName}, 方法: ${config.method}`);

  // 为特定字段提供默认值（如果没有传入值或值为空）
  if (!value || value === '') {
    if (element.placeholder === 'Listing Type') {
      value = 'AI Tool';
    } else if (element.placeholder === 'Domain Authority') {
      value = '45';
    } else if (element.placeholder === 'Similarweb Traffic') {
      value = '50K';
    } else if (element.placeholder === 'Domain Age') {
      value = '3 years';
    }
  }

  // 如果仍然没有值，跳过填写
  if (!value || value === '') {
    console.log(`⚠️ 跳过空值字段: ${element.placeholder}`);
    return false;
  }

  switch (config.method) {
    case 'value':
      // Material-UI输入框处理
      element.focus();
      await new Promise(resolve => setTimeout(resolve, 300));

      // 清空现有值
      element.value = '';
      element.dispatchEvent(new Event('input', { bubbles: true }));
      await new Promise(resolve => setTimeout(resolve, 100));

      // 设置新值
      element.value = value;
      element.dispatchEvent(new Event('input', { bubbles: true }));
      element.dispatchEvent(new Event('change', { bubbles: true }));
      element.dispatchEvent(new Event('blur', { bubbles: true }));

      console.log(`✓ 填写字段: ${element.placeholder} = "${value}"`);
      return true;

    default:
      element.value = value;
      element.dispatchEvent(new Event('input', { bubbles: true }));
      return true;
  }
}