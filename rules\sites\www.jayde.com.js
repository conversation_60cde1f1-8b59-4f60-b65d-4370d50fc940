// Jayde.com 网站规则配置
// 网站: https://www.jayde.com/submit.html
// 最后更新: 2025-07-24

export const SITE_RULE = {
  domain: 'www.jayde.com',
  siteName: 'Jayde',
  priority: 1,
  lastUpdated: '2025-07-24',

  fieldMappings: {
    siteUrl: {
      selectors: [
        'input[name="URL"]',
        'td:contains("Your URL") + td input',
        'input[value="https://"]'
      ],
      method: 'value',
      validation: 'required|url',
      notes: '网站URL，预填https://'
    },

    contactEmail: {
      selectors: [
        'input[name="EMAIL"]',
        'td:contains("Your email") + td input',
        'input[title="Email"]'
      ],
      method: 'value',
      validation: 'required|email',
      notes: '联系邮箱，建议使用公司邮箱'
    },

    fullName: {
      selectors: [
        'input[name="NAME"]',
        'td:contains("Your name") + td input',
        '#id5_18_NAME_text input'
      ],
      method: 'value',
      validation: 'optional',
      notes: '提交者姓名，最多100字符'
    },

    companyName: {
      selectors: [
        'input[name="BUSINESS_NAME"]',
        'td:contains("Business name") + td input',
        '#id5_18_BUSINESS_NAME_text input'
      ],
      method: 'value',
      validation: 'optional',
      notes: '企业名称，最多100字符'
    },

    streetAddress: {
      selectors: [
        'input[name="ADDRESS"]',
        'td:contains("business street address") + td input'
      ],
      method: 'value',
      validation: 'optional',
      notes: '企业街道地址'
    },

    phone: {
      selectors: [
        'input[name="PHONE"]',
        'td:contains("Business phone number") + td input'
      ],
      method: 'value',
      validation: 'optional',
      notes: '企业电话，含国家和区号'
    },

    zipCode: {
      selectors: [
        'input[name="ZIP"]',
        'td:contains("zip/postal code") + td input'
      ],
      method: 'value',
      validation: 'optional',
      notes: '邮政编码'
    },

    country: {
      selectors: [
        'select[name="COUNTRY"]',
        'td:contains("Country") + td select'
      ],
      method: 'select',
      validation: 'optional',
      defaultValue: 'US',
      notes: '国家选择，默认United States'
    },

    state: {
      selectors: [
        'select[name="STATE"]',
        'td:contains("State or Province") + td select'
      ],
      method: 'select',
      validation: 'optional',
      defaultValue: 'CA',
      notes: '州/省选择，默认California'
    },

    category: {
      selectors: [
        'select[name="INDUSTRY"]',
        'td:contains("Industry") + td select'
      ],
      method: 'select',
      validation: 'optional',
      defaultValue: 'web_development',
      notes: '行业分类，默认Web Development'
    },

    detailedIntro: {
      selectors: [
        'textarea[name="XDESCRIPTION"]',
        'td:contains("Business description") + td textarea'
      ],
      method: 'value',
      validation: 'optional',
      notes: '企业描述'
    },

    facebookUrl: {
      selectors: [
        'input[name="FACEBOOK"]',
        'td:contains("Facebook URL") + td input'
      ],
      method: 'value',
      validation: 'optional|url',
      notes: 'Facebook URL，预填https://'
    },

    googlePlusUrl: {
      selectors: [
        'input[name="GPLUS"]',
        'td:contains("Google+ profile URL") + td input'
      ],
      method: 'value',
      validation: 'optional|url',
      notes: 'Google+ URL，预填https://'
    },

    twitterUrl: {
      selectors: [
        'input[name="TWITTER"]',
        'td:contains("Twitter account name") + td input'
      ],
      method: 'value',
      validation: 'optional',
      notes: 'Twitter账号名，预填@'
    },

    twellowUrl: {
      selectors: [
        'input[name="TWELLOW"]',
        'td:contains("Twellow account name") + td input'
      ],
      method: 'value',
      validation: 'optional',
      notes: 'Twellow账号名'
    },

    youtubeUrl: {
      selectors: [
        'input[name="YOUTUBE"]',
        'td:contains("YouTube URL") + td input'
      ],
      method: 'value',
      validation: 'optional|url',
      notes: 'YouTube URL，预填https://'
    },

    logoUrl: {
      selectors: [
        'input[name="LOGO"]',
        'td:contains("Company logo image URL") + td input'
      ],
      method: 'value',
      validation: 'optional|url',
      notes: '公司Logo图片URL'
    },

    googleMapsUrl: {
      selectors: [
        'input[name="GMAP"]',
        'td:contains("Google maps URL") + td input'
      ],
      method: 'value',
      validation: 'optional|url',
      notes: 'Google地图URL'
    }
  },

  submitConfig: {
    submitButton: 'button[name="Add my Site"], button[type="submit"]',
    submitMethod: 'click',
    successIndicators: ['.success-message'],
    errorIndicators: ['.error-message']
  },

  specialHandling: {
    requiresLogin: false,
    hasCaptcha: false,
    hasFileUpload: false,
    customScript: 'handleJaydeSubmission',
    formValidation: {
      requiredFields: ['siteUrl', 'contactEmail'],
      emailValidation: true,
      urlValidation: true
    },
    notes: [
      '企业目录提交平台',
      '详细的可选信息字段',
      '多个社交媒体链接',
      '地理位置信息',
      '行业分类选择',
      '公司Logo和地图支持'
    ]
  }
};

export function handleJaydeSubmission(data, rule) {
  const processedData = { ...data };

  // URL格式化
  const urlFields = ['siteUrl', 'facebookUrl', 'googlePlusUrl', 'youtubeUrl', 'logoUrl', 'googleMapsUrl'];
  urlFields.forEach(field => {
    if (processedData[field] && !processedData[field].startsWith('http')) {
      processedData[field] = 'https://' + processedData[field];
    }
  });

  // Twitter用户名处理
  if (processedData.twitterUrl && !processedData.twitterUrl.startsWith('@')) {
    processedData.twitterUrl = '@' + processedData.twitterUrl;
  }

  // 设置默认选择值
  const countrySelect = document.querySelector('select[name="COUNTRY"]');
  if (countrySelect) {
    countrySelect.value = 'US';
  }

  const stateSelect = document.querySelector('select[name="STATE"]');
  if (stateSelect) {
    stateSelect.value = 'CA';
  }

  const industrySelect = document.querySelector('select[name="INDUSTRY"]');
  if (industrySelect) {
    industrySelect.value = 'web_development';
  }

  return processedData;
}

export async function customFillElement(element, value, config) {
  // 处理下拉选择框
  if (element.tagName === 'SELECT') {
    let targetValue = null;

    if (element.name === 'COUNTRY') {
      targetValue = 'US';
    } else if (element.name === 'STATE') {
      targetValue = 'CA';
    } else if (element.name === 'INDUSTRY') {
      targetValue = 'web_development';
    }

    if (targetValue) {
      const options = element.querySelectorAll('option');
      const option = Array.from(options).find(opt => opt.value === targetValue);
      if (option) {
        element.value = option.value;
        element.dispatchEvent(new Event('change', { bubbles: true }));
        return true;
      }
    }
  }

  if (element.tagName === 'INPUT' || element.tagName === 'TEXTAREA') {
    element.value = value;
    element.dispatchEvent(new Event('input', { bubbles: true }));
    element.dispatchEvent(new Event('change', { bubbles: true }));
    return true;
  }

  return false;
}