// BAI.tools 网站规则配置
// 网站: https://bai.tools/submit-ai-tools
// 最后更新: 2025-07-06

export const SITE_RULE = {
  // 基本信息
  domain: 'bai.tools',
  siteName: 'BAI',
  priority: 1,
  lastUpdated: '2025-07-06',
  
  // 字段映射规则
  fieldMappings: {
    // AI工具名称 -> AI Tool Name (使用siteName)
    siteName: {
      selectors: [
        'textarea[name="toolname"]',
        'textarea[placeholder*="name of your AI Tools"]',
        'textarea[maxlength="256"]:first-of-type'
      ],
      method: 'value',
      validation: 'required',
      notes: 'AI工具名称，使用website-info.js中的siteName字段'
    },

    // 网站URL -> What's your website URL? (使用siteUrl)
    siteUrl: {
      selectors: [
        'textarea[name="toolurl"]',
        'textarea[placeholder*="Enter the URL to your website"]',
        'textarea[maxlength="256"]:last-of-type'
      ],
      method: 'value',
      validation: 'required|url',
      notes: '工具网站URL，使用website-info.js中的siteUrl字段'
    }
  },
  
  // 自定义填写函数
  customFillElement: async function(element, value, config) {
    console.log(`BAI.tools自定义填写: ${element.placeholder || element.name}, 方法: ${config.method}`);
    
    switch (config.method) {
      case 'value':
        // 标准表单处理
        element.focus();
        await new Promise(resolve => setTimeout(resolve, 100));
        
        element.value = value;
        element.dispatchEvent(new Event('input', { bubbles: true }));
        element.dispatchEvent(new Event('change', { bubbles: true }));
        element.dispatchEvent(new Event('blur', { bubbles: true }));
        
        console.log(`✓ 填写字段: ${element.placeholder || element.name} = "${value}"`);
        break;
        
      default:
        element.value = value;
        element.dispatchEvent(new Event('input', { bubbles: true }));
        element.dispatchEvent(new Event('change', { bubbles: true }));
    }
  },
  
  // 提交流程配置
  submitConfig: {
    submitButton: 'button[type="submit"], input[type="submit"], button:contains("Submit")',
    submitMethod: 'click',
    waitAfterFill: 1000, // 简单表单，等待时间短
    waitAfterSubmit: 3000,
    successIndicators: [
      '.success-message',
      '.thank-you',
      'div:contains("successfully")',
      'div:contains("submitted")'
    ],
    errorIndicators: [
      '.error-message',
      '.validation-error',
      '[class*="error"]'
    ]
  },
  
  // 特殊处理
  specialHandling: {
    requiresLogin: false,
    hasCaptcha: false,
    hasFileUpload: false,
    
    // 表单验证规则
    formValidation: {
      requiredFields: ['siteName', 'siteUrl'],
      emailValidation: false,
      urlValidation: true
    },
    
    // 特殊注意事项
    notes: [
      '这是最简单的AI工具提交表单',
      '只有2个必填字段',
      '无需注册或登录',
      '支持多语言界面',
      '提交速度快，审核简单',
      '适合快速提交工具'
    ]
  }
};

// 自定义处理函数
export function handleBAIToolsSubmission(data, rule) {
  console.log('Processing BAI.tools submission...');
  
  const processedData = { ...data };
  
  // 确保URL格式正确
  if (processedData.siteUrl && !processedData.siteUrl.startsWith('http')) {
    processedData.siteUrl = 'https://' + processedData.siteUrl;
  }
  
  return processedData;
}
