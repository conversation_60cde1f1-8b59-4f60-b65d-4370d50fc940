// findaiforthat.com 网站规则配置
// 网站: https://findaiforthat.com/submit-ai-tool/
// 表单技术: Elementor Forms with File Upload
// 最后更新: 2025-07-07

export const SITE_RULE = {
  // 基本信息
  domain: 'findaiforthat.com',
  siteName: 'Find AI For That',
  priority: 1,
  lastUpdated: '2025-07-07',
  
  // 字段映射规则
  fieldMappings: {
    // 提交者姓名 -> Name
    fullName: {
      selectors: [
        'input[id="form-field-name"]',
        'input[name="form_fields[name]"]',
        '.elementor-field-group-name input'
      ],
      method: 'value',
      validation: 'required',
      notes: '提交者姓名，使用website-info.js中的fullName字段'
    },
    
    // 公司名称 -> Company Name
    companyName: {
      selectors: [
        'input[id="form-field-field_8c4ec9a"]',
        'input[name="form_fields[field_8c4ec9a]"]',
        '.elementor-field-group-field_8c4ec9a input'
      ],
      method: 'value',
      validation: 'required',
      notes: '公司名称，使用website-info.js中的companyName字段'
    },
    
    // 邮箱地址 -> Email
    contactEmail: {
      selectors: [
        'input[id="form-field-email"]',
        'input[name="form_fields[email]"]',
        'input[type="email"]',
        'input[placeholder="<EMAIL>"]'
      ],
      method: 'value',
      validation: 'required|email',
      notes: '邮箱地址，使用website-info.js中的contactEmail字段'
    },
    
    // 工具分类 -> Category
    category: {
      selectors: [
        'select[id="form-field-field_ffa8578"]',
        'select[name="form_fields[field_ffa8578][]"]',
        '.elementor-field-group-field_ffa8578 select'
      ],
      method: 'select-multiple',
      validation: 'required',
      defaultValue: 'Design & Art',
      notes: '工具分类，多选下拉框，默认选择Design & Art'
    },
    
    // AI工具网站链接 -> Ai Tool Website Link
    siteUrl: {
      selectors: [
        'input[id="form-field-field_94f6456"]',
        'input[name="form_fields[field_94f6456]"]',
        'input[type="url"]',
        'input[placeholder="www.example.com"]'
      ],
      method: 'value',
      validation: 'required|url',
      notes: 'AI工具网站链接，使用website-info.js中的siteUrl字段'
    },
    
    // 上传Logo -> Upload Logo
    logoUpload: {
      selectors: [
        'input[id="form-field-field_691aa4e"]',
        'input[name="form_fields[field_691aa4e][]"]',
        'input[type="file"]',
        '.elementor-upload-field'
      ],
      method: 'file-upload',
      validation: 'required',
      notes: 'Logo文件上传，必填字段，最大5MB'
    },
    
    // 消息 -> Message
    message: {
      selectors: [
        'textarea[id="form-field-message"]',
        'textarea[name="form_fields[message]"]',
        'textarea[placeholder="Message"]'
      ],
      method: 'value',
      validation: 'optional',
      defaultValue: 'This is an innovative AI tool that provides excellent features and user experience.',
      notes: '附加消息，可选字段，使用默认值'
    }
  },
  
  // 自定义填写函数
  customFillElement: async function(element, value, config) {
    console.log(`Find AI For That自定义填写: ${element.id || element.name}, 方法: ${config.method}`);
    
    switch (config.method) {
      case 'value':
        // 标准输入框和文本域处理
        element.focus();
        await new Promise(resolve => setTimeout(resolve, 100));
        
        // 清空现有内容
        element.value = '';
        await new Promise(resolve => setTimeout(resolve, 50));
        
        // 处理特殊字段
        let finalValue = value;
        if (element.id === 'form-field-message') {
          // 消息字段使用默认值
          finalValue = config.defaultValue;
        }
        
        // 设置新值
        element.value = finalValue;
        
        // 触发事件
        element.dispatchEvent(new Event('input', { bubbles: true }));
        element.dispatchEvent(new Event('change', { bubbles: true }));
        element.dispatchEvent(new Event('blur', { bubbles: true }));
        
        console.log(`✓ 填写字段: ${element.id} = "${finalValue.substring(0, 50)}..."`);
        break;
        
      case 'select-multiple':
        // 多选下拉框处理
        if (element.tagName.toLowerCase() === 'select' && element.multiple) {
          // 分类映射表
          const categoryMapping = {
            'AI Tools': 'Design & Art',
            'Developer Tools': 'Code & IT',
            'Content Creation': 'Text & Writing',
            'Image Generation': 'Image',
            'Video Generation': 'Video',
            'Audio Tools': 'Voice',
            'Chatbot': 'Chatbot',
            'Design': 'Design & Art',
            'Marketing': 'Marketing',
            'Productivity': 'Productivity',
            'Education': 'Education',
            'Business': 'Business',
            'Writing': 'Text & Writing',
            '3D': '3D'
          };
          
          // 尝试映射分类
          let targetValue = categoryMapping[value] || config.defaultValue;
          
          // 查找匹配的选项
          const option = Array.from(element.options).find(opt => 
            opt.value === targetValue || 
            opt.text === targetValue ||
            opt.text.includes(targetValue)
          );
          
          if (option) {
            // 清除所有选择
            Array.from(element.options).forEach(opt => opt.selected = false);
            // 选择目标选项
            option.selected = true;
            element.dispatchEvent(new Event('change', { bubbles: true }));
            console.log(`✓ 选择分类: ${option.text}`);
          } else {
            // 使用默认值
            const defaultOption = Array.from(element.options).find(opt => 
              opt.text === config.defaultValue || opt.value === config.defaultValue
            );
            if (defaultOption) {
              Array.from(element.options).forEach(opt => opt.selected = false);
              defaultOption.selected = true;
              element.dispatchEvent(new Event('change', { bubbles: true }));
              console.log(`✓ 使用默认分类: ${defaultOption.text}`);
            }
          }
        }
        break;
        
      case 'file-upload':
        // 文件上传处理
        console.log('⚠️ 文件上传字段需要手动处理');
        console.log('请手动上传Logo文件（最大5MB）');
        // 文件上传无法通过脚本自动完成，需要用户手动操作
        break;
        
      default:
        element.value = value;
        element.dispatchEvent(new Event('input', { bubbles: true }));
        element.dispatchEvent(new Event('change', { bubbles: true }));
    }
  },
  
  // 提交流程配置
  submitConfig: {
    submitButton: [
      'button[type="submit"]',
      '.elementor-button',
      'button:contains("Submit & Pay")'
    ],
    submitMethod: 'click',
    waitAfterFill: 3000, // 等待文件上传
    waitAfterSubmit: 5000,
    successIndicators: [
      '.elementor-message-success',
      '.success-message',
      '.thank-you',
      '[class*="success"]'
    ],
    errorIndicators: [
      '.elementor-message-danger',
      '.error-message',
      '[class*="error"]'
    ]
  },
  
  // 特殊处理
  specialHandling: {
    requiresLogin: false,
    hasCaptcha: false,
    hasFileUpload: true, // 有文件上传字段
    isElementorForm: true, // 使用Elementor表单
    hasPayment: true, // 需要付费提交
    
    // 表单验证规则
    formValidation: {
      requiredFields: ['fullName', 'companyName', 'contactEmail', 'category', 'siteUrl', 'logoUpload'],
      emailValidation: true,
      urlValidation: true,
      fileUploadFields: ['logoUpload'],
      fileUploadLimits: {
        logoUpload: { maxSize: '5MB', multiple: true }
      }
    },
    
    // 特殊注意事项
    notes: [
      '这是WordPress网站，使用Elementor表单插件',
      '表单包含7个字段，6个必填，1个可选',
      '需要付费提交，按钮显示"Submit & Pay"',
      '有文件上传字段，需要上传Logo（最大5MB）',
      '分类字段是多选下拉框，可选择多个分类',
      '分类选项包含16个选项，默认选择Design & Art',
      '文件上传支持多文件上传',
      '消息字段是可选的',
      '网站使用PayPal支付保护',
      '表单字段名格式：form_fields[field_id]'
    ]
  }
};

// 自定义处理函数
export function handleFindAIForThatSubmission(data, _rule) {
  console.log('Processing Find AI For That form submission...');

  const processedData = { ...data };

  // 确保URL格式正确
  if (processedData.siteUrl && !processedData.siteUrl.startsWith('http')) {
    processedData.siteUrl = 'https://' + processedData.siteUrl;
  }

  // 处理分类映射
  if (processedData.category) {
    const categoryMapping = {
      'AI Tools': 'Design & Art',
      'Developer Tools': 'Code & IT',
      'Content Creation': 'Text & Writing',
      'Image Generation': 'Image',
      'Video Generation': 'Video',
      'Audio Tools': 'Voice',
      'Chatbot': 'Chatbot',
      'Design': 'Design & Art',
      'Marketing': 'Marketing',
      'Productivity': 'Productivity',
      'Education': 'Education',
      'Business': 'Business',
      'Writing': 'Text & Writing'
    };
    
    processedData.category = categoryMapping[processedData.category] || 'Design & Art';
  }

  // 设置默认值
  processedData.message = 'This is an innovative AI tool that provides excellent features and user experience.';

  // 文件上传提醒
  console.log('⚠️ 请注意：需要手动上传Logo文件');

  return processedData;
}
