// listedai.co 网站规则配置
// 网站: https://www.listedai.co/submit
// 表单技术: React Form with Tailwind CSS
// 最后更新: 2025-07-08

export const SITE_RULE = {
  // 基本信息
  domain: 'www.listedai.co',
  siteName: 'Listed AI',
  priority: 1,
  lastUpdated: '2025-07-08',
  
  // 字段映射规则
  fieldMappings: {
    // 工具名称 -> Tool Name
    siteName: {
      selectors: [
        'input[name="name"]',
        'input[placeholder="ListedAI"]',
        'input[for="name"]',
        'label:contains("Tool Name") + input'
      ],
      method: 'value',
      validation: 'required',
      notes: '工具名称，使用website-info.js中的siteName字段'
    },

    // 工具网站 -> Tool Website (完整URL，手动删除https://)
    siteUrl: {
      selectors: [
        'input[name="url"]',
        'input[placeholder="listedai.co"]',
        'label[for="url"] + div input',
        '.rounded-r-md.border input[type="text"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '工具网站，使用website-info.js中的siteUrl字段，输出完整URL，手动删除https://'
    },

    // 简短描述 -> Description (max 220 characters)
    siteDescription: {
      selectors: [
        'input[name="shortDescription"]',
        'input[maxlength="220"]',
        'input[placeholder*="ListedAI is a list of tools"]'
      ],
      method: 'value',
      validation: 'required',
      maxLength: 220,
      notes: '简短描述，使用website-info.js中的siteDescription字段，最大220字符'
    },

    // 公司邮箱 -> Company Email
    contactEmail: {
      selectors: [
        'input[name="email"]',
        'input[type="email"]',
        'input[placeholder="<EMAIL>"]'
      ],
      method: 'value',
      validation: 'required|email',
      notes: '公司邮箱，使用website-info.js中的contactEmail字段'
    },

    // 完整描述 -> Full description (max 3000 characters)
    detailedIntro: {
      selectors: [
        'textarea[name="description"]',
        'textarea[id="description"]',
        'textarea[maxlength="3000"]'
      ],
      method: 'value',
      validation: 'required',
      maxLength: 3000,
      notes: '完整描述，使用website-info.js中的detailedIntro字段，最大3000字符'
    },

    // Market Segment -> Individual (默认选择)
    marketSegmentIndividual: {
      selectors: [
        'button[id="individual"]',
        'input[name="individual"]'
      ],
      method: 'checkbox',
      validation: 'optional',
      defaultValue: true,
      notes: 'Market Segment选择Individual'
    },

    // Price Segment -> Free (默认选择)
    priceSegmentFree: {
      selectors: [
        'button[id="free"]',
        'input[name="free"]'
      ],
      method: 'checkbox',
      validation: 'optional',
      defaultValue: true,
      notes: 'Price Segment选择Free'
    },

    // Features -> No Signup Required (默认选择)
    featureNoSignup: {
      selectors: [
        'button[id="no-signup-required"]',
        'input[name="no-signup-required"]'
      ],
      method: 'checkbox',
      validation: 'optional',
      defaultValue: true,
      notes: 'Features选择No Signup Required'
    }
  },
  
  // 自定义填写函数
  customFillElement: async function(element, value, config) {
    console.log(`Listed AI自定义填写: ${element.name || element.placeholder}, 方法: ${config.method}`);
    
    switch (config.method) {
      case 'value':
        // React表单处理
        element.focus();
        await new Promise(resolve => setTimeout(resolve, 100));
        
        // 清空现有内容
        element.value = '';
        await new Promise(resolve => setTimeout(resolve, 50));

        // 处理特殊字段值
        let finalValue = value;

        // 处理字符限制
        if (config.maxLength && finalValue.length > config.maxLength) {
          finalValue = finalValue.substring(0, config.maxLength);
        }

        // 设置新值
        element.value = finalValue;
        
        // 触发React事件
        const inputEvent = new Event('input', { bubbles: true });
        const changeEvent = new Event('change', { bubbles: true });
        
        // React特殊处理
        Object.defineProperty(inputEvent, 'target', {
          writable: false,
          value: element
        });
        Object.defineProperty(changeEvent, 'target', {
          writable: false,
          value: element
        });
        
        element.dispatchEvent(inputEvent);
        element.dispatchEvent(changeEvent);
        element.dispatchEvent(new Event('blur', { bubbles: true }));
        
        console.log(`✓ 填写字段: ${element.name} = "${value}"`);
        break;

      case 'checkbox':
        // React复选框处理
        const shouldCheck = config.defaultValue || false;

        if (shouldCheck) {
          // 查找对应的button元素
          const button = element.tagName === 'BUTTON' ? element :
                        document.querySelector(`button[id="${element.name}"]`);

          if (button) {
            // 检查当前状态
            const isChecked = button.getAttribute('aria-checked') === 'true';

            if (!isChecked) {
              button.click();
              console.log(`✓ 选中复选框: ${element.name || element.id}`);
            } else {
              console.log(`✓ 复选框已选中: ${element.name || element.id}`);
            }
          }
        }
        break;

      default:
        element.value = value;
        element.dispatchEvent(new Event('input', { bubbles: true }));
        element.dispatchEvent(new Event('change', { bubbles: true }));
    }
  },
  
  // 提交流程配置
  submitConfig: {
    submitButton: [
      'button[type="submit"]',
      'button:contains("Submit")',
      'button:contains("Add Tool")',
      'form button'
    ],
    submitMethod: 'click',
    waitAfterFill: 2000,
    waitAfterSubmit: 5000,
    successIndicators: [
      '.success-message',
      'text:contains("submitted")',
      'text:contains("success")',
      'text:contains("thank you")'
    ],
    errorIndicators: [
      '.error-message',
      'text:contains("error")',
      'text:contains("required")'
    ]
  },
  
  // 特殊处理
  specialHandling: {
    requiresLogin: false,
    hasCaptcha: false,
    hasFileUpload: false,
    isReactForm: true, // React表单
    isTailwindCSS: true, // 使用Tailwind CSS
    isResponsive: true, // 响应式设计
    
    // 表单验证规则
    formValidation: {
      requiredFields: ['siteName', 'siteUrl', 'siteDescription', 'contactEmail', 'detailedIntro'],
      emailValidation: true,
      urlValidation: true,
      characterLimits: {
        siteDescription: 220,
        detailedIntro: 3000
      },
      checkboxFields: ['marketSegmentIndividual', 'priceSegmentFree', 'featureNoSignup'],
      reactValidation: true // React表单验证
    },
    
    // 特殊注意事项
    notes: [
      '这是Listed AI的React表单',
      '使用Tailwind CSS样式框架',
      '表单有响应式设计',
      '包含8个字段：5个必填，3个复选框',
      'Tool Website字段需要去掉https://',
      'Description限制220字符',
      'Full description限制3000字符',
      '有3个复选框组：Market Segment, Price Segment, Features',
      '默认选择：Individual, Free, No Signup Required',
      '使用现代化的UI组件',
      '专注于AI工具列表服务',
      '表单使用flex布局',
      '有完整的无障碍支持',
      '复选框使用button元素实现'
    ]
  }
};

// 自定义处理函数
export function handleListedAISubmission(data, _rule) {
  console.log('Processing Listed AI form submission...');

  const processedData = { ...data };

  // 确保工具名称不为空
  if (!processedData.siteName) {
    processedData.siteName = 'AI Tool'; // 备用名称
  }

  // URL字段保持完整，手动删除https://

  // 处理字符限制
  if (processedData.siteDescription && processedData.siteDescription.length > 220) {
    processedData.siteDescription = processedData.siteDescription.substring(0, 220);
  }

  if (processedData.detailedIntro && processedData.detailedIntro.length > 3000) {
    processedData.detailedIntro = processedData.detailedIntro.substring(0, 3000);
  }

  // 设置默认选择
  processedData.marketSegmentIndividual = true;
  processedData.priceSegmentFree = true;
  processedData.featureNoSignup = true;

  return processedData;
}

// React表单处理函数
export async function handleReactForm() {
  console.log('处理React表单...');
  
  // 等待React组件加载
  await new Promise(resolve => setTimeout(resolve, 1000));
  
  // 检查表单是否存在
  const form = document.querySelector('form');
  if (!form) {
    console.log('⚠️ 未找到表单元素');
    return false;
  }
  
  // 检查是否是React表单
  const isReactForm = form.action && form.action.includes('React form was unexpectedly submitted');
  if (isReactForm) {
    console.log('✓ 确认为React表单');
    return true;
  }
  
  return false;
}

// 动态字段检测
export async function detectDynamicFields() {
  console.log('检测动态字段...');
  
  // 等待可能的动态内容加载
  await new Promise(resolve => setTimeout(resolve, 2000));
  
  const inputs = document.querySelectorAll('input, textarea, select');
  console.log(`检测到 ${inputs.length} 个输入字段`);
  
  inputs.forEach((input, index) => {
    console.log(`字段 ${index + 1}: ${input.name || input.placeholder || input.type}`);
  });
  
  return inputs.length;
}

// Tailwind CSS样式检测
export function detectTailwindCSS() {
  console.log('检测Tailwind CSS...');
  
  const tailwindClasses = [
    'max-w-7xl', 'mx-auto', 'flex', 'flex-col', 'space-y-8',
    'border-input', 'ring-offset-background', 'focus-visible:ring-ring'
  ];
  
  let foundClasses = 0;
  tailwindClasses.forEach(className => {
    if (document.querySelector(`.${className}`)) {
      foundClasses++;
    }
  });
  
  if (foundClasses > 3) {
    console.log('✓ 检测到Tailwind CSS样式');
    return true;
  }
  
  return false;
}

// 响应式设计检测
export function detectResponsiveDesign() {
  console.log('检测响应式设计...');
  
  const responsiveClasses = ['md:flex-row', 'sm:px-0', 'lg:px-0'];
  let foundResponsive = false;
  
  responsiveClasses.forEach(className => {
    if (document.querySelector(`[class*="${className}"]`)) {
      foundResponsive = true;
    }
  });
  
  if (foundResponsive) {
    console.log('✓ 检测到响应式设计');
    return true;
  }
  
  return false;
}

// 等待表单完全加载
export async function waitForFormLoad() {
  console.log('等待表单完全加载...');

  let attempts = 0;
  const maxAttempts = 10;

  while (attempts < maxAttempts) {
    const inputs = document.querySelectorAll('input, textarea, select');

    if (inputs.length > 1) {
      console.log(`✓ 表单加载完成，检测到 ${inputs.length} 个字段`);
      return true;
    }

    console.log(`等待中... (${attempts + 1}/${maxAttempts})`);
    await new Promise(resolve => setTimeout(resolve, 1000));
    attempts++;
  }

  console.log('⚠️ 表单可能只有一个字段或加载未完成');
  return false;
}

// 专门检测URL字段
export function findUrlField() {
  console.log('检测URL字段...');

  // 多种方式查找URL字段
  const selectors = [
    'input[name="url"]',
    'input[placeholder="listedai.co"]',
    'label[for="url"] + div input',
    '.rounded-r-md input[type="text"]',
    'span:contains("https://") + input'
  ];

  for (const selector of selectors) {
    try {
      const element = document.querySelector(selector);
      if (element) {
        console.log(`✓ 找到URL字段: ${selector}`);
        console.log(`字段属性: name="${element.name}", placeholder="${element.placeholder}"`);
        return element;
      }
    } catch (error) {
      console.log(`选择器无效: ${selector}`);
    }
  }

  // 通过父元素查找
  const httpsSpan = Array.from(document.querySelectorAll('span')).find(span =>
    span.textContent.trim() === 'https://'
  );

  if (httpsSpan) {
    const urlInput = httpsSpan.nextElementSibling;
    if (urlInput && urlInput.tagName === 'INPUT') {
      console.log('✓ 通过https://前缀找到URL字段');
      return urlInput;
    }
  }

  console.log('⚠️ 未找到URL字段');
  return null;
}
