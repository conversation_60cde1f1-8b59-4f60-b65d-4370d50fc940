// Expify.ai 网站规则配置
// 网站: https://www.expify.ai/dashboard/manage-products
// 最后更新: 2025-07-09

export const SITE_RULE = {
  // 基本信息
  domain: 'expify.ai',
  siteName: 'Expify',
  priority: 1,
  lastUpdated: '2025-07-09',
  
  // 字段映射规则
  fieldMappings: {
    // 产品名称 -> Product Name
    siteName: {
      selectors: [
        'input[id="name"]',
        'input[placeholder="Enter product name"]',
        '.chakra-input[id="name"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '产品名称'
    },
    
    // 产品URL -> Product URL
    siteUrl: {
      selectors: [
        'input[id="url"]',
        'input[placeholder="https://www.example.com"]',
        '.chakra-input[id="url"]'
      ],
      method: 'value',
      validation: 'required|url',
      notes: '产品URL地址'
    }
  },
  
  // 提交流程配置
  submitConfig: {
    submitButton: 'button[type="button"], .chakra-button, button:contains("Submit")',
    submitMethod: 'click',
    waitAfterFill: 1000, // 填写后等待1秒
    waitAfterSubmit: 3000, // 提交后等待3秒
    successIndicators: [
      '.success-message',
      '.alert-success',
      '.chakra-alert--success',
      '[class*="success"]'
    ],
    errorIndicators: [
      '.error-message',
      '.alert-error',
      '.chakra-alert--error',
      '[class*="error"]'
    ]
  },
  
  // 特殊处理
  specialHandling: {
    requiresLogin: true, // 需要登录到dashboard
    hasCaptcha: false,
    hasFileUpload: false,
    
    // 表单验证规则
    formValidation: {
      requiredFields: ['siteName', 'siteUrl'],
      emailValidation: false,
      urlValidation: true
    },
    
    // 特殊注意事项
    notes: [
      '需要登录到dashboard才能访问',
      '使用Chakra UI组件库构建',
      '极简表单设计，只有2个字段',
      '必填字段：产品名称、产品URL',
      '深色主题设计',
      '响应式布局'
    ]
  }
};

// 自定义处理函数
export function handleExpifySubmission(data, rule) {
  console.log('Processing Expify.ai submission...');
  
  // 特殊处理逻辑
  const processedData = { ...data };
  
  // 确保URL格式正确
  if (processedData.siteUrl && !processedData.siteUrl.startsWith('http')) {
    processedData.siteUrl = 'https://' + processedData.siteUrl;
  }
  
  return processedData;
}

// 自定义元素填写函数，专门处理Chakra UI组件
export async function customFillElement(element, value, config) {
  console.log('🔧 Expify自定义填写函数被调用:', element, value);
  
  // 处理Chakra UI输入组件
  if (element.classList.contains('chakra-input')) {
    try {
      // 聚焦输入框
      element.focus();
      
      // 清空现有值
      element.value = '';
      
      // 设置新值
      element.value = value;
      
      // 触发React事件
      const nativeInputValueSetter = Object.getOwnPropertyDescriptor(
        window.HTMLInputElement.prototype, 
        'value'
      ).set;
      nativeInputValueSetter.call(element, value);
      
      // 触发事件
      element.dispatchEvent(new Event('input', { bubbles: true }));
      element.dispatchEvent(new Event('change', { bubbles: true }));
      element.dispatchEvent(new Event('blur', { bubbles: true }));
      
      console.log('✅ 使用Chakra UI输入框填写:', value);
      return true;
    } catch (error) {
      console.warn('Chakra UI输入框填写失败:', error);
    }
  }
  
  // 处理Chakra UI按钮
  if (element.classList.contains('chakra-button')) {
    try {
      element.click();
      console.log('✅ 点击Chakra UI按钮');
      return true;
    } catch (error) {
      console.warn('Chakra UI按钮点击失败:', error);
    }
  }
  
  // 默认处理
  return false;
}
