// AINewsHub.org 网站规则配置
// 网站: https://www.ainewshub.org/submit-ai-tool
// 最后更新: 2025-07-06

export const SITE_RULE = {
  // 基本信息
  domain: 'ainewshub.org',
  siteName: 'AI News Hub',
  priority: 1,
  lastUpdated: '2025-07-06',
  
  // 字段映射规则
  fieldMappings: {
    // AI工具名称 -> AI Tool Name (使用siteName)
    siteName: {
      selectors: [
        '#input_comp-lr80oegl',
        'input[name="ai-tool name"]',
        'input[id*="comp-lr80oegl"]'
      ],
      method: 'value',
      validation: 'required',
      notes: 'AI工具名称，使用website-info.js中的siteName字段'
    },

    // 网站链接 -> Website Link (使用siteUrl)
    siteUrl: {
      selectors: [
        '#input_comp-lr80oegu',
        'input[name="website-link"]',
        'input[id*="comp-lr80oegu"]'
      ],
      method: 'value',
      validation: 'required|url',
      notes: '工具网站链接，使用website-info.js中的siteUrl字段'
    },

    // 分类 -> Category (使用category)
    category: {
      selectors: [
        '#input_comp-m7iyrhh2',
        'input[name="catagory"]',
        'input[id*="comp-m7iyrhh2"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '工具分类，使用website-info.js中的category字段'
    },

    // 付费或免费 -> Paid Or Freemium? (固定值Freemium)
    pricing: {
      selectors: [
        '#input_comp-lr80oeh4',
        'input[name="paid-or freemeium?"]',
        'input[id*="comp-lr80oeh4"]'
      ],
      method: 'value',
      validation: 'required',
      defaultValue: 'Freemium',
      notes: '定价模式，固定填写Freemium'
    },

    // 邮箱 -> Email (使用contactEmail)
    contactEmail: {
      selectors: [
        '#input_comp-lr80td2t',
        'input[name="email"]',
        'input[type="email"][id*="comp-lr80td2t"]'
      ],
      method: 'value',
      validation: 'required|email',
      notes: '联系邮箱地址，使用website-info.js中的contactEmail字段'
    },

    // 描述 -> Description (使用siteDescription)
    siteDescription: {
      selectors: [
        '#textarea_comp-lr80oeh7',
        'textarea[id*="comp-lr80oeh7"]',
        'textarea.wixui-text-box__input'
      ],
      method: 'value',
      validation: 'required',
      notes: '工具描述，使用website-info.js中的siteDescription字段'
    },

    // 如何了解到我们 -> How did you hear about A.I News Hub? (固定值Google search)
    referralSource: {
      selectors: [
        '#input_comp-m7kiuhkp',
        'input[name="how-did you hear about a.i news hub?"]',
        'input[id*="comp-m7kiuhkp"]'
      ],
      method: 'value',
      validation: 'optional',
      defaultValue: 'Google search',
      notes: '了解渠道，固定填写Google search'
    }
  },
  
  // 自定义填写函数
  customFillElement: async function(element, value, config) {
    console.log(`AINewsHub自定义填写: ${element.id}, 方法: ${config.method}`);

    switch (config.method) {
      case 'value':
        // Wix文本输入框处理
        element.focus();
        await new Promise(resolve => setTimeout(resolve, 200));

        // 对于有默认值的字段，使用默认值
        const finalValue = config.defaultValue || value;

        element.value = finalValue;
        element.dispatchEvent(new Event('input', { bubbles: true }));
        element.dispatchEvent(new Event('change', { bubbles: true }));
        element.dispatchEvent(new Event('blur', { bubbles: true }));

        // Wix可能需要额外的事件
        if (window.wixDevelopersAnalytics) {
          element.dispatchEvent(new Event('wixInput', { bubbles: true }));
        }

        console.log(`✓ 填写字段: ${element.id} = "${finalValue}"`);
        break;

      default:
        element.value = value;
        element.dispatchEvent(new Event('input', { bubbles: true }));
        element.dispatchEvent(new Event('change', { bubbles: true }));
    }
  },
  
  // 提交流程配置
  submitConfig: {
    submitButton: 'button[aria-label="Submit"], button[data-testid="buttonElement"], .wixui-button',
    submitMethod: 'click',
    waitAfterFill: 3000, // Wix表单需要更长等待时间
    waitAfterSubmit: 5000,
    successIndicators: [
      '#comp-lr80oeho',
      'p:contains("Thanks for submitting")',
      '.wixui-rich-text__text:contains("Thanks")'
    ],
    errorIndicators: [
      '[data-testid*="error"]',
      '.error-message',
      '[aria-label*="error"]'
    ]
  },

  // 特殊处理
  specialHandling: {
    requiresLogin: false,
    hasCaptcha: false,
    hasFileUpload: false,

    // 表单验证规则
    formValidation: {
      requiredFields: ['siteName', 'siteUrl', 'category', 'pricing', 'contactEmail', 'siteDescription'],
      emailValidation: true,
      urlValidation: true
    },

    // 特殊注意事项
    notes: [
      '这是Wix网站，使用文本输入框而非下拉选择',
      '有7个字段，全部必填',
      '使用website-info.js中的siteName、siteUrl、category、contactEmail、siteDescription字段',
      '定价固定填写Freemium',
      '了解渠道固定填写Google search',
      '提交后显示"Thanks for submitting!"消息'
    ]
  }
};

// 自定义处理函数
export function handleAINewsHubSubmission(data, rule) {
  console.log('Processing AI News Hub submission...');

  const processedData = { ...data };

  // 确保URL格式正确
  if (processedData.siteUrl && !processedData.siteUrl.startsWith('http')) {
    processedData.siteUrl = 'https://' + processedData.siteUrl;
  }

  // 设置固定定价
  processedData.pricing = 'Freemium';

  // 设置固定了解渠道
  processedData.referralSource = 'Google search';

  return processedData;
}
