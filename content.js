// AI Site Submitter v3.0.0 - Content Script
// 处理页面表单检测和填写
// 最后更新: 2025-07-25

// 监听来自background script的消息
chrome.runtime.onMessage.addListener((request, _sender, sendResponse) => {
    if (request.action === 'checkRules') {
        checkSiteRules()
            .then(result => sendResponse({ success: true, result }))
            .catch(error => sendResponse({ success: false, error: error.message }));
        return true; // 异步响应
    }

    if (request.action === 'fillForm') {
        fillFormWithRules()
            .then(result => sendResponse({ success: true, result }))
            .catch(error => sendResponse({ success: false, error: error.message }));
        return true; // 异步响应
    }



    return true;
});

// 检查当前网站是否有规则配置
async function checkSiteRules() {
    const domain = window.location.hostname;
    console.log(`Checking rules for domain: ${domain}`);

    // 尝试多种域名格式
    const domainVariants = [
        domain,                                    // 原始域名 (如: www.insidr.ai)
        domain.replace(/^www\./, ''),             // 去掉www (如: insidr.ai)
        domain.startsWith('www.') ? domain : `www.${domain}` // 添加www (如果没有的话)
    ];

    for (const testDomain of domainVariants) {
        try {
            console.log(`Trying to load rules for: ${testDomain}`);
            const ruleModule = await import(chrome.runtime.getURL(`rules/sites/${testDomain}.js`));
            const rule = ruleModule.SITE_RULE;

            if (rule && validateRule(rule)) {
                console.log(`✅ Found rules for ${testDomain}`);
                return {
                    hasRules: true,
                    domain: domain,
                    ruleDomain: testDomain,
                    siteName: rule.siteName,
                    fieldsCount: Object.keys(rule.fieldMappings).length,
                    lastUpdated: rule.lastUpdated
                };
            }
        } catch (error) {
            console.log(`No rules found for ${testDomain}:`, error.message);
        }
    }

    return {
        hasRules: false,
        domain: domain,
        message: '未找到该网站的规则配置'
    };
}

// 验证规则有效性
function validateRule(rule) {
    if (!rule || !rule.domain || !rule.fieldMappings) {
        return false;
    }
    
    // 验证字段映射
    for (const [field, config] of Object.entries(rule.fieldMappings)) {
        if (!config.selectors || !Array.isArray(config.selectors)) {
            console.warn(`Invalid selectors for field ${field}`);
            return false;
        }
    }
    
    return true;
}

// 使用规则填写表单
async function fillFormWithRules() {
    const domain = window.location.hostname;

    try {
        // 尝试加载规则文件
        const domainVariants = [
            domain,                                    // 原始域名
            domain.replace(/^www\./, ''),             // 去掉www
            domain.startsWith('www.') ? domain : `www.${domain}` // 添加www
        ];

        let rule = null;
        let ruleDomain = null;

        for (const testDomain of domainVariants) {
            try {
                const ruleModule = await import(chrome.runtime.getURL(`rules/sites/${testDomain}.js`));
                rule = ruleModule.SITE_RULE;

                // 如果有自定义处理函数，也加载进来
                if (ruleModule.handleSubmission) {
                    rule.customHandler = ruleModule.handleSubmission;
                }

                // 如果有自定义元素填写函数，也加载进来
                if (ruleModule.customFillElement) {
                    rule.customFillElement = ruleModule.customFillElement;
                }

                ruleDomain = testDomain;
                console.log(`✅ Loaded rules from: ${testDomain}`);
                break;
            } catch (error) {
                console.log(`Failed to load rules from: ${testDomain}`);
            }
        }

        if (!rule) {
            throw new Error('规则加载失败');
        }
        
        // 加载网站信息
        const infoModule = await import(chrome.runtime.getURL('website-info.js'));
        const websiteInfo = infoModule.WEBSITE_INFO;
        
        console.log(`开始填写表单，使用规则: ${rule.siteName}`);
        
        const results = {};
        let filledCount = 0;
        
        // 遍历规则中的每个字段
        for (const [fieldName, fieldConfig] of Object.entries(rule.fieldMappings)) {
            let value = websiteInfo[fieldName];

            // 如果网站信息中没有该字段，但规则中有默认值，使用默认值
            if (!value && fieldConfig.defaultValue) {
                value = fieldConfig.defaultValue;
                console.log(`使用默认值填写 ${fieldName}: ${value}`);
            }

            if (!value) {
                console.warn(`网站信息中缺少字段且无默认值: ${fieldName}`);
                continue;
            }
            
            try {
                const success = await fillField(fieldConfig, value, rule);
                results[fieldName] = success;
                
                if (success) {
                    filledCount++;
                    console.log(`✓ 已填写 ${fieldName}: ${value}`);
                } else {
                    console.warn(`✗ 填写失败 ${fieldName}`);
                }
            } catch (error) {
                console.error(`填写字段 ${fieldName} 时出错:`, error.message || error.toString());
                console.error('错误详情:', error);
                console.error('字段值:', value);
                console.error('字段配置:', fieldConfig);
                results[fieldName] = false;
            }
            
            // 短暂延迟，避免填写过快
            await new Promise(resolve => setTimeout(resolve, 300));
        }
        
        return {
            success: filledCount > 0,
            filledCount,
            totalFields: Object.keys(rule.fieldMappings).length,
            results,
            message: `成功填写 ${filledCount}/${Object.keys(rule.fieldMappings).length} 个字段`
        };
        
    } catch (error) {
        console.error('表单填写失败:', error);
        throw new Error(`表单填写失败: ${error.message}`);
    }
}

// 填写单个字段
async function fillField(fieldConfig, value, rule) {
    // 尝试每个选择器
    for (const selector of fieldConfig.selectors) {
        const elements = document.querySelectorAll(selector);

        for (const element of elements) {
            // 对于复选框，不检查是否已填写，因为可能需要选中特定的选项
            const shouldFill = fieldConfig.method === 'checkbox' ?
                isElementVisible(element) :
                (isElementVisible(element) && !isElementFilled(element));

            if (shouldFill) {
                try {
                    console.log(`🎯 尝试填写元素:`, {
                        selector: selector,
                        element: element,
                        tagName: element.tagName,
                        type: element.type,
                        name: element.name,
                        id: element.id,
                        value: value,
                        method: fieldConfig.method
                    });

                    await fillElement(element, value, fieldConfig, rule);
                    console.log(`✅ 成功填写字段，选择器: ${selector}, 最终值: ${element.value}`);
                    return true;
                } catch (error) {
                    console.warn(`❌ 选择器 ${selector} 填写失败:`, error);
                }
            } else {
                console.log(`⏭️ 跳过元素 (不可见或已填写):`, {
                    selector: selector,
                    element: element,
                    visible: isElementVisible(element),
                    filled: isElementFilled(element),
                    method: fieldConfig.method
                });
            }
        }
    }

    return false;
}

// 填写单个元素
async function fillElement(element, value, config, rule) {
    // 如果规则中有自定义填写函数，优先使用
    if (rule.customFillElement && typeof rule.customFillElement === 'function') {
        return await rule.customFillElement(element, value, config);
    }

    // 默认填写逻辑
    console.log(`🔧 使用填写方法: ${config.method}, 元素类型: ${element.tagName}, 值: ${value}`);

    switch (config.method) {
        case 'value':
            try {
                // 验证值的长度，避免过长的值导致DOM异常
                const stringValue = String(value);
                const oldValue = element.value;

                if (stringValue.length > 10000) {
                    console.warn(`字段值过长 (${stringValue.length} 字符)，截断到10000字符`);
                    element.value = stringValue.substring(0, 10000);
                } else {
                    element.value = stringValue;
                }

                console.log(`📝 设置元素值: "${oldValue}" -> "${element.value}"`);

                // 触发事件
                element.dispatchEvent(new Event('input', { bubbles: true }));
                element.dispatchEvent(new Event('change', { bubbles: true }));

                console.log(`🎉 事件已触发，当前元素值: "${element.value}"`);
            } catch (error) {
                console.error('设置元素值时出错:', error);
                throw error;
            }
            break;

        case 'skip':
            console.log(`⏭️ 跳过填写 (method: skip)`);
            break;
            
        case 'select':
            if (element.tagName.toLowerCase() === 'select') {
                let selectedValue = null;

                // 智能选择策略：多层次匹配
                const options = Array.from(element.options);

                // 1. 精确值匹配
                let option = options.find(opt => opt.value === value);

                // 2. 精确文本匹配
                if (!option) {
                    option = options.find(opt => opt.text.trim() === value);
                }

                // 3. 包含匹配（不区分大小写）
                if (!option && value) {
                    option = options.find(opt =>
                        opt.value.toLowerCase().includes(value.toLowerCase()) ||
                        opt.text.toLowerCase().includes(value.toLowerCase())
                    );
                }

                // 4. 使用配置的默认值
                if (!option && config.defaultValue) {
                    // 精确匹配默认值
                    option = options.find(opt =>
                        opt.value === config.defaultValue ||
                        opt.text.trim() === config.defaultValue
                    );

                    // 包含匹配默认值
                    if (!option) {
                        option = options.find(opt =>
                            opt.value.toLowerCase().includes(config.defaultValue.toLowerCase()) ||
                            opt.text.toLowerCase().includes(config.defaultValue.toLowerCase())
                        );
                    }
                }

                // 5. 最后选择第一个非空选项
                if (!option) {
                    option = options.find(opt => opt.value && opt.value !== '');
                }

                if (option) {
                    selectedValue = option.value;
                    element.value = selectedValue;
                    element.dispatchEvent(new Event('change', { bubbles: true }));
                    element.dispatchEvent(new Event('input', { bubbles: true }));

                    // 如果是Select2，触发Select2的change事件
                    if (element.classList.contains('select2-hidden-accessible')) {
                        $(element).trigger('change');
                    }

                    console.log(`✅ 选择框填写成功: ${selectedValue} (选项文本: ${option.text})`);
                } else {
                    console.warn(`⚠️ 选择框无法找到合适的选项: ${value}, 默认值: ${config.defaultValue}`);
                }
            }
            break;
            
        case 'checkbox':
            if (element.type === 'checkbox') {
                const targetValue = config.targetValue || config.defaultValue || value;
                if (element.value === targetValue) {
                    element.checked = true;
                    element.dispatchEvent(new Event('change', { bubbles: true }));
                    console.log(`✓ 选中复选框: ${element.value}`);
                }
            }
            break;

        case 'radio':
            if (element.type === 'radio') {
                const targetValue = config.targetValue || config.defaultValue || value;
                if (element.value === targetValue) {
                    element.checked = true;
                    element.dispatchEvent(new Event('change', { bubbles: true }));
                    console.log(`✓ 选中单选按钮: ${element.value}`);
                }
            }
            break;

        case 'mui-select':
            // Material-UI下拉选择，使用网站特定的处理逻辑
            console.log(`MUI下拉选择将由网站特定逻辑处理: ${element.id}`);
            break;

        case 'click':
            element.click();
            break;

        default:
            element.value = value;
            element.dispatchEvent(new Event('input', { bubbles: true }));
    }
    
    // 等待一小段时间让页面响应
    await new Promise(resolve => setTimeout(resolve, 100));
}

// 检查元素是否可见
function isElementVisible(element) {
    const rect = element.getBoundingClientRect();
    const style = window.getComputedStyle(element);
    
    return rect.width > 0 && 
           rect.height > 0 && 
           style.display !== 'none' && 
           style.visibility !== 'hidden' &&
           style.opacity !== '0';
}

// 检查元素是否已填写
function isElementFilled(element) {
    if (element.type === 'checkbox' || element.type === 'radio') {
        return element.checked;
    }

    // 对于有占位文本的字段，不认为是已填写
    const value = element.value ? element.value.trim() : '';
    const isPlaceholder = value.includes('please provide') ||
                         value.includes('placeholder') ||
                         value.includes('enter your') ||
                         value === '';

    return !isPlaceholder && value.length > 0;
}

console.log('AI Site Submitter content script loaded');
