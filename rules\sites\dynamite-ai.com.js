// Dynamite-ai.com 网站规则配置
// 网站: https://www.dynamite-ai.com/submit
// 最后更新: 2025-07-09

export const SITE_RULE = {
  // 基本信息
  domain: 'dynamite-ai.com',
  siteName: 'Dynamite-ai',
  priority: 1,
  lastUpdated: '2025-07-09',
  
  // 字段映射规则
  fieldMappings: {
    // 工具名称 -> Tool Name
    siteName: {
      selectors: [
        'input[name="name"]',
        'input[id="name"]',
        'input[required][name="name"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '工具名称'
    },
    
    // 邮箱 -> Your Email
    contactEmail: {
      selectors: [
        'input[name="email"]',
        'input[id="email"]',
        'input[type="email"]'
      ],
      method: 'value',
      validation: 'required|email',
      notes: '联系邮箱'
    },
    
    // 网站 -> Website
    siteUrl: {
      selectors: [
        'input[name="website"]',
        'input[id="website"]',
        'input[type="url"]'
      ],
      method: 'value',
      validation: 'required|url',
      notes: '网站URL地址'
    },
    
    // 描述 -> Description
    siteDescription: {
      selectors: [
        'textarea[name="description"]',
        'textarea[id="description"]',
        'textarea[rows="2"]'
      ],
      method: 'value',
      validation: 'optional',
      notes: '工具描述'
    },
    
    // 分类 -> Category (选择Productivity)
    category: {
      selectors: [
        'select[name="category"]',
        'button[role="combobox"]:first-of-type',
        'button[aria-controls*="radix"]:first-of-type'
      ],
      method: 'select',
      validation: 'required',
      targetValue: 'productivity',
      defaultValue: 'Productivity',
      notes: '产品分类，选择Productivity'
    },
    
    // 定价 -> Pricing (选择Free)
    pricing: {
      selectors: [
        'select[name="pricing"]',
        'button[role="combobox"]:last-of-type',
        'button[aria-controls*="radix"]:last-of-type'
      ],
      method: 'select',
      validation: 'required',
      targetValue: 'free',
      defaultValue: 'Free',
      notes: '定价模式，选择Free'
    }
  },
  
  // 提交流程配置
  submitConfig: {
    submitButton: 'button[type="submit"], button:contains("Submit")',
    submitMethod: 'click',
    waitAfterFill: 2000, // 填写后等待2秒
    waitAfterSubmit: 3000, // 提交后等待3秒
    successIndicators: [
      '.success-message',
      '.alert-success',
      '.notification-success',
      '[class*="success"]'
    ],
    errorIndicators: [
      '.error-message',
      '.alert-error',
      '.alert-danger',
      '[class*="error"]'
    ]
  },
  
  // 特殊处理
  specialHandling: {
    requiresLogin: false,
    hasCaptcha: false,
    hasFileUpload: true, // 有图片上传
    
    // 表单验证规则
    formValidation: {
      requiredFields: ['siteName', 'contactEmail', 'siteUrl', 'category', 'pricing'],
      emailValidation: true,
      urlValidation: true
    },
    
    // 特殊注意事项
    notes: [
      '现代化表单设计，使用Radix UI和Tailwind CSS',
      '表单包含7个字段：工具名称、邮箱、网站、描述、图片、分类、定价',
      '必填字段：工具名称、邮箱、网站、图片、分类、定价',
      '有文件上传功能（图片必填）',
      '使用下拉选择组件',
      '紫色主题设计'
    ]
  }
};

// 自定义处理函数
export function handleDynamiteAiSubmission(data, rule) {
  console.log('Processing Dynamite-ai.com submission...');
  
  // 特殊处理逻辑
  const processedData = { ...data };
  
  // 确保URL格式正确
  if (processedData.siteUrl && !processedData.siteUrl.startsWith('http')) {
    processedData.siteUrl = 'https://' + processedData.siteUrl;
  }
  
  // 设置默认分类为Productivity
  processedData.category = 'productivity';
  
  // 设置默认定价为Free
  processedData.pricing = 'free';
  
  return processedData;
}

// 自定义元素填写函数，专门处理Radix UI组件
export async function customFillElement(element, value, config) {
  console.log('🔧 Dynamite-ai自定义填写函数被调用:', element, value);
  
  // 处理Radix UI下拉选择组件
  if (element.getAttribute('role') === 'combobox') {
    try {
      element.click();
      
      // 等待下拉菜单出现
      await new Promise(resolve => setTimeout(resolve, 500));
      
      // 查找并点击对应选项
      const option = document.querySelector(`option[value="${config.targetValue}"]`);
      if (option) {
        // 设置隐藏select的值
        const hiddenSelect = element.parentElement.querySelector('select[aria-hidden="true"]');
        if (hiddenSelect) {
          hiddenSelect.value = config.targetValue;
          hiddenSelect.dispatchEvent(new Event('change', { bubbles: true }));
        }
        
        // 更新按钮显示文本
        const span = element.querySelector('span');
        if (span) {
          span.textContent = config.defaultValue;
        }
        
        console.log('✅ 选择Radix UI下拉选项:', config.defaultValue);
        return true;
      }
      
      // 备用方法：查找包含文本的选项
      const textOption = Array.from(document.querySelectorAll('[role="option"]')).find(
        opt => opt.textContent.includes(config.defaultValue)
      );
      if (textOption) {
        textOption.click();
        console.log('✅ 通过文本匹配点击Radix UI选项:', config.defaultValue);
        return true;
      }
    } catch (error) {
      console.warn('Radix UI下拉选择失败:', error);
    }
  }
  
  // 处理隐藏的select元素
  if (element.tagName === 'SELECT' && element.getAttribute('aria-hidden') === 'true') {
    try {
      element.value = config.targetValue;
      element.dispatchEvent(new Event('change', { bubbles: true }));
      
      // 更新对应的combobox显示
      const combobox = element.parentElement.querySelector('button[role="combobox"]');
      if (combobox) {
        const span = combobox.querySelector('span');
        if (span) {
          span.textContent = config.defaultValue;
        }
      }
      
      console.log('✅ 设置隐藏select值:', config.defaultValue);
      return true;
    } catch (error) {
      console.warn('隐藏select设置失败:', error);
    }
  }
  
  // 处理文件上传提示
  if (element.type === 'file') {
    console.log('⚠️ 检测到文件上传字段，需要用户手动上传图片');
    return false; // 让用户手动处理文件上传
  }
  
  // 默认处理
  return false;
}
