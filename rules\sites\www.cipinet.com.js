// Cipinet.com 网站规则配置
// 网站: https://www.cipinet.com/suggest.php?action=addlink&TID=sf
// 最后更新: 2025-07-24

export const SITE_RULE = {
  domain: 'www.cipinet.com',
  siteName: 'Cipinet',
  priority: 1,
  lastUpdated: '2025-07-24',

  fieldMappings: {
    category: {
      selectors: [
        'select[name="catlevelid[0]"]',
        '#levelbox0 select',
        'label:contains("Category") + div select'
      ],
      method: 'select',
      validation: 'required',
      defaultValue: '26',
      notes: '分类选择，默认Business'
    },

    siteUrl: {
      selectors: [
        'input[name="url"]',
        '.form-control[name="url"]',
        'label:contains("URL") + input'
      ],
      method: 'value',
      validation: 'required|url',
      notes: '网站URL，预填https://'
    },

    siteName: {
      selectors: [
        'input[name="title"]',
        '.form-control[name="title"]',
        'label:contains("Site Title") + input'
      ],
      method: 'value',
      validation: 'required',
      notes: '网站标题，最多60字符'
    },

    siteDescription: {
      selectors: [
        'textarea[name="description"]',
        '#description',
        'label:contains("Description") + textarea'
      ],
      method: 'value',
      validation: 'required',
      notes: '网站描述，最多230字符'
    },

    contactEmail: {
      selectors: [
        'input[name="email"]',
        'input[type="email"]',
        'label:contains("E-Mail") + input'
      ],
      method: 'value',
      validation: 'optional|email',
      notes: '联系邮箱'
    },

    agreeTerms: {
      selectors: [
        'input[name="cipinettos"]',
        'input[type="checkbox"]',
        'input[value="1"]'
      ],
      method: 'checkbox',
      validation: 'required',
      defaultValue: true,
      notes: '同意服务条款'
    }
  },

  submitConfig: {
    submitButton: 'button[name="processform"], .btn-success',
    submitMethod: 'click',
    successIndicators: ['.success-message'],
    errorIndicators: ['.has-error']
  },

  specialHandling: {
    requiresLogin: false,
    hasCaptcha: true,
    hasFileUpload: false,
    customScript: 'handleCipinetSubmission',
    formValidation: {
      requiredFields: ['category', 'siteUrl', 'siteName', 'siteDescription', 'agreeTerms'],
      emailValidation: true,
      urlValidation: true
    },
    notes: [
      '网站目录提交平台',
      '多级分类选择',
      'Google reCAPTCHA验证',
      '字符计数器',
      '需要同意服务条款',
      'Bootstrap样式表单'
    ]
  }
};

export function handleCipinetSubmission(data, rule) {
  const processedData = { ...data };

  if (processedData.siteUrl && !processedData.siteUrl.startsWith('http')) {
    processedData.siteUrl = 'https://' + processedData.siteUrl;
  }

  // 自动选择默认分类Business
  const categorySelect = document.querySelector('select[name="catlevelid[0]"]');
  if (categorySelect) {
    categorySelect.value = '26';
    categorySelect.dispatchEvent(new Event('change', { bubbles: true }));
  }

  // 自动勾选同意条款
  const agreeCheckbox = document.querySelector('input[name="cipinettos"]');
  if (agreeCheckbox) {
    agreeCheckbox.checked = true;
  }

  return processedData;
}

export async function customFillElement(element, value, config) {
  // 处理分类选择框
  if (element.tagName === 'SELECT' && element.name === 'catlevelid[0]') {
    const options = element.querySelectorAll('option');
    const option = Array.from(options).find(opt => opt.value === '26');
    if (option) {
      element.value = option.value;
      element.dispatchEvent(new Event('change', { bubbles: true }));
      element.click(); // 触发wsn_getnextlevel函数
      return true;
    }
  }

  // 处理复选框
  if (element.type === 'checkbox') {
    element.checked = Boolean(value);
    element.dispatchEvent(new Event('change', { bubbles: true }));
    return true;
  }

  // 处理文本域字符计数
  if (element.name === 'description') {
    element.value = value;
    element.dispatchEvent(new Event('input', { bubbles: true }));
    element.dispatchEvent(new Event('change', { bubbles: true }));

    // 更新字符计数器
    const counter = document.getElementById('counter');
    if (counter) {
      const remaining = 230 - value.length;
      counter.textContent = remaining;
    }
    return true;
  }

  if (element.tagName === 'INPUT' || element.tagName === 'TEXTAREA') {
    element.value = value;
    element.dispatchEvent(new Event('input', { bubbles: true }));
    element.dispatchEvent(new Event('change', { bubbles: true }));
    return true;
  }

  return false;
}