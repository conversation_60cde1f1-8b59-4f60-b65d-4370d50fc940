// AIXCollection.com 网站规则配置
// 网站: https://aixcollection.com/submit
// 最后更新: 2025-07-24

export const SITE_RULE = {
  domain: 'aixcollection.com',
  siteName: 'AIX Collection',
  priority: 1,
  lastUpdated: '2025-07-24',

  fieldMappings: {
    siteUrl: {
      selectors: [
        '#website-url',
        'input[type="url"]',
        'input[placeholder="https://your-tool.com"]'
      ],
      method: 'value',
      validation: 'required|url',
      notes: '工具网站链接'
    },

    siteName: {
      selectors: [
        '#tool-name',
        'input[placeholder="e.g., AI Writer Pro"]',
        'input[maxlength="100"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '工具名称'
    },

    category: {
      selectors: [
        '#category-select',
        'select:first-of-type',
        'select[id*="category"]'
      ],
      method: 'select',
      validation: 'required',
      defaultValue: 'ai-assistants',
      notes: '工具分类'
    },

    pricing: {
      selectors: [
        '#pricing-select',
        'select:nth-of-type(2)',
        'select[id*="pricing"]'
      ],
      method: 'select',
      validation: 'required',
      defaultValue: 'freemium',
      notes: '定价模式'
    },

    tagline: {
      selectors: [
        '#summary',
        'textarea[maxlength="150"]',
        'textarea[placeholder*="short, impactful description"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '简短描述'
    },

    siteDescription: {
      selectors: [
        '#about-tool',
        'textarea[maxlength="1000"]',
        'textarea[placeholder*="overview of your tool"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '详细描述'
    }
  },

  submitConfig: {
    submitButton: 'button[type="button"]:contains("Submit Tool for Review"), .btn-primary',
    submitMethod: 'click',
    successIndicators: ['.success-message'],
    errorIndicators: ['.error-message']
  },

  specialHandling: {
    requiresLogin: false,
    hasCaptcha: false,
    hasFileUpload: true,
    customScript: 'handleAIXCollectionSubmission',
    formValidation: {
      requiredFields: ['siteUrl', 'siteName', 'category', 'pricing', 'tagline', 'siteDescription'],
      emailValidation: false,
      urlValidation: true
    },
    notes: [
      '需要上传截图文件',
      '有特色展示选项',
      '需要同意条款复选框',
      '表单有字符限制',
      '使用现代UI设计'
    ]
  }
};

export function handleAIXCollectionSubmission(data, rule) {
  const processedData = { ...data };

  if (processedData.siteUrl && !processedData.siteUrl.startsWith('http')) {
    processedData.siteUrl = 'https://' + processedData.siteUrl;
  }

  if (processedData.pricing) {
    const pricingMap = {
      'Free': 'free',
      'Free Trial': 'free-trial',
      'Freemium': 'freemium',
      'Paid': 'paid'
    };
    processedData.pricing = pricingMap[processedData.pricing] || 'freemium';
  }

  return processedData;
}

export async function customFillElement(element, value, config) {
  if (element.tagName === 'SELECT') {
    const options = element.querySelectorAll('option');
    const option = Array.from(options).find(opt =>
      opt.value === value || opt.textContent.trim().toLowerCase() === value.toLowerCase()
    );
    if (option) {
      element.value = option.value;
      element.dispatchEvent(new Event('change', { bubbles: true }));
      return true;
    }
  }

  if (element.tagName === 'INPUT' || element.tagName === 'TEXTAREA') {
    element.value = value;
    element.dispatchEvent(new Event('input', { bubbles: true }));
    return true;
  }

  if (element.type === 'file') {
    console.log('⚠️ 文件上传字段需要用户手动处理');
    return false;
  }

  return false;
}