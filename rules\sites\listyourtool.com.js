// ListYourTool.com 网站规则配置
// 网站: https://www.listyourtool.com/submit-tool
// 最后更新: 2025-07-24

export const SITE_RULE = {
  // 基本信息
  domain: 'listyourtool.com',
  siteName: 'ListYourTool',
  priority: 1,
  lastUpdated: '2025-07-24',
  
  // 字段映射规则
  fieldMappings: {
    // 工具名称 -> Tool Name
    siteName: {
      selectors: [
        'input[name="name"]',
        '#name',
        'input[placeholder="Enter the name of your tool"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '工具名称'
    },
    
    // 网站URL -> Website URL
    siteUrl: {
      selectors: [
        'input[name="website"]',
        '#website',
        'input[placeholder="https://example.com"]'
      ],
      method: 'value',
      validation: 'required|url',
      notes: '网站URL地址'
    },
    
    // 简短描述 -> Short Description
    siteDescription: {
      selectors: [
        'input[name="shortDescription"]',
        '#shortDescription',
        'input[placeholder*="brief description"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '简短描述（最多150字符）'
    },
    
    // 详细描述 -> Detailed Description
    detailedIntro: {
      selectors: [
        'textarea[name="longDescription"]',
        '#longDescription',
        'textarea[placeholder*="detailed description"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '详细描述（最多2000字符）'
    },
    
    // 标签 -> Tags (需要选择1-3个)
    tags: {
      selectors: [
        'input[name="tags"]',
        '#tags',
        'div[class*="min-h-10"]'
      ],
      method: 'select',
      validation: 'required',
      targetValue: 'productivity',
      defaultValue: 'Productivity',
      notes: '标签（选择1-3个）'
    },
    
    // 定价模式 -> Pricing Model (选择free)
    pricing: {
      selectors: [
        'select[name="pricing"]',
        '#pricing',
        'select[required]'
      ],
      method: 'select',
      validation: 'required',
      targetValue: 'free',
      defaultValue: 'Free',
      notes: '定价模式，选择Free'
    },
    
    // 价格 -> Price
    price: {
      selectors: [
        'input[name="price"]',
        '#price',
        'input[type="number"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '价格（数字）'
    },
    
    // 提交者姓名 -> Your Name
    fullName: {
      selectors: [
        'input[name="submitterName"]',
        '#submitterName',
        'input[placeholder="Your full name"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '提交者姓名'
    },
    
    // 提交者邮箱 -> Email Address
    contactEmail: {
      selectors: [
        'input[name="submitterEmail"]',
        '#submitterEmail',
        'input[placeholder="<EMAIL>"]'
      ],
      method: 'value',
      validation: 'required|email',
      notes: '提交者邮箱'
    }
  },
  
  // 提交流程配置
  submitConfig: {
    submitButton: 'button[type="submit"], button:contains("Submit Tool for Review")',
    submitMethod: 'click',
    successIndicators: [
      '.success-message',
      '.alert-success',
      '.notification-success',
      '[class*="success"]'
    ],
    errorIndicators: [
      '.error-message',
      '.alert-error',
      '.text-error-main',
      '[class*="error"]'
    ]
  },
  
  // 特殊处理
  specialHandling: {
    requiresLogin: false,
    hasCaptcha: false,
    hasFileUpload: false,
    
    // 表单验证规则
    formValidation: {
      requiredFields: ['siteName', 'siteUrl', 'siteDescription', 'detailedIntro', 'tags', 'pricing', 'price', 'fullName', 'contactEmail'],
      emailValidation: true,
      urlValidation: true
    },
    
    // 特殊注意事项
    notes: [
      '详细的工具提交表单，包含工具信息和联系信息',
      '表单包含9个字段：工具名称、网站、简短描述、详细描述、标签、定价、价格、姓名、邮箱',
      '所有字段都是必填的',
      '简短描述限制150字符，详细描述限制2000字符',
      '标签需要选择1-3个',
      '有字符计数显示',
      '支持添加功能特性（可选）',
      '有广告插入'
    ]
  }
};

// 自定义处理函数
export function handleListYourToolSubmission(data, rule) {
  console.log('Processing ListYourTool.com submission...');
  
  // 特殊处理逻辑
  const processedData = { ...data };
  
  // 确保URL格式正确
  if (processedData.siteUrl && !processedData.siteUrl.startsWith('http')) {
    processedData.siteUrl = 'https://' + processedData.siteUrl;
  }
  
  // 设置默认值
  processedData.pricing = 'free';
  processedData.tags = 'productivity';
  
  // 设置默认价格（如果是免费的话设为0）
  if (processedData.pricing === 'free' && !processedData.price) {
    processedData.price = '0';
  }
  
  return processedData;
}

// 自定义元素填写函数
export async function customFillElement(element, value, config) {
  console.log('🔧 ListYourTool自定义填写函数被调用:', element, value);
  
  // 处理标签选择器
  if (element.name === 'tags' || element.id === 'tags') {
    try {
      // 查找标签选择容器
      const tagContainer = element.closest('div').querySelector('div[class*="min-h-10"]');
      if (tagContainer) {
        tagContainer.click();
        
        // 等待标签选项出现
        await new Promise(resolve => setTimeout(resolve, 500));
        
        // 查找包含目标值的标签选项
        const tagOption = Array.from(document.querySelectorAll('[role="option"], .tag-option')).find(
          opt => opt.textContent.includes(config.defaultValue) || opt.textContent.includes('Productivity')
        );
        
        if (tagOption) {
          tagOption.click();
          console.log('✅ 选择标签:', config.defaultValue);
          return true;
        }
      }
    } catch (error) {
      console.warn('标签选择失败:', error);
    }
  }
  
  // 处理定价模式下拉选择
  if (element.tagName === 'SELECT' && element.name === 'pricing') {
    try {
      element.value = config.targetValue;
      element.dispatchEvent(new Event('change', { bubbles: true }));
      console.log('✅ 选择定价模式:', config.defaultValue);
      return true;
    } catch (error) {
      console.warn('定价模式选择失败:', error);
    }
  }
  
  // 处理价格输入框
  if (element.type === 'number' && element.name === 'price') {
    try {
      element.value = value;
      element.dispatchEvent(new Event('input', { bubbles: true }));
      element.dispatchEvent(new Event('change', { bubbles: true }));
      console.log('✅ 设置价格:', value);
      return true;
    } catch (error) {
      console.warn('价格设置失败:', error);
    }
  }
  
  // 处理带有字符限制的输入框
  if (element.hasAttribute('maxlength')) {
    try {
      element.value = value;
      element.dispatchEvent(new Event('input', { bubbles: true }));
      element.dispatchEvent(new Event('change', { bubbles: true }));
      
      // 更新字符计数显示
      const maxLength = element.getAttribute('maxlength');
      const currentLength = value.length;
      const counterElement = element.parentElement.parentElement.querySelector('.text-text-secondary span');
      if (counterElement && counterElement.textContent.includes('/')) {
        counterElement.textContent = `${currentLength}/${maxLength} characters`;
      }
      
      console.log('✅ 填写带字符限制的字段:', value);
      return true;
    } catch (error) {
      console.warn('字符限制字段填写失败:', error);
    }
  }
  
  // 默认处理
  return false;
}
