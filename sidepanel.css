/* AI Site Submitter - Sidepanel Styles */
/* 最后更新: 2025-07-06 */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background-color: #f8f9fa;
    color: #333;
    line-height: 1.6;
}

.container {
    max-width: 100%;
    padding: 16px;
}

/* 头部样式 */
.header {
    text-align: center;
    margin-bottom: 24px;
    padding: 16px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 12px;
    color: white;
}

.header h1 {
    font-size: 20px;
    margin-bottom: 4px;
}

.version {
    font-size: 12px;
    opacity: 0.8;
}

/* 区块通用样式 */
section {
    margin-bottom: 20px;
}

section h2 {
    font-size: 16px;
    margin-bottom: 12px;
    color: #495057;
    display: flex;
    align-items: center;
    gap: 8px;
}

/* 当前网站信息 */
.current-site {
    background: white;
    border-radius: 8px;
    padding: 16px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.site-info {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.site-url {
    font-weight: 600;
    color: #007bff;
    word-break: break-all;
}

.site-domain {
    font-size: 14px;
    color: #6c757d;
    background: #f8f9fa;
    padding: 4px 8px;
    border-radius: 4px;
    display: inline-block;
}

/* 规则状态卡片 */
.rule-status {
    background: white;
    border-radius: 8px;
    padding: 16px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.status-card {
    display: flex;
    align-items: center;
    gap: 12px;
}

.status-indicator {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
}

.status-indicator.loading {
    background: #e9ecef;
}

.status-indicator.success {
    background: #d4edda;
    color: #155724;
}

.status-indicator.error {
    background: #f8d7da;
    color: #721c24;
}

.loading-spinner {
    width: 20px;
    height: 20px;
    border: 2px solid #e9ecef;
    border-top: 2px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.status-content {
    flex: 1;
}

.status-title {
    font-weight: 600;
    margin-bottom: 4px;
}

.status-message {
    font-size: 14px;
    color: #6c757d;
}

/* 操作按钮 */
.actions {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.action-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    padding: 12px 16px;
    border: none;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
}

.action-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.action-btn.primary {
    background: #007bff;
    color: white;
}

.action-btn.primary:hover:not(:disabled) {
    background: #0056b3;
    transform: translateY(-1px);
}

.action-btn.secondary {
    background: #6c757d;
    color: white;
}

.action-btn.secondary:hover {
    background: #545b62;
    transform: translateY(-1px);
}



.btn-icon {
    font-size: 16px;
}

/* 结果显示 */
.results {
    background: white;
    border-radius: 8px;
    padding: 16px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.result-card {
    border-left: 4px solid #28a745;
    padding-left: 12px;
}

.result-summary {
    font-weight: 600;
    margin-bottom: 8px;
    color: #28a745;
}

.result-details {
    font-size: 14px;
    color: #6c757d;
}

/* 网站信息预览 */
.website-info {
    background: white;
    border-radius: 8px;
    padding: 16px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.info-card {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.info-item {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    gap: 8px;
    padding: 8px 0;
    border-bottom: 1px solid #e9ecef;
}

.info-item:last-child {
    border-bottom: none;
}

.info-label {
    font-weight: 600;
    color: #495057;
    min-width: 80px;
}

.info-value {
    flex: 1;
    text-align: right;
    color: #6c757d;
    word-break: break-all;
}

/* 帮助信息 */
.help {
    background: white;
    border-radius: 8px;
    padding: 16px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.help details summary {
    cursor: pointer;
    font-weight: 600;
    color: #495057;
    list-style: none;
    display: flex;
    align-items: center;
    gap: 8px;
}

.help details summary::-webkit-details-marker {
    display: none;
}

.help-content {
    margin-top: 12px;
    padding-top: 12px;
    border-top: 1px solid #e9ecef;
}

.help-content ol {
    padding-left: 20px;
}

.help-content li {
    margin-bottom: 8px;
    color: #6c757d;
}

/* 页脚 */
.footer {
    text-align: center;
    margin-top: 24px;
    padding: 16px;
    color: #6c757d;
    font-size: 12px;
}

.footer p {
    margin-bottom: 4px;
}

/* 响应式设计 */
@media (max-width: 320px) {
    .container {
        padding: 12px;
    }

    .header h1 {
        font-size: 18px;
    }

    section h2 {
        font-size: 14px;
    }
}

/* 成功/错误状态样式 */
.result-card.success {
    border-left-color: #28a745;
}

.result-card.error {
    border-left-color: #dc3545;
}

.result-card.error .result-summary {
    color: #dc3545;
}
