# AI Site Submitter v3.0

一个集成AI智能识别功能的Chrome扩展，用于自动化网站提交流程。采用硅基流动 Kimi-K2-Instruct 模型进行表单语义分析和字段映射。

## 🚀 核心特性

- **AI智能识别**: 集成硅基流动AI模型，自动分析表单字段并生成规则
- **分层决策机制**: 采用三层决策系统确保字段映射准确性
- **120+网站支持**: 预配置120+导航网站的提交规则
- **批量管理**: 支持批量网址管理和操作
- **动态规则生成**: 无规则网站可通过AI自动生成规则
- **智能容错**: 多选择器备选方案和语义理解

## 📁 项目结构

### 核心配置文件
- **`website-info.js`** - 网站信息配置文件，包含所有需要填写的字段数据
- **`manifest.json`** - Chrome扩展清单文件，定义权限和资源

### AI核心模块
- **`ai-service.js`** - AI服务模块，调用硅基流动API进行表单分析
- **`form-detector.js`** - 表单检测器，智能识别页面表单元素
- **`rule-generator.js`** - 规则生成器，基于AI分析生成和保存规则
- **`content.js`** - 内容脚本，处理页面表单填写和AI识别

### 用户界面
- **`sidepanel.html/js/css`** - 侧边栏界面，提供操作控制面板
- **`info.html`** - 网站信息展示页面，用于手动复制操作
- **`urls.html`** - 批量网址管理页面，支持分组批量操作

### 规则系统
- **`rules/sites/`** - 存储各网站的表单填写规则配置
- **`url.txt`** - 包含220个导航网站链接的列表文件

### 辅助文件
- **`background.js`** - 后台脚本，处理扩展生命周期
- **`icons/`** - 扩展图标资源文件夹

## 🔧 技术架构

### AI分析流程
1. **表单检测**: `form-detector.js` 扫描页面表单元素
2. **AI分析**: `ai-service.js` 调用硅基流动API进行语义分析
3. **规则生成**: `rule-generator.js` 基于AI结果生成规则文件
4. **自动填写**: `content.js` 执行表单自动填写操作

### 分层决策机制
**第一层：字段类型分类**
- 网站信息类：siteName, siteUrl, siteDescription
- 联系信息类：fullName, contactEmail, phone
- 商业信息类：companyName, pricing, category

**第二层：上下文权重计算**
- 位置权重：表单前1/3(+3分) > 中间(+1分) > 后1/3(0分)
- 区域权重：网站信息区(+2分) > 联系信息区(+1分)

**第三层：冲突解决机制**
- 优先级排序：siteName(10) > siteUrl(9) > contactEmail(8)
- 置信度评估：高置信度(>80%)优先

### 数据流向
```
website-info.js → AI分析 → 规则生成 → 本地存储 → 表单填写
```

## 🎯 核心文件详解

### website-info.js
存储所有网站提交所需的字段信息，包含：
- 基本信息：网站名称、URL、描述等
- 联系信息：邮箱、电话、地址等
- 商业信息：定价、分类、标签等
- 社交媒体：Twitter、LinkedIn等链接

### rules/sites/ 目录
包含120+网站的预定义规则，每个规则文件包含：
- 域名和网站基本信息
- 字段映射规则（selectors、method、validation）
- 选择框选项和默认值
- 表单提交配置

### AI服务配置
- **API**: 硅基流动 API (api.siliconflow.cn)
- **模型**: moonshotai/Kimi-K2-Instruct
- **Token限制**: 16384 tokens
- **分析策略**: 分层决策 + 语义理解

## 🔄 工作流程

### 自动识别流程
1. 用户访问目标网站
2. 扩展检测是否有预定义规则
3. 无规则时启动AI识别模式
4. AI分析表单结构和字段语义
5. 生成规则并保存到本地
6. 执行自动填写操作

### 批量操作流程
1. 打开 `urls.html` 批量管理页面
2. 选择目标网站组（每组5个）
3. 批量打开网站标签页
4. 逐个执行自动填写操作

## 📊 支持的网站类型

- **AI工具导航**: FutureTools、AI Hunter、Insidr等
- **产品发布平台**: Product Hunt、BetaList等
- **目录网站**: 各类Web目录和分类网站
- **技术社区**: GitHub、SourceForge等

## 🛠 开发和扩展

### 添加新网站规则
1. 在 `rules/sites/` 目录创建新的 `.js` 文件
2. 按照现有规则格式定义字段映射
3. 测试规则的准确性和完整性

### 修改网站信息
编辑 `website-info.js` 文件中的相应字段值

### AI提示词优化
修改 `ai-service.js` 中的 `systemPrompt` 来优化AI分析效果

## 📝 使用说明

1. **安装扩展**: 加载解压的扩展到Chrome
2. **配置信息**: 修改 `website-info.js` 中的网站信息
3. **访问网站**: 打开目标提交网站
4. **启动填写**: 点击扩展图标，选择填写表单
5. **AI识别**: 对于无规则网站，使用AI识别功能

## 🔍 故障排除

- **表单检测失败**: 检查网站是否使用iframe表单
- **AI识别错误**: 查看控制台日志，检查API配置
- **字段映射不准确**: 手动调整规则文件中的选择器

## 📈 性能优化

- 采用批量处理减少API调用
- 本地缓存规则避免重复生成
- 智能选择器优先级排序
- 异步处理提升响应速度

## 🔧 技术实现细节

### AI模型集成
```javascript
const AI_CONFIG = {
    apiUrl: 'https://api.siliconflow.cn/v1/chat/completions',
    model: 'moonshotai/Kimi-K2-Instruct',
    maxTokens: 16384,
    temperature: 0.7
};
```

### 字段映射示例
```javascript
// 规则文件结构示例
export const SITE_RULE = {
    domain: 'example.com',
    siteName: 'Example Site',
    fieldMappings: {
        siteName: {
            selectors: ['input[name="tool-name"]', '#tool-name'],
            method: 'value',
            validation: 'required'
        },
        contactEmail: {
            selectors: ['input[type="email"]', 'input[name="email"]'],
            method: 'value',
            validation: 'required|email'
        }
    }
};
```

### 表单检测逻辑
- 优先检测 `<form>` 标签内的字段
- 无form标签时扫描页面所有输入元素
- 自动识别iframe表单并提示不支持
- 支持动态加载的表单元素

### AI分析提示词策略
- 分层决策机制确保映射准确性
- 上下文权重计算优化字段识别
- 冲突解决机制处理歧义情况
- 语义理解而非字面匹配

## 📋 网站信息字段说明

### 基本信息字段
- `siteName`: 网站/工具名称
- `siteTitle`: 详细标题描述
- `siteUrl`: 完整网站地址
- `siteDescription`: 简短介绍(50-160字符)
- `detailedIntro`: 详细功能描述(200-500字符)

### 分类和标签
- `category`: 主要分类(如 'AI Tools')
- `keywords`: SEO关键词，逗号分隔
- `tags`: 相关标签
- `pricing`: 定价模式(Free/Freemium/Paid)

### 联系信息
- `contactEmail`: 联系邮箱
- `fullName`: 提交者姓名
- `companyName`: 公司名称
- `phone`: 联系电话
- `streetAddress`: 详细地址

### 技术信息
- `techStack`: 使用的技术栈
- `apiAvailable`: 是否提供API
- `isOpenSource`: 是否开源
- `supportedPlatforms`: 支持的平台

## 🎨 界面功能说明

### 侧边栏面板 (sidepanel.html)
- **当前网站检测**: 显示当前访问的网站信息
- **规则状态**: 显示是否有预定义规则或需要AI识别
- **操作按钮**: 填写表单、AI识别、重新检测
- **结果展示**: 显示填写结果和成功率

### 信息展示页面 (info.html)
- **字段搜索**: 快速查找特定字段信息
- **分类展示**: 按类别组织显示所有字段
- **复制功能**: 一键复制字段值
- **统计信息**: 显示字段总数和分类统计

### 批量管理页面 (urls.html)
- **分组显示**: 220个网址分44组，每组5个
- **批量操作**: 支持批量打开、全选、搜索过滤
- **进度跟踪**: 显示操作进度和成功率
- **状态管理**: 标记已完成、失败、跳过的网站

## 🔄 扩展开发指南

### 添加新的AI模型
1. 修改 `ai-service.js` 中的 `AI_CONFIG`
2. 调整提示词以适配新模型特性
3. 测试字段映射准确性

### 优化字段映射算法
1. 分析常见映射错误模式
2. 调整权重计算公式
3. 增加新的决策规则
4. 更新冲突解决机制

### 扩展支持的表单类型
1. 增加新的输入类型检测
2. 支持复杂的表单结构
3. 处理动态加载的表单
4. 优化iframe表单检测

---

**开发者**: 二词真君
**项目主页**: [CatchIdeas](https://catchideas.com/)
**版本**: v3.0.0 AI识别版
