// 9Sites.net 网站规则配置
// 网站: https://www.9sites.net/addurl.php
// 最后更新: 2025-07-24

export const SITE_RULE = {
  domain: 'www.9sites.net',
  siteName: '9Sites',
  priority: 1,
  lastUpdated: '2025-07-24',

  fieldMappings: {
    listingType: {
      selectors: [
        'input[name="vtype"][value="reg"]',
        'input[type="radio"][value="reg"]',
        'input[value="reg"]'
      ],
      method: 'radio',
      validation: 'required',
      defaultValue: 'reg',
      notes: '列表类型，默认选择免费Regular Links'
    },

    category: {
      selectors: [
        'select[name="vcat"]',
        'td:contains("Category") + td select'
      ],
      method: 'select',
      validation: 'required',
      defaultValue: '3_0',
      notes: '分类选择，默认Business & Economy'
    },

    siteUrl: {
      selectors: [
        'input[name="vurl"]',
        'td:contains("Web URL") + td input',
        'input[value="http://"]'
      ],
      method: 'value',
      validation: 'required|url',
      notes: '网站URL，预填http://，最多200字符'
    },

    siteName: {
      selectors: [
        'input[name="vtitle"]',
        'td:contains("Title") + td input'
      ],
      method: 'value',
      validation: 'required',
      notes: '网站标题，最多70字符'
    },

    siteDescription: {
      selectors: [
        'input[name="vdesc"]',
        'td:contains("Description") + td input'
      ],
      method: 'value',
      validation: 'required',
      notes: '网站描述，最多250字符'
    },

    fullName: {
      selectors: [
        'input[name="vname"]',
        'td:contains("Your Name") + td input'
      ],
      method: 'value',
      validation: 'optional',
      notes: '提交者姓名，最多50字符'
    },

    contactEmail: {
      selectors: [
        'input[name="vemail"]',
        'td:contains("Email") + td input'
      ],
      method: 'value',
      validation: 'optional|email',
      notes: '联系邮箱，最多70字符'
    }
  },

  submitConfig: {
    submitButton: 'input[type="submit"], input[value="Next »"]',
    submitMethod: 'click',
    successIndicators: ['.success-message'],
    errorIndicators: ['.error-message']
  },

  specialHandling: {
    requiresLogin: false,
    hasCaptcha: false,
    hasFileUpload: false,
    customScript: 'handle9SitesSubmission',
    formValidation: {
      requiredFields: ['listingType', 'category', 'siteUrl', 'siteName', 'siteDescription'],
      emailValidation: true,
      urlValidation: true
    },
    notes: [
      '网站目录提交平台',
      '有付费和免费选项',
      '使用v前缀字段命名',
      '字符数限制严格',
      '邮箱地址保密承诺',
      '付费链接全额退款政策'
    ]
  }
};

export function handle9SitesSubmission(data, rule) {
  const processedData = { ...data };

  if (processedData.siteUrl && !processedData.siteUrl.startsWith('http')) {
    processedData.siteUrl = 'http://' + processedData.siteUrl;
  }

  // 自动选择免费Regular Links
  const regRadio = document.querySelector('input[name="vtype"][value="reg"]');
  if (regRadio) {
    regRadio.checked = true;
  }

  // 自动选择默认分类Business & Economy
  const categorySelect = document.querySelector('select[name="vcat"]');
  if (categorySelect) {
    categorySelect.value = '3_0';
  }

  return processedData;
}

export async function customFillElement(element, value, config) {
  // 处理单选按钮
  if (element.type === 'radio' && element.value === 'reg') {
    element.checked = true;
    element.dispatchEvent(new Event('change', { bubbles: true }));
    return true;
  }

  // 处理下拉选择框
  if (element.tagName === 'SELECT') {
    const options = element.querySelectorAll('option');
    const option = Array.from(options).find(opt => opt.value === '3_0');
    if (option) {
      element.value = option.value;
      element.dispatchEvent(new Event('change', { bubbles: true }));
      return true;
    }
  }

  if (element.tagName === 'INPUT') {
    element.value = value;
    element.dispatchEvent(new Event('input', { bubbles: true }));
    element.dispatchEvent(new Event('change', { bubbles: true }));
    return true;
  }

  return false;
}