// Open-Launch.com 网站规则配置
// 网站: https://open-launch.com/projects/submit
// 最后更新: 2025-08-01

export const SITE_RULE = {
  // 基本信息
  domain: 'open-launch.com',
  siteName: 'Open Launch',
  priority: 1,
  lastUpdated: '2025-08-01',
  
  // 字段映射规则
  fieldMappings: {
    // 项目名称 -> Project Name
    siteName: {
      selectors: [
        'input[name="name"]',
        'input#name',
        'input[placeholder*="My Awesome Project"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '项目名称，必填字段'
    },

    // 网站URL -> Website URL
    siteUrl: {
      selectors: [
        'input[name="websiteUrl"]',
        'input#websiteUrl',
        'input[type="url"]',
        'input[placeholder*="https://myawesomeproject.com"]'
      ],
      method: 'value',
      validation: 'required|url',
      notes: '网站URL地址，必填字段'
    },

    // 项目描述 -> Short Description (富文本编辑器)
    siteDescription: {
      selectors: [
        '.tiptap.ProseMirror[contenteditable="true"]',
        'div[contenteditable="true"][data-placeholder*="Describe"]',
        'div.tiptap[contenteditable="true"]',
        'textarea[name="description"]',
        'textarea#description'
      ],
      method: 'contenteditable',
      validation: 'required',
      notes: '项目简短描述，使用富文本编辑器，必填字段'
    }
  },
  
  // 提交流程配置
  submitConfig: {
    submitButton: 'button[type="submit"], .submit-button, button:contains("Submit")',
    submitMethod: 'manual', // 手动提交，因为是多步骤表单
    waitAfterFill: 1000,
    waitAfterSubmit: 3000,
    successIndicators: [
      '.success-message',
      '.thank-you',
      '.submission-success'
    ],
    errorIndicators: [
      '.error-message',
      '.validation-error',
      '.form-error'
    ]
  },
  
  // 特殊处理
  specialHandling: {
    requiresLogin: false,
    hasCaptcha: false,
    hasFileUpload: false,
    isMultiStep: true, // 多步骤表单
    
    // 表单验证规则
    formValidation: {
      requiredFields: ['siteName', 'siteUrl', 'siteDescription'],
      emailValidation: false,
      urlValidation: true
    },
    
    // 特殊注意事项
    notes: [
      '多步骤表单，第一步填写基本项目信息',
      '需要手动点击下一步继续',
      '表单使用现代化的Tailwind CSS样式',
      '所有字段都是必填项'
    ]
  }
};

// 自定义处理函数
export function handleOpenLaunchSubmission(data, rule) {
  console.log('Processing Open-Launch.com submission...');

  const processedData = { ...data };

  // 确保URL格式正确
  if (processedData.siteUrl && !processedData.siteUrl.startsWith('http')) {
    processedData.siteUrl = 'https://' + processedData.siteUrl;
  }

  // 处理描述长度
  if (processedData.siteDescription) {
    processedData.siteDescription = processedData.siteDescription.trim();
  }

  return processedData;
}

// 自定义元素填写函数
export async function customFillElement(element, value, config) {
  console.log(`Open-Launch自定义填写: ${element.tagName}, 方法: ${config.method}`);

  switch (config.method) {
    case 'contenteditable':
      // 富文本编辑器处理
      element.focus();
      await new Promise(resolve => setTimeout(resolve, 300));

      // 清空现有内容
      element.innerHTML = '';

      // 创建段落元素并插入内容
      const p = document.createElement('p');
      p.textContent = value;
      element.appendChild(p);

      // 触发输入事件
      element.dispatchEvent(new Event('input', { bubbles: true }));
      element.dispatchEvent(new Event('change', { bubbles: true }));

      console.log(`✓ 填写富文本字段: "${value}"`);
      return true;

    case 'value':
      // 标准输入框处理
      element.focus();
      await new Promise(resolve => setTimeout(resolve, 200));

      element.value = value;
      element.dispatchEvent(new Event('input', { bubbles: true }));
      element.dispatchEvent(new Event('change', { bubbles: true }));
      element.dispatchEvent(new Event('blur', { bubbles: true }));

      console.log(`✓ 填写字段: ${element.name || element.id} = "${value}"`);
      return true;

    default:
      element.value = value;
      element.dispatchEvent(new Event('input', { bubbles: true }));
      return true;
  }

  return false;
}
