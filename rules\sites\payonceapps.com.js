// PayOnceApps.com 网站规则配置
// 网站: https://payonceapps.com/add-your-app/
// 最后更新: 2025-07-24

export const SITE_RULE = {
  domain: 'payonceapps.com',
  siteName: 'Pay Once Apps',
  priority: 1,
  lastUpdated: '2025-07-24',

  fieldMappings: {
    fullName: {
      selectors: [
        'input[name="name"]',
        'input[type="text"]:first-of-type',
        'span:contains("Your name") + input'
      ],
      method: 'value',
      validation: 'required',
      notes: '提交者姓名'
    },

    contactEmail: {
      selectors: [
        'input[name="email"]',
        'input[type="email"]',
        'span:contains("Your email") + input'
      ],
      method: 'value',
      validation: 'required|email',
      notes: '联系邮箱'
    },

    siteUrl: {
      selectors: [
        'input[name="website_url"]',
        'input[type="text"]:nth-of-type(2)',
        'span:contains("Application URL") + input'
      ],
      method: 'value',
      validation: 'required|url',
      notes: '应用URL'
    },

    siteDescription: {
      selectors: [
        'textarea[name="comments"]',
        'textarea[rows="6"]',
        'span:contains("Comments for the reviewer") + textarea'
      ],
      method: 'value',
      validation: 'optional',
      notes: '给审核者的评论'
    }
  },

  submitConfig: {
    submitButton: 'button[type="submit"], .bg-indigo-500',
    submitMethod: 'click',
    successIndicators: ['.success-message'],
    errorIndicators: ['.text-rose-500']
  },

  specialHandling: {
    requiresLogin: false,
    hasCaptcha: false,
    hasFileUpload: false,
    customScript: 'handlePayOnceAppsSubmission',
    formValidation: {
      requiredFields: ['fullName', 'contactEmail', 'siteUrl'],
      emailValidation: true,
      urlValidation: true
    },
    notes: [
      '付费应用目录提交',
      '使用Tailwind CSS样式',
      '简洁的4字段表单',
      '审核者评论可选',
      '专注于一次性付费应用'
    ]
  }
};

export function handlePayOnceAppsSubmission(data, rule) {
  const processedData = { ...data };

  if (processedData.siteUrl && !processedData.siteUrl.startsWith('http')) {
    processedData.siteUrl = 'https://' + processedData.siteUrl;
  }

  return processedData;
}

export async function customFillElement(element, value, config) {
  if (element.tagName === 'INPUT' || element.tagName === 'TEXTAREA') {
    element.value = value;
    element.dispatchEvent(new Event('input', { bubbles: true }));
    element.dispatchEvent(new Event('change', { bubbles: true }));
    return true;
  }

  return false;
}