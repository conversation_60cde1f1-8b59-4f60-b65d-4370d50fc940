// aitoolzdir.com 网站规则配置
// 网站: https://www.aitoolzdir.com/submit
// 表单技术: 自定义表单
// 最后更新: 2025-07-07

export const SITE_RULE = {
  // 基本信息
  domain: 'aitoolzdir.com',
  siteName: 'AI Toolz Dir',
  priority: 1,
  lastUpdated: '2025-07-07',
  
  // 字段映射规则
  fieldMappings: {
    // 工具名称 -> Tool Name
    siteName: {
      selectors: [
        'input[name*="tool"][name*="name"]',
        'input[placeholder*="Tool Name"]',
        'input[name*="name"]',
        '#tool-name',
        '.tool-name-input'
      ],
      method: 'value',
      validation: 'required',
      notes: '工具名称，使用website-info.js中的siteName字段'
    },
    
    // 工具URL -> Tool URL
    siteUrl: {
      selectors: [
        'input[name*="tool"][name*="url"]',
        'input[name*="url"]:not([name*="logo"])',
        'input[placeholder*="Tool URL"]',
        'input[type="url"]',
        '#tool-url',
        '.tool-url-input'
      ],
      method: 'value',
      validation: 'required|url',
      notes: '工具网站URL，使用website-info.js中的siteUrl字段'
    },
    
    // Logo URL -> Logo URL
    faviconUrl: {
      selectors: [
        'input[name*="logo"]',
        'input[placeholder*="Logo URL"]',
        'input[name*="image"]',
        '#logo-url',
        '.logo-url-input'
      ],
      method: 'value',
      validation: 'optional|url',
      notes: 'Logo图片URL，使用website-info.js中的faviconUrl字段'
    },
    
    // 简短描述 -> Short Description
    siteDescription: {
      selectors: [
        'input[name="shortDesc"]',
        '#shortDesc',
        'input[placeholder*="brief description"]',
        'input[maxlength="200"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '简短描述，使用website-info.js中的siteDescription字段，限制200字符'
    },
    
    // 详细描述 -> Long Description
    detailedIntro: {
      selectors: [
        'textarea[name*="long"]',
        'textarea[placeholder*="Long Description"]',
        'textarea[name*="detail"]',
        '#long-description',
        '.long-description-input'
      ],
      method: 'value',
      validation: 'required',
      notes: '详细描述，使用website-info.js中的detailedIntro字段，限制500字符'
    },
    
    // 分类 -> Category
    category: {
      selectors: [
        'button[role="combobox"]',
        'select[aria-hidden="true"]',
        'button[data-slot="select-trigger"]',
        'select option[value="Text Generation"]'
      ],
      method: 'combobox',
      validation: 'required',
      defaultValue: 'Text Generation',
      notes: '工具分类，使用combobox组件，默认选择Text Generation'
    },
    
    // 邮箱地址 -> Your Email Address
    contactEmail: {
      selectors: [
        'input[name*="email"]',
        'input[type="email"]',
        'input[placeholder*="Email Address"]',
        '#email',
        '.email-input'
      ],
      method: 'value',
      validation: 'required|email',
      notes: '联系邮箱，使用website-info.js中的contactEmail字段'
    }
  },
  
  // 自定义填写函数
  customFillElement: async function(element, value, config) {
    console.log(`AI Toolz Dir自定义填写: ${element.name || element.placeholder}, 方法: ${config.method}`);
    
    switch (config.method) {
      case 'value':
        // 标准输入框和文本域处理
        element.focus();
        await new Promise(resolve => setTimeout(resolve, 100));
        
        // 清空现有内容
        element.value = '';
        await new Promise(resolve => setTimeout(resolve, 50));
        
        // 处理字符限制
        let finalValue = value;
        if (element.name === 'shortDesc' || element.maxLength === '200') {
          // 简短描述限制200字符
          finalValue = value.substring(0, 200);
        } else if (element.placeholder && element.placeholder.includes('Long Description')) {
          // 详细描述限制500字符
          finalValue = value.substring(0, 500);
        }
        
        // 设置新值
        element.value = finalValue;
        
        // 触发事件
        element.dispatchEvent(new Event('input', { bubbles: true }));
        element.dispatchEvent(new Event('change', { bubbles: true }));
        element.dispatchEvent(new Event('blur', { bubbles: true }));
        
        console.log(`✓ 填写字段: ${element.placeholder || element.name} = "${finalValue.substring(0, 50)}..."`);
        break;

      case 'combobox':
        // Combobox组件处理（现代UI组件）
        try {
          // 方法1: 直接设置隐藏的select元素
          const hiddenSelect = document.querySelector('select[aria-hidden="true"]');
          if (hiddenSelect) {
            // 分类映射
            const categoryMapping = {
              'AI Tools': 'Text Generation',
              'Developer Tools': 'Copilots',
              'Content Creation': 'Text Generation',
              'Image Generation': 'Image Generation',
              'Video Generation': 'Video Generation',
              'Audio Tools': 'Audio Generation',
              'Chatbot': 'Chat Bots',
              'Chat': 'Chat Bots',
              'Agent': 'Agents'
            };

            let targetValue = categoryMapping[value] || config.defaultValue;

            // 查找匹配的选项
            const option = Array.from(hiddenSelect.options).find(opt =>
              opt.value === targetValue || opt.text === targetValue
            );

            if (option) {
              hiddenSelect.value = option.value;
              hiddenSelect.dispatchEvent(new Event('change', { bubbles: true }));
              console.log(`✓ 选择分类: ${option.value}`);
            }
          }

          // 方法2: 点击combobox按钮打开选项
          const comboboxButton = document.querySelector('button[role="combobox"]');
          if (comboboxButton) {
            comboboxButton.click();
            await new Promise(resolve => setTimeout(resolve, 500));

            // 查找并点击目标选项
            const targetOption = document.querySelector(`[data-value="${config.defaultValue}"], [role="option"]:contains("${config.defaultValue}")`);
            if (targetOption) {
              targetOption.click();
              console.log(`✓ 点击选择分类: ${config.defaultValue}`);
            }
          }
        } catch (error) {
          console.error('Combobox处理出错:', error);
        }
        break;

      case 'select':
        // 下拉选择框处理
        if (element.tagName.toLowerCase() === 'select') {
          // 分类映射表
          const categoryMapping = {
            'AI Tools': 'Text Generation',
            'Developer Tools': 'Copilots',
            'Content Creation': 'Text Generation',
            'Image Generation': 'Image Generation',
            'Video Generation': 'Video Generation',
            'Audio Tools': 'Audio Generation',
            'Chatbot': 'Chat Bots',
            'Chat': 'Chat Bots',
            'Agent': 'Agents'
          };
          
          // 尝试映射分类
          let targetValue = categoryMapping[value] || value;
          
          // 查找匹配的选项
          const option = Array.from(element.options).find(opt => 
            opt.value === targetValue || 
            opt.text === targetValue ||
            opt.text.toLowerCase().includes(targetValue.toLowerCase()) ||
            targetValue.toLowerCase().includes(opt.text.toLowerCase())
          );
          
          if (option) {
            element.value = option.value;
            element.dispatchEvent(new Event('change', { bubbles: true }));
            console.log(`✓ 选择分类: ${option.text}`);
          } else {
            // 使用默认值
            const defaultOption = Array.from(element.options).find(opt => 
              opt.value === config.defaultValue || opt.text === config.defaultValue
            );
            if (defaultOption) {
              element.value = defaultOption.value;
              element.dispatchEvent(new Event('change', { bubbles: true }));
              console.log(`✓ 使用默认分类: ${defaultOption.text}`);
            }
          }
        }
        break;
        
      default:
        element.value = value;
        element.dispatchEvent(new Event('input', { bubbles: true }));
        element.dispatchEvent(new Event('change', { bubbles: true }));
    }
  },
  
  // 提交流程配置
  submitConfig: {
    submitButton: [
      'button[type="submit"]',
      'input[type="submit"]',
      'button:contains("Get Listed for Free")',
      '.submit-button',
      '.get-listed-button'
    ],
    submitMethod: 'click',
    waitAfterFill: 2000,
    waitAfterSubmit: 5000,
    successIndicators: [
      '.success-message',
      '.thank-you',
      '.confirmation',
      '[class*="success"]'
    ],
    errorIndicators: [
      '.error-message',
      '.validation-error',
      '[class*="error"]'
    ]
  },
  
  // 特殊处理
  specialHandling: {
    requiresLogin: false,
    hasCaptcha: false,
    hasFileUpload: false,
    hasBacklinkRequirement: true, // 需要添加反向链接
    hasPaidOption: true, // 有付费选项($25)
    
    // 表单验证规则
    formValidation: {
      requiredFields: ['siteName', 'siteUrl', 'siteDescription', 'detailedIntro', 'category', 'contactEmail'],
      emailValidation: true,
      urlValidation: true,
      characterLimits: {
        siteDescription: 200,
        detailedIntro: 500
      }
    },
    
    // 特殊注意事项
    notes: [
      '这是自定义表单，简洁明了',
      '免费提交需要在网站添加反向链接',
      '付费选项$25无需反向链接',
      '简短描述限制200字符',
      '详细描述限制500字符',
      '分类选项包括：Text Generation, Image Generation, Audio Generation, Video Generation, Chat Bots, Agents, Copilots',
      '默认选择Text Generation分类',
      '提供DR 27的do-follow反向链接',
      '网站专注于AI工具目录和SEO优化'
    ]
  }
};

// 自定义处理函数
export function handleAIToolzDirSubmission(data, _rule) {
  console.log('Processing AI Toolz Dir form submission...');

  const processedData = { ...data };

  // 确保URL格式正确
  if (processedData.siteUrl && !processedData.siteUrl.startsWith('http')) {
    processedData.siteUrl = 'https://' + processedData.siteUrl;
  }

  // 确保Logo URL格式正确
  if (processedData.faviconUrl && !processedData.faviconUrl.startsWith('http')) {
    processedData.faviconUrl = 'https://' + processedData.faviconUrl;
  }

  // 处理字符限制
  if (processedData.siteDescription && processedData.siteDescription.length > 200) {
    processedData.siteDescription = processedData.siteDescription.substring(0, 200);
  }

  if (processedData.detailedIntro && processedData.detailedIntro.length > 500) {
    processedData.detailedIntro = processedData.detailedIntro.substring(0, 500);
  }

  // 处理分类映射
  if (processedData.category) {
    const categoryMapping = {
      'AI Tools': 'Text Generation',
      'Developer Tools': 'Copilots',
      'Content Creation': 'Text Generation',
      'Image Generation': 'Image Generation',
      'Video Generation': 'Video Generation',
      'Audio Tools': 'Audio Generation',
      'Chatbot': 'Chat Bots',
      'Chat': 'Chat Bots',
      'Agent': 'Agents'
    };

    processedData.category = categoryMapping[processedData.category] || 'Text Generation';
  }

  return processedData;
}
