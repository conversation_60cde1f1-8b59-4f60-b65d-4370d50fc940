// LaunchUrApp 网站规则配置
// 网站: https://launchurapp.com/developer_dashboard?v=mystartups
// 最后更新: 2025-08-01

export const SITE_RULE = {
  // 基本信息
  domain: 'launchurapp.com',
  siteName: 'LaunchUrApp',
  priority: 1,
  lastUpdated: '2025-08-01',
  
  // 字段映射规则
  fieldMappings: {
    // 开发者姓名 -> Developer Name
    fullName: {
      selectors: [
        'input.bubble-element.Input.baTaLaBq',
        'input[placeholder*="This name will be publicly visible"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '开发者姓名，公开可见，必填字段'
    },

    // 应用名称 -> App Name
    siteName: {
      selectors: [
        'input.bubble-element.Input.ai_aQxzfaXkaZk',
        'input[placeholder*="Enter the name of your app"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '应用名称，必填字段'
    },

    // 标语 -> Tagline
    siteDescription: {
      selectors: [
        'input.bubble-element.Input.baTaHwf',
        'input[placeholder*="A concise one-line description"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '应用简短描述，必填字段'
    },

    // 主页URL -> Homepage URL
    siteUrl: {
      selectors: [
        'input.bubble-element.Input.ai_aQxzfaXkaZq',
        'input[placeholder*="https://your-app-homepage.com"]'
      ],
      method: 'value',
      validation: 'required|url',
      notes: '应用主页URL，必填字段'
    }
  },
  
  // 提交流程配置
  submitConfig: {
    submitButton: 'button.bubble-element.Button.ai_aQxzfaXkaZz, button:contains("Submit App")',
    submitMethod: 'click',
    waitAfterFill: 2000,
    waitAfterSubmit: 5000,
    successIndicators: [
      '.success-message',
      '.thank-you',
      '.submission-success'
    ],
    errorIndicators: [
      '.error-message',
      '.validation-error',
      '.form-error'
    ]
  },
  
  // 特殊处理
  specialHandling: {
    requiresLogin: true, // 需要登录
    hasCaptcha: false,
    hasFileUpload: true, // 有Logo上传
    hasRadioButtons: true, // 有Launch Plan单选按钮
    
    // 表单验证规则
    formValidation: {
      requiredFields: ['fullName', 'siteName', 'siteDescription', 'siteUrl'],
      emailValidation: false,
      urlValidation: true,
      fileUploadValidation: true
    },
    
    // 特殊注意事项
    notes: [
      '需要登录才能访问表单',
      'Logo只接受PNG格式文件',
      'Launch Plan默认选择Standard Launch',
      '使用Bubble.io构建的表单',
      '所有字段都是必填项'
    ]
  }
};

// 自定义处理函数
export function handleLaunchUrAppSubmission(data, rule) {
  console.log('Processing LaunchUrApp submission...');
  
  const processedData = { ...data };
  
  // 确保URL格式正确
  if (processedData.siteUrl && !processedData.siteUrl.startsWith('http')) {
    processedData.siteUrl = 'https://' + processedData.siteUrl;
  }
  
  return processedData;
}

// 自定义元素填写函数
export async function customFillElement(element, value, config) {
  console.log(`LaunchUrApp自定义填写: ${element.tagName}, 方法: ${config.method}`);

  switch (config.method) {
    case 'value':
      // Bubble.io输入框处理
      element.focus();
      await new Promise(resolve => setTimeout(resolve, 300));

      // 清空现有值
      element.value = '';
      
      // 逐字符输入以触发Bubble.io的事件
      for (let i = 0; i < value.length; i++) {
        element.value += value[i];
        element.dispatchEvent(new Event('input', { bubbles: true }));
        await new Promise(resolve => setTimeout(resolve, 50));
      }
      
      element.dispatchEvent(new Event('change', { bubbles: true }));
      element.dispatchEvent(new Event('blur', { bubbles: true }));

      console.log(`✓ 填写Bubble字段: "${value}"`);
      return true;

    default:
      element.value = value;
      element.dispatchEvent(new Event('input', { bubbles: true }));
      return true;
  }

  return false;
}

// 特殊处理：自动选择Launch Plan
export async function handleSpecialFields() {
  console.log('处理特殊字段...');
  
  // 自动选择Standard Launch单选按钮
  const radioButton = document.querySelector('input[type="radio"][value="Standard Launch"]');
  if (radioButton && !radioButton.checked) {
    radioButton.click();
    console.log('✓ 已选择Standard Launch');
  }
  
  return true;
}
