// designtools.ai 网站规则配置
// 网站: https://designtools.ai/submit/
// 表单技术: WordPress Contact Form
// 最后更新: 2025-07-08

export const SITE_RULE = {
  // 基本信息
  domain: 'designtools.ai',
  siteName: 'Design Tools AI',
  priority: 1,
  lastUpdated: '2025-07-08',
  
  // 字段映射规则
  fieldMappings: {
    // 工具名称 -> Tool Name
    siteName: {
      selectors: [
        'input[name="tool-name"]',
        'input[placeholder*="name of the AI Tool"]',
        'form input[type="text"]:first-of-type',
        'input:first-of-type'
      ],
      method: 'value',
      validation: 'required',
      notes: '工具名称，使用website-info.js中的siteName字段'
    },
    
    // 工具URL -> Tool-URL
    siteUrl: {
      selectors: [
        'input[name="tool-url"]',
        'input[placeholder*="link of the tool"]',
        'input[type="url"]',
        'form input[type="text"]:nth-of-type(2)'
      ],
      method: 'value',
      validation: 'required|url',
      notes: '工具URL，使用website-info.js中的siteUrl字段'
    },
    
    // 分类 -> Category
    category: {
      selectors: [
        'input[name="input_5"][value="7"]',
        'input[id="choice_1_5_4"]',
        'input.gfield-choice-input[value="7"]',
        'input[type="radio"][value="7"]'
      ],
      method: 'radio',
      validation: 'required',
      defaultValue: '7', // Productivity的value值
      availableOptions: [
        { value: '3', text: 'Colors' },
        { value: '11', text: 'Figma Plugins' },
        { value: '2', text: 'Image' },
        { value: '6', text: 'Marketing' },
        { value: '7', text: 'Productivity' },
        { value: '14', text: 'SEO' },
        { value: '5', text: 'Typography' },
        { value: '4', text: 'UI / UX' },
        { value: '10', text: 'Video' }
      ],
      notes: '分类，默认选择Productivity (value=7)，使用单选按钮'
    },
    
    // 简短描述 -> Short description
    siteDescription: {
      selectors: [
        'textarea[name="short-description"]',
        'textarea[placeholder*="short description"]',
        'form textarea:first-of-type',
        'textarea'
      ],
      method: 'value',
      validation: 'required',
      maxLength: 140,
      notes: '简短描述，使用website-info.js中的siteDescription字段，限制140字符'
    },
    
    // 付费模式 -> Payment Model
    paymentModel: {
      selectors: [
        'select[name="input_4"]',
        'select[id="input_1_4"]',
        'select.gfield_select',
        'select.large'
      ],
      method: 'select',
      validation: 'required',
      defaultValue: 'Free',
      availableOptions: ['Free', 'Free Trial', 'Fremium', 'Paid'],
      notes: '付费模式，默认选择Free'
    },
    
    // 名字 -> First Name
    firstName: {
      selectors: [
        'input[name="input_7.3"]',
        'input[id="input_1_7_3"]',
        'span.name_first input',
        'input[type="text"]:nth-of-type(3)'
      ],
      method: 'value',
      validation: 'optional',
      defaultValue: 'Quinn',
      notes: '名字，固定值Quinn'
    },

    // 姓氏 -> Last Name
    lastName: {
      selectors: [
        'input[name="input_7.6"]',
        'input[id="input_1_7_6"]',
        'span.name_last input',
        'input[type="text"]:nth-of-type(4)'
      ],
      method: 'value',
      validation: 'optional',
      defaultValue: 'Halberg',
      notes: '姓氏，固定值Halberg'
    },
    
    // 职位 -> Job Title
    submitterRole: {
      selectors: [
        'input[name="input_8"]',
        'input[id="input_1_8"]',
        'input.large:nth-of-type(5)',
        'input[type="text"]:nth-of-type(5)'
      ],
      method: 'value',
      validation: 'optional',
      notes: '职位，使用website-info.js中的submitterRole字段'
    },
    
    // 邮箱 -> Email
    contactEmail: {
      selectors: [
        'input[name="email"]',
        'input[type="email"]',
        'form input[type="email"]:first-of-type'
      ],
      method: 'value',
      validation: 'optional|email',
      notes: '邮箱，使用website-info.js中的contactEmail字段，可选'
    }
  },
  
  // 自定义填写函数
  customFillElement: async function(element, value, config) {
    console.log(`Design Tools AI自定义填写: ${element.name || element.type}, 方法: ${config.method}`);
    
    switch (config.method) {
      case 'value':
        // 标准输入框和文本域处理
        element.focus();
        await new Promise(resolve => setTimeout(resolve, 100));
        
        // 清空现有内容
        element.value = '';
        await new Promise(resolve => setTimeout(resolve, 50));
        
        // 处理字符限制
        let finalValue = value;
        if (config.maxLength && finalValue.length > config.maxLength) {
          finalValue = finalValue.substring(0, config.maxLength);
          console.log(`⚠️ 内容被截断到${config.maxLength}字符: ${finalValue}`);
        }
        
        // 处理默认值
        if (config.defaultValue && (!value || value.trim() === '')) {
          finalValue = config.defaultValue;
        }
        
        // 设置新值
        element.value = finalValue;
        
        // 触发事件
        element.dispatchEvent(new Event('input', { bubbles: true }));
        element.dispatchEvent(new Event('change', { bubbles: true }));
        element.dispatchEvent(new Event('blur', { bubbles: true }));
        
        console.log(`✓ 填写字段: ${finalValue.substring(0, 50)}...`);
        break;
        
      case 'radio':
        // 单选按钮处理
        console.log(`处理分类单选按钮，目标值: ${config.defaultValue}`);

        // 查找所有同名单选按钮
        const radioButtons = document.querySelectorAll('input[type="radio"][name="input_5"]');

        // 先取消所有选择
        radioButtons.forEach(rb => {
          rb.checked = false;
        });

        // 选择目标分类
        const targetRadio = Array.from(radioButtons).find(rb =>
          rb.value === config.defaultValue
        );

        if (targetRadio) {
          targetRadio.checked = true;
          targetRadio.dispatchEvent(new Event('change', { bubbles: true }));
          console.log(`✓ 选择分类: Productivity (value: ${config.defaultValue})`);
        } else {
          console.log(`⚠️ 未找到分类值: ${config.defaultValue}`);
        }
        break;
        
      case 'select':
        // 下拉选择处理
        element.focus();
        await new Promise(resolve => setTimeout(resolve, 100));
        
        const targetValue = config.defaultValue;
        
        // 查找匹配的选项
        const option = Array.from(element.options).find(opt => 
          opt.text.includes(targetValue) || opt.value.includes(targetValue)
        );
        
        if (option) {
          element.value = option.value;
          element.dispatchEvent(new Event('change', { bubbles: true }));
          console.log(`✓ 选择付费模式: ${option.text}`);
        } else {
          console.log(`⚠️ 未找到付费模式: ${targetValue}`);
        }
        break;
        
      default:
        element.value = value;
        element.dispatchEvent(new Event('input', { bubbles: true }));
        element.dispatchEvent(new Event('change', { bubbles: true }));
    }
  },
  
  // 提交流程配置
  submitConfig: {
    submitButton: [
      'button[type="submit"]',
      'input[type="submit"]',
      'button:contains("Submit")',
      'input[value*="Submit"]'
    ],
    submitMethod: 'click',
    waitAfterFill: 2000,
    waitAfterSubmit: 4000,
    successIndicators: [
      'text:contains("submitted")',
      'text:contains("thank you")',
      'text:contains("success")',
      'text:contains("received")'
    ],
    errorIndicators: [
      'text:contains("error")',
      'text:contains("required")',
      'text:contains("invalid")'
    ]
  },
  
  // 特殊处理
  specialHandling: {
    requiresLogin: false,
    hasCaptcha: false,
    hasFileUpload: false,
    isDesignerFocused: true, // 专注于设计师
    hasCheckboxCategories: true, // 有复选框分类
    hasCharacterLimit: true, // 有字符限制
    
    // 表单验证规则
    formValidation: {
      requiredFields: ['siteName', 'siteUrl', 'category', 'siteDescription', 'paymentModel'],
      optionalFields: ['firstName', 'lastName', 'submitterRole', 'contactEmail'],
      emailValidation: true,
      urlValidation: true,
      characterLimits: {
        siteDescription: 140
      },
      radioGroups: ['category']
    },
    
    // 特殊注意事项
    notes: [
      '这是Design Tools AI的工具提交表单',
      '表单包含8个字段：5个必填，4个可选',
      '专注于设计师工作流程改进的AI工具',
      '分类使用单选按钮，默认选择Productivity',
      '简短描述限制140字符',
      '描述需要使用"you"形式并突出主要优势',
      '有9个设计相关分类可选',
      '支持4种付费模式（注意Fremium拼写）',
      '姓名分为First和Last两个字段',
      '职位字段使用submitterRole',
      '个人信息字段都是可选的',
      '使用Gravity Forms表单系统'
    ]
  }
};

// 自定义处理函数
export function handleDesignToolsAISubmission(data, _rule) {
  console.log('Processing Design Tools AI form submission...');

  const processedData = { ...data };

  // 确保URL格式正确
  if (processedData.siteUrl && !processedData.siteUrl.startsWith('http')) {
    processedData.siteUrl = 'https://' + processedData.siteUrl;
  }

  // 处理描述字符限制
  if (processedData.siteDescription && processedData.siteDescription.length > 140) {
    processedData.siteDescription = processedData.siteDescription.substring(0, 140);
  }

  // 设置默认值
  processedData.category = '7'; // Productivity
  processedData.paymentModel = 'Free';
  processedData.firstName = 'Alex';
  processedData.lastName = 'Chen';

  return processedData;
}

// 设计师工具分类检测
export function detectDesignerCategories() {
  console.log('检测设计师工具分类...');

  const categories = [
    'Colors', 'Figma Plugins', 'Image', 'Marketing',
    'Productivity', 'SEO', 'Typography', 'UI / UX', 'Video'
  ];

  categories.forEach(category => {
    const checkbox = document.querySelector(`input[type="checkbox"][value="${category}"]`);
    if (checkbox) {
      console.log(`✓ 找到分类: ${category}`);
    } else {
      console.log(`⚠️ 未找到分类: ${category}`);
    }
  });
}

// Design Tools AI信息提醒
export function showDesignToolsAIInfo() {
  console.log('🎨 Design Tools AI 信息:');
  console.log('');
  console.log('平台特色:');
  console.log('- 专注于设计师工作流程的AI工具');
  console.log('- 涵盖设计工作的各个方面');
  console.log('- 简洁的工具提交流程');
  console.log('- 支持咖啡打赏功能');
  console.log('');
  console.log('设计分类 (9个):');
  console.log('- Colors (颜色工具)');
  console.log('- Figma Plugins (Figma插件)');
  console.log('- Image (图像处理)');
  console.log('- Marketing (营销工具)');
  console.log('- Productivity (生产力)');
  console.log('- SEO (搜索优化)');
  console.log('- Typography (字体排版)');
  console.log('- UI / UX (界面设计) - 推荐');
  console.log('- Video (视频制作)');
  console.log('');
  console.log('付费模式 (4种):');
  console.log('- Free (免费)');
  console.log('- Free Trial (免费试用)');
  console.log('- Freemium (免费增值)');
  console.log('- Paid (付费)');
  console.log('');
  console.log('Design Tools AI - 为设计师精选的AI工具！');
}

// 描述优化建议
export function showDescriptionGuidelines() {
  console.log('📝 描述写作指南:');
  console.log('');
  console.log('描述要求:');
  console.log('- 限制140字符以内');
  console.log('- 使用"you"形式（第二人称）');
  console.log('- 突出工具的主要优势');
  console.log('- 关注设计师的实际需求');
  console.log('');
  console.log('优秀描述示例:');
  console.log('- "Generate stunning UI designs with AI-powered suggestions that save you hours of work"');
  console.log('- "Transform your sketches into professional mockups using advanced AI technology"');
  console.log('- "Create perfect color palettes for your designs with intelligent color theory"');
  console.log('');
  console.log('避免的写法:');
  console.log('- 过于技术性的描述');
  console.log('- 使用第一人称或第三人称');
  console.log('- 过长的句子');
  console.log('- 缺乏具体优势说明');
}

// 复选框分类处理
export async function handleCategoryCheckboxes(targetCategory) {
  console.log(`处理分类复选框: ${targetCategory}`);

  // 获取所有复选框
  const checkboxes = document.querySelectorAll('input[type="checkbox"]');

  // 先取消所有选择
  checkboxes.forEach(checkbox => {
    checkbox.checked = false;
  });

  // 查找目标分类
  const targetCheckbox = Array.from(checkboxes).find(checkbox => {
    const label = checkbox.nextElementSibling || checkbox.parentElement;
    return checkbox.value === targetCategory ||
           (label && label.textContent.includes(targetCategory));
  });

  if (targetCheckbox) {
    targetCheckbox.checked = true;
    targetCheckbox.dispatchEvent(new Event('change', { bubbles: true }));
    console.log(`✓ 已选择分类: ${targetCategory}`);
    return true;
  } else {
    console.log(`⚠️ 未找到分类: ${targetCategory}`);
    return false;
  }
}

// 表单验证
export function validateDesignToolsAIForm() {
  console.log('验证Design Tools AI表单...');

  const requiredFields = [
    { selector: 'input[name*="tool-name"]', label: '工具名称' },
    { selector: 'input[name*="tool-url"]', label: '工具URL' },
    { selector: 'textarea[name*="short-description"]', label: '简短描述' },
    { selector: 'select[name*="payment-model"]', label: '付费模式' }
  ];

  let isValid = true;

  requiredFields.forEach(field => {
    const element = document.querySelector(field.selector);
    if (!element || !element.value.trim()) {
      console.log(`⚠️ 必填字段为空: ${field.label}`);
      isValid = false;
    }
  });

  // 检查分类选择
  const categoryCheckboxes = document.querySelectorAll('input[type="checkbox"]:checked');
  if (categoryCheckboxes.length === 0) {
    console.log('⚠️ 需要选择至少一个分类');
    isValid = false;
  }

  // 检查描述长度
  const description = document.querySelector('textarea[name*="short-description"]');
  if (description && description.value.length > 140) {
    console.log('⚠️ 描述超过140字符限制');
    isValid = false;
  }

  if (isValid) {
    console.log('✓ 表单验证通过');
  }

  return isValid;
}

// 字符计数器
export function showCharacterCounter(textareaElement) {
  console.log('显示字符计数器...');

  if (!textareaElement) {
    textareaElement = document.querySelector('textarea[name*="short-description"]');
  }

  if (textareaElement) {
    const currentLength = textareaElement.value.length;
    const maxLength = 140;
    const remaining = maxLength - currentLength;

    console.log(`字符统计: ${currentLength}/${maxLength} (剩余: ${remaining})`);

    if (remaining < 0) {
      console.log('⚠️ 超过字符限制！');
    } else if (remaining < 20) {
      console.log('⚠️ 接近字符限制');
    }

    return { current: currentLength, max: maxLength, remaining: remaining };
  }
}

// 设计师职位建议
export function showDesignerJobTitles() {
  console.log('💼 设计师职位建议:');
  console.log('');
  console.log('常见设计师职位:');
  console.log('- Product Designer (产品设计师) - 推荐');
  console.log('- UI Designer (界面设计师)');
  console.log('- UX Designer (用户体验设计师)');
  console.log('- Visual Designer (视觉设计师)');
  console.log('- Graphic Designer (平面设计师)');
  console.log('- Web Designer (网页设计师)');
  console.log('- Design Lead (设计主管)');
  console.log('- Creative Director (创意总监)');
  console.log('- Freelance Designer (自由设计师)');
  console.log('');
  console.log('选择最符合您工作内容的职位！');
}
