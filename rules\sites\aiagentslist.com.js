// AIAgentsList.com 网站规则配置
// 网站: https://aiagentslist.com/submit
// 最后更新: 2025-07-24

export const SITE_RULE = {
  // 基本信息
  domain: 'aiagentslist.com',
  siteName: 'AI Agents List',
  priority: 1,
  lastUpdated: '2025-07-24',
  
  // 字段映射规则
  fieldMappings: {
    // AI代理名称 -> Name (对应website-info.js的siteName)
    siteName: {
      selectors: [
        'input[name="name"]',
        '#formsnap-1',
        'input[placeholder="Replit"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '网站/工具名称，对应website-info.js的siteName字段'
    },

    // 网站URL -> Website (对应website-info.js的siteUrl)
    siteUrl: {
      selectors: [
        'input[name="websiteUrl"]',
        '#formsnap-6',
        'input[placeholder*="https://replit.com"]'
      ],
      method: 'value',
      validation: 'required|url',
      notes: '网站URL地址，对应website-info.js的siteUrl字段'
    },

    // 定价模式 -> Pricing Model
    pricing: {
      selectors: [
        'button[name="pricing"]',
        '#formsnap-11',
        'button[data-select-trigger]'
      ],
      method: 'select',
      validation: 'required',
      options: ['free', 'paid', 'freemium'],
      optionTexts: ['Free - No payment required', 'Paid', 'Freemium'],
      defaultValue: 'free',
      notes: '定价模式选择'
    },

    // 分类 -> Category
    category: {
      selectors: [
        'button[name="categoryId"]',
        '#formsnap-16',
        'button[data-placeholder]'
      ],
      method: 'select',
      validation: 'required',
      defaultValue: 'AI Agent Builders',
      notes: '选择分类'
    },

    // 代码仓库 -> Repository (可选)
    sourceCodeUrl: {
      selectors: [
        'input[name="repositoryUrl"]',
        '#formsnap-21',
        'input[placeholder*="github.com"]'
      ],
      method: 'value',
      validation: 'optional|url',
      notes: 'GitHub或其他代码仓库链接'
    },

    // LinkedIn链接 -> LinkedIn (可选)
    linkedinUrl: {
      selectors: [
        'input[name="linkedinUrl"]',
        '#formsnap-26',
        'input[placeholder*="linkedin.com"]'
      ],
      method: 'value',
      validation: 'optional|url',
      notes: 'LinkedIn个人或公司页面链接'
    },

    // Twitter链接 -> Twitter/X (可选)
    twitterUrl: {
      selectors: [
        'input[name="twitterUrl"]',
        '#formsnap-31',
        'input[placeholder*="x.com"]'
      ],
      method: 'value',
      validation: 'optional|url',
      notes: 'Twitter/X账号链接'
    },

    // 额外资源 -> Additional Resources (可选)
    documentationUrl: {
      selectors: [
        'input[name="extraLinks"]',
        '#formsnap-36',
        'input[placeholder*="docs.replit.com"]'
      ],
      method: 'value',
      validation: 'optional|url',
      notes: '文档或其他相关资源链接'
    }
  },
  
  // 提交流程配置
  submitConfig: {
    submitButton: 'button[type="submit"], input[type="submit"]',
    submitMethod: 'click',
    successIndicators: [
      '.success-message',
      '.alert-success',
      '.notification-success'
    ],
    errorIndicators: [
      '.error-message',
      '.alert-error',
      '.text-destructive'
    ]
  },
  
  // 特殊处理
  specialHandling: {
    requiresLogin: true, // 需要登录
    hasCaptcha: false,
    hasFileUpload: false,
    
    // 自定义处理脚本
    customScript: 'handleAIAgentsListSubmission',
    
    // 表单验证规则
    formValidation: {
      requiredFields: ['siteName', 'siteUrl', 'pricing', 'category'],
      emailValidation: false,
      urlValidation: true
    },
    
    // 特殊注意事项
    notes: [
      '需要先登录才能提交',
      '使用现代的Svelte框架构建',
      '表单使用自定义的select组件',
      '必填字段：名称、网站、定价模式、分类',
      '可选字段：代码仓库、LinkedIn、Twitter、额外资源',
      '所有URL字段需要完整的https://格式'
    ]
  }
};

// 自定义处理函数
export function handleAIAgentsListSubmission(data, rule) {
  console.log('Processing AI Agents List submission...');

  const processedData = { ...data };

  // 确保URL格式正确
  if (processedData.siteUrl && !processedData.siteUrl.startsWith('http')) {
    processedData.siteUrl = 'https://' + processedData.siteUrl;
  }

  // 定价模式映射
  if (processedData.pricing) {
    const pricingMap = {
      'Free': 'free',
      'Freemium': 'freemium',
      'Paid': 'paid',
      'Subscription': 'paid'
    };

    processedData.pricing = pricingMap[processedData.pricing] || 'free';
  }

  // 处理可选的URL字段
  const urlFields = ['sourceCodeUrl', 'linkedinUrl', 'twitterUrl', 'documentationUrl'];
  urlFields.forEach(field => {
    if (processedData[field] && !processedData[field].startsWith('http')) {
      processedData[field] = 'https://' + processedData[field];
    }
  });

  // 处理Twitter URL格式
  if (processedData.twitterUrl) {
    // 将twitter.com转换为x.com
    processedData.twitterUrl = processedData.twitterUrl.replace('twitter.com', 'x.com');
  }

  console.log('AI Agents List 数据处理完成:', processedData);
  return processedData;
}

// 自定义元素填写函数
export async function customFillElement(element, value, config) {
  console.log('🔧 AI Agents List自定义填写函数被调用:', element, value);

  // 处理自定义select组件
  if (element.hasAttribute('data-select-trigger') || element.getAttribute('aria-haspopup') === 'listbox') {
    try {
      console.log('🎯 处理自定义select组件');

      // 点击打开下拉菜单
      element.click();

      // 等待下拉菜单出现
      await new Promise(resolve => setTimeout(resolve, 500));

      // 查找选项
      let optionElement = null;

      // 对于定价模式
      if (element.name === 'pricing') {
        const pricingOptions = {
          'free': 'Free - No payment required',
          'freemium': 'Freemium',
          'paid': 'Paid'
        };

        const targetText = pricingOptions[value] || value;
        optionElement = document.querySelector(`[data-value="${value}"], [role="option"]:contains("${targetText}")`);
      }

      // 对于分类
      if (element.name === 'categoryId') {
        // 查找包含"AI Agent Builders"或类似文本的选项
        optionElement = document.querySelector('[role="option"]:contains("AI Agent"), [role="option"]:contains("General Purpose")');
      }

      if (optionElement) {
        optionElement.click();
        console.log('✅ 选择了选项:', optionElement.textContent);
        return true;
      } else {
        console.warn('⚠️ 未找到匹配的选项');
      }

    } catch (error) {
      console.warn('自定义select处理失败:', error);
    }
  }

  // 处理普通输入框
  if (element.tagName === 'INPUT' && element.type !== 'file') {
    try {
      element.value = value;
      element.dispatchEvent(new Event('input', { bubbles: true }));
      element.dispatchEvent(new Event('change', { bubbles: true }));

      console.log('✅ 填写输入框:', value);
      return true;
    } catch (error) {
      console.warn('输入框填写失败:', error);
    }
  }

  // 默认处理
  return false;
};
