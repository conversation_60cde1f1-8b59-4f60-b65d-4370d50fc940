// freeinternetwebdirectory.com 网站规则配置
// 网站: https://www.freeinternetwebdirectory.com/submit.php
// 表单技术: PHP Form with CAPTCHA
// 最后更新: 2025-07-08

export const SITE_RULE = {
  // 基本信息
  domain: 'freeinternetwebdirectory.com',
  siteName: 'Free Internet Web Directory',
  priority: 1,
  lastUpdated: '2025-07-08',
  
  // 字段映射规则
  fieldMappings: {
    // 定价选项 -> Pricing
    linkType: {
      selectors: [
        'input[name="LINK_TYPE"][value="normal"]',
        'input[type="radio"][value="normal"]',
        'input[value="normal"]'
      ],
      method: 'radio',
      validation: 'required',
      defaultValue: 'normal',
      availableOptions: ['featured', 'normal', 'reciprocal'],
      notes: '定价选项，默认选择normal (Regular links，免费，2-3个月审核)'
    },
    
    // 标题 -> Title
    siteName: {
      selectors: [
        'input[name="TITLE"]',
        'input.text:first-of-type',
        'input[maxlength="100"]',
        'input[size="40"]:first-of-type'
      ],
      method: 'value',
      validation: 'required',
      notes: '网站标题，使用website-info.js中的siteName字段'
    },
    
    // URL -> URL
    siteUrl: {
      selectors: [
        'input[name="URL"]',
        'input[maxlength="255"]',
        'input.text:nth-of-type(2)',
        'input[size="40"]:nth-of-type(2)'
      ],
      method: 'value',
      validation: 'optional|url',
      notes: '网站URL，使用website-info.js中的siteUrl字段'
    },
    
    // 描述 -> Description
    detailedIntro: {
      selectors: [
        'textarea[name="DESCRIPTION"]',
        'textarea.text:first-of-type',
        'textarea[rows="3"]:first-of-type',
        'textarea[cols="37"]'
      ],
      method: 'value',
      validation: 'optional',
      maxLength: 500,
      notes: '网站详细描述，使用website-info.js中的detailedIntro字段，限制500字符'
    },
    
    // META关键词 -> META Keywords
    keywords: {
      selectors: [
        'input[name="META_KEYWORDS"]',
        'input[maxlength="2000"]',
        'input.text:nth-of-type(3)',
        'input[size="40"]:nth-of-type(3)'
      ],
      method: 'value',
      validation: 'optional',
      notes: 'META关键词，使用website-info.js中的keywords字段'
    },
    
    // META描述 -> META Description
    siteDescription: {
      selectors: [
        'textarea[name="META_DESCRIPTION"]',
        'textarea.text:nth-of-type(2)',
        'textarea[rows="3"]:nth-of-type(2)',
        'textarea[cols="30"]'
      ],
      method: 'value',
      validation: 'optional',
      maxLength: 250,
      notes: 'META描述，使用website-info.js中的siteDescription字段，限制250字符'
    },
    
    // 您的姓名 -> Your Name
    fullName: {
      selectors: [
        'input[name="OWNER_NAME"]',
        'input[maxlength="50"]',
        'input.text:nth-of-type(4)',
        'input[size="40"]:nth-of-type(4)'
      ],
      method: 'value',
      validation: 'required',
      notes: '您的姓名，使用website-info.js中的fullName字段'
    },
    
    // 您的邮箱 -> Your Email
    contactEmail: {
      selectors: [
        'input[name="OWNER_EMAIL"]',
        'td.field input',
        'input.text:nth-of-type(5)',
        'input[size="40"]:nth-of-type(5)'
      ],
      method: 'value',
      validation: 'required|email',
      notes: '您的邮箱，使用website-info.js中的contactEmail字段'
    }
  },
  
  // 自定义填写函数
  customFillElement: async function(element, value, config) {
    console.log(`Free Internet Web Directory自定义填写: ${element.name || element.type}, 方法: ${config.method}`);
    
    switch (config.method) {
      case 'value':
        // 标准输入框和文本域处理
        element.focus();
        await new Promise(resolve => setTimeout(resolve, 100));
        
        // 清空现有内容
        element.value = '';
        await new Promise(resolve => setTimeout(resolve, 50));
        
        // 处理字符限制
        let finalValue = value;
        if (config.maxLength && finalValue.length > config.maxLength) {
          finalValue = finalValue.substring(0, config.maxLength);
          console.log(`⚠️ 内容被截断到${config.maxLength}字符: ${finalValue}`);
        }
        
        // 设置新值
        element.value = finalValue;
        
        // 触发事件
        element.dispatchEvent(new Event('input', { bubbles: true }));
        element.dispatchEvent(new Event('change', { bubbles: true }));
        element.dispatchEvent(new Event('blur', { bubbles: true }));
        
        console.log(`✓ 填写字段: ${element.name} = "${finalValue.substring(0, 50)}..."`);
        break;
        
      case 'radio':
        // 单选按钮处理
        console.log(`处理定价选项，目标值: ${config.defaultValue}`);
        
        // 查找所有同名单选按钮
        const radioButtons = document.querySelectorAll('input[name="LINK_TYPE"]');
        
        // 先取消所有选择
        radioButtons.forEach(rb => {
          rb.checked = false;
        });
        
        // 选择目标选项
        const targetRadio = Array.from(radioButtons).find(rb => 
          rb.value === config.defaultValue
        );
        
        if (targetRadio) {
          targetRadio.checked = true;
          targetRadio.dispatchEvent(new Event('change', { bubbles: true }));
          console.log(`✓ 选择定价选项: Regular links (${config.defaultValue})`);
        } else {
          console.log(`⚠️ 未找到定价选项: ${config.defaultValue}`);
        }
        break;
        
      default:
        element.value = value;
        element.dispatchEvent(new Event('input', { bubbles: true }));
        element.dispatchEvent(new Event('change', { bubbles: true }));
    }
  },
  
  // 提交流程配置
  submitConfig: {
    submitButton: [
      'button[type="submit"]',
      'input[type="submit"]',
      'button:contains("Submit")',
      'input[value*="Submit"]'
    ],
    submitMethod: 'click',
    waitAfterFill: 2000,
    waitAfterSubmit: 5000,
    successIndicators: [
      'text:contains("submitted")',
      'text:contains("thank you")',
      'text:contains("success")',
      'text:contains("approved")'
    ],
    errorIndicators: [
      'text:contains("error")',
      'text:contains("required")',
      'text:contains("invalid")',
      'text:contains("captcha")'
    ]
  },
  
  // 特殊处理
  specialHandling: {
    requiresLogin: false,
    hasCaptcha: true, // 可能有验证码
    hasFileUpload: false,
    isPHPForm: true, // PHP表单
    isInternetDirectory: true, // 互联网目录
    hasMetaFields: true, // 有META字段
    hasPaidOptions: true, // 有付费选项
    hasReciprocalOption: true, // 有互惠链接选项
    hasCharacterLimits: true, // 有字符限制
    
    // 表单验证规则
    formValidation: {
      requiredFields: ['linkType', 'siteName', 'fullName', 'contactEmail'],
      optionalFields: ['siteUrl', 'detailedIntro', 'keywords', 'siteDescription'],
      emailValidation: true,
      urlValidation: true,
      characterLimits: {
        detailedIntro: 500,
        siteDescription: 250
      },
      radioGroups: ['linkType']
    },
    
    // 特殊注意事项
    notes: [
      '这是Free Internet Web Directory的网站提交表单',
      '表单包含8个字段：4个必填，4个可选',
      '免费的互联网网站目录',
      '可能有验证码保护，需要手动处理',
      '默认选择Regular links（免费，2-3个月审核）',
      '有付费选项：Featured links $6.97/年（1-2天审核）',
      '有互惠链接选项：Regular links with reciprocal（1-2周审核）',
      '包含META字段：关键词和描述',
      '描述限制500字符，META描述限制250字符',
      '使用实际字段名：LINK_TYPE, TITLE, URL, DESCRIPTION, META_KEYWORDS, META_DESCRIPTION, OWNER_NAME, OWNER_EMAIL',
      '与其他目录网站类似的表单结构',
      '专注于互联网相关网站收录'
    ]
  }
};

// 自定义处理函数
export function handleFreeInternetWebDirectorySubmission(data, _rule) {
  console.log('Processing Free Internet Web Directory form submission...');

  const processedData = { ...data };

  // 确保URL格式正确
  if (processedData.siteUrl && !processedData.siteUrl.startsWith('http')) {
    processedData.siteUrl = 'https://' + processedData.siteUrl;
  }

  // 处理字符限制
  if (processedData.detailedIntro && processedData.detailedIntro.length > 500) {
    processedData.detailedIntro = processedData.detailedIntro.substring(0, 500);
  }

  if (processedData.siteDescription && processedData.siteDescription.length > 250) {
    processedData.siteDescription = processedData.siteDescription.substring(0, 250);
  }

  // 设置默认值
  processedData.linkType = 'normal'; // Regular links

  return processedData;
}

// 互联网目录信息提醒
export function showFreeInternetWebDirectoryInfo() {
  console.log('🌐 Free Internet Web Directory 信息:');
  console.log('');
  console.log('平台特色:');
  console.log('- 免费的互联网网站目录');
  console.log('- 专注于互联网相关网站');
  console.log('- 包含META字段优化');
  console.log('- 支持互惠链接选项');
  console.log('');
  console.log('提交选项 (3种):');
  console.log('1. Featured links - $6.97/年');
  console.log('   - 1-2天审核');
  console.log('   - 快速审核');
  console.log('');
  console.log('2. Regular links - 免费 ✅ 默认选择');
  console.log('   - 2-3个月审核');
  console.log('   - 标准审核流程');
  console.log('');
  console.log('3. Regular links with reciprocal - 免费');
  console.log('   - 1-2周审核');
  console.log('   - 需要在首页或index页面放置互惠链接');
  console.log('');
  console.log('字段特点:');
  console.log('- 包含META关键词和描述');
  console.log('- 描述限制500字符');
  console.log('- META描述限制250字符');
  console.log('- 支持SEO优化');
  console.log('');
  console.log('Free Internet Web Directory - 免费的互联网网站目录！');
}

// 互惠链接详细说明
export function showReciprocalLinkDetails() {
  console.log('🔗 互惠链接详细说明:');
  console.log('');
  console.log('互惠链接要求:');
  console.log('- 必须放置在首页（home page）');
  console.log('- 或者放置在index页面');
  console.log('- 链接必须可见且可点击');
  console.log('- 使用指定的HTML代码');
  console.log('');
  console.log('审核优势:');
  console.log('- 审核时间大幅缩短（1-2周 vs 2-3个月）');
  console.log('- 提高审核通过率');
  console.log('- 建立双向链接关系');
  console.log('- 增加网站权重');
  console.log('');
  console.log('互惠链接代码示例:');
  console.log('<a href="https://www.freeinternetwebdirectory.com/">Free Internet Web Directory</a>');
  console.log('');
  console.log('注意：互惠链接是可选的，但能显著缩短审核时间！');
}

// 审核时间对比
export function showReviewTimeComparison() {
  console.log('⏰ 审核时间对比:');
  console.log('');
  console.log('Free Internet Web Directory:');
  console.log('- Featured links: 1-2天');
  console.log('- Regular links: 2-3个月');
  console.log('- Reciprocal links: 1-2周');
  console.log('');
  console.log('其他目录对比:');
  console.log('- Australia Web: 几小时 / 3-4个月 / 2-3个月');
  console.log('- All States USA: 24小时 / 8-10周');
  console.log('- Free PR Web: 24-48小时 / 3-4个月');
  console.log('- Free Internet: 1-2天 / 2-3个月 / 1-2周');
  console.log('');
  console.log('优势分析:');
  console.log('- 付费选项审核时间适中');
  console.log('- 互惠链接选项性价比高');
  console.log('- 免费选项审核时间合理');
}

// 价格对比分析
export function showPriceComparison() {
  console.log('💰 价格对比分析:');
  console.log('');
  console.log('目录网站价格排序:');
  console.log('1. All States USA: $6.79 (最便宜)');
  console.log('2. Free Internet: $6.97 (第二便宜) ✅');
  console.log('3. Free PR Web: $9.99/年');
  console.log('4. Australia Web: $12.95/年 (最贵)');
  console.log('');
  console.log('性价比分析:');
  console.log('- Free Internet: 价格低，审核时间适中');
  console.log('- 有互惠链接选项，免费且快速');
  console.log('- 适合预算有限的用户');
  console.log('- 互联网相关网站的好选择');
}

// 表单验证
export function validateFreeInternetWebDirectoryForm() {
  console.log('验证Free Internet Web Directory表单...');

  const requiredFields = [
    { selector: 'input[name="TITLE"]', label: '网站标题' },
    { selector: 'input[name="OWNER_NAME"]', label: '您的姓名' },
    { selector: 'input[name="OWNER_EMAIL"]', label: '您的邮箱' }
  ];

  let isValid = true;

  requiredFields.forEach(field => {
    const element = document.querySelector(field.selector);
    if (!element || !element.value.trim()) {
      console.log(`⚠️ 必填字段为空: ${field.label}`);
      isValid = false;
    }
  });

  // 检查定价选项
  const radioButtons = document.querySelectorAll('input[name="LINK_TYPE"]:checked');
  if (radioButtons.length === 0) {
    console.log('⚠️ 请选择定价选项');
    isValid = false;
  }

  // 检查字符限制
  const description = document.querySelector('textarea[name="DESCRIPTION"]');
  if (description && description.value.length > 500) {
    console.log('⚠️ 描述超过500字符限制');
  }

  const metaDescription = document.querySelector('textarea[name="META_DESCRIPTION"]');
  if (metaDescription && metaDescription.value.length > 250) {
    console.log('⚠️ META描述超过250字符限制');
  }

  if (isValid) {
    console.log('✓ 表单验证通过');
  }

  return isValid;
}

// 互联网网站类型
export function showInternetWebsiteTypes() {
  console.log('💻 适合互联网目录的网站类型:');
  console.log('');
  console.log('技术服务:');
  console.log('- 网站开发公司');
  console.log('- 软件开发服务');
  console.log('- 云服务提供商');
  console.log('- 域名注册商');
  console.log('');
  console.log('在线工具:');
  console.log('- 网站分析工具');
  console.log('- SEO优化工具');
  console.log('- 在线设计工具');
  console.log('- 代码编辑器');
  console.log('');
  console.log('数字服务:');
  console.log('- 电子商务平台');
  console.log('- 在线支付服务');
  console.log('- 数字营销工具');
  console.log('- 社交媒体平台');
  console.log('');
  console.log('教育资源:');
  console.log('- 在线学习平台');
  console.log('- 编程教程网站');
  console.log('- 技术博客');
  console.log('- 开发者社区');
}

// 目录网站完整对比
export function showCompleteDirectoryComparison() {
  console.log('📊 目录网站完整对比:');
  console.log('');
  console.log('已配置的目录网站 (4个):');
  console.log('1. australiawebdirectory.net - 澳大利亚目录');
  console.log('2. allstatesusadirectory.com - 美国各州目录');
  console.log('3. freeprwebdirectory.com - PR公关目录');
  console.log('4. freeinternetwebdirectory.com - 互联网目录 ✅ 当前');
  console.log('');
  console.log('定位差异:');
  console.log('- 地理定位: 澳大利亚、美国');
  console.log('- 行业定位: PR公关、互联网');
  console.log('- 通用 vs 专业');
  console.log('');
  console.log('价格排序:');
  console.log('1. $6.79 - All States USA');
  console.log('2. $6.97 - Free Internet ✅');
  console.log('3. $9.99 - Free PR Web');
  console.log('4. $12.95 - Australia Web');
  console.log('');
  console.log('特色功能:');
  console.log('- Australia Web: 互惠链接');
  console.log('- All States USA: META字段');
  console.log('- Free PR Web: PR专业定位');
  console.log('- Free Internet: 互惠链接 + 低价格 ✅');
}

// 最佳提交策略
export function showBestSubmissionStrategy() {
  console.log('🎯 最佳提交策略:');
  console.log('');
  console.log('免费策略:');
  console.log('1. 选择Regular links with reciprocal');
  console.log('2. 在首页添加互惠链接');
  console.log('3. 1-2周内完成审核');
  console.log('4. 性价比最高');
  console.log('');
  console.log('付费策略:');
  console.log('1. 选择Featured links ($6.97)');
  console.log('2. 1-2天快速审核');
  console.log('3. 价格相对便宜');
  console.log('4. 适合紧急需求');
  console.log('');
  console.log('建议:');
  console.log('- 互联网相关网站优先选择此目录');
  console.log('- 考虑互惠链接选项');
  console.log('- 价格合理，性价比高');
  console.log('- 审核时间适中');
}
