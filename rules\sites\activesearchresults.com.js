// activesearchresults.com 网站规则配置
// 网站: https://www.activesearchresults.com/addwebsite.php
// 表单技术: Simple HTML Form
// 最后更新: 2025-07-08

export const SITE_RULE = {
  // 基本信息
  domain: 'activesearchresults.com',
  siteName: 'Active Search Results',
  priority: 1,
  lastUpdated: '2025-07-08',
  
  // 字段映射规则
  fieldMappings: {
    // 网站地址 -> URL
    siteUrl: {
      selectors: [
        'input[name="url"]',
        'input[placeholder*="Web Site Address"]',
        'form input[type="text"]:first-of-type',
        'input:first-of-type'
      ],
      method: 'value',
      validation: 'required|url',
      notes: '网站地址，使用website-info.js中的siteUrl字段'
    },
    
    // 邮箱地址 -> Email
    contactEmail: {
      selectors: [
        'input[name="email"]',
        'input[placeholder*="Valid Email Address"]',
        'input[type="email"]',
        'form input[type="text"]:last-of-type'
      ],
      method: 'value',
      validation: 'required|email',
      notes: '邮箱地址，使用website-info.js中的contactEmail字段'
    }
  },
  
  // 自定义填写函数
  customFillElement: async function(element, value, config) {
    console.log(`Active Search Results自定义填写: ${element.name || element.placeholder}, 方法: ${config.method}`);
    
    switch (config.method) {
      case 'value':
        // 标准输入框处理
        element.focus();
        await new Promise(resolve => setTimeout(resolve, 100));
        
        // 清空现有内容
        element.value = '';
        await new Promise(resolve => setTimeout(resolve, 50));
        
        // 设置新值
        element.value = value;
        
        // 触发事件
        element.dispatchEvent(new Event('input', { bubbles: true }));
        element.dispatchEvent(new Event('change', { bubbles: true }));
        element.dispatchEvent(new Event('blur', { bubbles: true }));
        
        console.log(`✓ 填写字段: ${element.name} = "${value}"`);
        break;
        
      default:
        element.value = value;
        element.dispatchEvent(new Event('input', { bubbles: true }));
        element.dispatchEvent(new Event('change', { bubbles: true }));
    }
  },
  
  // 提交流程配置
  submitConfig: {
    submitButton: [
      'input[type="submit"]',
      'button[type="submit"]',
      'input[value*="Submit"]',
      'form input[type="submit"]'
    ],
    submitMethod: 'click',
    waitAfterFill: 1000,
    waitAfterSubmit: 3000,
    successIndicators: [
      'text:contains("submitted")',
      'text:contains("added")',
      'text:contains("indexed")',
      'text:contains("thank you")'
    ],
    errorIndicators: [
      'text:contains("error")',
      'text:contains("invalid")',
      'text:contains("required")'
    ]
  },
  
  // 特殊处理
  specialHandling: {
    requiresLogin: false,
    hasCaptcha: false,
    hasFileUpload: false,
    isSimpleForm: true, // 极简表单
    isSearchEngineSubmission: true, // 搜索引擎提交
    supportsGetAndPost: true, // 支持GET和POST请求
    
    // 表单验证规则
    formValidation: {
      requiredFields: ['siteUrl', 'contactEmail'],
      emailValidation: true,
      urlValidation: true,
      minimalFields: true // 只有两个字段
    },
    
    // 特殊注意事项
    notes: [
      '这是Active Search Results搜索引擎的网站提交表单',
      '只有2个字段：URL和Email，都是必填',
      '支持GET和POST请求方式',
      '字段名称：url和email',
      '无需注册或激活过程',
      '只需要有效的URL即可开始索引',
      '专注于搜索引擎收录服务',
      '有索引时间表说明',
      '提供ASR排名优化建议',
      '面向搜索引擎提交公司的API'
    ]
  }
};

// 自定义处理函数
export function handleActiveSearchResultsSubmission(data, _rule) {
  console.log('Processing Active Search Results form submission...');

  const processedData = { ...data };

  // 确保URL格式正确
  if (processedData.siteUrl && !processedData.siteUrl.startsWith('http')) {
    processedData.siteUrl = 'https://' + processedData.siteUrl;
  }

  // 验证邮箱格式
  if (processedData.contactEmail && !processedData.contactEmail.includes('@')) {
    console.log('⚠️ 邮箱格式可能不正确');
  }

  return processedData;
}

// 简单表单检测
export async function detectSimpleForm() {
  console.log('检测简单表单...');
  
  // 检查表单字段数量
  const inputs = document.querySelectorAll('input[type="text"], input[type="email"], input[type="url"]');
  console.log(`检测到 ${inputs.length} 个输入字段`);
  
  if (inputs.length === 2) {
    console.log('✓ 确认为双字段简单表单');
    return true;
  }
  
  return false;
}

// URL验证函数
export function validateUrl(url) {
  console.log(`验证URL: ${url}`);
  
  if (!url) {
    console.log('⚠️ URL不能为空');
    return false;
  }
  
  // 基本URL格式检查
  const urlPattern = /^https?:\/\/.+\..+/;
  if (!urlPattern.test(url)) {
    console.log('⚠️ URL格式不正确');
    return false;
  }
  
  console.log('✓ URL格式正确');
  return true;
}

// 邮箱验证函数
export function validateEmail(email) {
  console.log(`验证邮箱: ${email}`);
  
  if (!email) {
    console.log('⚠️ 邮箱不能为空');
    return false;
  }
  
  // 基本邮箱格式检查
  const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  if (!emailPattern.test(email)) {
    console.log('⚠️ 邮箱格式不正确');
    return false;
  }
  
  console.log('✓ 邮箱格式正确');
  return true;
}

// 搜索引擎提交提醒
export function showSearchEngineSubmissionInfo() {
  console.log('🔍 Active Search Results 提交信息:');
  console.log('');
  console.log('提交完成后:');
  console.log('- 您的网站将被添加到ASR搜索引擎');
  console.log('- 请查看索引时间表了解收录进度');
  console.log('- 可以通过活动获得更高的ASR排名');
  console.log('- 无需注册或激活过程');
  console.log('- 只需要有效的URL即可开始索引');
  console.log('');
  console.log('ASR专注于为网站提供搜索引擎收录服务！');
}

// API信息提醒
export function showApiInfo() {
  console.log('🔧 API信息:');
  console.log('');
  console.log('对于搜索引擎提交公司:');
  console.log('- 支持GET和POST请求');
  console.log('- 字段名称: url 和 email');
  console.log('- 无需登录或激活');
  console.log('- 联系方式: <EMAIL>');
  console.log('');
  console.log('适合批量提交和API集成！');
}
