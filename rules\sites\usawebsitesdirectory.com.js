// usawebsitesdirectory.com 网站规则配置
// 网站: https://www.usawebsitesdirectory.com/submit.php
// 表单技术: PHP Form with CAPTCHA
// 最后更新: 2025-07-08

export const SITE_RULE = {
  // 基本信息
  domain: 'usawebsitesdirectory.com',
  siteName: 'USA Websites Directory',
  priority: 1,
  lastUpdated: '2025-07-08',
  
  // 字段映射规则
  fieldMappings: {
    // 定价选项 -> Pricing
    linkType: {
      selectors: [
        'input[name="LINK_TYPE"][value="normal"]',
        'input[type="radio"][value="normal"]',
        'input[value="normal"]'
      ],
      method: 'radio',
      validation: 'required',
      defaultValue: 'normal',
      availableOptions: ['featured', 'normal', 'reciprocal'],
      notes: '定价选项，默认选择normal (Regular links，免费，2-3个月审核，通过率低)'
    },
    
    // 标题 -> Title
    siteName: {
      selectors: [
        'input[name="TITLE"]',
        'td.field input',
        'input[maxlength="100"]',
        'input.text:first-of-type'
      ],
      method: 'value',
      validation: 'required',
      notes: '网站标题，使用website-info.js中的siteName字段'
    },
    
    // URL -> URL
    siteUrl: {
      selectors: [
        'input[name="URL"]',
        'input[maxlength="255"]',
        'input.text:nth-of-type(2)',
        'input[size="40"]:nth-of-type(2)'
      ],
      method: 'value',
      validation: 'optional|url',
      notes: '网站URL，使用website-info.js中的siteUrl字段'
    },
    
    // 描述 -> Description
    detailedIntro: {
      selectors: [
        'textarea[name="DESCRIPTION"]',
        'textarea.text:first-of-type',
        'textarea[rows="3"]',
        'textarea[cols="37"]'
      ],
      method: 'value',
      validation: 'optional',
      maxLength: 500,
      notes: '网站详细描述，使用website-info.js中的detailedIntro字段，限制500字符'
    },
    
    // META关键词 -> META Keywords
    keywords: {
      selectors: [
        'input[name="META_KEYWORDS"]',
        'input[maxlength="2000"]',
        'input.text:nth-of-type(3)',
        'input[size="40"]:nth-of-type(3)'
      ],
      method: 'value',
      validation: 'optional',
      notes: 'META关键词，使用website-info.js中的keywords字段'
    },
    
    // META描述 -> META Description
    siteDescription: {
      selectors: [
        'textarea[name="META_DESCRIPTION"]',
        'textarea.text:nth-of-type(2)',
        'textarea[rows="3"]:nth-of-type(2)',
        'textarea[cols="30"]'
      ],
      method: 'value',
      validation: 'optional',
      maxLength: 250,
      notes: 'META描述，使用website-info.js中的siteDescription字段，限制250字符'
    },
    
    // 您的姓名 -> Your Name
    fullName: {
      selectors: [
        'input[name="OWNER_NAME"]',
        'input[maxlength="50"]',
        'input.text:nth-of-type(4)',
        'input[size="40"]:nth-of-type(4)'
      ],
      method: 'value',
      validation: 'required',
      notes: '您的姓名，使用website-info.js中的fullName字段'
    },
    
    // 您的邮箱 -> Your Email
    contactEmail: {
      selectors: [
        'input[name="OWNER_EMAIL"]',
        'input.text:nth-of-type(5)',
        'input[size="40"]:nth-of-type(5)',
        'input[maxlength="255"]:nth-of-type(2)'
      ],
      method: 'value',
      validation: 'required|email',
      notes: '您的邮箱，使用website-info.js中的contactEmail字段'
    }
  },
  
  // 自定义填写函数
  customFillElement: async function(element, value, config) {
    console.log(`USA Websites Directory自定义填写: ${element.name || element.type}, 方法: ${config.method}`);
    
    switch (config.method) {
      case 'value':
        // 标准输入框和文本域处理
        element.focus();
        await new Promise(resolve => setTimeout(resolve, 100));
        
        // 清空现有内容
        element.value = '';
        await new Promise(resolve => setTimeout(resolve, 50));
        
        // 处理字符限制
        let finalValue = value;
        if (config.maxLength && finalValue.length > config.maxLength) {
          finalValue = finalValue.substring(0, config.maxLength);
          console.log(`⚠️ 内容被截断到${config.maxLength}字符: ${finalValue}`);
        }
        
        // 设置新值
        element.value = finalValue;
        
        // 触发事件
        element.dispatchEvent(new Event('input', { bubbles: true }));
        element.dispatchEvent(new Event('change', { bubbles: true }));
        element.dispatchEvent(new Event('blur', { bubbles: true }));
        
        console.log(`✓ 填写字段: ${element.name} = "${finalValue.substring(0, 50)}..."`);
        break;
        
      case 'radio':
        // 单选按钮处理
        console.log(`处理定价选项，目标值: ${config.defaultValue}`);
        
        // 查找所有同名单选按钮
        const radioButtons = document.querySelectorAll('input[name="LINK_TYPE"]');
        
        // 先取消所有选择
        radioButtons.forEach(rb => {
          rb.checked = false;
        });
        
        // 选择目标选项
        const targetRadio = Array.from(radioButtons).find(rb => 
          rb.value === config.defaultValue
        );
        
        if (targetRadio) {
          targetRadio.checked = true;
          targetRadio.dispatchEvent(new Event('change', { bubbles: true }));
          console.log(`✓ 选择定价选项: Regular links (${config.defaultValue})`);
        } else {
          console.log(`⚠️ 未找到定价选项: ${config.defaultValue}`);
        }
        break;
        
      default:
        element.value = value;
        element.dispatchEvent(new Event('input', { bubbles: true }));
        element.dispatchEvent(new Event('change', { bubbles: true }));
    }
  },
  
  // 提交流程配置
  submitConfig: {
    submitButton: [
      'button[type="submit"]',
      'input[type="submit"]',
      'button:contains("Submit")',
      'input[value*="Submit"]'
    ],
    submitMethod: 'click',
    waitAfterFill: 2000,
    waitAfterSubmit: 5000,
    successIndicators: [
      'text:contains("submitted")',
      'text:contains("thank you")',
      'text:contains("success")',
      'text:contains("approved")'
    ],
    errorIndicators: [
      'text:contains("error")',
      'text:contains("required")',
      'text:contains("invalid")',
      'text:contains("captcha")'
    ]
  },
  
  // 特殊处理
  specialHandling: {
    requiresLogin: false,
    hasCaptcha: true, // 可能有验证码
    hasFileUpload: false,
    isPHPForm: true, // PHP表单
    isUSADirectory: true, // 美国目录
    isWebHostingDirectory: true, // 网站托管目录
    hasMetaFields: true, // 有META字段
    hasPaidOptions: true, // 有付费选项
    hasReciprocalOption: true, // 有互惠链接选项
    hasHumanEditor: true, // 人工编辑审核
    hasLowApprovalRate: true, // 通过率低
    hasCharacterLimits: true, // 有字符限制
    
    // 表单验证规则
    formValidation: {
      requiredFields: ['linkType', 'siteName', 'fullName', 'contactEmail'],
      optionalFields: ['siteUrl', 'detailedIntro', 'keywords', 'siteDescription'],
      emailValidation: true,
      urlValidation: true,
      characterLimits: {
        detailedIntro: 500,
        siteDescription: 250
      },
      radioGroups: ['linkType']
    },
    
    // 特殊注意事项
    notes: [
      '这是USA Websites Directory的网站提交表单',
      '表单包含8个字段：4个必填，4个可选',
      '美国网站目录，专注于网站托管相关',
      '可能有验证码保护，需要手动处理',
      '默认选择Regular links（免费，2-3个月审核，通过率低）',
      '有付费选项：Featured links $6.85/年（24小时内审核，保证通过）',
      '有互惠链接选项：Regular links with reciprocal（4-8周审核）',
      '包含META字段：关键词和描述',
      '描述限制500字符，META描述限制250字符',
      '使用实际字段名：LINK_TYPE, TITLE, URL, DESCRIPTION, META_KEYWORDS, META_DESCRIPTION, OWNER_NAME, OWNER_EMAIL',
      '人工编辑审核，按队列顺序处理',
      '专注于美国网站和网站托管服务',
      '免费选项通过率较低，需要耐心等待',
      '互惠链接需要在首页或index页面'
    ]
  }
};

// 自定义处理函数
export function handleUSAWebsitesDirectorySubmission(data, _rule) {
  console.log('Processing USA Websites Directory form submission...');

  const processedData = { ...data };

  // 确保URL格式正确
  if (processedData.siteUrl && !processedData.siteUrl.startsWith('http')) {
    processedData.siteUrl = 'https://' + processedData.siteUrl;
  }

  // 处理字符限制
  if (processedData.detailedIntro && processedData.detailedIntro.length > 500) {
    processedData.detailedIntro = processedData.detailedIntro.substring(0, 500);
  }

  if (processedData.siteDescription && processedData.siteDescription.length > 250) {
    processedData.siteDescription = processedData.siteDescription.substring(0, 250);
  }

  // 设置默认值
  processedData.linkType = 'normal'; // Regular links

  return processedData;
}

// USA Websites Directory信息提醒
export function showUSAWebsitesDirectoryInfo() {
  console.log('🇺🇸 USA Websites Directory 信息:');
  console.log('');
  console.log('平台特色:');
  console.log('- 美国网站目录');
  console.log('- 专注于网站托管相关服务');
  console.log('- 人工编辑审核');
  console.log('- 包含META字段优化');
  console.log('- 支持互惠链接选项');
  console.log('');
  console.log('提交选项 (3种):');
  console.log('1. Featured links - $6.85/年');
  console.log('   - 24小时内审核');
  console.log('   - 保证通过');
  console.log('   - 价格适中');
  console.log('');
  console.log('2. Regular links - 免费 ✅ 默认选择');
  console.log('   - 2-3个月审核');
  console.log('   - 人工编辑审核');
  console.log('   - 按队列顺序处理');
  console.log('   - 通过率较低 ⚠️');
  console.log('');
  console.log('3. Regular links with reciprocal - 免费');
  console.log('   - 4-8周审核');
  console.log('   - 需要在首页或index页面放置互惠链接');
  console.log('   - 免费快速选项');
  console.log('');
  console.log('字段特点:');
  console.log('- 包含META关键词和描述');
  console.log('- 描述限制500字符');
  console.log('- META描述限制250字符');
  console.log('- 支持SEO优化');
  console.log('');
  console.log('USA Websites Directory - 美国专业网站托管目录！');
}

// 网站托管目录特点
export function showWebHostingDirectoryFeatures() {
  console.log('🌐 网站托管目录特点:');
  console.log('');
  console.log('专业定位:');
  console.log('- 专注于网站托管服务');
  console.log('- 收录托管相关网站');
  console.log('- 服务器和域名服务');
  console.log('- 网站建设和维护');
  console.log('');
  console.log('适合的网站类型:');
  console.log('- 网站托管公司');
  console.log('- 域名注册商');
  console.log('- 服务器提供商');
  console.log('- 网站建设公司');
  console.log('- CDN服务商');
  console.log('- SSL证书提供商');
  console.log('');
  console.log('行业优势:');
  console.log('- 目标用户精准');
  console.log('- 行业权威性高');
  console.log('- 商业价值明显');
  console.log('- 转化率较高');
  console.log('');
  console.log('SEO价值:');
  console.log('- 行业相关性强');
  console.log('- 链接质量高');
  console.log('- 提升专业形象');
  console.log('- 增加行业曝光');
}

// 人工编辑审核说明
export function showHumanEditorReview() {
  console.log('👨‍💼 人工编辑审核说明:');
  console.log('');
  console.log('审核特点:');
  console.log('- 人工编辑逐一审核');
  console.log('- 按提交队列顺序处理');
  console.log('- 审核标准较为严格');
  console.log('- 质量要求较高');
  console.log('');
  console.log('审核流程:');
  console.log('1. 提交进入审核队列');
  console.log('2. 编辑按顺序审核');
  console.log('3. 检查网站质量和内容');
  console.log('4. 决定是否收录');
  console.log('');
  console.log('通过率低的原因:');
  console.log('- 质量标准严格');
  console.log('- 竞争激烈');
  console.log('- 人工审核主观性');
  console.log('- 行业相关性要求');
  console.log('');
  console.log('提高通过率建议:');
  console.log('- 确保网站内容完整');
  console.log('- 提高网站专业性');
  console.log('- 优化用户体验');
  console.log('- 添加详细的联系信息');
  console.log('- 确保网站稳定运行');
}

// 美国目录系列对比
export function showUSADirectorySeriesComparison() {
  console.log('🇺🇸 美国目录系列对比:');
  console.log('');
  console.log('美国相关目录 (2个):');
  console.log('1. allstatesusadirectory.com - 美国各州目录');
  console.log('2. usawebsitesdirectory.com - 美国网站目录 ✅ 新增');
  console.log('');
  console.log('定位差异:');
  console.log('- All States USA: 地理定位（各州）');
  console.log('- USA Websites: 行业定位（网站托管）');
  console.log('');
  console.log('价格对比:');
  console.log('- All States USA: $6.79');
  console.log('- USA Websites: $6.85/年');
  console.log('- 价格相近，差异很小');
  console.log('');
  console.log('审核时间对比:');
  console.log('- All States USA: 24小时 / 8-10周');
  console.log('- USA Websites: 24小时 / 2-3个月');
  console.log('');
  console.log('特色功能对比:');
  console.log('- All States USA: META字段');
  console.log('- USA Websites: META字段 + 托管专业');
  console.log('');
  console.log('选择建议:');
  console.log('- 托管相关网站: 优先选择USA Websites');
  console.log('- 通用网站: 可选择All States USA');
  console.log('- 预算充足: 两个都提交');
}

// 表单验证
export function validateUSAWebsitesDirectoryForm() {
  console.log('验证USA Websites Directory表单...');

  const requiredFields = [
    { selector: 'input[name="TITLE"]', label: '网站标题' },
    { selector: 'input[name="OWNER_NAME"]', label: '您的姓名' },
    { selector: 'input[name="OWNER_EMAIL"]', label: '您的邮箱' }
  ];

  let isValid = true;

  requiredFields.forEach(field => {
    const element = document.querySelector(field.selector);
    if (!element || !element.value.trim()) {
      console.log(`⚠️ 必填字段为空: ${field.label}`);
      isValid = false;
    }
  });

  // 检查定价选项
  const radioButtons = document.querySelectorAll('input[name="LINK_TYPE"]:checked');
  if (radioButtons.length === 0) {
    console.log('⚠️ 请选择定价选项');
    isValid = false;
  }

  // 检查字符限制
  const description = document.querySelector('textarea[name="DESCRIPTION"]');
  if (description && description.value.length > 500) {
    console.log('⚠️ 描述超过500字符限制');
  }

  const metaDescription = document.querySelector('textarea[name="META_DESCRIPTION"]');
  if (metaDescription && metaDescription.value.length > 250) {
    console.log('⚠️ META描述超过250字符限制');
  }

  if (isValid) {
    console.log('✓ 表单验证通过');
  }

  return isValid;
}

// 目录网站完整统计
export function showCompleteDirectoryStatistics() {
  console.log('📊 目录网站完整统计 (8个):');
  console.log('');
  console.log('地理分布:');
  console.log('🇦🇺 澳大利亚: 1个 (australiawebdirectory.net)');
  console.log('🇺🇸 美国: 2个 (allstatesusadirectory.com, usawebsitesdirectory.com)');
  console.log('🇬🇧 英国: 1个 (ukinternetdirectory.net)');
  console.log('🌐 全球: 4个 (专业目录)');
  console.log('');
  console.log('专业分类:');
  console.log('📍 地理目录: 4个 (澳洲、美国x2、英国)');
  console.log('🎯 专业目录: 4个 (PR、互联网、信息、质量)');
  console.log('');
  console.log('价格分布:');
  console.log('💰 $5-7: 4个 (最经济)');
  console.log('💰 $7-10: 2个 (中等价位)');
  console.log('💰 $10+: 1个 (高端)');
  console.log('💰 终身: 1个 (长期投资)');
  console.log('');
  console.log('审核时间分布:');
  console.log('⚡ 1-3个月: 4个');
  console.log('⚡ 3-5个月: 3个');
  console.log('⚡ 5个月+: 1个');
  console.log('');
  console.log('特色功能统计:');
  console.log('🔗 互惠链接: 4个');
  console.log('📝 META字段: 6个');
  console.log('💰 付费选项: 8个');
  console.log('🤖 人工审核: 8个');
}
