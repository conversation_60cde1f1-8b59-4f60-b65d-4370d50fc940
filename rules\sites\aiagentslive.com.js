// AIAgentsLive.com 网站规则配置
// 网站: https://aiagentslive.com/agents/products/new
// 最后更新: 2025-07-09

export const SITE_RULE = {
  // 基本信息
  domain: 'aiagentslive.com',
  siteName: 'AIAgentsLive',
  priority: 1,
  lastUpdated: '2025-07-09',
  
  // 字段映射规则
  fieldMappings: {
    // 您的姓名 -> Your name
    fullName: {
      selectors: [
        'input[name="agent_product[maker_name]"]',
        '#agent_product_maker_name',
        'input[placeholder="Your full name"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '提交者姓名'
    },
    
    // 您的邮箱 -> Your email
    contactEmail: {
      selectors: [
        'input[name="agent_product[maker_email]"]',
        '#agent_product_maker_email',
        'input[placeholder="<EMAIL>"]',
        'input[type="email"]'
      ],
      method: 'value',
      validation: 'required|email',
      notes: '联系邮箱'
    },
    
    // 列表类型 -> To list as (选择AI Agent)
    category: {
      selectors: [
        'select[name="agent_product[listed_as]"]',
        '#agent_product_listed_as'
      ],
      method: 'select',
      validation: 'required',
      targetValue: 'ai_agent',
      defaultValue: 'ai_agent',
      notes: '列表类型，选择AI Agent'
    },
    
    // AI代理名称 -> AI Agent Name
    siteName: {
      selectors: [
        'input[name="agent_product[name]"]',
        '#agent_product_name',
        'input[placeholder="AI Agent name"]'
      ],
      method: 'value',
      validation: 'required',
      notes: 'AI代理名称'
    },
    
    // 网站URL -> Website URL
    siteUrl: {
      selectors: [
        'input[name="agent_product[website]"]',
        '#agent_product_website',
        'input[placeholder*="your-agent-website"]',
        'input[type="url"]'
      ],
      method: 'value',
      validation: 'required|url',
      notes: '网站URL地址'
    },
    
    // 标语 -> Tag line
    siteDescription: {
      selectors: [
        'input[name="agent_product[tag_line]"]',
        '#agent_product_tag_line',
        'input[placeholder*="Catchy short line"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '网站简介标语'
    },
    
    // 详细描述 -> Description (Trix编辑器)
    detailedIntro: {
      selectors: [
        'trix-editor#agent_product_description',
        'input#agent_product_description_trix_input_agent_product',
        'trix-editor[input="agent_product_description_trix_input_agent_product"]',
        'trix-editor[placeholder*="Describe your product"]',
        '.disable-trix-file-tools trix-editor'
      ],
      method: 'value',
      validation: 'required',
      notes: '详细产品描述，使用Trix编辑器'
    }
  },
  
  // 提交流程配置
  submitConfig: {
    submitButton: 'button[type="submit"], input[type="submit"], .btn-submit',
    submitMethod: 'click',
    waitAfterFill: 2000, // 填写后等待2秒
    waitAfterSubmit: 3000, // 提交后等待3秒
    successIndicators: [
      '.success-message',
      '.alert-success',
      '.notification-success',
      '[class*="success"]'
    ],
    errorIndicators: [
      '.error-message',
      '.alert-error',
      '.alert-danger',
      '[class*="error"]'
    ]
  },
  
  // 特殊处理
  specialHandling: {
    requiresLogin: false,
    hasCaptcha: false,
    hasFileUpload: false,
    
    // 表单验证规则
    formValidation: {
      requiredFields: ['fullName', 'contactEmail', 'category', 'siteName', 'siteUrl', 'siteDescription', 'detailedIntro'],
      emailValidation: true,
      urlValidation: true
    },
    
    // 特殊注意事项
    notes: [
      '表单包含7个字段：姓名、邮箱、类型、代理名称、网站URL、标语、详细描述',
      '所有字段都是必填的',
      '列表类型自动选择AI Agent',
      '标语最少16字符',
      '详细描述使用Trix富文本编辑器',
      '网站URL需要完整格式'
    ]
  }
};

// 自定义处理函数
export function handleAIAgentsLiveSubmission(data, rule) {
  console.log('Processing AIAgentsLive.com submission...');
  
  // 特殊处理逻辑
  const processedData = { ...data };
  
  // 确保URL格式正确
  if (processedData.siteUrl && !processedData.siteUrl.startsWith('http')) {
    processedData.siteUrl = 'https://' + processedData.siteUrl;
  }
  
  // 确保列表类型设置为AI Agent
  processedData.category = 'ai_agent';
  
  // 处理标语，确保至少16字符
  if (processedData.siteDescription && processedData.siteDescription.length < 16) {
    // 如果太短，使用更详细的描述
    processedData.siteDescription = 'AI-powered username generator that creates unique, memorable usernames instantly';
  }
  
  return processedData;
}

// 自定义元素填写函数，专门处理Trix编辑器
export async function customFillElement(element, value, config) {
  console.log('🔧 AIAgentsLive自定义填写函数被调用:', element, value);

  // 方法1: 如果是Trix编辑器元素
  if (element.tagName === 'TRIX-EDITOR') {
    try {
      // 等待Trix编辑器初始化
      await new Promise(resolve => setTimeout(resolve, 500));

      // 尝试使用Trix editor API
      if (element.editor) {
        element.editor.loadHTML(value);
        console.log('✅ 使用Trix editor.loadHTML填写:', value);
        return true;
      }

      // 备用方法：直接设置内容
      element.innerHTML = value;
      element.dispatchEvent(new Event('trix-change', { bubbles: true }));
      element.dispatchEvent(new Event('input', { bubbles: true }));
      console.log('✅ 使用innerHTML填写Trix编辑器:', value);
      return true;
    } catch (error) {
      console.warn('Trix编辑器填写失败:', error);
    }
  }

  // 方法2: 如果是隐藏的input元素（Trix的数据存储）
  if (element.id === 'agent_product_description_trix_input_agent_product') {
    try {
      element.value = value;
      element.dispatchEvent(new Event('input', { bubbles: true }));
      element.dispatchEvent(new Event('change', { bubbles: true }));

      // 查找对应的trix-editor并更新
      const trixEditor = document.querySelector('trix-editor[input="agent_product_description_trix_input_agent_product"]');
      if (trixEditor && trixEditor.editor) {
        setTimeout(() => {
          trixEditor.editor.loadHTML(value);
        }, 100);
      }

      console.log('✅ 使用隐藏input填写Trix编辑器:', value);
      return true;
    } catch (error) {
      console.warn('隐藏input填写失败:', error);
    }
  }

  // 方法3: 全局查找Trix编辑器
  if (config.notes && config.notes.includes('Trix')) {
    const trixEditor = document.querySelector('trix-editor#agent_product_description');
    if (trixEditor) {
      try {
        await new Promise(resolve => setTimeout(resolve, 500));
        if (trixEditor.editor) {
          trixEditor.editor.loadHTML(value);
          console.log('✅ 使用全局查找的Trix编辑器填写:', value);
          return true;
        }
      } catch (error) {
        console.warn('全局Trix编辑器填写失败:', error);
      }
    }
  }

  // 对于select元素，确保选择正确的值
  if (element.tagName === 'SELECT' && config.targetValue) {
    const option = Array.from(element.options).find(opt =>
      opt.value === config.targetValue
    );
    if (option) {
      element.value = option.value;
      element.dispatchEvent(new Event('change', { bubbles: true }));
      console.log('✅ 选择下拉选项:', option.text);
      return true;
    }
  }

  // 默认处理
  return false;
}
