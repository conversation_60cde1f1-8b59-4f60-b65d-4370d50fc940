// allaitools.tech 网站规则配置
// 网站: https://www.allaitools.tech/submit-tool
// 表单技术: 现代Web表单
// 最后更新: 2025-07-07

export const SITE_RULE = {
  // 基本信息
  domain: 'allaitools.tech',
  siteName: 'Allaitools',
  priority: 1,
  lastUpdated: '2025-07-07',
  
  // 字段映射规则
  fieldMappings: {
    // AI工具名称 -> AI Tool Name
    siteName: {
      selectors: [
        'input[name="toolName"]',
        'input[id*=":r0:-form-item"]',
        'input[placeholder*="AI tool name"]',
        'input[placeholder*="tool name"]'
      ],
      method: 'value',
      validation: 'required',
      notes: 'AI工具名称，使用website-info.js中的siteName字段'
    },

    // 网站URL -> Website URL
    siteUrl: {
      selectors: [
        'input[name="website"]',
        'input[id*=":r1:-form-item"]',
        'input[placeholder*="https://your-ai-tool.com"]'
      ],
      method: 'value',
      validation: 'required|url',
      notes: '网站URL，使用website-info.js中的siteUrl字段'
    },

    // AI工具分类 -> AI Tool Category
    category: {
      selectors: [
        'button[role="combobox"][id*=":r2:-form-item"]',
        'select[aria-hidden="true"] option[value="Design"]',
        'button:contains("Select category")'
      ],
      method: 'combobox',
      validation: 'required',
      defaultValue: 'Design',
      notes: 'AI工具分类，默认选择Design & Creative Tools'
    },

    // 定价模式 -> Pricing Model
    pricing: {
      selectors: [
        'button[role="combobox"][id*=":r4:-form-item"]',
        'select[aria-hidden="true"] option[value="Free"]',
        'button:contains("Free - No Cost")'
      ],
      method: 'combobox',
      validation: 'required',
      defaultValue: 'Free',
      notes: '定价模式，默认选择Free - No Cost'
    },

    // 简短描述 -> Short Description
    siteDescription: {
      selectors: [
        'textarea[name="shortDescription"]',
        'textarea[maxlength="200"]',
        'textarea[id*=":r6:-form-item"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '简短描述，使用website-info.js中的siteDescription字段，限制200字符'
    },

    // 详细描述 -> Detailed Description
    detailedIntro: {
      selectors: [
        'textarea[name="description"]',
        'textarea[id*=":r7:-form-item"]',
        'textarea[class*="min-h-[200px]"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '详细描述，使用website-info.js中的detailedIntro字段'
    },

    // 联系邮箱 -> Contact Email
    contactEmail: {
      selectors: [
        'input[name="contactEmail"]',
        'input[type="email"]',
        'input[placeholder*="contact@"]',
        'input[placeholder*="email"]'
      ],
      method: 'value',
      validation: 'required|email',
      notes: '联系邮箱，使用website-info.js中的contactEmail字段'
    },

    // Logo/图标URL -> Logo/Icon URL
    faviconUrl: {
      selectors: [
        'input[name="logoUrl"]',
        'input[id*=":r8:-form-item"]',
        'input[placeholder*="logo.png"]'
      ],
      method: 'value',
      validation: 'optional|url',
      notes: 'Logo/图标URL，使用website-info.js中的faviconUrl字段'
    },

    // 创始人姓名 -> Founder/Creator Name
    fullName: {
      selectors: [
        'input[name="founderName"]',
        'input[id*=":r9:-form-item"]',
        'input[placeholder*="John Smith"]'
      ],
      method: 'value',
      validation: 'optional',
      notes: '创始人姓名，使用website-info.js中的fullName字段'
    },

    // 创始人邮箱 -> Founder/Creator Email
    founderEmail: {
      selectors: [
        'input[name="founderEmail"]',
        'input[id*=":ra:-form-item"]',
        'input[placeholder*="<EMAIL>"]'
      ],
      method: 'value',
      validation: 'optional|email',
      notes: '创始人邮箱，使用website-info.js中的contactEmail字段'
    },

    // 提交者姓名 -> Your Name
    companyName: {
      selectors: [
        'input[name="submitterName"]',
        'input[id*=":rb:-form-item"]',
        'input[placeholder*="Marketing Manager"]'
      ],
      method: 'value',
      validation: 'optional',
      notes: '提交者姓名，使用website-info.js中的companyName字段'
    },

    // 提交者角色 -> Your Role/Position
    submitterRole: {
      selectors: [
        'input[name="submitterRole"]',
        'input[id*=":rc:-form-item"]',
        'input[placeholder*="CEO, Developer"]'
      ],
      method: 'value',
      validation: 'optional',
      notes: '提交者角色，使用website-info.js中的submitterRole字段'
    }
  },
  
  // 自定义填写函数
  customFillElement: async function(element, value, config) {
    console.log(`All AI Tools自定义填写: ${element.name || element.placeholder}, 方法: ${config.method}`);
    
    switch (config.method) {
      case 'value':
        // 标准输入框和文本域处理
        element.focus();
        await new Promise(resolve => setTimeout(resolve, 100));
        
        // 清空现有内容
        element.value = '';
        await new Promise(resolve => setTimeout(resolve, 50));
        
        // 设置新值
        element.value = value;
        
        // 触发事件
        element.dispatchEvent(new Event('input', { bubbles: true }));
        element.dispatchEvent(new Event('change', { bubbles: true }));
        element.dispatchEvent(new Event('blur', { bubbles: true }));
        
        console.log(`✓ 填写字段: ${element.placeholder || element.name} = "${value.substring(0, 50)}..."`);
        break;
        
      case 'combobox':
        // Combobox组件处理（现代UI组件）
        try {
          // 方法1: 直接设置隐藏的select元素
          const hiddenSelect = document.querySelector('select[aria-hidden="true"]');
          if (hiddenSelect) {
            let targetValue = config.defaultValue;

            // 分类映射
            if (element.id && element.id.includes(':r2:')) {
              // AI Tool Category
              const categoryMapping = {
                'AI Tools': 'Design',
                'Developer Tools': 'Code Assistant',
                'Content Creation': 'Text Generation',
                'Image Generation': 'Image Generation',
                'Video Generation': 'Video Generation',
                'Audio Tools': 'Audio Generation',
                'Chatbot': 'Chatbot',
                'Design': 'Design',
                'Marketing': 'Marketing',
                'Productivity': 'Productivity',
                'Education': 'Education',
                'Finance': 'Finance',
                'Healthcare': 'Healthcare',
                'Writing': 'Text Generation'
              };
              targetValue = categoryMapping[value] || 'Design';
            } else if (element.id && element.id.includes(':r4:')) {
              // Pricing Model
              const pricingMapping = {
                'Free': 'Free',
                'Freemium': 'Freemium',
                'Paid': 'Paid',
                'Open Source': 'Open Source'
              };
              targetValue = pricingMapping[value] || 'Free';
            }

            // 查找匹配的选项
            const option = Array.from(hiddenSelect.options).find(opt =>
              opt.value === targetValue || opt.text.includes(targetValue)
            );

            if (option) {
              hiddenSelect.value = option.value;
              hiddenSelect.dispatchEvent(new Event('change', { bubbles: true }));
              console.log(`✓ 选择选项: ${option.text}`);
            }
          }

          // 方法2: 点击combobox按钮打开选项
          if (element.tagName.toLowerCase() === 'button' && element.getAttribute('role') === 'combobox') {
            element.click();
            await new Promise(resolve => setTimeout(resolve, 500));

            // 查找并点击目标选项
            let targetText = config.defaultValue;
            if (config.defaultValue === 'Design') {
              targetText = 'Design & Creative Tools';
            } else if (config.defaultValue === 'Free') {
              targetText = 'Free - No Cost';
            }

            const targetOption = document.querySelector(`[role="option"]:contains("${targetText}"), [data-value="${config.defaultValue}"]`);
            if (targetOption) {
              targetOption.click();
              console.log(`✓ 点击选择: ${targetText}`);
            }
          }
        } catch (error) {
          console.error('Combobox处理出错:', error);
        }
        break;

      case 'select':
        // 标准下拉选择框处理（备用）
        if (element.tagName.toLowerCase() === 'select') {
          const option = Array.from(element.options).find(opt =>
            opt.value === config.defaultValue || opt.text.includes(config.defaultValue)
          );

          if (option) {
            element.value = option.value;
            element.dispatchEvent(new Event('change', { bubbles: true }));
            console.log(`✓ 选择选项: ${option.text}`);
          }
        }
        break;
        
      default:
        element.value = value;
        element.dispatchEvent(new Event('input', { bubbles: true }));
        element.dispatchEvent(new Event('change', { bubbles: true }));
    }
  },

  // 提交流程配置
  submitConfig: {
    submitButton: [
      'button[type="submit"]',
      'input[type="submit"]',
      'button:contains("Submit Tool")',
      'button:contains("Submit")',
      '.submit-button',
      '.submit-tool-button'
    ],
    submitMethod: 'click',
    waitAfterFill: 2000,
    waitAfterSubmit: 5000,
    successIndicators: [
      '.success-message',
      '.thank-you',
      '.confirmation',
      '[class*="success"]',
      'text:contains("submitted")',
      'text:contains("received")',
      'text:contains("thank you")'
    ],
    errorIndicators: [
      '.error-message',
      '.validation-error',
      '[class*="error"]'
    ]
  },

  // 特殊处理
  specialHandling: {
    requiresLogin: false,
    hasCaptcha: false,
    hasFileUpload: false,
    isAIToolsDirectory: true, // AI工具目录网站

    // 表单验证规则
    formValidation: {
      requiredFields: ['siteName', 'siteUrl', 'category', 'pricing', 'siteDescription', 'detailedIntro', 'contactEmail'],
      emailValidation: true,
      urlValidation: true,
      characterLimits: {
        siteDescription: 200
      }
    },

    // 特殊注意事项
    notes: [
      '这是专业的AI工具目录网站，使用现代React UI',
      '表单包含10个字段，7个必填，3个可选',
      '必填字段：AI Tool Name, Website URL, Category, Pricing, Short Description, Detailed Description, Contact Email',
      '可选字段：Logo URL, Founder Name, Founder Email, Submitter Name, Submitter Role',
      '使用Combobox组件进行分类和定价选择',
      '分类默认选择Design & Creative Tools',
      '定价默认选择Free - No Cost',
      '简短描述限制200字符',
      '字段ID格式：:r{number}:-form-item',
      '网站专注于AI工具发现和比较'
    ]
  }
};

// 自定义处理函数
export function handleAllAIToolsSubmission(data, _rule) {
  console.log('Processing All AI Tools form submission...');

  const processedData = { ...data };

  // 确保URL格式正确
  if (processedData.siteUrl && !processedData.siteUrl.startsWith('http')) {
    processedData.siteUrl = 'https://' + processedData.siteUrl;
  }

  // 确保Logo URL格式正确
  if (processedData.faviconUrl && !processedData.faviconUrl.startsWith('http')) {
    processedData.faviconUrl = 'https://' + processedData.faviconUrl;
  }

  // 处理字符限制
  if (processedData.siteDescription && processedData.siteDescription.length > 200) {
    processedData.siteDescription = processedData.siteDescription.substring(0, 200);
  }

  // 设置默认值
  processedData.category = 'Design';
  processedData.pricing = 'Free';

  return processedData;
}
