// once.tools 网站规则配置
// 网站: https://once.tools/submit
// 表单技术: Laravel Livewire
// 最后更新: 2025-07-08

export const SITE_RULE = {
  // 基本信息
  domain: 'once.tools',
  siteName: 'Once Tools',
  priority: 1,
  lastUpdated: '2025-07-08',
  
  // 字段映射规则
  fieldMappings: {
    // 提交者姓名 -> Submitter Name
    fullName: {
      selectors: [
        'input[wire\\:model="submitter_name"]',
        'input[id*="submitter_name"]',
        'input[placeholder*="<PERSON> smith"]',
        'input.input-primary:first-of-type'
      ],
      method: 'value',
      validation: 'required',
      notes: '提交者姓名，使用website-info.js中的fullName字段'
    },
    
    // 提交者邮箱 -> Submitter Email
    contactEmail: {
      selectors: [
        'input[wire\\:model="submitter_email"]',
        'input[id*="submitter_email"]',
        'input[placeholder*="<EMAIL>"]',
        'input.input-primary:nth-of-type(2)'
      ],
      method: 'value',
      validation: 'required|email',
      notes: '提交者邮箱，使用website-info.js中的contactEmail字段'
    },
    
    // 工具名称 -> Tool Name
    siteName: {
      selectors: [
        'input[wire\\:model="name"]',
        'input[id*="name"]',
        'input[placeholder*="Tool name"]',
        'input.input-primary:nth-of-type(3)'
      ],
      method: 'value',
      validation: 'required',
      notes: '工具名称，使用website-info.js中的siteName字段'
    },
    
    // 工具URL -> Tool URL
    siteUrl: {
      selectors: [
        'input[wire\\:model="url"]',
        'input[id*="url"]',
        'input[placeholder*="mytool.com"]',
        'input.input-primary:nth-of-type(4)'
      ],
      method: 'value',
      validation: 'required|url',
      notes: '工具URL，使用website-info.js中的siteUrl字段'
    },
    
    // 简短描述 -> Short Description
    siteTitle: {
      selectors: [
        'input[wire\\:model="short_description"]',
        'input[id*="short_description"]',
        'input[placeholder*="best tool for"]',
        'input.input-primary:nth-of-type(5)'
      ],
      method: 'value',
      validation: 'required',
      notes: '简短描述，使用website-info.js中的siteTitle字段'
    },
    
    // 详细描述 -> Description
    detailedIntro: {
      selectors: [
        'textarea[wire\\:model="description"]',
        'textarea[placeholder*="Some details of tool features"]',
        'textarea.textarea-primary',
        'textarea[rows="5"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '详细描述，使用website-info.js中的detailedIntro字段'
    },
    
    // 价格显示 -> Price Display
    priceDisplay: {
      selectors: [
        'input[wire\\:model="price_display"]',
        'input[id*="price_display"]',
        'input[placeholder*="$49 1 device"]',
        'input.input-primary:nth-of-type(6)'
      ],
      method: 'value',
      validation: 'required',
      defaultValue: 'Free to use',
      notes: '价格显示，使用固定的免费英文文本'
    }
  },
  
  // 自定义填写函数
  customFillElement: async function(element, value, config) {
    console.log(`Once Tools自定义填写: ${element.getAttribute('wire:model') || element.type}, 方法: ${config.method}`);
    
    switch (config.method) {
      case 'value':
        // Livewire表单处理
        element.focus();
        await new Promise(resolve => setTimeout(resolve, 100));
        
        // 清空现有内容
        element.value = '';
        await new Promise(resolve => setTimeout(resolve, 50));
        
        // 设置新值
        element.value = value;
        
        // 触发Livewire事件
        element.dispatchEvent(new Event('input', { bubbles: true }));
        element.dispatchEvent(new Event('change', { bubbles: true }));
        element.dispatchEvent(new Event('blur', { bubbles: true }));
        
        // Livewire特殊处理
        const livewireEvent = new CustomEvent('livewire:update', { 
          detail: { value: value },
          bubbles: true 
        });
        element.dispatchEvent(livewireEvent);
        
        console.log(`✓ 填写字段: ${element.getAttribute('wire:model')} = "${value.substring(0, 50)}..."`);
        break;
        
      default:
        element.value = value;
        element.dispatchEvent(new Event('input', { bubbles: true }));
        element.dispatchEvent(new Event('change', { bubbles: true }));
    }
  },
  
  // 提交流程配置
  submitConfig: {
    submitButton: [
      'button[type="submit"]',
      'button[wire\\:click*="submit"]',
      'button:contains("Submit")',
      'button.btn-primary'
    ],
    submitMethod: 'click',
    waitAfterFill: 3000,
    waitAfterSubmit: 5000,
    successIndicators: [
      'text:contains("submitted")',
      'text:contains("thank you")',
      'text:contains("success")',
      'text:contains("received")'
    ],
    errorIndicators: [
      'text:contains("error")',
      'text:contains("required")',
      'text:contains("invalid")',
      'text:contains("failed")'
    ]
  },
  
  // 特殊处理
  specialHandling: {
    requiresLogin: false,
    hasCaptcha: false, // 无验证码
    hasFileUpload: false,
    isLivewireForm: true, // Laravel Livewire表单
    isToolSubmission: true, // 工具提交
    hasUniqueIds: true, // 有唯一ID
    hasWireModel: true, // 有wire:model属性
    isModernFramework: true, // 现代框架
    
    // 表单验证规则
    formValidation: {
      requiredFields: ['fullName', 'contactEmail', 'siteName', 'siteUrl', 'siteTitle', 'detailedIntro', 'priceDisplay'],
      optionalFields: [],
      emailValidation: true,
      urlValidation: true
    },
    
    // 特殊注意事项
    notes: [
      '这是Once Tools的工具提交表单',
      '表单包含7个字段，全部必填',
      '工具收录和展示平台',
      '使用Laravel Livewire技术',
      '无验证码保护，提交便捷',
      '简短描述使用uniqueSellingPoints字段',
      '价格显示使用固定免费文本：Free to use',
      '使用wire:model进行数据绑定',
      '字段ID包含随机哈希值',
      '现代化的表单设计',
      '专注于工具收录和推广',
      '实时表单验证'
    ]
  }
};

// 自定义处理函数
export function handleOnceToolsSubmission(data, _rule) {
  console.log('Processing Once Tools submission...');

  const processedData = { ...data };

  // 确保URL格式正确
  if (processedData.siteUrl && !processedData.siteUrl.startsWith('http')) {
    processedData.siteUrl = 'https://' + processedData.siteUrl;
  }

  // 设置固定价格显示
  processedData.priceDisplay = 'Free to use';

  return processedData;
}

// Once Tools信息提醒
export function showOnceToolsInfo() {
  console.log('🔧 Once Tools 信息:');
  console.log('');
  console.log('平台特色:');
  console.log('- 工具收录和展示平台');
  console.log('- 使用Laravel Livewire技术');
  console.log('- 现代化界面设计');
  console.log('- 实时表单验证');
  console.log('- 无验证码干扰');
  console.log('');
  console.log('提交要求:');
  console.log('- 提交者姓名（必填）');
  console.log('- 提交者邮箱（必填）');
  console.log('- 工具名称（必填）');
  console.log('- 工具URL（必填）');
  console.log('- 简短描述（必填）');
  console.log('- 详细描述（必填）');
  console.log('- 价格显示（必填）');
  console.log('');
  console.log('字段映射:');
  console.log('- 简短描述 → uniqueSellingPoints ✅');
  console.log('- 价格显示 → "Free to use" ✅');
  console.log('');
  console.log('表单特点:');
  console.log('- 7个字段，全部必填');
  console.log('- 使用wire:model数据绑定');
  console.log('- 字段ID包含随机哈希');
  console.log('- 现代化占位符提示');
  console.log('');
  console.log('Once Tools - 专业的工具收录平台！');
}

// Laravel Livewire特点
export function showLivewireFeatures() {
  console.log('⚡ Laravel Livewire特点:');
  console.log('');
  console.log('技术优势:');
  console.log('- 服务器端渲染');
  console.log('- 实时数据绑定');
  console.log('- 无需JavaScript编写');
  console.log('- 自动CSRF保护');
  console.log('');
  console.log('数据绑定:');
  console.log('- wire:model属性');
  console.log('- 双向数据绑定');
  console.log('- 实时验证');
  console.log('- 自动同步');
  console.log('');
  console.log('事件处理:');
  console.log('- livewire:update事件');
  console.log('- 自动表单处理');
  console.log('- 实时响应');
  console.log('- 无页面刷新');
  console.log('');
  console.log('安全特性:');
  console.log('- 内置CSRF保护');
  console.log('- 服务器端验证');
  console.log('- 数据加密传输');
  console.log('- 防止XSS攻击');
}

// 工具提交平台特点
export function showToolSubmissionPlatformFeatures() {
  console.log('🛠️ 工具提交平台特点:');
  console.log('');
  console.log('Once Tools定位:');
  console.log('- 专业工具收录平台');
  console.log('- 开发者工具展示');
  console.log('- 生产力工具推广');
  console.log('- 创新工具发现');
  console.log('');
  console.log('收录范围:');
  console.log('- 开发工具');
  console.log('- 设计工具');
  console.log('- 生产力工具');
  console.log('- AI工具');
  console.log('- 在线服务');
  console.log('');
  console.log('平台价值:');
  console.log('- 提高工具曝光度');
  console.log('- 获得目标用户');
  console.log('- 建立品牌知名度');
  console.log('- 增加网站流量');
  console.log('');
  console.log('用户群体:');
  console.log('- 开发者');
  console.log('- 设计师');
  console.log('- 产品经理');
  console.log('- 创业者');
}

// 字段映射说明
export function showFieldMappingExplanation() {
  console.log('🔄 字段映射说明:');
  console.log('');
  console.log('特殊字段映射:');
  console.log('1. 简短描述 → uniqueSellingPoints');
  console.log('   - 使用独特卖点数据');
  console.log('   - 突出工具优势');
  console.log('   - 吸引用户注意');
  console.log('');
  console.log('2. 价格显示 → "Free to use"');
  console.log('   - 固定免费文本');
  console.log('   - 英文表述');
  console.log('   - 吸引用户试用');
  console.log('');
  console.log('标准字段映射:');
  console.log('- 提交者姓名 → fullName');
  console.log('- 提交者邮箱 → contactEmail');
  console.log('- 工具名称 → siteName');
  console.log('- 工具URL → siteUrl');
  console.log('- 详细描述 → detailedIntro');
  console.log('');
  console.log('数据来源:');
  console.log('- website-info.js配置文件');
  console.log('- 统一数据管理');
  console.log('- 自动填充表单');
  console.log('- 减少手动输入');
}

// 唯一ID处理说明
export function showUniqueIdHandling() {
  console.log('🆔 唯一ID处理说明:');
  console.log('');
  console.log('ID格式特点:');
  console.log('- 包含前缀：mary');
  console.log('- 包含哈希值：794c4a912931dd30191a36073f14c3aa');
  console.log('- 包含字段名：submitter_name');
  console.log('- 动态生成，每次不同');
  console.log('');
  console.log('处理策略:');
  console.log('- 使用wire:model属性选择器');
  console.log('- 使用部分ID匹配');
  console.log('- 使用占位符文本匹配');
  console.log('- 使用CSS类选择器');
  console.log('');
  console.log('选择器优先级:');
  console.log('1. wire:model属性（最可靠）');
  console.log('2. 部分ID匹配');
  console.log('3. 占位符文本');
  console.log('4. CSS类和位置');
  console.log('');
  console.log('兼容性考虑:');
  console.log('- 适应ID变化');
  console.log('- 多重选择器备份');
  console.log('- 提高匹配成功率');
}

// 表单验证
export function validateOnceToolsForm() {
  console.log('验证Once Tools表单...');

  const requiredFields = [
    { selector: 'input[wire\\:model="submitter_name"]', label: '提交者姓名' },
    { selector: 'input[wire\\:model="submitter_email"]', label: '提交者邮箱' },
    { selector: 'input[wire\\:model="name"]', label: '工具名称' },
    { selector: 'input[wire\\:model="url"]', label: '工具URL' },
    { selector: 'input[wire\\:model="short_description"]', label: '简短描述' },
    { selector: 'textarea[wire\\:model="description"]', label: '详细描述' },
    { selector: 'input[wire\\:model="price_display"]', label: '价格显示' }
  ];

  let isValid = true;

  requiredFields.forEach(field => {
    const element = document.querySelector(field.selector);
    if (!element || !element.value.trim()) {
      console.log(`⚠️ 必填字段为空: ${field.label}`);
      isValid = false;
    }
  });

  // 检查邮箱格式
  const emailField = document.querySelector('input[wire\\:model="submitter_email"]');
  if (emailField && emailField.value && !emailField.value.includes('@')) {
    console.log('⚠️ 邮箱格式可能不正确');
  }

  // 检查URL格式
  const urlField = document.querySelector('input[wire\\:model="url"]');
  if (urlField && urlField.value && !urlField.value.match(/^https?:\/\//)) {
    console.log('⚠️ URL格式可能不正确，建议包含http://或https://');
  }

  if (isValid) {
    console.log('✓ 表单验证通过');
  }

  return isValid;
}

// 现代框架对比
export function showModernFrameworkComparison() {
  console.log('🚀 现代框架对比:');
  console.log('');
  console.log('已配置的表单技术:');
  console.log('1. PHP Form: 8个网站 (传统目录)');
  console.log('2. Vue.js Form: 1个 (inside.thewarehouse.ai)');
  console.log('3. Contact Form 7: 1个 (offpagesavvy.com)');
  console.log('4. Laravel Livewire: 1个 (once.tools) ✅ 新增');
  console.log('');
  console.log('技术特点对比:');
  console.log('- PHP Form: 传统、稳定、服务器端');
  console.log('- Vue.js: 现代、客户端、交互丰富');
  console.log('- Contact Form 7: WordPress生态、易用');
  console.log('- Laravel Livewire: 服务器端、实时、安全');
  console.log('');
  console.log('适用场景:');
  console.log('- PHP Form: 批量目录提交');
  console.log('- Vue.js: 产品展示、用户体验');
  console.log('- Contact Form 7: 联系咨询');
  console.log('- Laravel Livewire: 工具提交、数据管理');
  console.log('');
  console.log('开发体验:');
  console.log('- PHP Form: 简单直接');
  console.log('- Vue.js: 需要前端技能');
  console.log('- Contact Form 7: 配置化');
  console.log('- Laravel Livewire: 全栈PHP');
}
