// FreeOnline.org 网站规则配置
// 网站: https://www.freeonline.org/segnala-un-sito/
// 最后更新: 2025-07-24

export const SITE_RULE = {
  domain: 'www.freeonline.org',
  siteName: 'Free Online',
  priority: 1,
  lastUpdated: '2025-07-24',

  fieldMappings: {
    siteName: {
      selectors: [
        '#form-field-field_sito',
        'input[name="form_fields[field_sito]"]',
        'input[placeholder="Titolo del sito"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '网站标题'
    },

    siteUrl: {
      selectors: [
        '#form-field-field_2951bd0',
        'input[name="form_fields[field_2951bd0]"]',
        'input[type="url"]:first-of-type'
      ],
      method: 'value',
      validation: 'required|url',
      notes: '网站URL'
    },

    iosAppUrl: {
      selectors: [
        '#form-field-field_8eaf4c9',
        'input[name="form_fields[field_8eaf4c9]"]',
        'label:contains("iOS") + input'
      ],
      method: 'value',
      validation: 'optional|url',
      notes: 'iOS应用URL'
    },

    androidAppUrl: {
      selectors: [
        '#form-field-field_2de8793',
        'input[name="form_fields[field_2de8793]"]',
        'label:contains("Android") + input'
      ],
      method: 'value',
      validation: 'optional|url',
      notes: 'Android应用URL'
    },

    siteDescription: {
      selectors: [
        '#form-field-message',
        'textarea[name="form_fields[message]"]',
        'textarea[placeholder*="Descrizione del sito"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '网站、应用或服务描述'
    },

    language: {
      selectors: [
        '#form-field-field_99f99c1',
        'select[name="form_fields[field_99f99c1]"]',
        'label:contains("Lingua") + div select'
      ],
      method: 'select',
      validation: 'optional',
      defaultValue: 'Italiano',
      notes: '网站语言'
    },

    contactEmail: {
      selectors: [
        '#form-field-email',
        'input[name="form_fields[email]"]',
        'input[type="email"]'
      ],
      method: 'value',
      validation: 'required|email',
      notes: '联系邮箱'
    },

    privacyAccept: {
      selectors: [
        '#form-field-field_9fe1dd8',
        'input[name="form_fields[field_9fe1dd8]"]',
        'input[type="checkbox"]'
      ],
      method: 'checkbox',
      validation: 'required',
      defaultValue: true,
      notes: '隐私政策同意'
    }
  },

  submitConfig: {
    submitButton: 'button[type="submit"], .elementor-button',
    submitMethod: 'click',
    successIndicators: ['.success-message'],
    errorIndicators: ['.error-message']
  },

  specialHandling: {
    requiresLogin: false,
    hasCaptcha: true,
    hasFileUpload: false,
    customScript: 'handleFreeOnlineSubmission',
    formValidation: {
      requiredFields: ['siteName', 'siteUrl', 'siteDescription', 'contactEmail', 'privacyAccept'],
      emailValidation: true,
      urlValidation: true
    },
    notes: [
      '意大利语网站提交表单',
      '使用Elementor表单构建',
      '有reCAPTCHA v3验证',
      '支持iOS和Android应用URL',
      '需要同意隐私政策',
      '有语言选择选项'
    ]
  }
};

export function handleFreeOnlineSubmission(data, rule) {
  const processedData = { ...data };

  if (processedData.siteUrl && !processedData.siteUrl.startsWith('http')) {
    processedData.siteUrl = 'https://' + processedData.siteUrl;
  }

  if (processedData.iosAppUrl && !processedData.iosAppUrl.startsWith('http')) {
    processedData.iosAppUrl = 'https://' + processedData.iosAppUrl;
  }

  if (processedData.androidAppUrl && !processedData.androidAppUrl.startsWith('http')) {
    processedData.androidAppUrl = 'https://' + processedData.androidAppUrl;
  }

  return processedData;
}

export async function customFillElement(element, value, config) {
  // 处理下拉选择框
  if (element.tagName === 'SELECT') {
    const options = element.querySelectorAll('option');
    const option = Array.from(options).find(opt => opt.value === 'Italiano');
    if (option) {
      element.value = option.value;
      element.dispatchEvent(new Event('change', { bubbles: true }));
      return true;
    }
  }

  // 处理复选框
  if (element.type === 'checkbox') {
    element.checked = Boolean(value);
    element.dispatchEvent(new Event('change', { bubbles: true }));
    return true;
  }

  if (element.tagName === 'INPUT' || element.tagName === 'TEXTAREA') {
    element.value = value;
    element.dispatchEvent(new Event('input', { bubbles: true }));
    element.dispatchEvent(new Event('change', { bubbles: true }));
    return true;
  }

  return false;
}