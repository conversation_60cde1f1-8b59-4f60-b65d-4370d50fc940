// theainavigator.com 网站规则配置
// 网站: https://www.theainavigator.com/submit-an-ai-tool
// 表单技术: Custom Form with hCaptcha
// 最后更新: 2025-07-07

export const SITE_RULE = {
  // 基本信息
  domain: 'theainavigator.com',
  siteName: 'The AI Navigator',
  priority: 1,
  lastUpdated: '2025-07-07',
  
  // 字段映射规则
  fieldMappings: {
    // 工具名称 -> Tool Name
    siteName: {
      selectors: [
        'input[name="$item1684874554022#shortText"]',
        'input[aria-label="input"]:first-of-type',
        '.s-kit-input[maxlength="500"]:first-of-type'
      ],
      method: 'value',
      validation: 'required',
      notes: '工具名称，使用website-info.js中的siteName字段，最大500字符'
    },
    
    // 工具URL -> URL of the AI Tool
    siteUrl: {
      selectors: [
        'input[name="$item1684874588950#shortText"]',
        'input[aria-label="input"][maxlength="500"]:nth-of-type(2)',
        '.s-kit-input[maxlength="500"]:nth-of-type(2)'
      ],
      method: 'value',
      validation: 'required|url',
      notes: '工具URL，使用website-info.js中的siteUrl字段，需要以https://开头'
    },
    
    // 工具功能描述 -> What does the tool do (in one sentence)?
    siteDescription: {
      selectors: [
        'textarea[name="$item1606282604448#longText"]',
        'textarea[maxlength="1000"]:first-of-type',
        '.s-kit-input[type="textarea"]:first-of-type'
      ],
      method: 'value',
      validation: 'required',
      notes: '工具功能描述，使用website-info.js中的siteDescription字段，最大1000字符'
    },
    
    // Threads账号 -> Threads handle
    threadsHandle: {
      selectors: [
        'input[name="$item1726764697788#shortText"]',
        'input[aria-label="input"][maxlength="500"]:nth-of-type(3)',
        '.s-kit-input[maxlength="500"]:nth-of-type(3)'
      ],
      method: 'value',
      validation: 'optional',
      defaultValue: '@aitool',
      notes: 'Threads账号，使用默认值@aitool'
    },
    
    // 提交者姓名 -> Your Name
    fullName: {
      selectors: [
        'input[name="$item1606282593441#shortText"]',
        'input[aria-label="input"][maxlength="500"]:nth-of-type(4)',
        '.s-kit-input[maxlength="500"]:nth-of-type(4)'
      ],
      method: 'value',
      validation: 'required',
      notes: '提交者姓名，使用website-info.js中的fullName字段'
    },
    
    // 邮箱地址 -> Email Address
    contactEmail: {
      selectors: [
        'input[name="$item1606282596776#email"]',
        'input[type="text"][maxlength="100"]',
        'input[value*="@"]'
      ],
      method: 'value',
      validation: 'required|email',
      notes: '邮箱地址，使用website-info.js中的contactEmail字段，最大100字符'
    },
    
    // 联盟计划链接 -> Link to your affiliate program
    affiliateUrl: {
      selectors: [
        'input[name="$item1726764851048#shortText"]',
        'input[aria-label="input"][maxlength="500"]:nth-of-type(5)',
        '.s-kit-input[maxlength="500"]:nth-of-type(5)'
      ],
      method: 'value',
      validation: 'optional|url',
      notes: '联盟计划链接，可选字段'
    },
    
    // 附加信息 -> Additional message
    additionalMessage: {
      selectors: [
        'textarea[name="$item1684875119392#longText"]',
        'textarea[maxlength="1000"]:last-of-type',
        '.s-kit-input[type="textarea"]:last-of-type'
      ],
      method: 'value',
      validation: 'optional',
      defaultValue: 'This is an innovative AI tool that provides excellent features and user experience.',
      notes: '附加信息，使用默认值'
    }
  },
  
  // 自定义填写函数
  customFillElement: async function(element, value, config) {
    console.log(`AI Navigator自定义填写: ${element.name || element.getAttribute('aria-label')}, 方法: ${config.method}`);
    
    switch (config.method) {
      case 'value':
        // 标准输入框和文本域处理
        element.focus();
        await new Promise(resolve => setTimeout(resolve, 100));
        
        // 清空现有内容
        element.value = '';
        await new Promise(resolve => setTimeout(resolve, 50));
        
        // 处理特殊字段
        let finalValue = value;
        if (element.name === '$item1726764697788#shortText') {
          // Threads handle使用默认值
          finalValue = config.defaultValue;
        } else if (element.name === '$item1684875119392#longText') {
          // 附加信息使用默认值
          finalValue = config.defaultValue;
        } else if (element.name === '$item1684874588950#shortText') {
          // URL确保以https://开头
          if (value && !value.startsWith('http')) {
            finalValue = 'https://' + value;
          }
        } else if (element.maxLength) {
          // 处理字符限制
          const maxLength = parseInt(element.maxLength);
          if (finalValue.length > maxLength) {
            finalValue = finalValue.substring(0, maxLength);
          }
        }
        
        // 设置新值
        element.value = finalValue;
        
        // 触发事件
        element.dispatchEvent(new Event('input', { bubbles: true }));
        element.dispatchEvent(new Event('change', { bubbles: true }));
        element.dispatchEvent(new Event('blur', { bubbles: true }));
        
        console.log(`✓ 填写字段: ${element.name} = "${finalValue.substring(0, 50)}..."`);
        break;
        
      default:
        element.value = value;
        element.dispatchEvent(new Event('input', { bubbles: true }));
        element.dispatchEvent(new Event('change', { bubbles: true }));
    }
  },
  
  // 提交流程配置
  submitConfig: {
    submitButton: [
      'button[type="submit"]',
      '.s-email-form-button',
      '.s-common-button'
    ],
    submitMethod: 'click',
    waitAfterFill: 3000, // 等待hCaptcha加载
    waitAfterSubmit: 5000,
    
    // 提交前检查
    preSubmitChecks: [
      {
        type: 'checkbox',
        selector: 'input[type="checkbox"][id*="gdpr"]',
        action: 'check',
        description: '勾选隐私政策同意框'
      },
      {
        type: 'captcha',
        selector: '.g-recaptcha, .h-captcha',
        action: 'wait',
        description: '等待hCaptcha验证'
      }
    ],
    
    successIndicators: [
      '.success-message',
      '.thank-you',
      '[class*="success"]',
      'text:contains("submitted")'
    ],
    errorIndicators: [
      '.error-message',
      '[class*="error"]',
      '.s-kit-error'
    ]
  },
  
  // 特殊处理
  specialHandling: {
    requiresLogin: false,
    hasCaptcha: true, // 有hCaptcha验证
    hasFileUpload: false,
    hasGDPRConsent: true, // 有GDPR同意框
    isCustomForm: true, // 自定义表单系统
    
    // 表单验证规则
    formValidation: {
      requiredFields: ['siteName', 'siteUrl', 'siteDescription', 'fullName', 'contactEmail'],
      emailValidation: true,
      urlValidation: true,
      characterLimits: {
        siteName: 500,
        siteUrl: 500,
        siteDescription: 1000,
        threadsHandle: 500,
        fullName: 500,
        contactEmail: 100,
        affiliateUrl: 500,
        additionalMessage: 1000
      }
    },
    
    // 特殊注意事项
    notes: [
      '这是自定义表单系统，使用独特的字段命名',
      '表单包含8个字段，5个必填，3个可选',
      '有hCaptcha验证，需要等待验证完成',
      '有GDPR隐私政策同意框，需要勾选',
      '字段名格式：$item{timestamp}#{type}',
      'URL字段要求以https://开头',
      '邮箱字段限制100字符，其他文本字段500字符',
      '文本域字段限制1000字符',
      'Threads handle使用默认值@aitool',
      '附加信息使用默认描述'
    ]
  }
};

// 自定义处理函数
export function handleAINavigatorSubmission(data, _rule) {
  console.log('Processing AI Navigator form submission...');

  const processedData = { ...data };

  // 确保URL格式正确
  if (processedData.siteUrl && !processedData.siteUrl.startsWith('http')) {
    processedData.siteUrl = 'https://' + processedData.siteUrl;
  }

  // 处理字符限制
  const characterLimits = {
    siteName: 500,
    siteUrl: 500,
    siteDescription: 1000,
    fullName: 500,
    contactEmail: 100,
    affiliateUrl: 500,
    additionalMessage: 1000
  };

  Object.keys(characterLimits).forEach(field => {
    if (processedData[field] && processedData[field].length > characterLimits[field]) {
      processedData[field] = processedData[field].substring(0, characterLimits[field]);
    }
  });

  // 设置默认值
  processedData.threadsHandle = '@aitool';
  processedData.additionalMessage = 'This is an innovative AI tool that provides excellent features and user experience.';

  // 移除空的可选字段
  if (!processedData.affiliateUrl) {
    delete processedData.affiliateUrl;
  }

  return processedData;
}

// GDPR同意框处理函数
export async function handleGDPRConsent() {
  console.log('处理GDPR同意框...');
  
  const gdprCheckbox = document.querySelector('input[type="checkbox"][id*="gdpr"]');
  if (gdprCheckbox && !gdprCheckbox.checked) {
    gdprCheckbox.click();
    console.log('✓ 已勾选GDPR隐私政策同意框');
    await new Promise(resolve => setTimeout(resolve, 500));
  }
}

// hCaptcha处理函数
export async function handleCaptcha() {
  console.log('等待hCaptcha验证...');
  
  // 检查hCaptcha是否存在
  const captcha = document.querySelector('.g-recaptcha, .h-captcha');
  if (captcha) {
    console.log('检测到hCaptcha，需要手动验证');
    // 这里需要用户手动完成验证
    return new Promise((resolve) => {
      const checkCaptcha = setInterval(() => {
        const response = document.querySelector('textarea[name="h-captcha-response"], textarea[name="g-recaptcha-response"]');
        if (response && response.value) {
          clearInterval(checkCaptcha);
          console.log('✓ hCaptcha验证完成');
          resolve();
        }
      }, 1000);
    });
  }
}
