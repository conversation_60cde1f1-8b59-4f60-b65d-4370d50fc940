// Bokequ.com 网站规则配置
// 网站: http://m.bokequ.com/e/DoInfo/AddInfo.php?mid=10&enews=MAddInfo&classid=139&Submit=%E7%A1%AE%E5%AE%9A%E9%80%89%E6%8B%A9
// 最后更新: 2025-07-24

export const SITE_RULE = {
  // 基本信息
  domain: 'm.bokequ.com',
  siteName: 'Bokequ.com',
  priority: 1,
  lastUpdated: '2025-07-24',

  // 字段映射规则
  fieldMappings: {
    // 网站名称 -> 网站名称
    siteName: {
      selectors: [
        '#title',
        'input[name="title"]',
        'input[placeholder="网站标题"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '网站标题'
    },

    // 网站网址 -> 您的网址
    siteUrl: {
      selectors: [
        '#weburl',
        'input[name="weburl"]',
        'input[placeholder*="顶级域名"]'
      ],
      method: 'value',
      validation: 'required|url',
      notes: '网站URL地址'
    },

    // 关键词 -> 关键词
    keywords: {
      selectors: [
        'input[name="keyboard"]',
        'input[placeholder*="网站关键字"]'
      ],
      method: 'value',
      validation: 'optional',
      notes: '网站关键字，用英文逗号分隔'
    },

    // 联系QQ -> 联系QQ
    contactQQ: {
      selectors: [
        '#qq',
        'input[name="qq"]',
        'input[placeholder*="QQ号"]'
      ],
      method: 'value',
      validation: 'optional',
      notes: '联系QQ号码'
    },

    // 所属地区 -> 所属地区
    province: {
      selectors: [
        'select[name="pro"]'
      ],
      method: 'select',
      validation: 'optional',
      notes: '省/直辖市选择'
    },

    // 城市 -> 城市
    city: {
      selectors: [
        'select[name="city"]'
      ],
      method: 'select',
      validation: 'optional',
      notes: '城市选择'
    },

    // 区域 -> 区域
    area: {
      selectors: [
        'select[name="area"]'
      ],
      method: 'select',
      validation: 'optional',
      notes: '区域选择'
    },

    // 网站简介 -> 网站简介
    siteDescription: {
      selectors: [
        '#description',
        'textarea[name="description"]',
        'textarea[placeholder*="网站简介"]'
      ],
      method: 'value',
      validation: 'optional',
      notes: '网站简介或描述'
    },

    // 网站类型 -> 网站类型
    websiteType: {
      selectors: [
        'input[name="web"]',
        'input[value="个人类型网站"]',
        'input[value="企业类型网站"]'
      ],
      method: 'radio',
      validation: 'optional',
      notes: '网站类型选择：个人或企业'
    }
  },
  // 提交流程配置
  submitConfig: {
    submitButton: 'input[name="Submit"], input[value="提交网站"]',
    submitMethod: 'manual', // 手动提交
    waitAfterFill: 0,
    waitAfterSubmit: 0,
    successIndicators: ['.success-message'],
    errorIndicators: ['.error-message']
  },

  // 特殊处理
  specialHandling: {
    requiresLogin: false,
    hasCaptcha: false,
    hasFileUpload: false,
    customScript: 'handleBokequSubmission',
    formValidation: {
      requiredFields: ['siteName', 'siteUrl'],
      emailValidation: false,
      urlValidation: true
    },
    notes: [
      'Bokequ.com 网站目录提交平台',
      '基于帝国CMS系统构建',
      '中文界面',
      '支持地区选择（省市区三级联动）',
      '支持个人和企业网站类型选择',
      '包含QQ联系方式字段',
      '支持关键词设置',
      '手动提交模式'
    ]
  }
};

// 自定义处理函数
export function handleBokequSubmission(data) {
  console.log('Processing Bokequ form submission...');

  const processedData = { ...data };

  // 确保URL格式正确
  if (processedData.siteUrl && !processedData.siteUrl.startsWith('http')) {
    processedData.siteUrl = 'https://' + processedData.siteUrl;
  }

  return processedData;
}

// 自定义元素填写函数
export async function customFillElement(element, value, config) {
  console.log(`Bokequ自定义填写: ${element.tagName}, 方法: ${config.method}`);

  switch (config.method) {
    case 'value':
      // 标准输入框和文本域处理
      element.focus();
      await new Promise(resolve => setTimeout(resolve, 200));

      element.value = value;
      element.dispatchEvent(new Event('input', { bubbles: true }));
      element.dispatchEvent(new Event('change', { bubbles: true }));
      element.dispatchEvent(new Event('blur', { bubbles: true }));

      console.log(`✓ 填写字段: ${element.name} = "${value}"`);
      return true;

    case 'select':
      // 下拉选择框处理
      if (element.tagName === 'SELECT') {
        const options = element.querySelectorAll('option');
        let selectedOption;

        // 智能匹配选项
        for (const option of options) {
          if (option.value && option.value !== '') {
            const optionText = option.textContent.trim();
            if (element.name === 'pro') {
              // 省份选择，默认选择北京市
              if (optionText.includes('北京') || optionText.includes('上海') || optionText.includes('广东')) {
                selectedOption = option;
                break;
              }
            }
          }
        }

        // 如果没找到合适的，选择第一个非空选项
        if (!selectedOption) {
          selectedOption = Array.from(options).find(opt => opt.value !== '');
        }

        if (selectedOption) {
          element.value = selectedOption.value;
          element.dispatchEvent(new Event('change', { bubbles: true }));
          console.log(`✓ 选择选项: ${selectedOption.textContent}`);
          return true;
        }
      }
      break;

    case 'radio':
      // 单选按钮处理 - 默认选择企业类型
      const enterpriseRadio = document.querySelector('input[name="web"][value="企业类型网站"]');
      if (enterpriseRadio) {
        enterpriseRadio.checked = true;
        enterpriseRadio.dispatchEvent(new Event('change', { bubbles: true }));
        console.log(`✓ 选择网站类型: 企业类型网站`);
        return true;
      }
      break;

    default:
      element.value = value;
      element.dispatchEvent(new Event('input', { bubbles: true }));
      return true;
  }

  return false;
}