// Zhijian100.cn 网站规则配置
// 网站: https://www.zhijian100.cn/submit-website
// 最后更新: 2025-07-24

export const SITE_RULE = {
  // 基本信息
  domain: 'www.zhijian100.cn',
  siteName: 'Zhijian100',
  priority: 1,
  lastUpdated: '2025-07-24',

  // 字段映射规则
  fieldMappings: {
    // 工具名称 -> 工具名称
    siteName: {
      selectors: [
        '#name',
        'input[name="name"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '工具名称，最多50字符'
    },

    // 网址 -> 网址
    siteUrl: {
      selectors: [
        '#url',
        'input[name="url"]'
      ],
      method: 'value',
      validation: 'required|url',
      notes: '网址URL地址'
    },

    // 分类 -> 分类
    category: {
      selectors: [
        '#category',
        'select[name="category"]'
      ],
      method: 'select',
      validation: 'required',
      notes: '分类选择'
    },

    // 简介 -> 简介
    siteDescription: {
      selectors: [
        '#description',
        'textarea[name="description"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '简介，最多100字符'
    },

    // 费用 -> 费用
    pricing: {
      selectors: [
        '#fee',
        'select[name="fee"]'
      ],
      method: 'select',
      validation: 'required',
      notes: '费用模式选择'
    },

    // 语言 -> 语言
    languages: {
      selectors: [
        '#languages',
        'select[name="languages"]'
      ],
      method: 'select',
      validation: 'required',
      notes: '语言支持'
    },

    // 工具详情 -> 工具详情
    detailedIntro: {
      selectors: [
        '#details',
        'textarea[name="details"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '工具详情，最多100字符'
    },

    // 功能介绍 -> 功能介绍
    features: {
      selectors: [
        '#features',
        'textarea[name="features"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '功能介绍，最多100字符'
    },

    // 优点 -> 优点
    pros: {
      selectors: [
        '#pros',
        'textarea[name="pros"]'
      ],
      method: 'value',
      validation: 'optional',
      notes: '优点，选填',
      defaultValue: '免费功能丰富，支持多种语言，界面简洁易用'
    },

    // 缺点 -> 缺点
    cons: {
      selectors: [
        '#cons',
        'textarea[name="cons"]'
      ],
      method: 'value',
      validation: 'optional',
      notes: '缺点，选填',
      defaultValue: '产品新上线，用户使用目前不够多'
    },

    // 上传图标 -> 上传图标
    logoFile: {
      selectors: [
        '#icon',
        'input[name="icon"]'
      ],
      method: 'file',
      validation: 'optional',
      notes: '图标文件上传'
    },

    // 提交人姓名 -> 提交人姓名
    fullName: {
      selectors: [
        '#submitterName',
        'input[name="submitterName"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '提交人姓名'
    },

    // 提交人联系方式 -> 提交人联系方式
    contactEmail: {
      selectors: [
        '#contact',
        'input[name="contact"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '提交人联系方式'
    }
  },
  // 提交流程配置
  submitConfig: {
    submitButton: '#submit, .btn-submit',
    submitMethod: 'click',
    waitAfterFill: 2000,
    waitAfterSubmit: 5000,
    successIndicators: ['.success-message'],
    errorIndicators: ['.error-message']
  },

  // 特殊处理
  specialHandling: {
    requiresLogin: false,
    hasCaptcha: false,
    hasFileUpload: false,
    customScript: 'handleZhijian100Submission',
    formValidation: {
      requiredFields: ['siteName', 'siteUrl', 'category', 'siteDescription', 'pricing', 'languages', 'detailedIntro', 'features', 'fullName', 'contactEmail'],
      emailValidation: false,
      urlValidation: true
    },
    notes: [
      'Zhijian100 智见100网站收录平台',
      '支持多种网站分类',
      '有详细的分类选项',
      '中文界面',
      '字符数限制提示',
      '与zhexieai.com使用相同系统源码'
    ]
  }
};

// 自定义处理函数
export function handleZhijian100Submission(data) {
  console.log('Processing Zhijian100 form submission...');

  const processedData = { ...data };

  // 确保URL格式正确
  if (processedData.siteUrl && !processedData.siteUrl.startsWith('http')) {
    processedData.siteUrl = 'https://' + processedData.siteUrl;
  }

  // 为优点提供默认值
  if (!processedData.pros) {
    processedData.pros = '免费功能丰富，支持多种语言，界面简洁易用';
  }

  // 为缺点提供默认值
  if (!processedData.cons) {
    processedData.cons = '产品新上线，用户使用目前不够多';
  }

  return processedData;
}

// 自定义元素填写函数
export async function customFillElement(element, value, config) {
  console.log(`Zhijian100自定义填写: ${element.tagName}, 方法: ${config.method}`);

  // 为特定字段提供默认值
  if (!value || value === '') {
    if (element.name === 'pros') {
      value = '免费功能丰富，支持多种语言，界面简洁易用';
    } else if (element.name === 'cons') {
      value = '产品新上线，用户使用目前不够多';
    }
  }

  switch (config.method) {
    case 'value':
      // 标准输入框和文本域处理
      element.focus();
      await new Promise(resolve => setTimeout(resolve, 200));

      element.value = value;
      element.dispatchEvent(new Event('input', { bubbles: true }));
      element.dispatchEvent(new Event('change', { bubbles: true }));
      element.dispatchEvent(new Event('blur', { bubbles: true }));

      console.log(`✓ 填写字段: ${element.name} = "${value}"`);
      return true;

    case 'select':
      // 下拉选择框处理
      if (element.tagName === 'SELECT') {
        const options = element.querySelectorAll('option');
        let selectedOption;

        // 根据字段名智能选择
        if (element.name === 'category') {
          // 分类选择 - 优先选择AI写作工具
          selectedOption = Array.from(options).find(opt => opt.textContent.includes('AI写作工具'));
        } else if (element.name === 'fee') {
          // 费用选择 - 选择部分功能免费
          selectedOption = Array.from(options).find(opt => opt.textContent.includes('部分功能免费'));
        } else if (element.name === 'languages') {
          // 语言选择 - 选择支持简体中文
          selectedOption = Array.from(options).find(opt => opt.textContent.includes('支持简体中文'));
        }

        // 如果没找到合适的，选择第一个非空选项
        if (!selectedOption) {
          selectedOption = Array.from(options).find(opt => opt.value !== '');
        }

        if (selectedOption) {
          element.value = selectedOption.value;
          element.dispatchEvent(new Event('change', { bubbles: true }));
          console.log(`✓ 选择选项: ${selectedOption.textContent}`);
          return true;
        }
      }
      break;

    case 'file':
      // 文件上传处理
      console.warn('文件上传需要手动操作');
      return false;

    default:
      element.value = value;
      element.dispatchEvent(new Event('input', { bubbles: true }));
      return true;
  }

  return false;
}