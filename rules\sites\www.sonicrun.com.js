// SonicRun.com 网站规则配置
// 网站: https://www.sonicrun.com/freelisting.html
// 最后更新: 2025-07-24

export const SITE_RULE = {
  domain: 'www.sonicrun.com',
  siteName: 'Sonic Run',
  priority: 1,
  lastUpdated: '2025-07-24',

  fieldMappings: {
    siteUrl: {
      selectors: [
        'input[name="url"]',
        '.form_field[name="url"]',
        'label:contains("Your URL") + input'
      ],
      method: 'value',
      validation: 'required|url',
      notes: '网站URL'
    },

    contactEmail: {
      selectors: [
        'input[name="email"]',
        '.form_field[name="email"]',
        'label:contains("Your Email Address") + input'
      ],
      method: 'value',
      validation: 'required|email',
      notes: '联系邮箱'
    },

    fullName: {
      selectors: [
        'input[name="name"]',
        '.form_field[name="name"]',
        'label:contains("Your Name") + input'
      ],
      method: 'value',
      validation: 'required',
      notes: '提交者姓名'
    },

    agreeTerms: {
      selectors: [
        'input[name="terms"]',
        'input[type="checkbox"]',
        '.agree'
      ],
      method: 'checkbox',
      validation: 'required',
      defaultValue: true,
      notes: '同意提交条款'
    }
  },

  submitConfig: {
    submitButton: 'input[type="submit"], .search_btn',
    submitMethod: 'click',
    successIndicators: ['.success-message'],
    errorIndicators: ['.error-message']
  },

  specialHandling: {
    requiresLogin: false,
    hasCaptcha: true,
    hasFileUpload: false,
    customScript: 'handleSonicRunSubmission',
    formValidation: {
      requiredFields: ['siteUrl', 'contactEmail', 'fullName', 'agreeTerms'],
      emailValidation: true,
      urlValidation: true
    },
    notes: [
      '搜索引擎提交平台',
      '简洁的4字段表单',
      '有Cloudflare Turnstile验证',
      '需要同意提交条款',
      '免费列表服务'
    ]
  }
};

export function handleSonicRunSubmission(data, rule) {
  const processedData = { ...data };

  if (processedData.siteUrl && !processedData.siteUrl.startsWith('http')) {
    processedData.siteUrl = 'https://' + processedData.siteUrl;
  }

  // 自动勾选同意条款
  const termsCheckbox = document.querySelector('input[name="terms"]');
  if (termsCheckbox) {
    termsCheckbox.checked = true;
  }

  return processedData;
}

export async function customFillElement(element, value, config) {
  // 处理复选框
  if (element.type === 'checkbox') {
    element.checked = Boolean(value);
    element.dispatchEvent(new Event('change', { bubbles: true }));
    return true;
  }

  if (element.tagName === 'INPUT') {
    element.value = value;
    element.dispatchEvent(new Event('input', { bubbles: true }));
    element.dispatchEvent(new Event('change', { bubbles: true }));
    return true;
  }

  return false;
}