// EU-Startups.com 网站规则配置
// 网站: https://www.eu-startups.com/directory/?wpbdp_view=submit_listing
// 最后更新: 2025-07-24

export const SITE_RULE = {
  domain: 'www.eu-startups.com',
  siteName: 'EU-Startups',
  priority: 1,
  lastUpdated: '2025-07-24',

  fieldMappings: {
    category: {
      selectors: [
        '#wpbdp-field-2',
        'select[name="listingfields[2]"]',
        '.wpbdp-js-select2'
      ],
      method: 'select',
      validation: 'required',
      defaultValue: '797',
      notes: '欧洲国家分类，默认Austria'
    },

    companyName: {
      selectors: [
        '#wpbdp-field-1',
        'input[name="listingfields[1]"]',
        'label:contains("Business Name") + div input'
      ],
      method: 'value',
      validation: 'required',
      notes: '企业名称'
    },

    siteDescription: {
      selectors: [
        '#wpbdp-field-7',
        'textarea[name="listingfields[7]"]',
        'label:contains("Business Description") + div textarea'
      ],
      method: 'value',
      validation: 'required',
      notes: '企业描述'
    },

    detailedIntro: {
      selectors: [
        '#wpbdp-field-3',
        'textarea[name="listingfields[3]"]',
        'label:contains("Long Business Description") + div textarea'
      ],
      method: 'value',
      validation: 'optional',
      notes: '详细企业描述'
    },

    city: {
      selectors: [
        '#wpbdp-field-6',
        'input[name="listingfields[6]"]',
        'label:contains("Based in") + div input'
      ],
      method: 'value',
      validation: 'required',
      notes: '所在城市'
    },

    tags: {
      selectors: [
        '#wpbdp-field-9',
        'input[name="listingfields[9]"]',
        'label:contains("Tags") + div input'
      ],
      method: 'value',
      validation: 'required',
      notes: '标签，3-4个词用逗号分隔'
    },

    totalFunding: {
      selectors: [
        '#wpbdp-field-10',
        'select[name="listingfields[10]"]',
        'label:contains("Total Funding") + div select'
      ],
      method: 'select',
      validation: 'required',
      defaultValue: 'No funding announced yet',
      notes: '总融资额'
    },

    foundedYear: {
      selectors: [
        '#wpbdp-field-4',
        'select[name="listingfields[4]"]',
        'label:contains("Founded") + div select'
      ],
      method: 'select',
      validation: 'required',
      defaultValue: '2025',
      notes: '成立年份'
    },

    siteUrl: {
      selectors: [
        '#wpbdp-field-5',
        'input[name="listingfields[5]"]',
        'label:contains("Website") + div input'
      ],
      method: 'value',
      validation: 'required|url',
      notes: '网站URL'
    },

    companyStatus: {
      selectors: [
        'input[name="listingfields[11]"][value="Active"]',
        '#wpbdp-field-11-Active',
        'label:contains("Company Status") + div input[value="Active"]'
      ],
      method: 'radio',
      validation: 'optional',
      defaultValue: 'Active',
      notes: '公司状态，默认Active'
    },

    linkedinUrl: {
      selectors: [
        '#wpbdp-field-12',
        'input[name="listingfields[12]"]',
        'label:contains("LinkedIn Page") + div input'
      ],
      method: 'value',
      validation: 'optional|url',
      notes: 'LinkedIn页面'
    },

    contactEmail: {
      selectors: [
        '#wpbdp-field-8',
        'input[name="listingfields[8]"]',
        'label:contains("Business Contact Email") + div input'
      ],
      method: 'value',
      validation: 'required|email',
      notes: '企业联系邮箱'
    }
  },

  submitConfig: {
    submitButton: '.submit-next-button, button[type="submit"]',
    submitMethod: 'click',
    successIndicators: ['.success-message'],
    errorIndicators: ['.error-message']
  },

  specialHandling: {
    requiresLogin: false,
    hasCaptcha: false,
    hasFileUpload: true,
    customScript: 'handleEUStartupsSubmission',
    formValidation: {
      requiredFields: ['category', 'companyName', 'siteDescription', 'city', 'tags', 'totalFunding', 'foundedYear', 'siteUrl', 'contactEmail'],
      emailValidation: true,
      urlValidation: true
    },
    notes: [
      '多步骤表单提交',
      '欧洲创业公司目录',
      '支持图片上传',
      '有融资额选择',
      '按国家分类',
      '免费列表服务'
    ]
  }
};

export function handleEUStartupsSubmission(data, rule) {
  const processedData = { ...data };

  if (processedData.siteUrl && !processedData.siteUrl.startsWith('http')) {
    processedData.siteUrl = 'https://' + processedData.siteUrl;
  }

  if (processedData.linkedinUrl && !processedData.linkedinUrl.startsWith('http')) {
    processedData.linkedinUrl = 'https://' + processedData.linkedinUrl;
  }

  return processedData;
}

export async function customFillElement(element, value, config) {
  // 处理Select2下拉框
  if (element.classList.contains('wpbdp-js-select2') || element.classList.contains('select2-hidden-accessible')) {
    const select2Container = element.nextElementSibling;
    if (select2Container && select2Container.classList.contains('select2-container')) {
      select2Container.click();
      setTimeout(() => {
        const option = document.querySelector('.select2-results__option[data-select2-id="select2-data-797-0"]');
        if (option) option.click();
      }, 100);
      return true;
    }
  }

  // 处理普通下拉框
  if (element.tagName === 'SELECT') {
    const options = element.querySelectorAll('option');
    let targetOption = null;

    if (element.name === 'listingfields[10]') {
      targetOption = Array.from(options).find(opt => opt.value === 'No funding announced yet');
    } else if (element.name === 'listingfields[4]') {
      targetOption = Array.from(options).find(opt => opt.value === '2025');
    }

    if (targetOption) {
      element.value = targetOption.value;
      element.dispatchEvent(new Event('change', { bubbles: true }));
      return true;
    }
  }

  // 处理单选按钮
  if (element.type === 'radio' && element.value === 'Active') {
    element.checked = true;
    element.dispatchEvent(new Event('change', { bubbles: true }));
    return true;
  }

  // 处理文件上传
  if (element.type === 'file') {
    console.warn('图片上传需要手动处理');
    return false;
  }

  if (element.tagName === 'INPUT' || element.tagName === 'TEXTAREA') {
    element.value = value;
    element.dispatchEvent(new Event('input', { bubbles: true }));
    element.dispatchEvent(new Event('change', { bubbles: true }));
    return true;
  }

  return false;
}