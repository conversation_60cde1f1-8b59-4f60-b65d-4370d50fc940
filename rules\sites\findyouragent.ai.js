// FindYourAgent.ai 网站规则配置
// 网站: https://findyouragent.ai/agents/new
// 最后更新: 2025-07-24

export const SITE_RULE = {
  // 基本信息
  domain: 'findyouragent.ai',
  siteName: 'FindYourAgent',
  priority: 1,
  lastUpdated: '2025-07-24',
  
  // 字段映射规则
  fieldMappings: {
    // 网站名称 -> Name (对应website-info.js的siteName)
    siteName: {
      selectors: [
        'input[name="agent[name]"]',
        '#agent_name',
        'input[id="agent_name"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '网站/工具名称，对应website-info.js的siteName字段'
    },

    // 网站URL -> Website URL (对应website-info.js的siteUrl)
    siteUrl: {
      selectors: [
        'input[name="agent[website_url]"]',
        '#agent_website_url',
        'input[type="url"]'
      ],
      method: 'value',
      validation: 'required|url',
      notes: '网站URL地址，对应website-info.js的siteUrl字段，必须以https://开头'
    },

    // 一句话介绍 -> Tagline
    tagline: {
      selectors: [
        'input[name="agent[tagline]"]',
        '#agent_tagline',
        'input[maxlength="100"]'
      ],
      method: 'value',
      validation: 'required',
      maxLength: 100,
      notes: '一句话介绍（最多100字符）'
    },

    // 详细描述 -> Description
    siteDescription: {
      selectors: [
        'textarea[name="agent[description]"]',
        '#agent_description',
        'textarea[maxlength="750"]'
      ],
      method: 'value',
      validation: 'required',
      maxLength: 750,
      notes: '详细描述（最多750字符）'
    },

    // 定价类型 -> Pricing Type
    pricing: {
      selectors: [
        'select[name="agent[pricing_type]"]',
        '#agent_pricing_type',
        'select[required]'
      ],
      method: 'select',
      validation: 'required',
      options: ['free', 'paid', 'freemium'],
      optionTexts: ['Free', 'Paid', 'Freemium'],
      defaultValue: 'freemium',
      notes: '定价类型选择'
    },

    // 价格 -> Price
    priceAmount: {
      selectors: [
        'input[name="agent[price]"]',
        '#agent_price',
        'input[type="number"]'
      ],
      method: 'value',
      validation: 'optional',
      notes: '月度起始价格（美元）'
    },

    // 分类 -> Category
    category: {
      selectors: [
        'select[name="agent[category_id]"]',
        '#agent_category_id'
      ],
      method: 'select',
      validation: 'required',
      options: ['1', '2', '3', '4'],
      optionTexts: ['Build your own', 'Business intelligence', 'Blockchain', 'Coding'],
      defaultValue: '4',
      notes: '选择分类，默认选择Coding'
    },

    // 标签 -> Tags (多选)
    tags: {
      selectors: [
        '.multi-select-header',
        '[data-multi-select-target="header"]',
        '.multi-select'
      ],
      method: 'multiselect',
      validation: 'required',
      notes: '选择标签（最多5个）'
    },

    // 功能特性 -> Features
    features: {
      selectors: [
        'input[name="agent[features][]"]',
        '[data-nested-fields-target="field"] input'
      ],
      method: 'features',
      validation: 'required',
      maxLength: 40,
      notes: '功能特性列表（每个最多40字符）'
    }
  },
  
  // 提交流程配置
  submitConfig: {
    submitButton: 'input[type="submit"][value="Submit Agent"]',
    submitMethod: 'click',
    successIndicators: [
      '.success-message',
      '.alert-success',
      '.notification-success'
    ],
    errorIndicators: [
      '.error-message',
      '.alert-error',
      '.text-red-500'
    ]
  },
  
  // 特殊处理
  specialHandling: {
    requiresLogin: true, // 需要登录
    hasCaptcha: false,
    hasFileUpload: true, // 有Logo和封面图上传
    
    // 自定义处理脚本
    customScript: 'handleFindYourAgentSubmission',
    
    // 表单验证规则
    formValidation: {
      requiredFields: ['siteName', 'siteUrl', 'tagline', 'siteDescription', 'pricing', 'category', 'tags', 'features'],
      emailValidation: false,
      urlValidation: true,
      characterLimits: {
        tagline: 100,
        siteDescription: 750,
        features: 40
      }
    },
    
    // 特殊注意事项
    notes: [
      '需要先登录才能提交',
      'URL必须以https://开头',
      'Tagline限制100字符',
      '描述限制750字符',
      '标签最多选择5个',
      '功能特性每个限制40字符',
      '需要上传Logo和封面图（文件上传需手动处理）',
      '有字符计数器实时显示'
    ]
  }
};

// 自定义处理函数
export function handleFindYourAgentSubmission(data, rule) {
  console.log('Processing FindYourAgent.ai submission...');

  const processedData = { ...data };

  // 确保URL格式正确（必须以https://开头）
  if (processedData.siteUrl && !processedData.siteUrl.startsWith('https://')) {
    if (processedData.siteUrl.startsWith('http://')) {
      processedData.siteUrl = processedData.siteUrl.replace('http://', 'https://');
    } else {
      processedData.siteUrl = 'https://' + processedData.siteUrl;
    }
  }

  // 定价类型映射
  if (processedData.pricing) {
    const pricingMap = {
      'Free': 'free',
      'Freemium': 'freemium',
      'Paid': 'paid',
      'Subscription': 'paid'
    };

    processedData.pricing = pricingMap[processedData.pricing] || 'freemium';
  }

  // 处理价格字段 - 如果是免费的，设置价格为0
  if (processedData.pricing === 'free') {
    processedData.priceAmount = '0';
  } else if (processedData.priceAmount) {
    // 移除价格中的货币符号
    processedData.priceAmount = processedData.priceAmount.replace(/[$,]/g, '');
  }

  // 确保tagline长度不超过100字符
  if (processedData.tagline && processedData.tagline.length > 100) {
    processedData.tagline = processedData.tagline.substring(0, 100);
  }

  // 确保描述长度不超过750字符
  if (processedData.siteDescription && processedData.siteDescription.length > 750) {
    processedData.siteDescription = processedData.siteDescription.substring(0, 750);
  }

  // 处理功能特性 - 将字符串转换为数组
  if (processedData.features && typeof processedData.features === 'string') {
    processedData.features = processedData.features.split(',').map(feature =>
      feature.trim().substring(0, 40) // 每个功能限制40字符
    ).filter(feature => feature.length > 0);
  }

  console.log('FindYourAgent.ai 数据处理完成:', processedData);
  return processedData;
}

// 自定义元素填写函数
export async function customFillElement(element, value, config) {
  console.log('🔧 FindYourAgent自定义填写函数被调用:', element, value);

  // 处理多选标签
  if (element.classList.contains('multi-select-header') || element.hasAttribute('data-multi-select-target')) {
    try {
      // 点击打开下拉菜单
      element.click();

      // 等待下拉菜单出现
      await new Promise(resolve => setTimeout(resolve, 500));

      // 查找AI相关的标签选项
      const aiOption = document.querySelector('.multi-select-option[data-value="1"]'); // ai标签
      if (aiOption) {
        aiOption.click();
        console.log('✅ 选择了AI标签');
        return true;
      }

    } catch (error) {
      console.warn('多选标签处理失败:', error);
    }
  }

  // 处理功能特性输入
  if (element.name === 'agent[features][]') {
    try {
      // 处理功能特性数组
      const features = Array.isArray(value) ? value : [value];
      const featureInputs = document.querySelectorAll('input[name="agent[features][]"]');

      // 填写第一个功能特性
      if (featureInputs.length > 0 && features.length > 0) {
        const firstFeature = features[0].substring(0, 40); // 限制40字符
        featureInputs[0].value = firstFeature;
        featureInputs[0].dispatchEvent(new Event('input', { bubbles: true }));

        console.log('✅ 填写功能特性:', firstFeature);
        return true;
      }

    } catch (error) {
      console.warn('功能特性填写失败:', error);
    }
  }

  // 处理文件上传提示
  if (element.type === 'file') {
    console.log('⚠️ 检测到文件上传字段，需要用户手动上传文件');
    if (element.id === 'agent_logo') {
      console.log('📷 Logo上传：推荐方形图片(1:1)，PNG或JPEG，最大2MB');
    } else if (element.id === 'agent_cover_image') {
      console.log('🖼️ 封面图上传：推荐1200x675px(16:9)，PNG或JPEG，最大2MB');
    }
    return false; // 让用户手动处理文件上传
  }

  // 处理带有字符计数的输入框
  if (element.hasAttribute('maxlength')) {
    try {
      const maxLength = parseInt(element.getAttribute('maxlength'));
      const truncatedValue = String(value).substring(0, maxLength);

      element.value = truncatedValue;
      element.dispatchEvent(new Event('input', { bubbles: true }));
      element.dispatchEvent(new Event('change', { bubbles: true }));

      console.log('✅ 填写带字符限制的字段:', truncatedValue);
      return true;
    } catch (error) {
      console.warn('字符限制字段填写失败:', error);
    }
  }

  // 默认处理
  return false;
};
