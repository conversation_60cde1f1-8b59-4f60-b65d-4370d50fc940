// landing.mycloudmedia.co.uk 网站规则配置
// 网站: https://landing.mycloudmedia.co.uk/apps-and-websites-submit-ai-or-saas-tool/new-submission.html
// 表单技术: HTML Form with Select Dropdowns
// 最后更新: 2025-07-08

export const SITE_RULE = {
  // 基本信息
  domain: 'landing.mycloudmedia.co.uk',
  siteName: 'Mycloudmedia',
  priority: 1,
  lastUpdated: '2025-07-08',
  
  // 字段映射规则
  fieldMappings: {
    // 名字 -> First Name
    firstName: {
      selectors: [
        'input[name="first_name"]',
        'input[placeholder*="First Name"]',
        'form input[type="text"]:first-of-type',
        'input:first-of-type'
      ],
      method: 'value',
      validation: 'required',
      defaultValue: 'Quinn',
      notes: '名字，固定值Quinn'
    },
    
    // 姓氏 -> Last Name
    lastName: {
      selectors: [
        'input[name="last_name"]',
        'input[placeholder*="Last Name"]',
        'form input[type="text"]:nth-of-type(2)',
        'input:nth-of-type(2)'
      ],
      method: 'value',
      validation: 'required',
      defaultValue: 'Halberg',
      notes: '姓氏，固定值Halberg'
    },
    
    // 邮箱 -> Email
    contactEmail: {
      selectors: [
        'input[name="email"]',
        'input[type="email"]',
        'input[placeholder*="Email"]',
        'form input[type="email"]:first-of-type'
      ],
      method: 'value',
      validation: 'required|email',
      notes: '邮箱地址，使用website-info.js中的contactEmail字段'
    },
    
    // 公司名称 -> Company Name
    companyName: {
      selectors: [
        'input[name="company_name"]',
        'input[placeholder*="Company Name"]',
        'form input[type="text"]:nth-of-type(3)',
        'input:nth-of-type(4)'
      ],
      method: 'value',
      validation: 'required',
      notes: '公司名称，使用website-info.js中的companyName字段'
    },
    
    // 电话 -> Phone
    phone: {
      selectors: [
        'input[name="f8f23b5b-22bf-4f70-bab8-e7b920303572"]',
        'input[placeholder="Phone*"]',
        'input[pattern="\\d*"]',
        'input[maxlength="50"]'
      ],
      method: 'value',
      validation: 'required',
      defaultValue: '14158372947',
      notes: '电话号码，固定美国号码格式，只包含数字'
    },
    
    // 工具类型 -> Tool Type
    toolType: {
      selectors: [
        'select[name="055fc74f-23d9-420f-a367-93276f0d0421"]',
        'select.f24-fb-form-control:first-of-type',
        'select:has(option[value="AI"])',
        'form select:first-of-type'
      ],
      method: 'select',
      validation: 'required',
      defaultValue: 'AI',
      availableOptions: ['AI', 'SaaS'],
      notes: '工具类型，默认选择AI'
    },
    
    // 工具URL -> Tool URL
    siteUrl: {
      selectors: [
        'input[name="46a57c52-3f9f-4bfc-92e0-99c1db697493"]',
        'input[placeholder="https://example.io"]',
        'input[pattern*="http"]',
        'input.f24-fb-form-control[type="text"]:nth-of-type(3)'
      ],
      method: 'value',
      validation: 'required|url',
      notes: '工具URL，使用website-info.js中的siteUrl字段'
    },
    
    // 工具描述 -> Tool Description
    siteDescription: {
      selectors: [
        'textarea[name*="tool_description"]',
        'textarea.f24-fb-form-control',
        'form textarea:first-of-type',
        'textarea'
      ],
      method: 'value',
      validation: 'required',
      maxLength: 100,
      notes: '工具描述，使用website-info.js中的siteDescription字段，限制100字符'
    },
    
    // 主分类 -> Select a Main Category
    category: {
      selectors: [
        'select[name="430ece87-c5df-4ac3-9a83-047d4615ac2f"]',
        'select:has(option[value="Productivity"])',
        'select.f24-fb-form-control:nth-of-type(2)',
        'form select:nth-of-type(2)'
      ],
      method: 'select',
      validation: 'required',
      defaultValue: 'Productivity',
      availableOptions: [
        'Art & Pictures', 'Avatars', 'Copywriting', 'Chat & Customer Support',
        'Design', 'Developer', 'Music', 'Productivity', 'Prompts', 'Research',
        'SEO', 'Social Media', 'Text to Speech', 'Transcriber', 'Video',
        'Analytics & Affiliates', 'Calendar & Meetings', 'Content Marketing',
        'CRM', 'Email Marketing', 'Finance, HR & Legal', 'Lead Generation',
        'Survey & Quiz', 'Website Builder & Hosting', 'Workflow & Project Management'
      ],
      notes: '主分类，默认选择Productivity'
    },
    
    // 工具费用状态 -> Tool Cost / Status
    pricing: {
      selectors: [
        'select[name="a7b16854-67c4-4b8c-930a-19cf4a7bf675"]',
        'select:has(option[value="Free"])',
        'select.f24-fb-form-control:nth-of-type(3)',
        'form select:nth-of-type(3)'
      ],
      method: 'select',
      validation: 'required',
      defaultValue: 'Free',
      availableOptions: ['Free', 'Freemium', 'Paid', 'Waitlist', 'Open Source'],
      notes: '工具费用状态，默认选择Free'
    }
  },
  
  // 自定义填写函数
  customFillElement: async function(element, value, config) {
    console.log(`myCloud Media自定义填写: ${element.name || element.tagName}, 方法: ${config.method}`);
    
    switch (config.method) {
      case 'value':
        // 标准输入框和文本域处理
        element.focus();
        await new Promise(resolve => setTimeout(resolve, 100));
        
        // 清空现有内容
        element.value = '';
        await new Promise(resolve => setTimeout(resolve, 50));
        
        // 处理默认值
        let finalValue = value;
        if (config.defaultValue && (!value || value.trim() === '')) {
          finalValue = config.defaultValue;
        }

        // 处理字符限制
        if (config.maxLength && finalValue.length > config.maxLength) {
          finalValue = finalValue.substring(0, config.maxLength);
          console.log(`⚠️ 内容被截断到${config.maxLength}字符: ${finalValue}`);
        }

        // 设置新值
        element.value = finalValue;
        
        // 触发事件
        element.dispatchEvent(new Event('input', { bubbles: true }));
        element.dispatchEvent(new Event('change', { bubbles: true }));
        element.dispatchEvent(new Event('blur', { bubbles: true }));
        
        console.log(`✓ 填写字段: ${finalValue.substring(0, 50)}...`);
        break;
        
      case 'select':
        // 下拉选择处理
        element.focus();
        await new Promise(resolve => setTimeout(resolve, 100));
        
        const targetValue = config.defaultValue;
        
        // 查找匹配的选项
        const option = Array.from(element.options).find(opt => 
          opt.text.includes(targetValue) || opt.value.includes(targetValue)
        );
        
        if (option) {
          element.value = option.value;
          element.dispatchEvent(new Event('change', { bubbles: true }));
          console.log(`✓ 选择选项: ${option.text}`);
        } else {
          console.log(`⚠️ 未找到选项: ${targetValue}`);
        }
        break;
        
      default:
        element.value = value;
        element.dispatchEvent(new Event('input', { bubbles: true }));
        element.dispatchEvent(new Event('change', { bubbles: true }));
    }
  },
  
  // 提交流程配置
  submitConfig: {
    submitButton: [
      'button[type="submit"]',
      'input[type="submit"]',
      'button:contains("Send")',
      'input[value*="Send"]'
    ],
    submitMethod: 'click',
    waitAfterFill: 2000,
    waitAfterSubmit: 4000,
    successIndicators: [
      'text:contains("submitted")',
      'text:contains("thank you")',
      'text:contains("received")',
      'text:contains("review")'
    ],
    errorIndicators: [
      'text:contains("error")',
      'text:contains("required")',
      'text:contains("invalid")'
    ]
  },
  
  // 特殊处理
  specialHandling: {
    requiresLogin: false,
    hasCaptcha: false,
    hasFileUpload: false,
    isUKBased: true, // 英国公司
    hasPrivacyPolicy: true, // 有隐私政策
    hasHumanReview: true, // 人工审核
    requiresCompanyInfo: true, // 需要公司信息
    
    // 表单验证规则
    formValidation: {
      requiredFields: ['firstName', 'lastName', 'contactEmail', 'companyName', 'phone', 'toolType', 'siteUrl', 'siteDescription', 'category', 'pricing'],
      optionalFields: [],
      emailValidation: true,
      urlValidation: true,
      phoneValidation: true,
      characterLimits: {
        siteDescription: 100,
        phone: 50
      },
      phonePattern: '\\d*' // 只允许数字
    },
    
    // 特殊注意事项
    notes: [
      '这是myCloud Media的AI和SaaS工具目录提交表单',
      '表单包含10个字段，全部必填',
      '英国数字营销公司运营，总部在英国',
      '需要详细的公司联系信息',
      '有人工审核流程，3-5天内发布',
      '要求网站有有效的隐私政策',
      '要求有可识别的公司联系信息',
      '保留不列出所有提交的权利',
      '数据安全传输到英国托管的基础设施',
      '支持25个主要分类',
      '支持5种费用状态',
      '专注于AI和SaaS工具'
    ]
  }
};

// 自定义处理函数
export function handleMyCloudMediaSubmission(data, _rule) {
  console.log('Processing myCloud Media form submission...');

  const processedData = { ...data };

  // 确保URL格式正确
  if (processedData.siteUrl && !processedData.siteUrl.startsWith('http')) {
    processedData.siteUrl = 'https://' + processedData.siteUrl;
  }

  // 处理描述字符限制
  if (processedData.siteDescription && processedData.siteDescription.length > 100) {
    processedData.siteDescription = processedData.siteDescription.substring(0, 100);
  }

  // 设置默认值
  processedData.firstName = 'Quinn';
  processedData.lastName = 'Halberg';
  processedData.phone = '14158372947'; // 美国格式，只有数字
  processedData.toolType = 'AI';
  processedData.category = 'Productivity';
  processedData.pricing = 'Free';

  return processedData;
}
