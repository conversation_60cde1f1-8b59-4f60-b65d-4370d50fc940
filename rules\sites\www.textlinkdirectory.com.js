// TextLinkDirectory.com 网站规则配置
// 网站: https://www.textlinkdirectory.com/index.php?go=addpage&catid=43
// 最后更新: 2025-07-24

export const SITE_RULE = {
  // 基本信息
  domain: 'www.textlinkdirectory.com',
  siteName: 'Text Link Directory',
  priority: 1,
  lastUpdated: '2025-07-24',

  // 字段映射规则
  fieldMappings: {
    // 网站URL -> Url
    siteUrl: {
      selectors: [
        'input[name="url"]',
        'input[type="text"][name="url"]'
      ],
      method: 'value',
      validation: 'required|url',
      notes: '网站URL地址，最多255字符'
    },

    // 邮箱 -> Email
    contactEmail: {
      selectors: [
        '#email',
        'input[name="email"]'
      ],
      method: 'value',
      validation: 'required|email',
      notes: '联系邮箱'
    },

    // 网站标题 -> Title
    siteName: {
      selectors: [
        'input[name="title"]',
        'input[type="text"][name="title"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '网站标题，最多100字符'
    },

    // 网站描述 -> Description
    siteDescription: {
      selectors: [
        'textarea[name="description"]',
        'textarea[cols="60"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '网站描述，最多250字符'
    },

    // 链接类型 -> Link Type
    linkType: {
      selectors: [
        'input[name="linkType"]',
        'input[value="free"]',
        'input[value="reciprocal"]',
        'input[value="sponsor"]'
      ],
      method: 'radio',
      validation: 'required',
      notes: '链接类型选择：免费、互惠、付费'
    },

    // 互惠链接URL -> Reciprocal Link URL
    reciprocalUrl: {
      selectors: [
        '#rLink',
        'input[name="rLink"]'
      ],
      method: 'value',
      validation: 'optional|url',
      notes: '互惠链接URL，最多255字符'
    },

    // 互惠链接代码 -> Reciprocal Link Code
    reciprocalCode: {
      selectors: [
        'textarea[name="RECPR_TEXT"]',
        'textarea[readonly]'
      ],
      method: 'value',
      validation: 'optional',
      notes: '互惠链接HTML代码'
    },

    // 关键词 -> Keywords
    keywords: {
      selectors: [
        'input[name="keywords"]',
        'input[size="100"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '关键词，最多200字符'
    }
  },
  // 提交流程配置
  submitConfig: {
    submitButton: 'input[type="submit"], input[value="Submit"]',
    submitMethod: 'manual', // 手动提交
    waitAfterFill: 0,
    waitAfterSubmit: 0,
    successIndicators: ['.success-message'],
    errorIndicators: ['.error-message']
  },

  // 特殊处理
  specialHandling: {
    requiresLogin: false,
    hasCaptcha: false,
    hasFileUpload: false,
    customScript: 'handleTextLinkDirectorySubmission',
    formValidation: {
      requiredFields: ['siteUrl', 'contactEmail', 'siteName', 'siteDescription', 'linkType', 'keywords'],
      emailValidation: true,
      urlValidation: true
    },
    notes: [
      'Text Link Directory 文本链接目录',
      '传统的网站目录服务',
      '提供三种提交选项：',
      '- 免费链接：30-60天审核，不保证收录',
      '- 互惠链接：免费，需要反向链接，30天审核',
      '- 付费链接：$5一次性费用，24小时审核，保证收录',
      '付费链接显示在分类页面顶部',
      '描述限制250字符，有实时计数器',
      '传统表格样式界面',
      '手动提交模式'
    ]
  }
};

// 自定义处理函数
export function handleTextLinkDirectorySubmission(data) {
  console.log('Processing Text Link Directory form submission...');

  const processedData = { ...data };

  // 确保URL格式正确
  if (processedData.siteUrl && !processedData.siteUrl.startsWith('http')) {
    processedData.siteUrl = 'https://' + processedData.siteUrl;
  }

  // 处理互惠链接URL
  if (processedData.reciprocalUrl && !processedData.reciprocalUrl.startsWith('http')) {
    processedData.reciprocalUrl = 'https://' + processedData.reciprocalUrl;
  }

  return processedData;
}

// 自定义元素填写函数
export async function customFillElement(element, value, config) {
  console.log(`Text Link Directory自定义填写: ${element.tagName}, 方法: ${config.method}`);

  switch (config.method) {
    case 'value':
      // 标准输入框和文本域处理
      element.focus();
      await new Promise(resolve => setTimeout(resolve, 200));

      // 清除默认值（如果是URL字段的http://）
      if (element.name === 'url' && element.value === 'http://') {
        element.value = '';
      }
      if (element.name === 'rLink' && element.value === 'http://') {
        element.value = '';
      }

      element.value = value;
      element.dispatchEvent(new Event('input', { bubbles: true }));
      element.dispatchEvent(new Event('change', { bubbles: true }));
      element.dispatchEvent(new Event('blur', { bubbles: true }));

      // 触发字符计数器（如果是描述字段）
      if (element.name === 'description') {
        const event = new Event('keyup', { bubbles: true });
        element.dispatchEvent(event);
      }

      console.log(`✓ 填写字段: ${element.name} = "${value}"`);
      return true;

    case 'radio':
      // 单选按钮处理 - 默认选择免费选项
      const freeRadio = document.querySelector('input[name="linkType"][value="free"]');
      if (freeRadio) {
        freeRadio.checked = true;
        freeRadio.dispatchEvent(new Event('change', { bubbles: true }));
        console.log(`✓ 选择链接类型: 免费链接`);
        return true;
      }
      break;

    default:
      element.value = value;
      element.dispatchEvent(new Event('input', { bubbles: true }));
      return true;
  }

  return false;
}