// AnyFP.com 网站规则配置
// 网站: https://anyfp.com/contact/
// 最后更新: 2025-07-24

export const SITE_RULE = {
  domain: 'anyfp.com',
  siteName: 'AnyFP',
  priority: 1,
  lastUpdated: '2025-07-24',

  fieldMappings: {
    siteName: {
      selectors: [
        '#ff_3_post_title',
        'input[name="post_title"]',
        'input[placeholder*="tool/service name"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '工具名称'
    },

    siteUrl: {
      selectors: [
        '#ff_3_url',
        'input[name="url"]',
        'input[type="url"]'
      ],
      method: 'value',
      validation: 'required|url',
      notes: '工具网站链接'
    },

    category: {
      selectors: [
        '#ff_3_category',
        'select[name="category"]',
        'select[data-name="category"]'
      ],
      method: 'select',
      validation: 'required',
      defaultValue: '26',
      notes: '工具分类'
    },

    siteDescription: {
      selectors: [
        '#ff_3_post_content',
        'textarea[name="post_content"]',
        'textarea[placeholder*="write your content"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '详细描述'
    },

    tagline: {
      selectors: [
        '#ff_3_post_excerpt',
        'textarea[name="post_excerpt"]',
        'textarea[placeholder*="summary"]'
      ],
      method: 'value',
      validation: 'optional',
      notes: '简短描述'
    },

    tags: {
      selectors: [
        '#ff_3_post_tag',
        'input[name="post_tag"]',
        'input[data-name="post_tag"]'
      ],
      method: 'value',
      validation: 'optional',
      notes: '标签'
    }
  },

  submitConfig: {
    submitButton: 'button[type="submit"], .ff-btn-submit',
    submitMethod: 'click',
    successIndicators: ['.ff-message-success'],
    errorIndicators: ['.ff-message-error']
  },

  specialHandling: {
    requiresLogin: false,
    hasCaptcha: true,
    hasFileUpload: true,
    customScript: 'handleAnyFPSubmission',
    formValidation: {
      requiredFields: ['siteName', 'siteUrl', 'category', 'siteDescription'],
      emailValidation: false,
      urlValidation: true
    },
    notes: [
      '使用FluentForms表单',
      '有reCAPTCHA v3验证',
      '需要上传特色图片',
      '有反垃圾字段',
      '分类选择必填'
    ]
  }
};

export function handleAnyFPSubmission(data, rule) {
  const processedData = { ...data };

  if (processedData.siteUrl && !processedData.siteUrl.startsWith('http')) {
    processedData.siteUrl = 'https://' + processedData.siteUrl;
  }

  if (processedData.category) {
    const categoryMap = {
      'Code': '26',
      'Design': '16',
      'Writing': '14',
      'Image': '18',
      'Video': '12',
      'Audio': '20',
      'Business': '28',
      'Productivity': '22'
    };
    processedData.category = categoryMap[processedData.category] || '26';
  }

  return processedData;
}

export async function customFillElement(element, value, config) {
  if (element.tagName === 'SELECT') {
    const options = element.querySelectorAll('option');
    const option = Array.from(options).find(opt =>
      opt.value === value || opt.textContent.trim().toLowerCase() === value.toLowerCase()
    );
    if (option) {
      element.value = option.value;
      element.dispatchEvent(new Event('change', { bubbles: true }));
      return true;
    }
  }

  if (element.tagName === 'INPUT' || element.tagName === 'TEXTAREA') {
    element.value = value;
    element.dispatchEvent(new Event('input', { bubbles: true }));
    return true;
  }

  if (element.type === 'file') {
    console.log('⚠️ 文件上传字段需要用户手动处理');
    return false;
  }

  return false;
}