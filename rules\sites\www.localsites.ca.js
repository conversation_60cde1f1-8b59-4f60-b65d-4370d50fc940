// LocalSites.ca 网站规则配置
// 网站: http://www.localsites.ca/article/basic-listing--add-your-site-360.asp
// 最后更新: 2025-07-24

export const SITE_RULE = {
  domain: 'www.localsites.ca',
  siteName: 'Local Sites Canada',
  priority: 1,
  lastUpdated: '2025-07-24',

  fieldMappings: {
    firstName: {
      selectors: [
        '#First_Name',
        'input[name="First_Name"]',
        'label:contains("First Name") + input'
      ],
      method: 'value',
      validation: 'required',
      notes: '名字，最多50字符'
    },

    lastName: {
      selectors: [
        '#Last_Name',
        'input[name="Last_Name"]',
        'label:contains("Last Name") + input'
      ],
      method: 'value',
      validation: 'required',
      notes: '姓氏，最多50字符'
    },

    contactEmail: {
      selectors: [
        '#Email',
        'input[name="Email"]',
        'label:contains("Email") + input'
      ],
      method: 'value',
      validation: 'required|email',
      notes: '联系邮箱，最多50字符'
    },

    companyName: {
      selectors: [
        '#Company_Name',
        'input[name="Company_Name"]',
        'label:contains("Company Name") + input'
      ],
      method: 'value',
      validation: 'required',
      notes: '公司名称，最多50字符'
    },

    siteUrl: {
      selectors: [
        '#Web_Site_URL',
        'input[name="Web_Site_URL"]',
        'label:contains("Web Site URL") + input'
      ],
      method: 'value',
      validation: 'required|url',
      notes: '网站URL，最多200字符，预填http://'
    },

    category: {
      selectors: [
        '#Business_Category',
        'select[name="Business_Category"]',
        'label:contains("Business Category") + select'
      ],
      method: 'select',
      validation: 'required',
      defaultValue: 'Business_&_Economy',
      notes: '业务分类'
    },

    serviceArea: {
      selectors: [
        'input[name="Service_Area"][value="local"]',
        'input[type="radio"][value="local"]'
      ],
      method: 'radio',
      validation: 'required',
      defaultValue: 'local',
      notes: '服务区域，默认选择Local'
    },

    siteDescription: {
      selectors: [
        '#Web_Site_Description',
        'textarea[name="Web_Site_Description"]',
        'label:contains("Web Site Description") + textarea'
      ],
      method: 'value',
      validation: 'required',
      notes: '网站描述，最多75字符'
    },

    city: {
      selectors: [
        '#City',
        'input[name="City"]',
        'label:contains("City") + input'
      ],
      method: 'value',
      validation: 'required',
      notes: '城市，人口超过20000的最近城市'
    },

    province: {
      selectors: [
        '#Province',
        'select[name="Province"]',
        'label:contains("Province") + select'
      ],
      method: 'select',
      validation: 'required',
      defaultValue: 'ON',
      notes: '省份，默认Ontario'
    }
  },

  submitConfig: {
    submitButton: '#conf-submit, input[name="conf-submit"]',
    submitMethod: 'click',
    successIndicators: ['.success-message'],
    errorIndicators: ['.error-message']
  },

  specialHandling: {
    requiresLogin: false,
    hasCaptcha: true,
    hasFileUpload: false,
    customScript: 'handleLocalSitesSubmission',
    formValidation: {
      requiredFields: ['firstName', 'lastName', 'contactEmail', 'companyName', 'siteUrl', 'category', 'serviceArea', 'siteDescription', 'city', 'province'],
      emailValidation: true,
      urlValidation: true
    },
    notes: [
      '加拿大本地企业目录',
      '多步骤表单提交',
      '有Google reCAPTCHA',
      '字符数限制严格',
      '需要选择省份和城市',
      '免费基础列表服务'
    ]
  }
};

export function handleLocalSitesSubmission(data, rule) {
  const processedData = { ...data };

  // URL格式化，确保以http://开头
  if (processedData.siteUrl && !processedData.siteUrl.startsWith('http')) {
    processedData.siteUrl = 'http://' + processedData.siteUrl;
  }

  // 自动选择默认业务分类
  const categorySelect = document.querySelector('#Business_Category');
  if (categorySelect) {
    categorySelect.value = 'Business_&_Economy';
  }

  // 自动选择Local服务区域
  const localRadio = document.querySelector('input[name="Service_Area"][value="local"]');
  if (localRadio) {
    localRadio.checked = true;
  }

  // 自动选择默认省份Ontario
  const provinceSelect = document.querySelector('#Province');
  if (provinceSelect) {
    provinceSelect.value = 'ON';
  }

  return processedData;
}

export async function customFillElement(element, value, config) {
  // 处理下拉选择框
  if (element.tagName === 'SELECT') {
    let targetValue = null;

    if (element.name === 'Business_Category') {
      targetValue = 'Business_&_Economy';
    } else if (element.name === 'Province') {
      targetValue = 'ON';
    }

    if (targetValue) {
      const options = element.querySelectorAll('option');
      const option = Array.from(options).find(opt => opt.value === targetValue);
      if (option) {
        element.value = option.value;
        element.dispatchEvent(new Event('change', { bubbles: true }));
        return true;
      }
    }
  }

  // 处理单选按钮
  if (element.type === 'radio' && element.value === 'local') {
    element.checked = true;
    element.dispatchEvent(new Event('change', { bubbles: true }));
    return true;
  }

  if (element.tagName === 'INPUT' || element.tagName === 'TEXTAREA') {
    element.value = value;
    element.dispatchEvent(new Event('input', { bubbles: true }));
    element.dispatchEvent(new Event('change', { bubbles: true }));
    return true;
  }

  return false;
}