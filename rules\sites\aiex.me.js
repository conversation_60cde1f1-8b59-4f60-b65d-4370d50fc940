// Aiex.me 网站规则配置
// 网站: https://aiex.me/submit/form?plan=free
// 最后更新: 2025-07-09

export const SITE_RULE = {
  // 基本信息
  domain: 'aiex.me',
  siteName: 'Aiex',
  priority: 1,
  lastUpdated: '2025-07-09',
  
  // 字段映射规则
  fieldMappings: {
    // 网站名称 -> Website Name
    siteName: {
      selectors: [
        'input[placeholder="AI Explorer"]',
        'label:contains("Website Name") + input',
        'input:first-of-type'
      ],
      method: 'value',
      validation: 'required',
      notes: '网站名称'
    },
    
    // 网站URL -> Website URL
    siteUrl: {
      selectors: [
        'input[placeholder="https://aiex.me/"]',
        'label:contains("Website URL") + input',
        'input:nth-of-type(2)'
      ],
      method: 'value',
      validation: 'required|url',
      notes: '网站URL地址'
    },
    
    // 邮箱 -> Email
    contactEmail: {
      selectors: [
        'input[type="email"]',
        'input[placeholder="<EMAIL>"]',
        'label:contains("Email") + input'
      ],
      method: 'value',
      validation: 'required|email',
      notes: '联系邮箱'
    },
    
    // 发布年份 -> Release Year
    releaseYear: {
      selectors: [
        'input[placeholder="2020"]',
        'label:contains("Release Year") + input',
        'input:nth-of-type(4)'
      ],
      method: 'value',
      validation: 'required',
      notes: '发布年份'
    },
    
    // 定价模式 -> Pricing (选择Free)
    pricing: {
      selectors: [
        'input[type="checkbox"] + span:contains("Free")',
        'label:has(span:contains("Free")) input[type="checkbox"]'
      ],
      method: 'checkbox',
      validation: 'required',
      targetValue: true,
      notes: '定价模式，勾选Free'
    },
    
    // 分类 -> Categories (选择Productivity)
    category: {
      selectors: [
        'input[type="checkbox"] + span:contains("Productivity")',
        'label:has(span:contains("Productivity")) input[type="checkbox"]'
      ],
      method: 'checkbox',
      validation: 'required',
      targetValue: true,
      notes: '产品分类，勾选Productivity'
    }
  },
  
  // 提交流程配置
  submitConfig: {
    submitButton: 'button:contains("Next"), button[type="submit"]',
    submitMethod: 'click',
    waitAfterFill: 2000, // 填写后等待2秒
    waitAfterSubmit: 3000, // 提交后等待3秒
    successIndicators: [
      '.success-message',
      '.alert-success',
      '.notification-success',
      '[class*="success"]'
    ],
    errorIndicators: [
      '.error-message',
      '.alert-error',
      '.alert-danger',
      '[class*="error"]'
    ]
  },
  
  // 特殊处理
  specialHandling: {
    requiresLogin: false,
    hasCaptcha: false,
    hasFileUpload: false,
    isMultiStep: true, // 多步骤表单
    
    // 表单验证规则
    formValidation: {
      requiredFields: ['siteName', 'siteUrl', 'contactEmail', 'releaseYear', 'pricing', 'category'],
      emailValidation: true,
      urlValidation: true
    },
    
    // 特殊注意事项
    notes: [
      '多步骤表单，这是第一步：Required Information',
      '表单包含6个字段：网站名称、URL、邮箱、发布年份、定价、分类',
      '所有字段都是必填的',
      '定价至少选择一个选项（推荐Free）',
      '分类需要选择1-3个类别（推荐Productivity）',
      '完成后点击Next按钮进入下一步',
      '使用现代化的Tailwind CSS设计'
    ]
  }
};

// 自定义处理函数
export function handleAiexSubmission(data, rule) {
  console.log('Processing Aiex.me submission...');
  
  // 特殊处理逻辑
  const processedData = { ...data };
  
  // 确保URL格式正确
  if (processedData.siteUrl && !processedData.siteUrl.startsWith('http')) {
    processedData.siteUrl = 'https://' + processedData.siteUrl;
  }
  
  // 设置发布年份为当前年份
  if (!processedData.releaseYear) {
    processedData.releaseYear = '2025';
  }
  
  // 确保定价设置为Free
  processedData.pricing = true;
  
  // 确保分类设置为Productivity
  processedData.category = true;
  
  return processedData;
}

// 自定义元素填写函数，专门处理复选框
export async function customFillElement(element, value, config) {
  console.log('🔧 Aiex自定义填写函数被调用:', element, value);
  
  // 处理复选框
  if (element.type === 'checkbox') {
    try {
      if (config.targetValue === true) {
        element.checked = true;
        element.dispatchEvent(new Event('change', { bubbles: true }));
        console.log('✅ 勾选复选框:', element.nextElementSibling?.textContent || 'unknown');
        return true;
      }
    } catch (error) {
      console.warn('复选框处理失败:', error);
    }
  }
  
  // 处理通过label查找的复选框
  if (config.notes && config.notes.includes('勾选')) {
    try {
      // 查找包含特定文本的复选框
      const checkboxes = document.querySelectorAll('input[type="checkbox"]');
      for (const checkbox of checkboxes) {
        const label = checkbox.closest('label');
        const span = label?.querySelector('span');
        if (span && (
          (config.notes.includes('Free') && span.textContent.includes('Free')) ||
          (config.notes.includes('Productivity') && span.textContent.includes('Productivity'))
        )) {
          checkbox.checked = true;
          checkbox.dispatchEvent(new Event('change', { bubbles: true }));
          console.log('✅ 通过文本匹配勾选复选框:', span.textContent);
          return true;
        }
      }
    } catch (error) {
      console.warn('文本匹配复选框失败:', error);
    }
  }
  
  // 默认处理
  return false;
}
