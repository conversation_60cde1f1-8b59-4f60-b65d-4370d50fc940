// AI Site Submitter - 10015Io 规则配置
// 自动生成于: 2025/7/15 16:58:21
// 域名: 10015.io

export const SITE_RULE = {
  "domain": "10015.io",
  "siteName": "10015Io",
  "lastUpdated": "2025-07-15T08:58:21.869Z",
  "fieldMappings": {
    "siteName": {
      "selectors": [
        "#name",
        "input[name='name']",
        "input[placeholder*='product name' i]"
      ],
      "type": "input",
      "fillMethod": "value",
      "required": false,
      "validation": "required"
    },
    "siteUrl": {
      "selectors": [
        "#url",
        "input[name='url']",
        "input[placeholder*='url' i]"
      ],
      "type": "input",
      "fillMethod": "value",
      "required": false,
      "validation": "required"
    },
    "siteDescription": {
      "selectors": [
        "#shortDescription",
        "input[name='shortDescription']",
        "textarea[name='shortDescription']"
      ],
      "type": "input",
      "fillMethod": "value",
      "required": false,
      "validation": "required"
    },
    "detailedIntro": {
      "selectors": [
        "#description",
        "textarea[name='description']",
        "textarea[placeholder*='details' i]"
      ],
      "type": "input",
      "fillMethod": "value",
      "required": false,
      "validation": "required"
    },
    "category": {
      "selectors": [
        "#category",
        "select[name='category']"
      ],
      "type": "input",
      "fillMethod": "value",
      "defaultValue": "Development",
      "required": false,
      "validation": "required"
    },
    "tags": {
      "selectors": [
        "#otherTags",
        "input[name='otherTags']",
        "input[placeholder*='tag' i]"
      ],
      "type": "input",
      "fillMethod": "value",
      "required": false,
      "validation": "optional"
    },
    "pricing": {
      "selectors": [
        "#pricing",
        "select[name='pricing']"
      ],
      "type": "input",
      "fillMethod": "value",
      "defaultValue": "Free",
      "required": false,
      "validation": "required"
    },
    "imageUrl": {
      "selectors": [
        "#imageUrl",
        "input[name='imageUrl']",
        "input[placeholder*='image url' i]"
      ],
      "type": "input",
      "fillMethod": "value",
      "required": false,
      "validation": "optional"
    },
    "siteTitle": {
      "selectors": [
        "#title",
        "input[name='title']",
        "input[placeholder*='title' i]"
      ],
      "type": "input",
      "fillMethod": "value",
      "required": false,
      "validation": "optional"
    },
    "tagline": {
      "selectors": [
        "#tagline",
        "input[name='tagline']",
        "input[placeholder*='tagline' i]"
      ],
      "type": "input",
      "fillMethod": "value",
      "required": false,
      "validation": "optional"
    }
  },
  "formInfo": {
    "description": "10015.io 产品提交表单，用于收录新工具/产品",
    "submitSelector": "button[type=\"submit\"], input[type=\"submit\"], button",
    "totalFields": 19,
    "notes": [
      "表单包含文件上传字段imageUploaded/iconUploaded，可忽略",
      "字段name重复出现，以第一个为准",
      "select字段options需运行时获取"
    ]
  },
  "metadata": {
    "generatedBy": "AI",
    "generatedAt": "2025-07-15T08:58:21.869Z",
    "version": "3.0.0",
    "aiModel": "moonshotai/Kimi-K2-Instruct"
  }
};

// 自定义处理函数 (可选)
export function handle10015IoSubmission(data, rule) {
  console.log('Processing 10015Io form submission...');
  
  const processedData = { ...data };
  
  // 在这里添加特殊处理逻辑
  // 例如：URL格式化、字段验证、默认值设置等
  
  return processedData;
}

// 自定义元素填写函数 (可选)
export async function customFillElement(element, value, config) {
  console.log('🔧 10015Io 自定义填写函数被调用:', element, value);
  
  // 在这里添加特殊的元素填写逻辑
  // 例如：处理特殊的UI组件、异步操作等
  
  return false; // 返回 false 使用默认填写方法
}