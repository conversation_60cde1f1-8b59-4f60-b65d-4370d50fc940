// AI Site Submitter - SitesWebDirectory 规则配置
// 自动生成于: 2025/7/15 19:22:14
// 域名: www.siteswebdirectory.com

export const SITE_RULE = {
  "domain": "www.siteswebdirectory.com",
  "siteName": "SitesWebDirectory",
  "lastUpdated": "2025-07-15T11:22:14.818Z",
  "fieldMappings": {
    "siteName": {
      "selectors": [
        "#TITLE",
        "input[name='TITLE']",
        "input[id='TITLE']"
      ],
      "type": "input",
      "fillMethod": "value",
      "required": false,
      "validation": "required"
    },
    "siteUrl": {
      "selectors": [
        "#URL",
        "input[name='URL']",
        "input[id='URL']"
      ],
      "type": "input",
      "fillMethod": "value",
      "required": false,
      "validation": "required"
    },
    "siteDescription": {
      "selectors": [
        "#DESCRIPTION",
        "textarea[name='DESCRIPTION']",
        "textarea[id='DESCRIPTION']"
      ],
      "type": "input",
      "fillMethod": "value",
      "required": false,
      "validation": "required"
    },
    "fullName": {
      "selectors": [
        "#OWNER_NAME",
        "input[name='OWNER_NAME']",
        "input[id='OWNER_NAME']"
      ],
      "type": "input",
      "fillMethod": "value",
      "required": false,
      "validation": "optional"
    },
    "contactEmail": {
      "selectors": [
        "#OWNER_EMAIL",
        "input[name='OWNER_EMAIL']",
        "input[id='OWNER_EMAIL']"
      ],
      "type": "input",
      "fillMethod": "value",
      "required": false,
      "validation": "required"
    },
    "agreeTerms": {
      "selectors": [
        "#AGREERULES",
        "input[name='AGREERULES']",
        "input[id='AGREERULES']"
      ],
      "type": "input",
      "fillMethod": "value",
      "required": false,
      "validation": "required"
    }
  },
  "formInfo": {
    "description": "网站目录提交表单，用于将网站添加到目录中",
    "submitSelector": "[name='continue']",
    "totalFields": 8,
    "notes": [
      "包含简单算术验证(DO_MATH字段)，需手动填写",
      "OWNER_NEWSLETTER_ALLOW为可选订阅，无需处理"
    ]
  },
  "metadata": {
    "generatedBy": "AI",
    "generatedAt": "2025-07-15T11:22:14.818Z",
    "version": "3.0.0",
    "aiModel": "moonshotai/Kimi-K2-Instruct"
  }
};

// 自定义处理函数 (可选)
export function handleWwwSiteswebdirectoryComSubmission(data, rule) {
  console.log('Processing SitesWebDirectory form submission...');
  
  const processedData = { ...data };
  
  // 在这里添加特殊处理逻辑
  // 例如：URL格式化、字段验证、默认值设置等
  
  return processedData;
}

// 自定义元素填写函数 (可选)
export async function customFillElement(element, value, config) {
  console.log('🔧 SitesWebDirectory 自定义填写函数被调用:', element, value);
  
  // 在这里添加特殊的元素填写逻辑
  // 例如：处理特殊的UI组件、异步操作等
  
  return false; // 返回 false 使用默认填写方法
}