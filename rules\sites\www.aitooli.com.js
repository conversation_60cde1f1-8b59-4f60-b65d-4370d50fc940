// AITooli.com 网站规则配置
// 网站: https://www.aitooli.com/submit
// 最后更新: 2025-07-25

export const SITE_RULE = {
  // 基本信息
  domain: 'www.aitooli.com',
  siteName: 'AI Tooli',
  priority: 1,
  lastUpdated: '2025-07-25',
  
  // 字段映射规则
  fieldMappings: {
    // 产品名称 -> Product Name (Must include AI keywords)
    siteName: {
      selectors: [
        'input[name="website"]',
        'input[placeholder*="Tap4 AI"]',
        'input[id*="form-item"]:first-of-type'
      ],
      method: 'value',
      validation: 'required',
      notes: '产品名称，必须包含AI关键词'
    },

    // 网站URL -> Website URL (HTTPS required)
    siteUrl: {
      selectors: [
        'input[name="url"]',
        'input[placeholder*="https://aitooli.com/"]',
        'input[id*="form-item"]:last-of-type'
      ],
      method: 'value',
      validation: 'required|url',
      notes: '网站URL，必须使用HTTPS'
    }
  },

  // 表单信息
  formInfo: {
    submitButton: {
      selectors: [
        'button[type="submit"]',
        'button:contains("Submit for Review")',
        '.flex-center.mt-auto'
      ],
      notes: '提交审核按钮'
    },
    formContainer: 'form.mx-3.mb-5',
    totalFields: 2
  },

  // 填写策略
  fillStrategy: {
    order: ['siteName', 'siteUrl'],
    delay: 300,
    waitForLoad: true
  }
};
