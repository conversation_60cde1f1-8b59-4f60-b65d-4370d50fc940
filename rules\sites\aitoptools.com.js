// AITopTools.com 网站规则配置
// 网站: https://aitoptools.com/account/submit-tool/
// 最后更新: 2025-07-06

export const SITE_RULE = {
  // 基本信息
  domain: 'aitoptools.com',
  siteName: 'AI Top Tools',
  priority: 1,
  lastUpdated: '2025-07-06',
  
  // 字段映射规则
  fieldMappings: {
    // AI工具名称 -> AI Tool Name (使用siteName)
    siteName: {
      selectors: [
        '#ai_tool_title',
        'input[name="ai_tool_title"]',
        'input[data-field-name="ai_tool_title"]'
      ],
      method: 'value',
      validation: 'required',
      notes: 'AI工具名称，使用website-info.js中的siteName字段'
    },
    
    // 分类组 -> Select Category Group (固定值Design & Media)
    categoryGroup: {
      selectors: [
        'select[name="field_name_level_0"]',
        'select[data-field-name="field_name_level_0"]',
        'select[data-taxonomy="ai-category"]'
      ],
      method: 'select',
      validation: 'required',
      options: ['Business & Productivity', 'Tech & Development', 'Design & Media', 'Lifestyle & Growth', 'Writing & Content'],
      defaultValue: 'Design & Media',
      notes: '分类组，固定选择Design & Media'
    },
    
    // 具体分类 -> Select Category (固定值Design Tool)
    category: {
      selectors: [
        'select[name="field_name_level_1"]',
        'select[data-field-name="field_name_level_1"]'
      ],
      method: 'select',
      validation: 'required',
      options: ['Video Generator', 'Video Editor', 'Voice Generator', 'Art Generator', 'Audio Editor', 'Music Generator', 'Icon Generator', 'Video Enhancer', '3D Model Generators', 'Image Recognition', 'Image Enhancer', 'Dubbing', 'Animation Generator', 'Image Generator', 'Design Tool', 'Logo Generator'],
      defaultValue: 'Design Tool',
      notes: '具体分类，固定选择Design Tool'
    },
    
    // 工具描述 -> Tool Description (使用detailedIntro)
    detailedIntro: {
      selectors: [
        '#ai_tool_content',
        'textarea[name="ai_tool_content"]',
        'textarea[data-field-name="ai_tool_content"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '工具详细描述，使用website-info.js中的detailedIntro字段'
    },
    
    // 简短描述 -> Short Description (使用siteDescription)
    siteDescription: {
      selectors: [
        '#ai_tool_excerpt',
        'input[name="ai_tool_excerpt"]',
        'input[data-field-name="ai_tool_excerpt"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '工具简短描述，使用website-info.js中的siteDescription字段'
    },
    
    // 使用案例1 -> Use Case 1 (使用useCases)
    useCases: {
      selectors: [
        '#ai_tool_use_case_1',
        'input[name="ai_tool_use_case_1"]',
        'input[data-field-name="ai_tool_use_case_1"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '第一个使用案例，使用website-info.js中的useCases字段'
    },
    
    // 使用案例2 -> Use Case 2 (使用secondUseCase)
    secondUseCase: {
      selectors: [
        '#ai_tool_use_case_2',
        'input[name="ai_tool_use_case_2"]',
        'input[data-field-name="ai_tool_use_case_2"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '第二个使用案例，使用website-info.js中的secondUseCase字段'
    },
    
    // 使用案例3 -> Use Case 3 (使用thirdUseCase)
    thirdUseCase: {
      selectors: [
        '#ai_tool_use_case_3',
        'input[name="ai_tool_use_case_3"]',
        'input[data-field-name="ai_tool_use_case_3"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '第三个使用案例，使用website-info.js中的thirdUseCase字段'
    },
    
    // 价格 -> Price (固定值Free)
    price: {
      selectors: [
        '#ai_tool_price',
        'input[name="ai_tool_price"]',
        'input[data-field-name="ai_tool_price"]'
      ],
      method: 'value',
      validation: 'optional',
      defaultValue: 'Free',
      notes: '工具价格，固定填写Free'
    },
    
    // 付费模式 -> Payment Model (固定值Free)
    pricing: {
      selectors: [
        'input[name="ai_tool_payment_model"][value="Free"]',
        'input[data-field-name="ai_tool_payment_model"][value="Free"]'
      ],
      method: 'radio',
      validation: 'required',
      defaultValue: 'Free',
      notes: '付费模式，固定选择Free'
    },
    
    // 工具URL -> Tool Url (使用siteUrl)
    siteUrl: {
      selectors: [
        '#tool_url',
        'input[name="tool_url"]',
        'input[data-field-name="tool_url"]'
      ],
      method: 'value',
      validation: 'required|url',
      notes: '工具网站URL，使用website-info.js中的siteUrl字段'
    }
  },
  
  // 自定义填写函数
  customFillElement: async function(element, value, config) {
    console.log(`AITopTools自定义填写: ${element.id || element.name}, 方法: ${config.method}`);
    
    switch (config.method) {
      case 'value':
        // JetFormBuilder文本输入框处理
        element.focus();
        await new Promise(resolve => setTimeout(resolve, 200));
        
        // 对于有默认值的字段，使用默认值
        const finalValue = config.defaultValue || value;
        
        element.value = finalValue;
        element.dispatchEvent(new Event('input', { bubbles: true }));
        element.dispatchEvent(new Event('change', { bubbles: true }));
        element.dispatchEvent(new Event('blur', { bubbles: true }));
        
        console.log(`✓ 填写字段: ${element.id || element.name} = "${finalValue}"`);
        break;
        
      case 'select':
        // JetFormBuilder下拉选择处理
        if (element.tagName.toLowerCase() === 'select') {
          const targetValue = config.defaultValue || value;
          
          // 尝试按value匹配
          let option = Array.from(element.options).find(opt => 
            opt.value === targetValue || opt.label === targetValue || opt.text === targetValue
          );
          
          // 如果没找到，尝试部分匹配
          if (!option) {
            option = Array.from(element.options).find(opt => 
              opt.text.includes(targetValue) || targetValue.includes(opt.text)
            );
          }
          
          if (option) {
            element.value = option.value;
            element.dispatchEvent(new Event('change', { bubbles: true }));
            console.log(`✓ 选择选项: ${option.text || option.value}`);
          }
        }
        break;
        
      case 'radio':
        // JetFormBuilder单选按钮处理
        if (element.type === 'radio') {
          const targetValue = config.defaultValue || value;
          if (element.value === targetValue) {
            // 先取消选中同组的其他单选按钮
            const sameGroupRadios = document.querySelectorAll(`input[name="${element.name}"]`);
            sameGroupRadios.forEach(radio => {
              radio.checked = false;
            });
            
            // 选中目标单选按钮
            element.checked = true;
            element.dispatchEvent(new Event('change', { bubbles: true }));
            element.dispatchEvent(new Event('click', { bubbles: true }));
            
            console.log(`✓ 选中单选按钮: ${element.value}`);
          }
        }
        break;
        
      default:
        element.value = value;
        element.dispatchEvent(new Event('input', { bubbles: true }));
        element.dispatchEvent(new Event('change', { bubbles: true }));
    }
  },
  
  // 提交流程配置
  submitConfig: {
    submitButton: 'button[type="submit"], input[type="submit"], .jet-form-builder__submit',
    submitMethod: 'click',
    waitAfterFill: 3000,
    waitAfterSubmit: 5000,
    successIndicators: [
      '.jet-form-builder__message--success',
      '.success-message',
      'div:contains("successfully submitted")'
    ],
    errorIndicators: [
      '.jet-form-builder__message--error',
      '.error-message',
      '[class*="error"]'
    ]
  },
  
  // 特殊处理
  specialHandling: {
    requiresLogin: true, // 需要登录
    hasCaptcha: false,
    hasFileUpload: true, // 有工具图片上传功能
    
    // 表单验证规则
    formValidation: {
      requiredFields: ['siteName', 'categoryGroup', 'category', 'detailedIntro', 'siteDescription', 'useCases', 'secondUseCase', 'thirdUseCase', 'pricing', 'siteUrl'],
      emailValidation: false,
      urlValidation: true
    },
    
    // 特殊注意事项
    notes: [
      '这是JetFormBuilder表单，需要登录后访问',
      '有11个字段，其中10个必填，1个可选',
      '包含文件上传功能（工具图片），需要手动处理',
      '分类组固定选择Design & Media',
      '具体分类固定选择Design Tool',
      '付费模式固定选择Free',
      '价格固定填写Free',
      '使用了三个使用案例字段'
    ]
  }
};

// 自定义处理函数
export function handleAITopToolsSubmission(data, rule) {
  console.log('Processing AI Top Tools submission...');
  
  const processedData = { ...data };
  
  // 确保URL格式正确
  if (processedData.siteUrl && !processedData.siteUrl.startsWith('http')) {
    processedData.siteUrl = 'https://' + processedData.siteUrl;
  }
  
  // 设置固定值
  processedData.categoryGroup = 'Design & Media';
  processedData.category = 'Design Tool';
  processedData.pricing = 'Free';
  processedData.price = 'Free';
  
  return processedData;
}
