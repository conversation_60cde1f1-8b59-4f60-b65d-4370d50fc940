// AI Site Submitter - BritainBusinessDirectory 规则配置
// 自动生成于: 2025/7/15 19:16:16
// 域名: www.britainbusinessdirectory.com

export const SITE_RULE = {
  "domain": "www.britainbusinessdirectory.com",
  "siteName": "BritainBusinessDirectory",
  "lastUpdated": "2025-07-15T11:16:16.897Z",
  "fieldMappings": {
    "siteName": {
      "selectors": [
        "[name=\"TITLE\"]",
        "input[name=\"TITLE\"]",
        "#TITLE"
      ],
      "type": "input",
      "fillMethod": "value",
      "required": false,
      "validation": "required"
    },
    "siteUrl": {
      "selectors": [
        "[name=\"URL\"]",
        "input[name=\"URL\"]",
        "#URL"
      ],
      "type": "input",
      "fillMethod": "value",
      "required": false,
      "validation": "required"
    },
    "siteDescription": {
      "selectors": [
        "[name=\"DESCRIPTION\"]",
        "textarea[name=\"DESCRIPTION\"]",
        "#DESCRIPTION"
      ],
      "type": "input",
      "fillMethod": "value",
      "required": false,
      "validation": "required"
    },
    "keywords": {
      "selectors": [
        "[name=\"META_KEYWORDS\"]",
        "input[name=\"META_KEYWORDS\"]",
        "#META_KEYWORDS"
      ],
      "type": "input",
      "fillMethod": "value",
      "required": false,
      "validation": "optional"
    },
    "contactEmail": {
      "selectors": [
        "[name=\"OWNER_EMAIL\"]",
        "input[name=\"OWNER_EMAIL\"]",
        "#OWNER_EMAIL"
      ],
      "type": "input",
      "fillMethod": "value",
      "required": false,
      "validation": "required"
    },
    "fullName": {
      "selectors": [
        "[name=\"OWNER_NAME\"]",
        "input[name=\"OWNER_NAME\"]",
        "#OWNER_NAME"
      ],
      "type": "input",
      "fillMethod": "value",
      "required": false,
      "validation": "optional"
    },
    "linkType": {
      "selectors": [
        "[name=\"LINK_TYPE\"]",
        "input[type=\"radio\"][name=\"LINK_TYPE\"]",
        "input[name=\"LINK_TYPE\"]"
      ],
      "type": "input",
      "fillMethod": "value",
      "defaultValue": "Regular links(Review in 4-6 weeks. Low chance of approval)",
      "required": false,
      "validation": "optional"
    },
    "agreeRules": {
      "selectors": [
        "#AGREERULES",
        "[name=\"AGREERULES\"]",
        "input[type=\"checkbox\"]#AGREERULES"
      ],
      "type": "input",
      "fillMethod": "value",
      "defaultValue": "on",
      "required": false,
      "validation": "required"
    }
  },
  "formInfo": {
    "description": "英国商业目录网站提交表单，包含网站基本信息、SEO元数据和联系信息",
    "submitSelector": "[name=\"submit\"]",
    "totalFields": 15,
    "notes": [
      "表单包含3种链接提交类型选择",
      "需要填写SEO相关的meta keywords和description",
      "可选填写互惠链接信息",
      "必须勾选同意规则才能提交"
    ]
  },
  "metadata": {
    "generatedBy": "AI",
    "generatedAt": "2025-07-15T11:16:16.897Z",
    "version": "3.0.0",
    "aiModel": "moonshotai/Kimi-K2-Instruct"
  }
};

// 自定义处理函数 (可选)
export function handleWwwBritainbusinessdirectoryComSubmission(data, rule) {
  console.log('Processing BritainBusinessDirectory form submission...');
  
  const processedData = { ...data };
  
  // 在这里添加特殊处理逻辑
  // 例如：URL格式化、字段验证、默认值设置等
  
  return processedData;
}

// 自定义元素填写函数 (可选)
export async function customFillElement(element, value, config) {
  console.log('🔧 BritainBusinessDirectory 自定义填写函数被调用:', element, value);
  
  // 在这里添加特殊的元素填写逻辑
  // 例如：处理特殊的UI组件、异步操作等
  
  return false; // 返回 false 使用默认填写方法
}