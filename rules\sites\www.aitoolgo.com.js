// AIToolGo.com 网站规则配置
// 网站: https://www.aitoolgo.com/submit
// 最后更新: 2025-07-24

export const SITE_RULE = {
  // 基本信息
  domain: 'www.aitoolgo.com',
  siteName: 'AI Tool Go',
  priority: 1,
  lastUpdated: '2025-07-24',

  // 字段映射规则
  fieldMappings: {
    // AI工具名称 -> AI Tool Name
    siteName: {
      selectors: [
        '#toolName',
        'input[id="toolName"]',
        'input[maxlength="100"]'
      ],
      method: 'value',
      validation: 'required',
      notes: 'AI工具名称，最多100字符'
    },

    // 网站URL -> Website URL
    siteUrl: {
      selectors: [
        '#websiteUrl',
        'input[id="websiteUrl"]',
        'input[type="url"]'
      ],
      method: 'value',
      validation: 'required|url',
      notes: '网站URL地址，最多200字符'
    },

    // 提交计划 -> Plan Selection
    pricing: {
      selectors: [
        'input[name="plan"]',
        '#free',
        '#vip'
      ],
      method: 'radio',
      validation: 'required',
      notes: '提交计划选择：免费或VIP'
    }
  },
  // 提交流程配置
  submitConfig: {
    submitButton: 'button[type="submit"], .bg-gradient-primary',
    submitMethod: 'manual', // 手动提交
    waitAfterFill: 0,
    waitAfterSubmit: 0,
    successIndicators: ['.success-message'],
    errorIndicators: ['.error-message']
  },

  // 特殊处理
  specialHandling: {
    requiresLogin: false,
    hasCaptcha: false,
    hasFileUpload: false,
    customScript: 'handleAiToolGoSubmission',
    formValidation: {
      requiredFields: ['siteName', 'siteUrl', 'pricing'],
      emailValidation: false,
      urlValidation: true
    },
    notes: [
      'AI Tool Go AI工具目录提交平台',
      '极简表单，只有工具名称和URL两个字段',
      '提供免费和VIP两种提交方式',
      '免费提交：需要添加反向链接，72小时内上线',
      'VIP提交：即将推出，无需反向链接，24小时内上线',
      '反向链接格式：<a href="https://www.aitoolgo.com" title="AiToolGo">AiToolGo</a>',
      '现代化的Tailwind CSS界面',
      '手动提交模式'
    ]
  }
};

// 自定义处理函数
export function handleAiToolGoSubmission(data) {
  console.log('Processing AI Tool Go form submission...');

  const processedData = { ...data };

  // 确保URL格式正确
  if (processedData.siteUrl && !processedData.siteUrl.startsWith('http')) {
    processedData.siteUrl = 'https://' + processedData.siteUrl;
  }

  return processedData;
}

// 自定义元素填写函数
export async function customFillElement(element, value, config) {
  console.log(`AI Tool Go自定义填写: ${element.tagName}, 方法: ${config.method}`);

  switch (config.method) {
    case 'value':
      // 标准输入框处理
      element.focus();
      await new Promise(resolve => setTimeout(resolve, 200));

      element.value = value;
      element.dispatchEvent(new Event('input', { bubbles: true }));
      element.dispatchEvent(new Event('change', { bubbles: true }));
      element.dispatchEvent(new Event('blur', { bubbles: true }));

      console.log(`✓ 填写字段: ${element.id} = "${value}"`);
      return true;

    case 'radio':
      // 单选按钮处理 - 默认选择免费选项
      const freeRadio = document.querySelector('#free');
      if (freeRadio) {
        freeRadio.checked = true;
        freeRadio.dispatchEvent(new Event('change', { bubbles: true }));
        console.log(`✓ 选择提交计划: 免费提交`);
        return true;
      }
      break;

    default:
      element.value = value;
      element.dispatchEvent(new Event('input', { bubbles: true }));
      return true;
  }

  return false;
}