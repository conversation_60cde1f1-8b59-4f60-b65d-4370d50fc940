// AI Site Submitter - Sidepanel Script
// 处理侧边栏UI交互和逻辑
// 最后更新: 2025-07-06

document.addEventListener('DOMContentLoaded', function() {
    initializeSidepanel();
});

// 初始化侧边栏
async function initializeSidepanel() {
    console.log('初始化侧边栏...');

    // 绑定事件监听器
    bindEventListeners();

    // 获取当前页面信息
    await getCurrentPageInfo();

    // 检查规则状态
    await checkRuleStatus();

    // 加载网站信息预览
    loadWebsiteInfoPreview();
}

// 绑定事件监听器
function bindEventListeners() {
    // 填写表单按钮
    document.getElementById('fill-form-btn')?.addEventListener('click', fillForm);



    // 刷新按钮
    document.getElementById('refresh-btn')?.addEventListener('click', refreshStatus);
}

// 获取当前页面信息
async function getCurrentPageInfo() {
    try {
        const response = await chrome.runtime.sendMessage({ action: 'getCurrentTab' });

        if (response.success) {
            const { url, hostname } = response.tab;

            // 更新UI显示
            document.getElementById('current-url').textContent = url;
            document.getElementById('current-domain').textContent = hostname;

            console.log(`当前页面: ${hostname}`);
        } else {
            showError('无法获取当前页面信息');
        }
    } catch (error) {
        console.error('获取页面信息失败:', error);
        showError('获取页面信息失败');
    }
}

// 检查规则状态
async function checkRuleStatus() {
    const statusIndicator = document.getElementById('status-indicator');
    const statusTitle = document.getElementById('status-title');
    const statusMessage = document.getElementById('status-message');
    const fillBtn = document.getElementById('fill-form-btn');


    // 显示加载状态
    statusIndicator.className = 'status-indicator loading';
    statusIndicator.innerHTML = '<div class="loading-spinner"></div>';
    statusTitle.textContent = '正在检查规则...';
    statusMessage.textContent = '请稍候';
    fillBtn.disabled = true;
    
    try {
        const response = await chrome.runtime.sendMessage({
            action: 'executeContentScript',
            contentAction: 'checkRules'
        });
        
        if (response.success && response.result) {
            const result = response.result;
            
            if (result.hasRules) {
                // 有规则可用
                statusIndicator.className = 'status-indicator success';
                statusIndicator.innerHTML = '✅';
                statusTitle.textContent = '规则可用';
                statusMessage.textContent = `${result.siteName} - ${result.fieldsCount}个字段`;
                fillBtn.disabled = false;

                console.log(`规则检查成功: ${result.siteName}`);
            } else {
                // 无规则配置
                statusIndicator.className = 'status-indicator error';
                statusIndicator.innerHTML = '❌';
                statusTitle.textContent = '无规则配置';
                statusMessage.textContent = '无规则配置，请手动设置规则';
                fillBtn.disabled = true;

                console.log(`无规则配置: ${result.domain}`);
            }
        } else {
            throw new Error(response.error || '规则检查失败');
        }
    } catch (error) {
        console.error('规则检查失败:', error);
        
        // 显示错误状态
        statusIndicator.className = 'status-indicator error';
        statusIndicator.innerHTML = '⚠️';
        statusTitle.textContent = '检查失败';
        statusMessage.textContent = error.message || '无法检查规则状态';
        fillBtn.disabled = true;
    }
}

// 填写表单
async function fillForm() {
    const fillBtn = document.getElementById('fill-form-btn');
    const resultsSection = document.getElementById('results-section');
    const resultCard = document.getElementById('result-card');
    const resultSummary = document.getElementById('result-summary');
    const resultDetails = document.getElementById('result-details');
    
    // 禁用按钮，显示加载状态
    fillBtn.disabled = true;
    fillBtn.innerHTML = '<span class="btn-icon">⏳</span><span class="btn-text">正在填写...</span>';
    
    // 隐藏之前的结果
    resultsSection.style.display = 'none';
    
    try {
        const response = await chrome.runtime.sendMessage({
            action: 'executeContentScript',
            contentAction: 'fillForm'
        });
        
        if (response.success && response.result) {
            const result = response.result;
            
            // 显示成功结果
            resultCard.className = 'result-card success';
            resultSummary.textContent = `✅ ${result.message}`;
            resultDetails.innerHTML = `
                <div>填写成功: ${result.filledCount}/${result.totalFields} 个字段</div>
                <div style="margin-top: 8px; font-size: 12px;">
                    请检查表单内容并手动提交
                </div>
            `;
            
            resultsSection.style.display = 'block';
            
            console.log('表单填写成功:', result);
        } else {
            throw new Error(response.error || '填写失败');
        }
    } catch (error) {
        console.error('表单填写失败:', error);
        
        // 显示错误结果
        resultCard.className = 'result-card error';
        resultSummary.textContent = '❌ 填写失败';
        resultDetails.textContent = error.message || '未知错误';
        
        resultsSection.style.display = 'block';
    } finally {
        // 恢复按钮状态
        fillBtn.disabled = false;
        fillBtn.innerHTML = '<span class="btn-icon">📝</span><span class="btn-text">填写表单</span>';
    }
}

// 刷新状态
async function refreshStatus() {
    const refreshBtn = document.getElementById('refresh-btn');

    // 显示刷新状态
    refreshBtn.innerHTML = '<span class="btn-icon">🔄</span><span class="btn-text">刷新中...</span>';
    refreshBtn.disabled = true;

    try {
        // 重新获取页面信息和检查规则
        await getCurrentPageInfo();
        await checkRuleStatus();

        // 隐藏结果区域
        document.getElementById('results-section').style.display = 'none';



        console.log('状态刷新完成');
    } catch (error) {
        console.error('刷新失败:', error);
        showError('刷新失败');
    } finally {
        // 恢复按钮状态
        refreshBtn.innerHTML = '<span class="btn-icon">🔄</span><span class="btn-text">重新检测</span>';
        refreshBtn.disabled = false;
    }
}

// 加载网站信息预览
function loadWebsiteInfoPreview() {
    try {
        // 使用传统方式加载网站信息，避免ES6模块兼容性问题
        // 假设 WEBSITE_INFO 已经通过其他方式加载到全局作用域
        if (typeof WEBSITE_INFO !== 'undefined') {
            // 更新UI显示网站信息
            const siteNameEl = document.getElementById('site-name');
            const siteUrlEl = document.getElementById('site-url');
            const siteCategoryEl = document.getElementById('site-category');
            const sitePricingEl = document.getElementById('site-pricing');

            if (siteNameEl) siteNameEl.textContent = WEBSITE_INFO.siteName || '未设置';
            if (siteUrlEl) siteUrlEl.textContent = WEBSITE_INFO.siteUrl || '未设置';
            if (siteCategoryEl) siteCategoryEl.textContent = WEBSITE_INFO.category || '未分类';
            if (sitePricingEl) sitePricingEl.textContent = WEBSITE_INFO.pricing || '未知';

            console.log('网站信息预览加载成功:', {
                siteName: WEBSITE_INFO.siteName,
                siteUrl: WEBSITE_INFO.siteUrl,
                category: WEBSITE_INFO.category,
                pricing: WEBSITE_INFO.pricing
            });
        } else {
            throw new Error('WEBSITE_INFO 未定义');
        }

    } catch (error) {
        console.error('加载网站信息失败:', error);

        // 使用硬编码的备用信息
        const websiteInfo = {
            siteName: 'HumanWhisper',
            siteUrl: 'https://humanwhisper.com/',
            category: 'Ai Tools',
            pricing: 'Freemium'
        };

        // 显示备用信息
        const siteNameEl = document.getElementById('site-name');
        const siteUrlEl = document.getElementById('site-url');
        const siteCategoryEl = document.getElementById('site-category');
        const sitePricingEl = document.getElementById('site-pricing');

        if (siteNameEl) siteNameEl.textContent = websiteInfo.siteName;
        if (siteUrlEl) siteUrlEl.textContent = websiteInfo.siteUrl;
        if (siteCategoryEl) siteCategoryEl.textContent = websiteInfo.category;
        if (sitePricingEl) sitePricingEl.textContent = websiteInfo.pricing;

        console.log('使用备用网站信息');
    }
}

// 显示错误信息
function showError(message) {
    console.error('错误:', message);
    
    const statusIndicator = document.getElementById('status-indicator');
    const statusTitle = document.getElementById('status-title');
    const statusMessage = document.getElementById('status-message');
    
    statusIndicator.className = 'status-indicator error';
    statusIndicator.innerHTML = '⚠️';
    statusTitle.textContent = '发生错误';
    statusMessage.textContent = message;
}



console.log('AI Site Submitter sidepanel script loaded');
