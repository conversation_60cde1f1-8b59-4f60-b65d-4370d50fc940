// promoteproject.com 网站规则配置
// 网站: https://www.promoteproject.com/submit-startup
// 表单技术: Bootstrap Form
// 最后更新: 2025-07-08

export const SITE_RULE = {
  // 基本信息
  domain: 'promoteproject.com',
  siteName: 'Promote Project',
  priority: 1,
  lastUpdated: '2025-07-08',
  
  // 字段映射规则
  fieldMappings: {
    // 主页 -> Home Page
    siteUrl: {
      selectors: [
        'input[id="link"]',
        'input[name="link"]',
        'input[placeholder="Main page of your site"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '主页URL，使用website-info.js中的siteUrl字段'
    },
    
    // 创业公司名称 -> Startup Name
    siteName: {
      selectors: [
        'input[id="title"]',
        'input[name="title"]',
        'input[placeholder="Enter title"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '创业公司名称，使用website-info.js中的siteName字段'
    },
    
    // 简短描述 -> Short Description
    siteDescription: {
      selectors: [
        'input[id="short_description"]',
        'input[name="short_description"]',
        'input[maxlength="120"]'
      ],
      method: 'value',
      validation: 'required',
      minLength: 10,
      maxLength: 120,
      notes: '简短描述，使用website-info.js中的siteDescription字段，10-120字符'
    },
    
    // 完整描述 -> Full Description
    detailedIntro: {
      selectors: [
        'textarea[id="full_description"]',
        'textarea[name="full_description"]',
        'textarea[maxlength="16384"]'
      ],
      method: 'value',
      validation: 'required',
      maxLength: 16384,
      notes: '完整描述，使用website-info.js中的detailedIntro字段，最大16384字符'
    },
    
    // 服务 -> Services
    features: {
      selectors: [
        'textarea[id="services"]',
        'textarea[name="services"]',
        'textarea[placeholder*="Services provided"]'
      ],
      method: 'value',
      validation: 'optional',
      maxLength: 4096,
      notes: '服务，使用website-info.js中的features字段，最大4096字符'
    },
    
    // 奖项 -> Awards
    awards: {
      selectors: [
        'textarea[id="awards"]',
        'textarea[name="awards"]',
        'textarea[placeholder*="Awards and recognitions"]'
      ],
      method: 'value',
      validation: 'optional',
      defaultValue: 'As a newly launched innovative product, we are currently building our track record and look forward to earning recognition in the AI industry.',
      notes: '奖项，固定英文内容，新产品暂无奖项'
    },
    
    // 关键词 -> Keywords
    seoKeywords: {
      selectors: [
        'input[id="tags"]',
        'input[name="tags"]',
        'input[maxlength="40"]'
      ],
      method: 'value',
      validation: 'required',
      maxLength: 40,
      notes: '关键词，使用website-info.js中的seoKeywords字段，最大40字符'
    },
    
    // 员工总数 -> Total Employees
    totalEmployees: {
      selectors: [
        'input[id="total_employees"]',
        'input[name="total_employees"]',
        'input[type="number"][placeholder="Enter number"]'
      ],
      method: 'value',
      validation: 'optional',
      defaultValue: '16',
      notes: '员工总数，固定值16'
    },
    
    // 成立年份 -> Founded Year
    foundedYear: {
      selectors: [
        'select[id="founded_year"]',
        'select[name="founded_year"]'
      ],
      method: 'select',
      validation: 'required',
      defaultValue: '2025',
      notes: '成立年份，选择2025'
    },
    
    // 阶段 -> Stage
    stage: {
      selectors: [
        'select[id="stage"]',
        'select[name="stage"]'
      ],
      method: 'select',
      validation: 'optional',
      defaultValue: '1',
      notes: '阶段，选择Early Stage (值为1)'
    },
    
    // 融资总额 -> Total Funding Amount
    totalFunding: {
      selectors: [
        'input[id="total_funding_amount"]',
        'input[name="total_funding_amount"]',
        'input[placeholder="USD"]'
      ],
      method: 'value',
      validation: 'optional',
      defaultValue: '50000',
      notes: '融资总额，固定值50000美元'
    },
    
    // 融资轮次 -> Total Funding Rounds
    fundingRounds: {
      selectors: [
        'input[id="funding_rounds"]',
        'input[name="funding_rounds"]',
        'input[placeholder="Total"]'
      ],
      method: 'value',
      validation: 'optional',
      defaultValue: '1',
      notes: '融资轮次，固定值1'
    },
    
    // 国家 -> Country
    country: {
      selectors: [
        'select[id="country"]',
        'select[name="country"]'
      ],
      method: 'select',
      validation: 'required',
      defaultValue: '185',
      notes: '国家，选择United States of America (值为185)'
    },
    
    // 总部 -> Headquarters
    headquarters: {
      selectors: [
        'input[id="headquarters"]',
        'input[name="headquarters"]',
        'input[placeholder="Enter address"]'
      ],
      method: 'value',
      validation: 'optional',
      defaultValue: '1600 Amphitheatre Parkway, Mountain View, CA 94043, United States',
      notes: '总部地址，美国真实办公地址'
    },
    
    // 分类 -> Select Category
    category: {
      selectors: [
        'select[id="category"]',
        'select[name="category"]'
      ],
      method: 'select',
      validation: 'required',
      defaultValue: '4',
      notes: '分类，选择Artificial Intelligence (AI) (值为4)'
    }
  },
  
  // 自定义填写函数
  customFillElement: async function(element, value, config) {
    console.log(`Promote Project自定义填写: ${element.id || element.name}, 方法: ${config.method}`);
    
    switch (config.method) {
      case 'value':
        // 标准输入框和文本域处理
        element.focus();
        await new Promise(resolve => setTimeout(resolve, 100));
        
        // 清空现有内容
        element.value = '';
        await new Promise(resolve => setTimeout(resolve, 50));
        
        // 处理特殊字段
        let finalValue = value;
        if (config.defaultValue && (!value || value.trim() === '')) {
          finalValue = config.defaultValue;
        }
        
        // 处理字符限制
        if (config.maxLength && finalValue.length > config.maxLength) {
          finalValue = finalValue.substring(0, config.maxLength);
        }
        
        // 设置新值
        element.value = finalValue;
        
        // 触发事件
        element.dispatchEvent(new Event('input', { bubbles: true }));
        element.dispatchEvent(new Event('change', { bubbles: true }));
        element.dispatchEvent(new Event('blur', { bubbles: true }));
        
        console.log(`✓ 填写字段: ${element.id} = "${finalValue.substring(0, 50)}..."`);
        break;
        
      case 'select':
        // 下拉选择处理
        element.focus();
        await new Promise(resolve => setTimeout(resolve, 100));
        
        const targetValue = config.defaultValue;
        
        // 查找匹配的选项
        const option = Array.from(element.options).find(opt => 
          opt.value === targetValue
        );
        
        if (option) {
          element.value = option.value;
          element.dispatchEvent(new Event('change', { bubbles: true }));
          console.log(`✓ 选择选项: ${option.text} (值: ${option.value})`);
        } else {
          console.log(`⚠️ 未找到选项值: ${targetValue}`);
        }
        break;
        
      default:
        element.value = value;
        element.dispatchEvent(new Event('input', { bubbles: true }));
        element.dispatchEvent(new Event('change', { bubbles: true }));
    }
  },
  
  // 提交流程配置
  submitConfig: {
    submitButton: [
      'button[type="submit"]',
      'input[type="submit"]',
      'button:contains("Submit")',
      '.btn-primary'
    ],
    submitMethod: 'click',
    waitAfterFill: 3000,
    waitAfterSubmit: 5000,
    successIndicators: [
      '.alert-success',
      'text:contains("submitted")',
      'text:contains("success")'
    ],
    errorIndicators: [
      '.alert-danger',
      'text:contains("error")',
      'text:contains("required")'
    ]
  },
  
  // 特殊处理
  specialHandling: {
    requiresLogin: false,
    hasCaptcha: false,
    hasFileUpload: false,
    isBootstrapForm: true, // 使用Bootstrap表单
    hasCharacterLimits: true, // 有字符限制
    isStartupDirectory: true, // 创业公司目录
    
    // 表单验证规则
    formValidation: {
      requiredFields: ['siteUrl', 'siteName', 'siteDescription', 'detailedIntro', 'seoKeywords', 'foundedYear', 'country', 'category'],
      characterLimits: {
        siteDescription: { min: 10, max: 120 },
        detailedIntro: { max: 16384 },
        features: { max: 4096 },
        awards: { max: 4096 },
        seoKeywords: { max: 40 }
      }
    },
    
    // 特殊注意事项
    notes: [
      '这是Promote Project的创业公司提交表单',
      '表单包含16个字段，8个必填，8个可选',
      '使用Bootstrap样式框架',
      '有严格的字符限制要求',
      '需要选择具体的数值选项',
      '总部地址使用美国真实地址',
      '奖项字段使用英文固定内容',
      '专注于创业公司和项目推广',
      '成立年份选择2025',
      '国家选择美国(185)',
      '分类选择AI(4)',
      '阶段选择Early Stage(1)'
    ]
  }
};

// 自定义处理函数
export function handlePromoteProjectSubmission(data, _rule) {
  console.log('Processing Promote Project form submission...');

  const processedData = { ...data };

  // 确保URL格式正确
  if (processedData.siteUrl && !processedData.siteUrl.startsWith('http')) {
    processedData.siteUrl = 'https://' + processedData.siteUrl;
  }

  // 处理字符限制
  if (processedData.siteDescription) {
    if (processedData.siteDescription.length < 10) {
      processedData.siteDescription += ' - Innovative AI-powered solution for modern users.';
    }
    if (processedData.siteDescription.length > 120) {
      processedData.siteDescription = processedData.siteDescription.substring(0, 120);
    }
  }

  if (processedData.detailedIntro && processedData.detailedIntro.length > 16384) {
    processedData.detailedIntro = processedData.detailedIntro.substring(0, 16384);
  }

  if (processedData.seoKeywords && processedData.seoKeywords.length > 40) {
    processedData.seoKeywords = processedData.seoKeywords.substring(0, 40);
  }

  // 设置固定值
  processedData.awards = 'As a newly launched innovative product, we are currently building our track record and look forward to earning recognition in the AI industry.';
  processedData.totalEmployees = '16';
  processedData.foundedYear = '2025';
  processedData.stage = '1'; // Early Stage
  processedData.totalFunding = '50000';
  processedData.fundingRounds = '1';
  processedData.country = '185'; // United States
  processedData.headquarters = '1600 Amphitheatre Parkway, Mountain View, CA 94043, United States';
  processedData.category = '4'; // Artificial Intelligence (AI)

  return processedData;
}

// 字段验证函数
export function validatePromoteProjectForm() {
  console.log('验证Promote Project表单...');

  const requiredFields = [
    { id: 'link', name: 'Home Page' },
    { id: 'title', name: 'Startup Name' },
    { id: 'short_description', name: 'Short Description' },
    { id: 'full_description', name: 'Full Description' },
    { id: 'tags', name: 'Keywords' },
    { id: 'founded_year', name: 'Founded Year' },
    { id: 'country', name: 'Country' },
    { id: 'category', name: 'Category' }
  ];

  let isValid = true;

  requiredFields.forEach(field => {
    const element = document.getElementById(field.id);
    if (!element || !element.value.trim()) {
      console.log(`⚠️ 必填字段为空: ${field.name}`);
      isValid = false;
    }
  });

  // 验证字符限制
  const shortDesc = document.getElementById('short_description');
  if (shortDesc && shortDesc.value.length < 10) {
    console.log('⚠️ 简短描述少于10字符');
    isValid = false;
  }

  if (isValid) {
    console.log('✓ 表单验证通过');
  }

  return isValid;
}

// 美国地址生成器
export function generateUSAddress() {
  const addresses = [
    '1600 Amphitheatre Parkway, Mountain View, CA 94043, United States',
    '1 Hacker Way, Menlo Park, CA 94301, United States',
    '410 Terry Avenue North, Seattle, WA 98109, United States',
    '1 Apple Park Way, Cupertino, CA 95014, United States',
    '2000 N Forest Rd, Getzville, NY 14068, United States'
  ];

  return addresses[Math.floor(Math.random() * addresses.length)];
}
