// Gainweb.org 网站规则配置
// 网站: https://gainweb.org/submit.php?LINK_TYPE=2
// 最后更新: 2025-07-24

export const SITE_RULE = {
  // 基本信息
  domain: 'gainweb.org',
  siteName: 'Gainweb Directory',
  priority: 1,
  lastUpdated: '2025-07-24',

  // 字段映射规则
  fieldMappings: {
    // 链接类型选择 -> Available Link Types
    linkType: {
      selectors: [
        'input[name="LINK_TYPE"]',
        'input[value="2"]', // Regular - Free
        'input[value="5"]', // Premium - $39
        'input[value="6"]'  // Featured - $75
      ],
      method: 'radio',
      validation: 'required',
      notes: '链接类型选择：免费、付费高级、特色展示'
    },

    // 网站标题 -> Title
    siteName: {
      selectors: [
        '#TITLE',
        'input[name="TITLE"]',
        'input[id="TITLE"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '网站标题，最多100字符'
    },

    // 网站URL -> URL
    siteUrl: {
      selectors: [
        '#URL',
        'input[name="URL"]',
        'input[id="URL"]'
      ],
      method: 'value',
      validation: 'required|url',
      notes: '网站URL地址'
    },

    // 分类选择 -> Category
    category: {
      selectors: [
        '#CATEGORY_ID',
        'select[name="CATEGORY_ID"]'
      ],
      method: 'select',
      validation: 'required',
      notes: '网站分类选择'
    },

    // 网站描述 -> Description
    siteDescription: {
      selectors: [
        '#DESCRIPTION',
        'textarea[name="DESCRIPTION"]'
      ],
      method: 'value',
      validation: 'optional',
      notes: '网站描述'
    },

    // 所有者姓名 -> Owner Name
    fullName: {
      selectors: [
        '#OWNER_NAME',
        'input[name="OWNER_NAME"]'
      ],
      method: 'value',
      validation: 'optional',
      notes: '网站所有者姓名，最多50字符'
    },

    // 所有者邮箱 -> Owner Email
    contactEmail: {
      selectors: [
        '#OWNER_EMAIL',
        'input[name="OWNER_EMAIL"]'
      ],
      method: 'value',
      validation: 'optional|email',
      notes: '网站所有者邮箱'
    },

    // META关键词 -> META Keywords
    keywords: {
      selectors: [
        '#META_KEYWORDS',
        'input[name="META_KEYWORDS"]'
      ],
      method: 'value',
      validation: 'optional',
      notes: 'META关键词，用逗号分隔'
    },

    // META描述 -> META Description
    detailedIntro: {
      selectors: [
        '#META_DESCRIPTION',
        'textarea[name="META_DESCRIPTION"]'
      ],
      method: 'value',
      validation: 'optional',
      notes: 'META描述，最多500字符'
    },

    // 深度链接标题 -> Deep Links Titles
    additionalLinkTitles: {
      selectors: [
        'input[name="ADD_LINK_TITLE[]"]'
      ],
      method: 'value',
      validation: 'optional',
      notes: '额外链接标题'
    },

    // 深度链接URL -> Deep Links URLs
    additionalLinkUrls: {
      selectors: [
        'input[name="ADD_LINK_URL[]"]'
      ],
      method: 'value',
      validation: 'optional|url',
      notes: '额外链接URL'
    },

    // 同意规则 -> Agree with submission rules
    agreeRules: {
      selectors: [
        '#AGREERULES',
        'input[name="AGREERULES"]',
        'input[type="checkbox"]'
      ],
      method: 'checkbox',
      validation: 'required',
      notes: '同意提交规则'
    }
  },
  // 提交流程配置
  submitConfig: {
    submitButton: 'input[name="continue"], input[value="Continue"]',
    submitMethod: 'manual', // 手动提交
    waitAfterFill: 0,
    waitAfterSubmit: 0,
    successIndicators: ['.success-message'],
    errorIndicators: ['.error-message']
  },

  // 特殊处理
  specialHandling: {
    requiresLogin: false,
    hasCaptcha: false,
    hasFileUpload: false,
    customScript: 'handleGainwebSubmission',
    formValidation: {
      requiredFields: ['linkType', 'siteName', 'siteUrl', 'category', 'agreeRules'],
      emailValidation: true,
      urlValidation: true
    },
    notes: [
      'Gainweb.org 网站目录提交平台',
      '传统的网站目录服务',
      '提供免费和付费提交选项',
      '免费提交：无审核保证',
      '付费提交：$39高级版，$75特色版',
      '支持添加深度链接',
      '必须同意提交规则',
      '传统表格样式界面',
      '手动提交模式'
    ]
  }
};

// 自定义处理函数
export function handleGainwebSubmission(data) {
  console.log('Processing Gainweb form submission...');

  const processedData = { ...data };

  // 确保URL格式正确
  if (processedData.siteUrl && !processedData.siteUrl.startsWith('http')) {
    processedData.siteUrl = 'https://' + processedData.siteUrl;
  }

  // 处理额外链接URL
  if (processedData.additionalLinkUrls) {
    const urls = Array.isArray(processedData.additionalLinkUrls)
      ? processedData.additionalLinkUrls
      : [processedData.additionalLinkUrls];

    processedData.additionalLinkUrls = urls.map(url => {
      if (url && !url.startsWith('http')) {
        return 'https://' + url;
      }
      return url;
    });
  }

  return processedData;
}

// 自定义元素填写函数
export async function customFillElement(element, value, config) {
  console.log(`Gainweb自定义填写: ${element.tagName}, 方法: ${config.method}`);

  switch (config.method) {
    case 'value':
      // 标准输入框和文本域处理
      element.focus();
      await new Promise(resolve => setTimeout(resolve, 200));

      element.value = value;
      element.dispatchEvent(new Event('input', { bubbles: true }));
      element.dispatchEvent(new Event('change', { bubbles: true }));
      element.dispatchEvent(new Event('blur', { bubbles: true }));

      console.log(`✓ 填写字段: ${element.name || element.id} = "${value}"`);
      return true;

    case 'select':
      // 下拉选择框处理
      if (element.tagName === 'SELECT') {
        const options = element.querySelectorAll('option');
        let selectedOption;

        // 智能匹配分类
        for (const option of options) {
          if (option.value && option.value !== '0') {
            const optionText = option.textContent.trim();
            if (optionText.includes('Arts') || optionText.includes('Business') || optionText.includes('Technology')) {
              selectedOption = option;
              break;
            }
          }
        }

        // 如果没找到合适的，选择第一个非禁用选项
        if (!selectedOption) {
          selectedOption = Array.from(options).find(opt => opt.value !== '0' && !opt.disabled);
        }

        if (selectedOption) {
          element.value = selectedOption.value;
          element.dispatchEvent(new Event('change', { bubbles: true }));
          console.log(`✓ 选择分类: ${selectedOption.textContent}`);
          return true;
        }
      }
      break;

    case 'radio':
      // 单选按钮处理 - 默认选择免费选项
      const freeRadio = document.querySelector('input[name="LINK_TYPE"][value="2"]');
      if (freeRadio) {
        freeRadio.checked = true;
        freeRadio.dispatchEvent(new Event('change', { bubbles: true }));
        console.log(`✓ 选择链接类型: 免费提交`);
        return true;
      }
      break;

    case 'checkbox':
      // 复选框处理
      if (element.type === 'checkbox') {
        element.checked = true;
        element.dispatchEvent(new Event('change', { bubbles: true }));
        console.log(`✓ 复选框设置: ${element.checked}`);
        return true;
      }
      break;

    default:
      element.value = value;
      element.dispatchEvent(new Event('input', { bubbles: true }));
      return true;
  }

  return false;
}