// startupaitools.com 网站规则配置
// 网站: https://www.startupaitools.com/submit-ai-tools/
// 表单技术: WPForms
// 最后更新: 2025-07-07

export const SITE_RULE = {
  // 基本信息
  domain: 'startupaitools.com',
  siteName: 'Startup AI Tools',
  priority: 1,
  lastUpdated: '2025-07-07',
  
  // 字段映射规则
  fieldMappings: {
    // AI工具名称 -> Ai Tools Name
    siteName: {
      selectors: [
        'input[id="wpforms-7755-field_1"]',
        'input[name="wpforms[fields][1]"]',
        '#wpforms-7755-field_1'
      ],
      method: 'value',
      validation: 'required',
      notes: 'AI工具名称，使用website-info.js中的siteName字段'
    },
    
    // 网站URL -> Website / URL
    siteUrl: {
      selectors: [
        'input[name*="website"]',
        'input[name*="url"]',
        'input[type="url"]',
        'input[placeholder*="Website"]',
        '#wpforms-field_2',
        '.wpforms-field-url input'
      ],
      method: 'value',
      validation: 'required|url',
      notes: '网站URL，使用website-info.js中的siteUrl字段'
    },
    
    // 工具分类 -> Tool Category
    category: {
      selectors: [
        'input[id="wpforms-7755-field_3"]',
        'input[name="wpforms[fields][3]"]',
        '#wpforms-7755-field_3'
      ],
      method: 'value',
      validation: 'required',
      notes: '工具分类，文本输入框，使用website-info.js中的category字段'
    },
    

    
    // 简短描述 -> Short Description
    siteDescription: {
      selectors: [
        'textarea[name*="short"]',
        'textarea[placeholder*="Short Description"]',
        'textarea[maxlength="500"]',
        '#wpforms-field_5',
        '.wpforms-field-textarea textarea:first-of-type'
      ],
      method: 'value',
      validation: 'required',
      notes: '简短描述，使用website-info.js中的siteDescription字段，最大500字符'
    },
    
    // 详细描述 -> Long description
    detailedIntro: {
      selectors: [
        'textarea[name*="long"]',
        'textarea[placeholder*="Long description"]',
        'textarea[name*="description"]:not([maxlength="500"])',
        '#wpforms-field_6',
        '.wpforms-field-textarea textarea:last-of-type'
      ],
      method: 'value',
      validation: 'required',
      notes: '详细描述，使用website-info.js中的detailedIntro字段，300-4000字符'
    },
    
    // 提交者姓名 -> Name
    fullName: {
      selectors: [
        'input[id="wpforms-7755-field_7"]',
        'input[name="wpforms[fields][7]"]',
        '#wpforms-7755-field_7'
      ],
      method: 'value',
      validation: 'required',
      notes: '提交者姓名，使用website-info.js中的fullName字段'
    },
    
    // 邮箱地址 -> Email
    contactEmail: {
      selectors: [
        'input[name*="email"]',
        'input[type="email"]',
        'input[placeholder*="Email"]',
        '#wpforms-field_8',
        '.wpforms-field-email input'
      ],
      method: 'value',
      validation: 'required|email',
      notes: '邮箱地址，使用website-info.js中的contactEmail字段'
    }
  },
  
  // 自定义填写函数
  customFillElement: async function(element, value, config) {
    console.log(`Startup AI Tools自定义填写: ${element.name || element.placeholder}, 方法: ${config.method}`);
    
    switch (config.method) {
      case 'value':
        // 标准输入框和文本域处理
        element.focus();
        await new Promise(resolve => setTimeout(resolve, 100));
        
        // 清空现有内容
        element.value = '';
        await new Promise(resolve => setTimeout(resolve, 50));
        
        // 处理字符限制
        let finalValue = value;
        if (element.maxLength && element.maxLength === 500) {
          // 简短描述限制500字符
          finalValue = value.substring(0, 500);
        } else if (element.placeholder && element.placeholder.includes('Long description')) {
          // 详细描述确保至少300字符
          if (value.length < 300) {
            finalValue = value + '. This AI tool provides innovative solutions with advanced features and capabilities for users seeking efficient and effective automation.';
            // 如果还不够300字符，继续添加
            while (finalValue.length < 300) {
              finalValue += ' It offers comprehensive functionality and user-friendly interface.';
            }
          }
          // 限制最大4000字符
          if (finalValue.length > 4000) {
            finalValue = finalValue.substring(0, 4000);
          }
        }
        
        // 设置新值
        element.value = finalValue;
        
        // 触发事件
        element.dispatchEvent(new Event('input', { bubbles: true }));
        element.dispatchEvent(new Event('change', { bubbles: true }));
        element.dispatchEvent(new Event('blur', { bubbles: true }));
        
        console.log(`✓ 填写字段: ${element.placeholder || element.name} = "${finalValue.substring(0, 50)}..."`);
        break;
        
      default:
        element.value = value;
        element.dispatchEvent(new Event('input', { bubbles: true }));
        element.dispatchEvent(new Event('change', { bubbles: true }));
    }
  },

  // 提交流程配置
  submitConfig: {
    submitButton: [
      'button[type="submit"]',
      'input[type="submit"]',
      'button:contains("Submit Tool")',
      '.wpforms-submit',
      '#wpforms-submit'
    ],
    submitMethod: 'click',
    waitAfterFill: 2000,
    waitAfterSubmit: 5000,
    successIndicators: [
      '.wpforms-confirmation-container',
      '.success-message',
      '.thank-you',
      '[class*="success"]'
    ],
    errorIndicators: [
      '.wpforms-error',
      '.error-message',
      '[class*="error"]'
    ]
  },

  // 特殊处理
  specialHandling: {
    requiresLogin: false,
    hasCaptcha: false,
    hasFileUpload: false,
    isWPForms: true, // 使用WPForms插件

    // 表单验证规则
    formValidation: {
      requiredFields: ['siteName', 'siteUrl', 'category', 'siteDescription', 'detailedIntro', 'fullName', 'contactEmail'],
      emailValidation: true,
      urlValidation: true,
      characterLimits: {
        siteDescription: 500,
        detailedIntro: { min: 300, max: 4000 }
      }
    },

    // 特殊注意事项
    notes: [
      '这是WordPress网站，使用WPForms插件',
      '表单包含7个字段，全部必填',
      '字段ID格式：wpforms-7755-field_{number}',
      '工具分类是文本输入框，直接输入分类名称',
      '定价默认为Free，无需选择',
      '简短描述限制500字符',
      '详细描述要求300-4000字符',
      '表单提交后需要人工审核',
      '网站专注于AI工具收录和推广'
    ]
  }
};

// 自定义处理函数
export function handleStartupAIToolsSubmission(data, _rule) {
  console.log('Processing Startup AI Tools form submission...');

  const processedData = { ...data };

  // 确保URL格式正确
  if (processedData.siteUrl && !processedData.siteUrl.startsWith('http')) {
    processedData.siteUrl = 'https://' + processedData.siteUrl;
  }

  // 处理字符限制
  if (processedData.siteDescription && processedData.siteDescription.length > 500) {
    processedData.siteDescription = processedData.siteDescription.substring(0, 500);
  }

  // 处理详细描述长度要求
  if (processedData.detailedIntro) {
    let description = processedData.detailedIntro;

    // 确保至少300字符
    if (description.length < 300) {
      description += '. This AI tool provides innovative solutions with advanced features and capabilities for users seeking efficient and effective automation.';
      while (description.length < 300) {
        description += ' It offers comprehensive functionality and user-friendly interface.';
      }
    }

    // 限制最大4000字符
    if (description.length > 4000) {
      description = description.substring(0, 4000);
    }

    processedData.detailedIntro = description;
  }

  // 定价默认为Free，无需处理

  return processedData;
}
