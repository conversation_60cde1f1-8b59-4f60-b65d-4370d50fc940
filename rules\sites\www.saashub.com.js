// SaaSHub.com 网站规则配置
// 网站: https://www.saashub.com/services/submit
// 最后更新: 2025-07-24

export const SITE_RULE = {
  domain: 'www.saashub.com',
  siteName: 'SaaSHub',
  priority: 1,
  lastUpdated: '2025-07-24',

  fieldMappings: {
    siteUrl: {
      selectors: [
        '#service_url',
        'input[name="service[url]"]',
        'input[type="hidden"][name="service[url]"]'
      ],
      method: 'value',
      validation: 'required|url',
      notes: '产品URL，通过隐藏字段提交'
    },

    siteName: {
      selectors: [
        '#service_name',
        'input[name="service[name]"]',
        'input.input[type="text"]:first-of-type'
      ],
      method: 'value',
      validation: 'required',
      notes: '产品名称'
    },

    tagline: {
      selectors: [
        '#service_tagline',
        'textarea[name="service[tagline]"]',
        'textarea[maxlength="250"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '产品标语，最多250字符'
    },

    category: {
      selectors: [
        '#react-select-2-input',
        '.react-select__input:first-of-type',
        'input[placeholder="Category names..."]'
      ],
      method: 'reactSelect',
      validation: 'optional',
      notes: '分类选择，React Select组件'
    },

    competitors: {
      selectors: [
        '#react-select-3-input',
        '.react-select__input:nth-of-type(2)',
        'input[placeholder="Product names..."]'
      ],
      method: 'reactSelect',
      validation: 'optional',
      notes: '竞争对手产品'
    },

    linkedinUrl: {
      selectors: [
        '#service_linkedin_url',
        'input[name="service[linkedin_url]"]',
        'input[placeholder*="linkedin.com"]'
      ],
      method: 'value',
      validation: 'optional|url',
      notes: 'LinkedIn链接'
    },

    contactEmail: {
      selectors: [
        '#service_contact_email',
        'input[name="service[contact_email]"]',
        '.field_with_errors input'
      ],
      method: 'value',
      validation: 'required|email',
      notes: '联系邮箱，用于审核通知'
    }
  },

  submitConfig: {
    submitButton: 'button[type="submit"]:last-of-type, .bg-gray-100',
    submitMethod: 'click',
    successIndicators: ['.success-message'],
    errorIndicators: ['.error-message']
  },

  specialHandling: {
    requiresLogin: false,
    hasCaptcha: false,
    hasFileUpload: false,
    customScript: 'handleSaaSHubSubmission',
    formValidation: {
      requiredFields: ['siteUrl', 'siteName', 'tagline', 'contactEmail'],
      emailValidation: true,
      urlValidation: true
    },
    notes: [
      '有付费优先审核选项($49)',
      '使用React Select组件',
      '有字符计数器',
      '支持竞争对手选择',
      '默认选择免费提交',
      '使用Bulma CSS框架'
    ]
  }
};

export function handleSaaSHubSubmission(data, rule) {
  const processedData = { ...data };

  if (processedData.siteUrl && !processedData.siteUrl.startsWith('http')) {
    processedData.siteUrl = 'https://' + processedData.siteUrl;
  }

  return processedData;
}

export async function customFillElement(element, value, config) {
  // 处理React Select组件
  if (element.classList.contains('react-select__input')) {
    element.value = value;
    element.dispatchEvent(new Event('input', { bubbles: true }));
    // 模拟选择第一个选项
    setTimeout(() => {
      const option = document.querySelector('.react-select__option');
      if (option) {
        option.click();
      }
    }, 500);
    return true;
  }

  if (element.tagName === 'INPUT' || element.tagName === 'TEXTAREA') {
    element.value = value;
    element.dispatchEvent(new Event('input', { bubbles: true }));
    return true;
  }

  return false;
}