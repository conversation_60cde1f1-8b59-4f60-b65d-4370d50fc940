// StartupList 网站规则配置
// 网站: https://startuplist.ing/products/new
// 最后更新: 2025-08-01

export const SITE_RULE = {
  // 基本信息
  domain: 'startuplist.ing',
  siteName: 'StartupList',
  priority: 1,
  lastUpdated: '2025-08-01',
  
  // 字段映射规则
  fieldMappings: {
    // 网站URL -> URL
    siteUrl: {
      selectors: [
        'input[name="url"]',
        'input[placeholder*="https://startuplist.ing"]'
      ],
      method: 'value',
      validation: 'required|url',
      notes: '网站URL地址，必填字段'
    },

    // 产品名称 -> Name
    siteName: {
      selectors: [
        'input[name="name"]',
        'input[placeholder*="Product Hunt"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '产品名称，必填字段'
    },

    // 标语 -> Tagline
    siteDescription: {
      selectors: [
        'input[name="tagline"]',
        'input[placeholder*="A platform for listing products"]'
      ],
      method: 'value',
      validation: 'required',
      maxLength: 200,
      notes: '产品标语，最多200字符'
    },

    // 详细描述 -> 富文本编辑器
    detailedIntro: {
      selectors: [
        'div[contenteditable="true"][data-placeholder*="Describe your product"]',
        'div[contenteditable="true"]'
      ],
      method: 'contenteditable',
      validation: 'required',
      maxLength: 1000,
      notes: '产品详细描述，支持富文本格式，最多1000字符'
    },

    // Twitter链接 -> X (Twitter) Handle
    twitterUrl: {
      selectors: [
        'input[name="twitter"]',
        'input[placeholder*="https://x.com/product-hunt"]'
      ],
      method: 'value',
      validation: 'optional|url',
      notes: 'Twitter/X链接，可选字段'
    },

    // 标签 -> Tags
    tags: {
      selectors: [
        'input[name="tags"]',
        'input[placeholder*="Healthcare, Finance"]'
      ],
      method: 'value',
      validation: 'optional',
      notes: '产品标签，逗号分隔'
    },

    // 功能特性 -> Features
    features: {
      selectors: [
        'input[name="features"]',
        'input[placeholder*="Amazing product with AI, Open-source, Developer tools"]'
      ],
      method: 'value',
      validation: 'optional',
      notes: '产品功能特性，逗号分隔'
    }
  },
  
  // 提交流程配置
  submitConfig: {
    submitButton: 'button[type="submit"], .submit-button',
    submitMethod: 'click',
    waitAfterFill: 2000,
    waitAfterSubmit: 5000,
    successIndicators: [
      '.success-message',
      '.thank-you',
      '.submission-success'
    ],
    errorIndicators: [
      '.error-message',
      '.validation-error',
      '.form-error'
    ]
  },
  
  // 特殊处理
  specialHandling: {
    requiresLogin: false,
    hasCaptcha: false,
    hasFileUpload: true, // 有Logo上传
    hasRichTextEditor: true, // 有富文本编辑器
    hasDropdowns: true, // 有Category下拉选择
    hasAnalyzeButton: true, // 有URL分析按钮
    
    // 表单验证规则
    formValidation: {
      requiredFields: ['siteUrl', 'siteName', 'siteDescription', 'detailedIntro'],
      optionalFields: ['twitterUrl', 'tags', 'features'],
      emailValidation: false,
      urlValidation: true,
      fileUploadValidation: true
    },
    
    // 特殊注意事项
    notes: [
      'Logo推荐尺寸256x256',
      'Tagline最多200字符',
      '富文本描述最多1000字符',
      '支持URL分析功能',
      'Category需要从下拉列表选择',
      'Tags和Features支持逗号分隔多个值'
    ]
  }
};

// 自定义处理函数
export function handleStartupListSubmission(data, rule) {
  console.log('Processing StartupList submission...');
  
  const processedData = { ...data };
  
  // 确保URL格式正确
  if (processedData.siteUrl && !processedData.siteUrl.startsWith('http')) {
    processedData.siteUrl = 'https://' + processedData.siteUrl;
  }
  
  if (processedData.twitterUrl && !processedData.twitterUrl.startsWith('http')) {
    processedData.twitterUrl = 'https://' + processedData.twitterUrl;
  }
  
  // 处理标签和功能特性，确保格式正确
  if (processedData.tags && Array.isArray(processedData.tags)) {
    processedData.tags = processedData.tags.join(', ');
  }
  
  if (processedData.features && Array.isArray(processedData.features)) {
    processedData.features = processedData.features.join(', ');
  }
  
  return processedData;
}

// 自定义元素填写函数
export async function customFillElement(element, value, config) {
  console.log(`StartupList自定义填写: ${element.tagName}, 方法: ${config.method}`);

  switch (config.method) {
    case 'contenteditable':
      // 富文本编辑器处理
      element.focus();
      await new Promise(resolve => setTimeout(resolve, 300));

      // 清空现有内容
      element.innerHTML = '';
      
      // 设置新内容
      element.innerHTML = `<p>${value}</p>`;
      
      // 触发输入事件
      element.dispatchEvent(new Event('input', { bubbles: true }));
      element.dispatchEvent(new Event('change', { bubbles: true }));
      
      console.log(`✓ 填写富文本字段: "${value}"`);
      return true;

    case 'value':
      // 标准输入框处理
      element.focus();
      await new Promise(resolve => setTimeout(resolve, 200));

      element.value = value;
      element.dispatchEvent(new Event('input', { bubbles: true }));
      element.dispatchEvent(new Event('change', { bubbles: true }));
      element.dispatchEvent(new Event('blur', { bubbles: true }));

      console.log(`✓ 填写字段: ${element.name || element.id} = "${value}"`);
      return true;

    default:
      element.value = value;
      element.dispatchEvent(new Event('input', { bubbles: true }));
      return true;
  }

  return false;
}

// 特殊处理：URL分析功能
export async function handleAnalyzeUrl() {
  console.log('处理URL分析功能...');
  
  // 点击Analyze按钮
  const analyzeButton = document.querySelector('button[data-umami-event="analyze-url"]');
  if (analyzeButton && !analyzeButton.disabled) {
    analyzeButton.click();
    console.log('✓ 已点击Analyze按钮');
    
    // 等待分析完成
    await new Promise(resolve => setTimeout(resolve, 3000));
    return true;
  }
  
  return false;
}
