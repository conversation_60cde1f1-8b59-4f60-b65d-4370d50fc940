// FreeWebSubmission.com 网站规则配置
// 网站: https://www.freewebsubmission.com/
// 最后更新: 2025-07-24

export const SITE_RULE = {
  domain: 'www.freewebsubmission.com',
  siteName: 'Free Web Submission',
  priority: 1,
  lastUpdated: '2025-07-24',

  fieldMappings: {
    siteUrl: {
      selectors: [
        'input[name="url"]',
        'input[placeholder="https://www.example.com"]',
        'input[maxlength="220"]'
      ],
      method: 'value',
      validation: 'required|url',
      notes: '网站URL'
    },

    fullName: {
      selectors: [
        'input[name="Name"]',
        'input[maxlength="125"]:first-of-type'
      ],
      method: 'value',
      validation: 'optional',
      notes: '提交者姓名'
    },

    contactEmail: {
      selectors: [
        'input[name="email"]',
        'input[maxlength="125"]:nth-of-type(2)'
      ],
      method: 'value',
      validation: 'optional|email',
      notes: '联系邮箱'
    }
  },

  submitConfig: {
    submitButton: 'input[name="submit"], input[value="Submit Your Site"]',
    submitMethod: 'click',
    successIndicators: ['.success-message'],
    errorIndicators: ['.error-message']
  },

  specialHandling: {
    requiresLogin: false,
    hasCaptcha: false,
    hasFileUpload: false,
    customScript: 'handleFreeWebSubmissionSubmission',
    formValidation: {
      requiredFields: ['siteUrl'],
      emailValidation: true,
      urlValidation: true
    },
    notes: [
      '需要同意条款复选框',
      '有多个搜索引擎选择',
      '所有搜索引擎默认选中',
      '提交到多个搜索引擎',
      '使用CGI脚本处理',
      '简单的三字段表单'
    ]
  }
};

export function handleFreeWebSubmissionSubmission(data, rule) {
  const processedData = { ...data };

  if (processedData.siteUrl && !processedData.siteUrl.startsWith('http')) {
    processedData.siteUrl = 'https://' + processedData.siteUrl;
  }

  // 自动勾选同意条款
  const termsCheckbox = document.querySelector('input[name="Agreed to Terms"]');
  if (termsCheckbox) {
    termsCheckbox.checked = true;
  }

  // 保持所有搜索引擎选中状态（默认已选中）
  const engineCheckboxes = document.querySelectorAll('input[name="engines"]');
  engineCheckboxes.forEach(checkbox => {
    checkbox.checked = true;
  });

  return processedData;
}

export async function customFillElement(element, value, config) {
  if (element.tagName === 'INPUT') {
    element.value = value;
    element.dispatchEvent(new Event('input', { bubbles: true }));
    element.dispatchEvent(new Event('change', { bubbles: true }));
    return true;
  }

  return false;
}