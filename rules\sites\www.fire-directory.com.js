// Fire-Directory.com 网站规则配置
// 网站: https://www.fire-directory.com/submit.php
// 最后更新: 2025-07-24

export const SITE_RULE = {
  // 基本信息
  domain: 'www.fire-directory.com',
  siteName: 'Fire Directory',
  priority: 1,
  lastUpdated: '2025-07-24',

  // 字段映射规则
  fieldMappings: {
    // 网站标题 -> Title
    siteName: {
      selectors: [
        'input[name="TITLE"]',
        'input[type="text"][name="TITLE"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '网站标题，最多255字符'
    },

    // 网站URL -> URL
    siteUrl: {
      selectors: [
        'input[name="URL"]',
        'input[type="text"][name="URL"]'
      ],
      method: 'value',
      validation: 'required|url',
      notes: '网站URL地址，最多255字符'
    },

    // 网站描述 -> Description
    siteDescription: {
      selectors: [
        'textarea[name="DESCRIPTION"]',
        'textarea[rows="4"]'
      ],
      method: 'value',
      validation: 'optional',
      notes: '网站描述'
    },

    // 所有者姓名 -> Your Name
    fullName: {
      selectors: [
        'input[name="OWNER_NAME"]',
        'input[type="text"][name="OWNER_NAME"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '网站所有者姓名，最多255字符'
    },

    // 所有者邮箱 -> Your Email
    contactEmail: {
      selectors: [
        'input[name="OWNER_EMAIL"]',
        'input[type="text"][name="OWNER_EMAIL"]'
      ],
      method: 'value',
      validation: 'required|email',
      notes: '网站所有者邮箱，最多255字符'
    },

    // 分类选择 -> Category
    category: {
      selectors: [
        'select[name="CATEGORY_ID"]'
      ],
      method: 'select',
      validation: 'required',
      notes: '网站分类选择'
    },

    // 互惠链接URL -> Reciprocal Link URL
    reciprocalUrl: {
      selectors: [
        'input[name="RECPR_URL"]',
        'input[type="text"][name="RECPR_URL"]'
      ],
      method: 'value',
      validation: 'optional|url',
      notes: '互惠链接URL，最多255字符'
    },

    // 互惠链接代码 -> Reciprocal Link Code
    reciprocalCode: {
      selectors: [
        'textarea[name="RECPR_TEXT"]',
        'textarea[readonly]'
      ],
      method: 'value',
      validation: 'optional',
      notes: '互惠链接HTML代码'
    },

    // reCAPTCHA -> Please check reCAPTCHA box
    recaptcha: {
      selectors: [
        'textarea[name="g-recaptcha-response"]',
        '#g-recaptcha-response'
      ],
      method: 'value',
      validation: 'required',
      notes: 'Google reCAPTCHA验证'
    }
  },
  // 提交流程配置
  submitConfig: {
    submitButton: 'input[name="submit"], input[type="submit"]',
    submitMethod: 'manual', // 手动提交
    waitAfterFill: 0,
    waitAfterSubmit: 0,
    successIndicators: ['.success-message'],
    errorIndicators: ['.error-message']
  },

  // 特殊处理
  specialHandling: {
    requiresLogin: false,
    hasCaptcha: true,
    hasFileUpload: false,
    customScript: 'handleFireDirectorySubmission',
    formValidation: {
      requiredFields: ['siteName', 'siteUrl', 'fullName', 'contactEmail', 'category', 'recaptcha'],
      emailValidation: true,
      urlValidation: true
    },
    notes: [
      'Fire Directory 网站目录',
      '传统的网站目录服务',
      '链接在目录中至少保持一年活跃',
      '在此期间不提供链接删除服务',
      '包含Google reCAPTCHA验证',
      '支持互惠链接交换',
      '反向链接格式：<a href="http://www.fire-directory.com/">Fire Directory.com</a>',
      '传统表格样式界面',
      '手动提交模式'
    ]
  }
};

// 自定义处理函数
export function handleFireDirectorySubmission(data) {
  console.log('Processing Fire Directory form submission...');

  const processedData = { ...data };

  // 确保URL格式正确
  if (processedData.siteUrl && !processedData.siteUrl.startsWith('http')) {
    processedData.siteUrl = 'https://' + processedData.siteUrl;
  }

  // 处理互惠链接URL
  if (processedData.reciprocalUrl && !processedData.reciprocalUrl.startsWith('http')) {
    processedData.reciprocalUrl = 'https://' + processedData.reciprocalUrl;
  }

  return processedData;
}

// 自定义元素填写函数
export async function customFillElement(element, value, config) {
  console.log(`Fire Directory自定义填写: ${element.tagName}, 方法: ${config.method}`);

  switch (config.method) {
    case 'value':
      // 标准输入框和文本域处理
      element.focus();
      await new Promise(resolve => setTimeout(resolve, 200));

      element.value = value;
      element.dispatchEvent(new Event('input', { bubbles: true }));
      element.dispatchEvent(new Event('change', { bubbles: true }));
      element.dispatchEvent(new Event('blur', { bubbles: true }));

      console.log(`✓ 填写字段: ${element.name} = "${value}"`);
      return true;

    case 'select':
      // 下拉选择框处理
      if (element.tagName === 'SELECT') {
        const options = element.querySelectorAll('option');
        let selectedOption;

        // 智能匹配分类
        for (const option of options) {
          if (option.value && option.value !== '0') {
            const optionText = option.textContent.trim();
            if (optionText.includes('Arts') || optionText.includes('Business') || optionText.includes('Technology')) {
              selectedOption = option;
              break;
            }
          }
        }

        // 如果没找到合适的，选择第一个非默认选项
        if (!selectedOption) {
          selectedOption = Array.from(options).find(opt => opt.value !== '0');
        }

        if (selectedOption) {
          element.value = selectedOption.value;
          element.dispatchEvent(new Event('change', { bubbles: true }));
          console.log(`✓ 选择分类: ${selectedOption.textContent}`);
          return true;
        }
      }
      break;

    default:
      element.value = value;
      element.dispatchEvent(new Event('input', { bubbles: true }));
      return true;
  }

  return false;
}