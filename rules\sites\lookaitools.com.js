// AI Site Submitter - AI工具名称 规则配置
// 自动生成于: 2025/7/15 11:31:25
// 域名: lookaitools.com

export const SITE_RULE = {
  "domain": "lookaitools.com",
  "siteName": "Lookaitools",
  "lastUpdated": "2025-07-15T03:31:25.371Z",
  "fieldMappings": {
    "siteName": {
      "selectors": [
        "input[name='title']",
        "label:contains('Title') + * input",
        "#title"
      ],
      "type": "input",
      "fillMethod": "value",
      "required": false,
      "validation": "required"
    },
    "siteUrl": {
      "selectors": [
        "input[name='user_url']",
        "label:contains('Tool Url') + * input",
        "[placeholder*='Provide the URL']"
      ],
      "type": "input",
      "fillMethod": "value",
      "required": false,
      "validation": "required"
    },
    "siteDescription": {
      "selectors": [
        "textarea[name='user_excerpt']",
        "label:contains('Tool Excerpt') + * textarea",
        "[placeholder*='Small description']"
      ],
      "type": "input",
      "fillMethod": "value",
      "required": false,
      "validation": "required"
    },
    "detailedIntro": {
      "selectors": [
        "textarea[name='description']",
        "label:contains('Description') + * textarea",
        "#description"
      ],
      "type": "input",
      "fillMethod": "value",
      "required": false,
      "validation": "required"
    },
    "contactEmail": {
      "selectors": [
        "input[name='user_verification']",
        "label:contains('Verification') + * input",
        "[placeholder*='link of our website']"
      ],
      "type": "input",
      "fillMethod": "value",
      "required": false,
      "validation": "required"
    },
    "siteTitle": {
      "selectors": [
        "input[name='title']",
        "input[name='embed_2']"
      ],
      "type": "input",
      "fillMethod": "value",
      "required": false,
      "validation": "optional"
    },
    "screenshots": {
      "selectors": [
        "input[type='file']#images_6875c7a44370b",
        "label:contains('Select Images') + * input",
        "input[type='file']"
      ],
      "type": "input",
      "fillMethod": "value",
      "required": false,
      "validation": "optional"
    }
  },
  "formInfo": {
    "description": "AI工具提交表单，用于向lookaitools.com提交新的AI工具",
    "submitSelector": ".hp-form__button.button-primary.alt.button.hp-field.hp-field--submit",
    "totalFields": 9,
    "notes": [
      "表单包含验证机制，需创建包含网站链接的帖子",
      "图片上传非必填，但建议提供",
      "所有文本字段均为必填项"
    ]
  },
  "metadata": {
    "generatedBy": "AI",
    "generatedAt": "2025-07-15T03:31:25.371Z",
    "version": "3.0.0",
    "aiModel": "moonshotai/Kimi-K2-Instruct"
  }
};

// 自定义处理函数 (可选)
export function handleLookaitoolsComSubmission(data, rule) {
  console.log('Processing AI工具名称 form submission...');
  
  const processedData = { ...data };
  
  // 在这里添加特殊处理逻辑
  // 例如：URL格式化、字段验证、默认值设置等
  
  return processedData;
}

// 自定义元素填写函数 (可选)
export async function customFillElement(element, value, config) {
  console.log('🔧 AI工具名称 自定义填写函数被调用:', element, value);
  
  // 在这里添加特殊的元素填写逻辑
  // 例如：处理特殊的UI组件、异步操作等
  
  return false; // 返回 false 使用默认填写方法
}