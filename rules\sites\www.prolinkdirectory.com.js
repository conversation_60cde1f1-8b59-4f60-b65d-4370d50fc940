// ProLinkDirectory.com 网站规则配置
// 网站: https://www.prolinkdirectory.com/submit.php
// 最后更新: 2025-07-24

export const SITE_RULE = {
  domain: 'www.prolinkdirectory.com',
  siteName: 'ProLink Directory',
  priority: 1,
  lastUpdated: '2025-07-24',

  fieldMappings: {
    siteName: {
      selectors: [
        'input[name="TITLE"]',
        'input[maxlength="100"]',
        'input[size="48"]:first-of-type'
      ],
      method: 'value',
      validation: 'required',
      notes: '网站标题'
    },

    siteUrl: {
      selectors: [
        'input[name="URL"]',
        '#URL',
        'input[maxlength="255"]:first-of-type'
      ],
      method: 'value',
      validation: 'required|url',
      notes: '网站URL'
    },

    siteDescription: {
      selectors: [
        'textarea[name="DESCRIPTION"]',
        'textarea[rows="5"][cols="45"]:first-of-type'
      ],
      method: 'value',
      validation: 'required',
      notes: '网站描述'
    },

    tags: {
      selectors: [
        'input[name="META_KEYWORDS"]',
        'input[maxlength="1024"]'
      ],
      method: 'value',
      validation: 'optional',
      notes: 'META关键词'
    },

    detailedIntro: {
      selectors: [
        'textarea[name="META_DESCRIPTION"]',
        'textarea[rows="5"][cols="45"]:nth-of-type(2)'
      ],
      method: 'value',
      validation: 'optional',
      notes: 'META描述'
    },

    fullName: {
      selectors: [
        'input[name="OWNER_NAME"]',
        'input[maxlength="50"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '提交者姓名'
    },

    contactEmail: {
      selectors: [
        'input[name="OWNER_EMAIL"]',
        'input[maxlength="255"]:nth-of-type(2)'
      ],
      method: 'value',
      validation: 'required|email',
      notes: '联系邮箱'
    }
  },

  submitConfig: {
    submitButton: 'input[name="submit"], input[value="Continue"]',
    submitMethod: 'click',
    successIndicators: ['.success-message'],
    errorIndicators: ['.error-message']
  },

  specialHandling: {
    requiresLogin: false,
    hasCaptcha: true,
    hasFileUpload: false,
    customScript: 'handleProLinkDirectorySubmission',
    formValidation: {
      requiredFields: ['siteName', 'siteUrl', 'siteDescription', 'fullName', 'contactEmail'],
      emailValidation: true,
      urlValidation: true
    },
    notes: [
      '需要选择Regular link (Free)选项',
      '有验证码验证',
      '包含可选的深层链接',
      '有META信息字段',
      '需要同意条款',
      '使用传统表格布局'
    ]
  }
};

export function handleProLinkDirectorySubmission(data, rule) {
  const processedData = { ...data };

  if (processedData.siteUrl && !processedData.siteUrl.startsWith('http')) {
    processedData.siteUrl = 'https://' + processedData.siteUrl;
  }

  // 自动选择Regular link (Free)选项
  const regularLinkRadio = document.querySelector('input[name="LINK_TYPE"][value="normal"]');
  if (regularLinkRadio) {
    regularLinkRadio.click();
  }

  return processedData;
}

export async function customFillElement(element, value, config) {
  if (element.tagName === 'INPUT' || element.tagName === 'TEXTAREA') {
    element.value = value;
    element.dispatchEvent(new Event('input', { bubbles: true }));
    element.dispatchEvent(new Event('change', { bubbles: true }));
    return true;
  }

  return false;
}