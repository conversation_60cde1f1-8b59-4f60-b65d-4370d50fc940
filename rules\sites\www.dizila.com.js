// Dizila.com 网站规则配置
// 网站: https://www.dizila.com/submit?c=4&LINK_TYPE=1
// 最后更新: 2025-07-24

export const SITE_RULE = {
  // 基本信息
  domain: 'www.dizila.com',
  siteName: 'Dizila Directory',
  priority: 1,
  lastUpdated: '2025-07-24',

  // 字段映射规则
  fieldMappings: {
    // 网站标题 -> Title
    siteName: {
      selectors: [
        '#TITLE',
        'input[name="TITLE"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '网站标题，最多100字符'
    },

    // 网站URL -> URL
    siteUrl: {
      selectors: [
        '#URL',
        'input[name="URL"]'
      ],
      method: 'value',
      validation: 'required|url',
      notes: '网站URL地址'
    },

    // 网站描述 -> Description
    siteDescription: {
      selectors: [
        '#DESCRIPTION',
        'textarea[name="DESCRIPTION"]'
      ],
      method: 'value',
      validation: 'optional',
      notes: '网站描述'
    },

    // 所有者姓名 -> Owner Name
    fullName: {
      selectors: [
        '#OWNER_NAME',
        'input[name="OWNER_NAME"]'
      ],
      method: 'value',
      validation: 'optional',
      notes: '网站所有者姓名，最多50字符'
    },

    // 所有者邮箱 -> Owner Email
    contactEmail: {
      selectors: [
        '#OWNER_EMAIL',
        'input[name="OWNER_EMAIL"]'
      ],
      method: 'value',
      validation: 'optional|email',
      notes: '网站所有者邮箱，最多255字符'
    },

    // 通讯订阅 -> Newsletter Allow
    newsletter: {
      selectors: [
        'input[name="OWNER_NEWSLETTER_ALLOW"]',
        'input[type="checkbox"][name="OWNER_NEWSLETTER_ALLOW"]'
      ],
      method: 'checkbox',
      validation: 'optional',
      notes: '允许管理员发送通讯'
    },

    // 同意规则 -> Submission Rules Agreement
    agreeRules: {
      selectors: [
        '#AGREERULES',
        'input[name="AGREERULES"]'
      ],
      method: 'checkbox',
      validation: 'required',
      notes: '同意提交规则'
    }
  },
  // 提交流程配置
  submitConfig: {
    submitButton: 'input[name="continue"], input[value="Continue"]',
    submitMethod: 'manual', // 手动提交
    waitAfterFill: 0,
    waitAfterSubmit: 0,
    successIndicators: ['.success-message'],
    errorIndicators: ['.error-message']
  },

  // 特殊处理
  specialHandling: {
    requiresLogin: false,
    hasCaptcha: false,
    hasFileUpload: false,
    customScript: 'handleDizilaSubmission',
    formValidation: {
      requiredFields: ['siteName', 'siteUrl', 'agreeRules'],
      emailValidation: true,
      urlValidation: true
    },
    notes: [
      'Dizila Directory 网站目录',
      '基于PHPLD系统构建',
      '艺术家分类目录',
      '必须同意提交规则',
      '支持通讯订阅选项',
      '链接类型已预设为1（标准链接）',
      '分类已预设为4（艺术家）',
      '现代化的表单样式',
      '手动提交模式'
    ]
  }
};

// 自定义处理函数
export function handleDizilaSubmission(data) {
  console.log('Processing Dizila form submission...');

  const processedData = { ...data };

  // 确保URL格式正确
  if (processedData.siteUrl && !processedData.siteUrl.startsWith('http')) {
    processedData.siteUrl = 'https://' + processedData.siteUrl;
  }

  return processedData;
}

// 自定义元素填写函数
export async function customFillElement(element, value, config) {
  console.log(`Dizila自定义填写: ${element.tagName}, 方法: ${config.method}`);

  switch (config.method) {
    case 'value':
      // 标准输入框和文本域处理
      element.focus();
      await new Promise(resolve => setTimeout(resolve, 200));

      element.value = value;
      element.dispatchEvent(new Event('input', { bubbles: true }));
      element.dispatchEvent(new Event('change', { bubbles: true }));
      element.dispatchEvent(new Event('blur', { bubbles: true }));

      console.log(`✓ 填写字段: ${element.name || element.id} = "${value}"`);
      return true;

    case 'checkbox':
      // 复选框处理
      if (element.type === 'checkbox') {
        element.checked = true;
        element.dispatchEvent(new Event('change', { bubbles: true }));
        console.log(`✓ 复选框设置: ${element.name} = ${element.checked}`);
        return true;
      }
      break;

    default:
      element.value = value;
      element.dispatchEvent(new Event('input', { bubbles: true }));
      return true;
  }

  return false;
}