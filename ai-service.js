// AI Site Submitter v3.0.0 - AI 服务模块
// 集成硅基流动 AI 模型进行表单分析和规则生成
// 最后更新: 2025-07-15

/**
 * AI 服务配置
 */
const AI_CONFIG = {
    apiUrl: 'https://api.siliconflow.cn/v1/chat/completions',
    apiKey: 'sk-qwykjbciiodizhzjaootmjbhvyldqkrbcgoqdumhjrfteefc',
    model: 'moonshotai/Kimi-K2-Instruct',
    maxTokens: 16384,  // 根据文档，Kimi-K2-Instruct 的 max_tokens 限制为 16384
    temperature: 0.7,
    topP: 0.7,
    topK: 50,
    frequencyPenalty: 0.0,
    systemPrompt: `你是一个专业的表单语义分析和字段映射专家，采用分层决策机制进行精确的字段映射。

## 🎯 核心任务
将网页表单字段智能映射到标准的website-info.js配置字段，重点解决字段映射混淆问题。

## 🧠 分层决策机制
**第一层：字段类型分类**
- 网站信息类：siteName, siteUrl, siteDescription, detailedIntro
- 联系信息类：fullName, firstName, lastName, contactEmail, phone
- 地址信息类：streetAddress, city, state, zipCode
- 商业信息类：companyName, pricing, price, submitterRole
- 分类标签类：category, tags, keywords

**第二层：上下文权重计算**
- 位置权重：表单前1/3(+3分) > 中间(+1分) > 后1/3(0分)
- 区域权重：网站信息区(+2分) > 联系信息区(+1分) > 其他(0分)
- 相邻字段权重：相关字段相邻(+1分)

**第三层：冲突解决机制**
- 优先级排序：siteName(10) > siteUrl(9) > contactEmail(8) > fullName(7) > companyName(6)
- 置信度评估：高置信度(>80%)优先，低置信度(<50%)使用默认值
- 排除规则：明确的否定条件必须严格执行

## 📝 输出要求
- 严格按照JSON格式返回，无额外解释
- 每个映射必须说明决策依据和置信度
- 选择器数组至少包含3个备选方案
- 优先映射核心字段，确保实用性

你的分层决策将确保字段映射的准确性和一致性。`
};

/**
 * AI 服务类
 */
class AIService {
    constructor() {
        this.config = AI_CONFIG;
    }

    /**
     * 分析表单字段并生成规则
     * @param {Object} formData - 表单数据
     * @param {string} domain - 网站域名
     * @returns {Promise<Object>} 生成的规则对象
     */
    async analyzeFormAndGenerateRule(formData, domain) {
        try {
            console.log('🤖 开始AI分析表单字段...');
            
            const prompt = this.buildAnalysisPrompt(formData, domain);
            const response = await this.callAI(prompt);
            
            if (!response || !response.choices || !response.choices[0]) {
                throw new Error('AI 响应格式无效');
            }
            
            const content = response.choices[0].message.content;
            const rule = this.parseAIResponse(content, domain);
            
            console.log('✅ AI 分析完成，规则生成成功');
            return rule;
            
        } catch (error) {
            console.error('❌ AI 分析失败:', error);
            throw new Error(`AI 分析失败: ${error.message}`);
        }
    }

    /**
     * 构建分析提示词
     * @param {Object} formData - 表单数据
     * @param {string} domain - 网站域名
     * @returns {string} 分析提示词
     */
    buildAnalysisPrompt(formData, domain) {
        const { fields, formInfo, htmlContent } = formData;

        // 如果有 HTML 内容，优先使用 HTML 分析
        if (htmlContent) {
            return this.buildHtmlAnalysisPrompt(htmlContent, domain);
        }

        // 否则使用字段信息分析
        const fieldsDescription = fields.map(field => {
            let description = `- ${field.name || field.id || 'unnamed'}: ${field.type} (${field.selector})
              标签: ${field.label || '无'}
              占位符: ${field.placeholder || '无'}
              必填: ${field.required ? '是' : '否'}`;

            // 对于select字段，详细显示所有选项
            if (field.type === 'select' && field.options && field.options.length > 0) {
                const optionsText = field.options.map(opt => `"${opt.value}" (${opt.text})`).join(', ');
                description += `\n              选项: ${optionsText}`;
            } else {
                description += `\n              选项: ${field.options ? field.options.join(', ') : '无'}`;
            }

            return description;
        }).join('\n');

        return `你是一个专业的表单字段语义分析专家。请仔细分析以下网站表单，并将表单字段智能映射到标准的website-info.js字段。

网站域名: ${domain}
表单信息: ${JSON.stringify(formInfo, null, 2)}

表单字段详情:
${fieldsDescription}

## 🎯 核心任务：智能字段映射

请根据字段的**语义含义**而非字面名称进行映射。重点分析：
1. **字段标签文本**（label）- 最重要的语义线索
2. **占位符文本**（placeholder）- 提供使用示例和含义
3. **字段名称**（name/id）- 技术标识符
4. **字段类型**（input type）- 数据类型提示
5. **选择框选项**（select options）- 分析所有可选值
6. **上下文位置** - 在表单中的相对位置

## 📋 精确字段映射规则

### 🏢 网站信息类字段（优先级最高）

**siteName (权重10) - 网站/产品/工具名称**
- ✅ 强匹配条件：
  * 位置在表单前1/3 + 包含"tool/product/site/app/service/project" + "name/title"
  * 相邻字段有siteUrl或siteDescription
  * 占位符包含"tool name/product name/app name"
- ❌ 排除条件：
  * 包含"contact/your/submitter/person/user" → 不是siteName
  * 包含"company/organization/business" → 不是siteName
  * 位置在联系信息区域 → 不是siteName
- 🎯 置信度评估：强匹配(90%) > 位置匹配(70%) > 关键词匹配(50%)

**siteUrl (权重9) - 网站URL地址**
- ✅ 强匹配条件：
  * input type="url" 或 包含"http"的placeholder
  * 标签包含"website/homepage/url/link/web address"
  * 占位符格式为"https://example.com"
- ❌ 排除条件：
  * 包含"social/twitter/facebook/linkedin" → 不是siteUrl
- 🎯 置信度评估：type="url"(95%) > 格式匹配(85%) > 关键词匹配(70%)

**siteDescription vs detailedIntro 智能区分**
- **siteDescription (简短描述)**：
  * input标签 或 textarea rows≤3 或 maxlength≤200
  * 标签包含"brief/short/summary/tagline"
  * 占位符提示字符限制
- **detailedIntro (详细介绍)**：
  * textarea rows>3 或 maxlength>200 或 无长度限制
  * 标签包含"detailed/full/complete/about/overview"
  * 占位符提示"详细说明/tell us more"

### 🏷️ 分类和标签
- **category**: 网站分类
  - 常见表单字段: "Category", "Type", "Classification", "Genre", "Section"
  - 通常是下拉选择框

- **tags**: 标签
  - 常见表单字段: "Tags", "Keywords", "Labels", "Topics"
  - 占位符示例: "AI, Tools, Productivity"

- **keywords**: 关键词
  - 常见表单字段: "Keywords", "SEO Keywords", "Search Terms", "Tags"
  - 占位符示例: "keyword1, keyword2, keyword3"

### 💰 定价信息
- **pricing**: 定价模式
  - 常见表单字段: "Pricing", "Price Model", "Cost", "Plan Type"
  - 选项: "Free", "Freemium", "Paid", "Subscription"

- **price**: 具体价格
  - 常见表单字段: "Price", "Cost", "Amount", "Fee"
  - 占位符示例: "$9.99", "Enter price"

### 👤 联系信息类字段（权重7-6）

**fullName vs companyName 精确区分**
- **fullName (权重7)**：
  * ✅ 强匹配：位置在联系信息区 + 包含"name/contact name/your name/submitter name"
  * ✅ 上下文：相邻有email/phone字段
  * ❌ 排除：包含"company/organization/business" → 不是fullName
  * ❌ 排除：位置在表单开头且相邻有siteUrl → 不是fullName

- **companyName (权重6)**：
  * ✅ 强匹配：包含"company/organization/business/team" + "name"
  * ✅ 上下文：在联系信息区但明确标注公司信息
  * ❌ 排除：包含"your/contact/submitter" → 不是companyName

**contactEmail (权重8)**
- ✅ 强匹配条件：
  * input type="email" 或 包含"@"的placeholder
  * 标签包含"email/contact email/your email"
- ❌ 排除条件：
  * 包含"company/business" → 可能是其他邮箱字段
- 🎯 置信度：type="email"(95%) > 格式匹配(85%) > 关键词匹配(70%)

### 🏠 地址信息类字段

**streetAddress (权重5)**
- ✅ 匹配：包含"address/street/address line 1/full address"
- ❌ 排除：包含"email/web" → 不是物理地址

**city/state/zipCode 组合识别**
- 通常成组出现，相邻字段权重+1
- city: 包含"city/town/municipality"
- state: 包含"state/province/region"
- zipCode: 包含"zip/postal/post code"

## 🧠 核心决策算法

**Step 1: 字段类型预分类**
1. 检查字段位置（前1/3、中间、后1/3）
2. 识别表单区域（网站信息区、联系信息区、其他）
3. 分析相邻字段语义关联

**Step 2: 权重计算与排序**
1. 基础权重 + 位置权重 + 上下文权重
2. 应用排除规则（强制-10分）
3. 计算最终置信度分数

**Step 3: 冲突解决机制**
1. 同类字段竞争：选择权重最高者
2. 置信度<50%：使用默认值或跳过
3. 多个高置信度：按优先级排序选择

## 🎯 选择框智能处理增强

### 📋 category字段专项优化
- **AI工具分类映射**：
  * "AI Tools/AI/Artificial Intelligence" → "Ai Tools"
  * "Developer Tools/Development/Programming" → "Developer Tools"
  * "Productivity/Business/Enterprise" → "Productivity"
  * "Design/Creative/Graphics" → "Design"
- **置信度评估**：精确匹配(95%) > 语义匹配(80%) > 通用匹配(60%)

### 📋 pricing字段专项优化
- **定价模式映射**：
  * "Free/Free to use/No cost" → "Free"
  * "Freemium/Free + Premium" → "Freemium"
  * "Paid/Premium/Subscription" → "Paid"
  * "One-time/Purchase" → "Paid"

### 📋 未知字段智能默认值
**优先级排序**：
1. **积极状态**: "active" > "enabled" > "public" > "yes" > "true"
2. **新建操作**: "new" > "add" > "create" > "submit"
3. **标准选项**: "normal" > "medium" > "standard" > "default"
4. **语言地区**: "en" > "english" > "US" > "United States"
5. **兜底策略**: 第一个非空选项

## 🔧 多选择器生成策略

**选择器优先级**：
1. **ID选择器** (最稳定): #field-id
2. **Name属性** (次稳定): [name="fieldname"]
3. **复合属性** (中等): input[type="text"][placeholder*="关键词"]
4. **类选择器** (较弱): .field-class
5. **位置选择器** (兜底): form input:nth-of-type(n)

**生成规则**：
- 每个字段必须提供3-5个备选选择器
- 按稳定性排序，优先使用稳定选择器
- 包含语义相关的属性选择器
- 避免过于具体的CSS路径

## 📋 输出格式要求

**JSON结构**：
{
  "domain": "${domain}",
  "siteName": "从域名提取的驼峰命名",
  "priority": 1,
  "lastUpdated": "${new Date().toISOString()}",
  "fieldMappings": {
    "字段名": {
      "selectors": ["主选择器", "备选选择器1", "备选选择器2"],
      "method": "value", // select字段使用"select"
      "validation": "required",
      "notes": "映射依据：[决策过程] + 置信度：[百分比]",
      "confidence": 85, // 置信度分数(0-100)
      "defaultValue": "默认值", // select字段必须提供
      "options": [{"value": "选项值", "text": "选项文本"}] // 仅select字段
    }
  },
  "formInfo": {
    "description": "表单描述",
    "submitSelector": "提交按钮选择器",
    "totalFields": 字段数量,
    "notes": ["映射统计和特殊说明"]
  }
}

## ⚠️ 关键要求

**映射质量**：
- 优先映射核心字段：siteName > siteUrl > siteDescription > contactEmail
- 每个字段必须说明决策依据和置信度
- 置信度<50%的字段建议跳过或使用默认值

**选择器质量**：
- 至少3个备选选择器，按稳定性排序
- 避免过于具体的CSS路径
- 包含语义相关的属性选择器

**输出格式**：
- 严格JSON格式，无额外解释文字
- notes字段必须包含决策过程和置信度
- select字段必须包含完整的options数组

只返回JSON，不要其他解释文字。`;
    }

    /**
     * 构建 HTML 分析提示词
     * @param {string} htmlContent - HTML 内容
     * @param {string} domain - 网站域名
     * @returns {string} HTML 分析提示词
     */
    buildHtmlAnalysisPrompt(htmlContent, domain) {
        return `你是一个专业的HTML表单语义分析专家。请深度分析以下网页HTML代码，智能识别表单字段并映射到标准的website-info.js字段。

网站域名: ${domain}
页面URL: ${window.location.href}

HTML 内容:
${htmlContent}

## 🔍 HTML分析任务

请仔细分析HTML代码中的所有表单元素：
1. **input标签**（text, email, url, password, number, tel等）
2. **textarea文本域**
3. **select下拉框**
4. **radio单选框**
5. **checkbox复选框**
6. **文件上传字段**

## 🧠 智能语义分析策略

### 1. 多层次语义识别
- **label标签内容** - 最直接的字段含义
- **placeholder属性** - 使用示例和提示
- **name/id属性** - 技术标识符
- **class属性** - 样式和功能提示
- **周围文本** - 上下文语义线索
- **表单结构** - 字段在表单中的位置和分组

### 2. 常见字段模式识别

**🏢 网站/产品名称 (siteName)**
- HTML模式: \`<input name="name">\`, \`<input name="title">\`, \`<input name="product_name">\`
- Label文本: "Tool Name", "Product Name", "App Name", "Site Name", "Service Name"
- Placeholder: "Enter your tool name", "Product title", "App name"

**🌐 网站URL (siteUrl)**
- HTML模式: \`<input type="url">\`, \`<input name="website">\`, \`<input name="url">\`
- Label文本: "Website", "URL", "Homepage", "Link", "Web Address"
- Placeholder: "https://example.com", "Enter website URL"

**📝 描述字段智能区分**
- **siteDescription** (简短): \`<input>\`或短\`<textarea>\`, placeholder提示字符限制
- **detailedIntro** (详细): 长\`<textarea>\`, 通常有更大的rows属性

**📧 联系邮箱 (contactEmail)**
- HTML模式: \`<input type="email">\`, \`<input name="email">\`
- Label文本: "Email", "Contact Email", "Your Email"

**👤 姓名字段**
- **fullName**: \`<input name="name">\` (如果不是产品名)
- **firstName**: \`<input name="first_name">\`
- **lastName**: \`<input name="last_name">\`

**🏷️ 分类标签**
- **category**: \`<select>\`下拉框，选项为分类
- **tags**: \`<input>\`，placeholder提示用逗号分隔

**💰 定价信息**
- **pricing**: \`<select>\`，选项包含"Free", "Paid"等
- **price**: \`<input type="number">\`或包含货币符号的输入框

### 3. 高级选择器生成策略

为每个识别的字段生成多个备选选择器：
1. **属性选择器**: \`input[name="fieldname"]\`
2. **ID选择器**: \`#field-id\`
3. **类选择器**: \`.field-class\`
4. **复合选择器**: \`form input[type="text"]:nth-child(1)\`
5. **标签关联**: \`label[for="field-id"] + input\`

### 4. 选择框智能处理

**🎯 select字段特殊处理规则**：
- 分析所有\`<option>\`标签的value和text属性
- 对于website-info.js中存在的字段，智能匹配最合适的选项
- 对于website-info.js中不存在的字段，根据以下规则选择默认值：
  * 提交类型: "new" > "add" > "submit" > "create"
  * 状态类型: "active" > "enabled" > "public" > "published"
  * 确认选项: "yes" > "true" > "agree" > "accept"
  * 可见性: "public" > "visible" > "show"
  * 语言选择: "en" > "english" > "英语"

### 5. 特殊情况处理

- **忽略系统字段**: csrf_token, authenticity_token, _token等
- **动态ID处理**: React/Vue生成的动态ID，使用属性选择器
- **隐藏字段**: 跳过\`type="hidden"\`字段
- **按钮识别**: 找到submit按钮作为submitSelector

## 📋 标准字段映射参考

### 基本信息 (优先级最高)
- **siteName**: 产品/工具/网站名称
- **siteUrl**: 网站URL地址
- **siteDescription**: 简短描述(50-160字符)
- **detailedIntro**: 详细介绍(200-500字符)
- **contactEmail**: 联系邮箱

### 分类标签
- **category**: 网站分类
- **tags**: 标签(逗号分隔)
- **keywords**: 关键词

### 联系信息
- **fullName**: 完整姓名
- **firstName**: 名字
- **lastName**: 姓氏
- **companyName**: 公司名称
- **phone**: 电话号码

### 定价信息
- **pricing**: 定价模式
- **price**: 具体价格

### 社交媒体
- **twitterUrl**: Twitter链接
- **linkedinUrl**: LinkedIn链接

请以JSON格式返回规则配置：
{
  "domain": "${domain}",
  "siteName": "网站名称",
  "priority": 1,
  "lastUpdated": "${new Date().toISOString()}",
  "fieldMappings": {
    "字段名": {
      "selectors": ["主选择器", "备选选择器1", "备选选择器2"],
      "method": "value", // select字段使用"select"
      "validation": "required",
      "notes": "映射原因和字段语义分析",
      "maxLength": 数字(可选),
      "defaultValue": "默认值", // select字段必须提供智能选择的默认值
      "options": [{"value": "选项值", "text": "选项文本"}] // 仅select字段需要
    }
  },
  "formInfo": {
    "description": "表单描述",
    "submitSelector": "提交按钮选择器",
    "totalFields": 字段数量,
    "notes": ["表单特殊说明"]
  }
}

⚠️ 关键要求：
- 基于语义含义映射，不是字面匹配
- 每个字段至少3个备选选择器
- notes字段详细说明映射逻辑
- 优先映射核心字段: siteName > siteUrl > siteDescription > contactEmail
- **select字段特殊要求**：
  * method必须设为"select"
  * 必须提供defaultValue（智能选择的默认值）
  * 必须包含options数组（从HTML中提取的所有选项）
  * 对于website-info.js中不存在的字段，根据语义规则选择最合理的defaultValue
- 只返回JSON，无其他文字

只返回JSON，不要其他解释文字。`;
    }

    /**
     * 调用 AI API
     * @param {string} prompt - 提示词
     * @returns {Promise<Object>} AI 响应
     */
    async callAI(prompt) {
        const requestBody = {
            model: this.config.model,
            messages: [
                {
                    role: "system",
                    content: this.config.systemPrompt
                },
                {
                    role: "user",
                    content: prompt
                }
            ],
            max_tokens: this.config.maxTokens,
            temperature: this.config.temperature,
            top_p: this.config.topP,
            top_k: this.config.topK,
            frequency_penalty: this.config.frequencyPenalty,
            stream: false
        };

        console.log('📡 发送 AI 请求...');
        
        const response = await fetch(this.config.apiUrl, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${this.config.apiKey}`
            },
            body: JSON.stringify(requestBody)
        });

        if (!response.ok) {
            const errorText = await response.text();
            throw new Error(`API 请求失败: ${response.status} - ${errorText}`);
        }

        return await response.json();
    }

    /**
     * 解析 AI 响应
     * @param {string} content - AI 响应内容
     * @param {string} domain - 网站域名
     * @returns {Object} 解析后的规则对象
     */
    parseAIResponse(content, domain) {
        try {
            // 提取JSON内容
            const jsonMatch = content.match(/\{[\s\S]*\}/);
            if (!jsonMatch) {
                throw new Error('AI 响应中未找到有效的 JSON');
            }

            const rule = JSON.parse(jsonMatch[0]);
            
            // 验证规则结构
            if (!rule.fieldMappings || typeof rule.fieldMappings !== 'object') {
                throw new Error('规则格式无效：缺少 fieldMappings');
            }

            // 确保域名正确
            rule.domain = domain;
            rule.lastUpdated = new Date().toISOString();

            return rule;
            
        } catch (error) {
            console.error('解析 AI 响应失败:', error);
            throw new Error(`解析 AI 响应失败: ${error.message}`);
        }
    }

    /**
     * 检查 API 连接状态
     * @returns {Promise<boolean>} 连接状态
     */
    async checkConnection() {
        try {
            const response = await this.callAI('测试连接');
            return response && response.choices && response.choices.length > 0;
        } catch (error) {
            console.error('AI 连接检查失败:', error);
            return false;
        }
    }
}

// 导出 AI 服务实例
export const aiService = new AIService();
