// W3.org 网站规则配置
// 网站: https://www.w3.org/WAI/test-evaluate/tools/submit-a-tool/
// 最后更新: 2025-07-24

export const SITE_RULE = {
  domain: 'www.w3.org',
  siteName: 'W3C WAI Tools List',
  priority: 1,
  lastUpdated: '2025-07-24',

  fieldMappings: {
    siteName: {
      selectors: [
        '#title',
        'input[name="title"]',
        'label:contains("Tool name") + input'
      ],
      method: 'value',
      validation: 'required',
      notes: '工具名称'
    },

    siteUrl: {
      selectors: [
        '#website',
        'input[name="website"]',
        'input[type="url"]:first-of-type'
      ],
      method: 'value',
      validation: 'required|url',
      notes: '工具网站地址'
    },

    companyName: {
      selectors: [
        '#provider',
        'input[name="provider"]',
        'label:contains("Vendor") + input'
      ],
      method: 'value',
      validation: 'required',
      notes: '供应商/组织名称'
    },

    contactEmail: {
      selectors: [
        '#contact',
        'input[name="contact"]',
        'input[type="email"]'
      ],
      method: 'value',
      validation: 'required|email',
      notes: '联系邮箱'
    },

    releaseDate: {
      selectors: [
        '#release',
        'input[name="release"]',
        'input[type="date"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '发布日期'
    },

    siteDescription: {
      selectors: [
        '#feature-desc',
        'textarea[name="features"]',
        'textarea[maxlength="350"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '产品描述，最多350字符'
    },

    language: {
      selectors: [
        '#language_1',
        'select[name="language[]"]',
        '.select-form:first-of-type'
      ],
      method: 'select',
      validation: 'required',
      defaultValue: 'en',
      notes: '工具语言'
    }
  },

  submitConfig: {
    submitButton: 'button[type="submit"], input[type="submit"]',
    submitMethod: 'click',
    successIndicators: ['.success-message'],
    errorIndicators: ['.error-message']
  },

  specialHandling: {
    requiresLogin: false,
    hasCaptcha: false,
    hasFileUpload: false,
    customScript: 'handleW3CSubmission',
    formValidation: {
      requiredFields: ['siteName', 'siteUrl', 'companyName', 'contactEmail', 'releaseDate', 'siteDescription', 'language'],
      emailValidation: true,
      urlValidation: true
    },
    notes: [
      '需要勾选确认条款',
      '有多个复选框组',
      '描述限制350字符',
      '需要填写发布日期',
      '表单提交到Netlify Functions',
      '复杂的无障碍工具评估表单'
    ]
  }
};

export function handleW3CSubmission(data, rule) {
  const processedData = { ...data };

  if (processedData.siteUrl && !processedData.siteUrl.startsWith('http')) {
    processedData.siteUrl = 'https://' + processedData.siteUrl;
  }

  // 自动勾选确认条款
  const termsCheckbox = document.querySelector('#readterms');
  if (termsCheckbox) {
    termsCheckbox.checked = true;
  }

  // 自动选择一些必需的复选框
  const purposeCheckbox = document.querySelector('#tool-purpose-automated');
  if (purposeCheckbox) {
    purposeCheckbox.checked = true;
  }

  const productCheckbox = document.querySelector('#tool-product-website');
  if (productCheckbox) {
    productCheckbox.checked = true;
  }

  const licenseCheckbox = document.querySelector('#tool-license-free');
  if (licenseCheckbox) {
    licenseCheckbox.checked = true;
  }

  const typeCheckbox = document.querySelector('#tool-type-typeWebOnlineTool');
  if (typeCheckbox) {
    typeCheckbox.checked = true;
  }

  const scopeCheckbox = document.querySelector('#tool-automated-component');
  if (scopeCheckbox) {
    scopeCheckbox.checked = true;
  }

  return processedData;
}

export async function customFillElement(element, value, config) {
  if (element.tagName === 'SELECT') {
    const options = element.querySelectorAll('option');
    const option = Array.from(options).find(opt =>
      opt.value === 'en' || opt.textContent.includes('English')
    );
    if (option) {
      element.value = option.value;
      element.dispatchEvent(new Event('change', { bubbles: true }));
      return true;
    }
  }

  if (element.tagName === 'INPUT' || element.tagName === 'TEXTAREA') {
    element.value = value;
    element.dispatchEvent(new Event('input', { bubbles: true }));
    return true;
  }

  return false;
}