// Sidebar.io 网站规则配置
// 网站: https://sidebar.io/submit
// 最后更新: 2025-07-24

export const SITE_RULE = {
  // 基本信息
  domain: 'sidebar.io',
  siteName: 'Sidebar',
  priority: 1,
  lastUpdated: '2025-07-24',

  // 字段映射规则
  fieldMappings: {
    // 网站URL -> URL
    siteUrl: {
      selectors: [
        'input[name="url"]',
        '#url',
        'input[type="url"]'
      ],
      method: 'value',
      validation: 'required|url',
      notes: '网站链接地址'
    },

    // 网站标题 -> Title
    siteName: {
      selectors: [
        'input[name="title"]',
        '#title',
        'input[path="title"]'
      ],
      method: 'value',
      validation: 'required',
      maxLength: 80,
      notes: '网站或工具标题'
    },

    // 网站描述 -> Body
    siteDescription: {
      selectors: [
        'textarea[name="body"]',
        '#body',
        'textarea[path="body"]'
      ],
      method: 'value',
      validation: 'required',
      maxLength: 150,
      notes: '网站描述，支持Markdown格式'
    },

    // Twitter用户名 -> Credit
    twitterUsername: {
      selectors: [
        'input[name="credit"]',
        '#credit',
        'input[path="credit"]'
      ],
      method: 'value',
      validation: 'optional',
      maxLength: 20,
      notes: 'Twitter用户名（不含@符号）'
    },

    // 分类标签 -> Categories
    tags: {
      selectors: [
        '.rbt-input-main',
        'input.rbt-input-main',
        '.rbt-input-multi input'
      ],
      method: 'value',
      validation: 'optional',
      notes: '最多可选择3个分类，使用自动完成输入'
    }
  },

  // 提交流程配置
  submitConfig: {
    submitButton: 'button[type="submit"].btn.btn-primary',
    submitMethod: 'click',
    successIndicators: [
      '.success-message',
      '.alert-success',
      '.form-success'
    ],
    errorIndicators: [
      '.form-errors',
      '.alert-danger',
      '.error-message'
    ]
  },

  // 特殊处理
  specialHandling: {
    requiresLogin: true, // 需要登录
    hasCaptcha: false,
    hasFileUpload: false,

    // 自定义处理脚本
    customScript: 'handleSidebarSubmission',

    // 表单验证规则
    formValidation: {
      requiredFields: ['siteUrl', 'siteName', 'siteDescription'],
      emailValidation: false,
      urlValidation: true,
      characterLimits: {
        siteName: 80,
        siteDescription: 150,
        twitterUsername: 20
      }
    },

    // 特殊注意事项
    notes: [
      '需要先登录才能提交',
      '标题限制80字符',
      '描述限制150字符，支持Markdown',
      'Credit字段填写Twitter用户名（不含@）',
      '分类最多选择3个',
      '提交后需要审核'
    ]
  }
};

// 自定义处理函数
export function handleSidebarSubmission(data, rule) {
  console.log('Processing Sidebar.io submission...');

  const processedData = { ...data };

  // 确保URL格式正确
  if (processedData.siteUrl && !processedData.siteUrl.startsWith('http')) {
    processedData.siteUrl = 'https://' + processedData.siteUrl;
  }

  // 处理Twitter用户名 - 移除@符号
  if (processedData.twitterUsername) {
    processedData.twitterUsername = processedData.twitterUsername.replace('@', '');
  }

  // 确保标题长度不超过80字符
  if (processedData.siteName && processedData.siteName.length > 80) {
    processedData.siteName = processedData.siteName.substring(0, 80);
  }

  // 确保描述长度不超过150字符
  if (processedData.siteDescription && processedData.siteDescription.length > 150) {
    processedData.siteDescription = processedData.siteDescription.substring(0, 150);
  }

  // 处理标签 - 如果有多个标签，用逗号分隔
  if (processedData.tags && typeof processedData.tags === 'string') {
    // 将tags字段转换为适合自动完成输入的格式
    processedData.tags = processedData.tags.split(',').map(tag => tag.trim()).join(', ');
  }

  console.log('Sidebar.io 数据处理完成:', processedData);
  return processedData;
}