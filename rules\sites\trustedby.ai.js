// TrustedBy.ai 网站规则配置
// 网站: https://trustedby.ai/submit
// 最后更新: 2025-07-24

export const SITE_RULE = {
  // 基本信息
  domain: 'trustedby.ai',
  siteName: 'TrustedBy.ai',
  priority: 1,
  lastUpdated: '2025-07-24',
  
  // 字段映射规则
  fieldMappings: {
    // 产品链接 -> Link (对应website-info.js的siteUrl)
    siteUrl: {
      selectors: [
        'input[name="link"]',
        'input[placeholder*="Enter the link to your product"]',
        '#\\:r0\\:-form-item'
      ],
      method: 'value',
      validation: 'required|url',
      notes: '产品链接，对应website-info.js的siteUrl字段'
    },

    // 产品名称 -> Name (对应website-info.js的siteName)
    siteName: {
      selectors: [
        'input[name="name"]',
        'input[placeholder*="Enter the name of your product"]',
        '#\\:r4\\:-form-item'
      ],
      method: 'value',
      validation: 'required',
      notes: '产品名称，对应website-info.js的siteName字段'
    },

    // 分类选择 -> Categories
    category: {
      selectors: [
        'button[id*="r5"]',
        'button[aria-controls*="radix"]',
        'button:contains("Select categories")'
      ],
      method: 'multiselect',
      validation: 'required',
      notes: '产品分类选择'
    },

    // 标签选择 -> Tags
    tags: {
      selectors: [
        'button[id*="r7"]',
        'button:contains("Select tags")',
        'button[aria-haspopup="dialog"]:nth-of-type(2)'
      ],
      method: 'multiselect',
      validation: 'optional',
      notes: '产品标签选择'
    },

    // 简短描述 -> Description
    siteDescription: {
      selectors: [
        'textarea[name="description"]',
        'textarea[placeholder*="Enter a brief description"]',
        '#\\:r9\\:-form-item'
      ],
      method: 'value',
      validation: 'required',
      notes: '产品简短描述'
    },

    // 详细介绍 -> Introduction (Markdown编辑器)
    detailedIntro: {
      selectors: [
        '.CodeMirror textarea',
        '#simplemde-editor-2',
        '.CodeMirror-scroll'
      ],
      method: 'markdown',
      validation: 'optional',
      notes: '详细介绍，支持Markdown格式'
    }
  },
  
  // 提交流程配置
  submitConfig: {
    submitButton: 'button[type="submit"], button:contains("Submit")',
    submitMethod: 'click',
    successIndicators: [
      '.success-message',
      '.alert-success',
      '.notification-success'
    ],
    errorIndicators: [
      '.error-message',
      '.alert-error',
      '.text-destructive'
    ]
  },
  
  // 特殊处理
  specialHandling: {
    requiresLogin: false, // 不需要登录
    hasCaptcha: false,
    hasFileUpload: true, // 有图标和图片上传
    
    // 自定义处理脚本
    customScript: 'handleTrustedBySubmission',
    
    // 表单验证规则
    formValidation: {
      requiredFields: ['siteUrl', 'siteName', 'category', 'siteDescription'],
      emailValidation: false,
      urlValidation: true
    },
    
    // 特殊注意事项
    notes: [
      '不需要登录即可提交',
      '有AI自动填充功能',
      '支持Markdown格式的详细介绍',
      '需要上传图标(1:1比例)和图片(16:9比例)',
      '文件上传限制：PNG或JPEG，最大1MB',
      '使用自定义的多选组件',
      '有拖拽上传功能',
      '提交后可以修改信息'
    ]
  }
};

// 自定义处理函数
export function handleTrustedBySubmission(data, rule) {
  console.log('Processing TrustedBy.ai submission...');

  const processedData = { ...data };

  // 确保URL格式正确
  if (processedData.siteUrl && !processedData.siteUrl.startsWith('http')) {
    processedData.siteUrl = 'https://' + processedData.siteUrl;
  }

  // 处理Markdown内容
  if (processedData.detailedIntro) {
    // 确保Markdown格式正确
    processedData.detailedIntro = processedData.detailedIntro.trim();
  }

  // 如果没有详细介绍，使用简短描述
  if (!processedData.detailedIntro && processedData.siteDescription) {
    processedData.detailedIntro = processedData.siteDescription;
  }

  console.log('TrustedBy.ai 数据处理完成:', processedData);
  return processedData;
}

// 自定义元素填写函数
export async function customFillElement(element, value, config) {
  console.log('🔧 TrustedBy.ai自定义填写函数被调用:', element, value);

  // 处理多选按钮（Categories和Tags）
  if (element.tagName === 'BUTTON' && element.hasAttribute('aria-haspopup')) {
    try {
      console.log('🎯 处理多选组件');

      // 点击打开下拉菜单
      element.click();

      // 等待下拉菜单出现
      await new Promise(resolve => setTimeout(resolve, 800));

      // 查找并选择相关选项
      const dialogId = element.getAttribute('aria-controls');
      if (dialogId) {
        const dialog = document.querySelector(`#${dialogId}`);
        if (dialog) {
          // 查找AI相关的选项
          const aiOptions = dialog.querySelectorAll('[role="option"], [data-value], .option');

          for (const option of aiOptions) {
            const optionText = option.textContent?.toLowerCase() || '';
            if (optionText.includes('ai') || optionText.includes('artificial') ||
                optionText.includes('productivity') || optionText.includes('tool')) {
              option.click();
              console.log('✅ 选择了选项:', option.textContent);
              break;
            }
          }
        }
      }

      // 关闭对话框
      await new Promise(resolve => setTimeout(resolve, 500));
      const backdrop = document.querySelector('[data-radix-popper-content-wrapper]');
      if (backdrop) {
        document.body.click(); // 点击外部关闭
      }

      return true;

    } catch (error) {
      console.warn('多选组件处理失败:', error);
    }
  }

  // 处理Markdown编辑器
  if (element.classList.contains('CodeMirror') || element.id === 'simplemde-editor-2') {
    try {
      console.log('🎯 处理Markdown编辑器');

      // 查找CodeMirror实例
      const codeMirrorElement = document.querySelector('.CodeMirror');
      if (codeMirrorElement && codeMirrorElement.CodeMirror) {
        codeMirrorElement.CodeMirror.setValue(value);
        console.log('✅ 设置Markdown内容:', value);
        return true;
      }

      // 备用方法：直接设置textarea
      const textarea = document.querySelector('#simplemde-editor-2');
      if (textarea) {
        textarea.value = value;
        textarea.dispatchEvent(new Event('input', { bubbles: true }));
        console.log('✅ 设置textarea内容:', value);
        return true;
      }

    } catch (error) {
      console.warn('Markdown编辑器处理失败:', error);
    }
  }

  // 处理文件上传提示
  if (element.type === 'file') {
    console.log('⚠️ 检测到文件上传字段，需要用户手动上传文件');
    if (element.id === 'dropzone-file-icon') {
      console.log('📷 图标上传：推荐1:1比例，PNG或JPEG，最大1MB');
    } else if (element.id === 'dropzone-file-image') {
      console.log('🖼️ 图片上传：推荐16:9比例，PNG或JPEG，最大1MB');
    }
    return false; // 让用户手动处理文件上传
  }

  // 处理普通输入框和文本域
  if (element.tagName === 'INPUT' || element.tagName === 'TEXTAREA') {
    try {
      element.value = value;
      element.dispatchEvent(new Event('input', { bubbles: true }));
      element.dispatchEvent(new Event('change', { bubbles: true }));

      console.log('✅ 填写字段:', value);
      return true;
    } catch (error) {
      console.warn('字段填写失败:', error);
    }
  }

  // 默认处理
  return false;
};
