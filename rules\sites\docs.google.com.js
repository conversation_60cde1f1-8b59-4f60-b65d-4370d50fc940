// AI Site Submitter - DocsGoogle 规则配置
// 自动生成于: 2025/7/16 09:24:12
// 域名: docs.google.com

export const SITE_RULE = {
  "domain": "docs.google.com",
  "siteName": "DocsGoogle",
  "lastUpdated": "2025-07-16T01:24:12.101Z",
  "fieldMappings": {
    "siteName": {
      "selectors": [
        "input.whsOnd.zHQkBf:nth-of-type(1)",
        "form#mG61Hd input[type='text']:nth-of-type(1)",
        "div[data-params*='Tool Name'] input.whsOnd.zHQkBf"
      ],
      "type": "input",
      "fillMethod": "value",
      "required": false,
      "validation": "required"
    },
    "siteUrl": {
      "selectors": [
        "input.whsOnd.zHQkBf:nth-of-type(2)",
        "form#mG61Hd input[type='text']:nth-of-type(2)",
        "div[data-params*='URL'] input.whsOnd.zHQkBf"
      ],
      "type": "input",
      "fillMethod": "value",
      "required": false,
      "validation": "optional"
    },
    "siteDescription": {
      "selectors": [
        "input.whsOnd.zHQkBf:nth-of-type(3)",
        "form#mG61Hd input[type='text']:nth-of-type(3)",
        "div[data-params*='Description'] input.whsOnd.zHQkBf"
      ],
      "type": "input",
      "fillMethod": "value",
      "required": false,
      "validation": "required"
    },
    "detailedIntro": {
      "selectors": [
        "textarea.KHxj8b.tL9Q4c",
        "form#mG61Hd textarea",
        "div[data-params*='Detailed'] textarea.KHxj8b.tL9Q4c"
      ],
      "type": "input",
      "fillMethod": "value",
      "required": false,
      "validation": "optional"
    },
    "contactEmail": {
      "selectors": [
        "input.whsOnd.zHQkBf:nth-of-type(4)",
        "form#mG61Hd input[type='text']:nth-of-type(4)",
        "div[data-params*='Email'] input.whsOnd.zHQkBf"
      ],
      "type": "input",
      "fillMethod": "value",
      "required": false,
      "validation": "optional"
    },
    "fullName": {
      "selectors": [
        "input.whsOnd.zHQkBf:nth-of-type(5)",
        "form#mG61Hd input[type='text']:nth-of-type(5)",
        "div[data-params*='Name'] input.whsOnd.zHQkBf"
      ],
      "type": "input",
      "fillMethod": "value",
      "required": false,
      "validation": "optional"
    },
    "tags": {
      "selectors": [
        "input.whsOnd.zHQkBf:nth-of-type(6)",
        "form#mG61Hd input[type='text']:nth-of-type(6)",
        "div[data-params*='Tags'] input.whsOnd.zHQkBf"
      ],
      "type": "input",
      "fillMethod": "value",
      "required": false,
      "validation": "optional"
    }
  },
  "formInfo": {
    "description": "Google 表单提交",
    "submitSelector": "form#mG61Hd",
    "totalFields": 6,
    "notes": [
      "表单为Google Forms，字段无明确标签，按出现顺序和必填状态推断语义",
      "所有输入框class相同，使用nth-of-type区分",
      "textarea用于长文本输入"
    ]
  },
  "metadata": {
    "generatedBy": "AI",
    "generatedAt": "2025-07-16T01:24:12.101Z",
    "version": "3.0.0",
    "aiModel": "moonshotai/Kimi-K2-Instruct"
  }
};

// 自定义处理函数 (可选)
export function handleDocsGoogleComSubmission(data, rule) {
  console.log('Processing DocsGoogle form submission...');
  
  const processedData = { ...data };
  
  // 在这里添加特殊处理逻辑
  // 例如：URL格式化、字段验证、默认值设置等
  
  return processedData;
}

// 自定义元素填写函数 (可选)
export async function customFillElement(element, value, config) {
  console.log('🔧 DocsGoogle 自定义填写函数被调用:', element, value);
  
  // 在这里添加特殊的元素填写逻辑
  // 例如：处理特殊的UI组件、异步操作等
  
  return false; // 返回 false 使用默认填写方法
}