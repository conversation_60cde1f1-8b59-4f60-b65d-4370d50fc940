// SubmitX.com 网站规则配置
// 网站: https://submitx.com/?page=submit-url.html
// 最后更新: 2025-07-24

export const SITE_RULE = {
  domain: 'submitx.com',
  siteName: 'SubmitX Directory',
  priority: 1,
  lastUpdated: '2025-07-24',

  fieldMappings: {
    siteUrl: {
      selectors: [
        '#url',
        'input[name="url"]',
        'input[type="url"]'
      ],
      method: 'value',
      validation: 'required|url',
      notes: '网站URL'
    },

    contactEmail: {
      selectors: [
        '#email',
        'input[name="email"]',
        'input[type="email"]'
      ],
      method: 'value',
      validation: 'required|email',
      notes: '联系邮箱'
    },

    category: {
      selectors: [
        'select[name="category"]',
        'select.form-control:first-of-type'
      ],
      method: 'select',
      validation: 'required',
      defaultValue: '1001',
      notes: '分类选择，默认选择Business'
    },

    country: {
      selectors: [
        'select[name="country"]',
        'select.form-control:nth-of-type(2)'
      ],
      method: 'select',
      validation: 'required',
      defaultValue: 'US',
      notes: '国家选择，默认选择US'
    },

    state: {
      selectors: [
        '#province',
        'input[name="province"]',
        'input[placeholder="Province / State"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '省份/州'
    },

    city: {
      selectors: [
        '#city',
        'input[name="city"]',
        'input[placeholder="City"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '城市'
    },

    zipCode: {
      selectors: [
        '#postal',
        'input[name="postal"]',
        'input[placeholder="Postal / Zip Code"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '邮政编码'
    },

    fullName: {
      selectors: [
        '#name',
        'input[name="name"]',
        'input[placeholder="Name"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '提交者姓名'
    },

    siteName: {
      selectors: [
        '#title',
        'input[name="title"]',
        'input[placeholder="Site Title"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '网站标题'
    },

    tags: {
      selectors: [
        '#keywords',
        'input[name="keywords"]',
        'input[placeholder="keyword, keyword..."]'
      ],
      method: 'value',
      validation: 'required',
      notes: '关键词，逗号分隔'
    },

    siteDescription: {
      selectors: [
        '#description',
        'input[name="description"]',
        'input[placeholder="Description of your website"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '网站描述'
    }
  },

  submitConfig: {
    submitButton: 'button[name="submit"], .btn.btn-danger',
    submitMethod: 'click',
    successIndicators: ['.success-message'],
    errorIndicators: ['.error-message']
  },

  specialHandling: {
    requiresLogin: false,
    hasCaptcha: true,
    hasFileUpload: false,
    customScript: 'handleSubmitXSubmission',
    formValidation: {
      requiredFields: ['siteUrl', 'contactEmail', 'category', 'country', 'state', 'city', 'zipCode', 'fullName', 'siteName', 'tags', 'siteDescription'],
      emailValidation: true,
      urlValidation: true
    },
    notes: [
      '有reCAPTCHA验证',
      '需要完整地址信息',
      '所有字段都是必填',
      '关键词用逗号分隔',
      '有分类和国家选择',
      '使用Bootstrap样式'
    ]
  }
};

export function handleSubmitXSubmission(data, rule) {
  const processedData = { ...data };

  if (processedData.siteUrl && !processedData.siteUrl.startsWith('http')) {
    processedData.siteUrl = 'https://' + processedData.siteUrl;
  }

  return processedData;
}

export async function customFillElement(element, value, config) {
  if (element.tagName === 'SELECT') {
    const options = element.querySelectorAll('option');
    let option;

    if (element.name === 'category') {
      option = Array.from(options).find(opt => opt.value === '1001' || opt.textContent.includes('Business'));
    } else if (element.name === 'country') {
      option = Array.from(options).find(opt => opt.value === 'US' || opt.textContent.includes('United States'));
    } else {
      option = Array.from(options).find(opt => opt.value === value);
    }

    if (option) {
      element.value = option.value;
      element.dispatchEvent(new Event('change', { bubbles: true }));
      return true;
    }
  }

  if (element.tagName === 'INPUT') {
    // 特殊处理URL字段，过滤掉我们数据中的https://前缀，因为字段已有默认值
    if (element.name === 'url') {
      const cleanUrl = value.replace(/^https?:\/\//, '');
      element.value = 'https://' + cleanUrl;
    } else {
      element.value = value;
    }
    element.dispatchEvent(new Event('input', { bubbles: true }));
    return true;
  }

  return false;
}