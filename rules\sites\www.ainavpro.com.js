// AiNavPro.com 网站规则配置
// 网站: https://www.ainavpro.com/contribute
// 最后更新: 2025-07-24

export const SITE_RULE = {
  domain: 'www.ainavpro.com',
  siteName: 'AI Nav Pro',
  priority: 1,
  lastUpdated: '2025-07-24',

  fieldMappings: {
    siteName: {
      selectors: [
        'input[name="post_title"]',
        '.sites-title',
        'input[placeholder="名称"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '网站名称，最多30字符'
    },

    wechatId: {
      selectors: [
        '#tougao_wechat_id',
        'input[name="wechat_id"]',
        'input[placeholder="公众号ID(微信号)"]'
      ],
      method: 'value',
      validation: 'optional',
      notes: '微信公众号ID，仅公众号类型显示'
    },

    siteUrl: {
      selectors: [
        'input[name="link"]',
        '.sites-link',
        'input[placeholder="链接"]'
      ],
      method: 'value',
      validation: 'required|url',
      notes: '网站链接'
    },

    siteDescription: {
      selectors: [
        'input[name="sescribe"]',
        '.sites-des',
        'input[placeholder="简介"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '网站简介，最多80字符'
    },

    detailedIntro: {
      selectors: [
        'textarea[name="post_content"]',
        'textarea[placeholder="输入网址介绍"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '详细介绍'
    },

    logoFile: {
      selectors: [
        '#upload_ico',
        'input[name="tougao_ico"][type="file"]',
        '.upload-sites'
      ],
      method: 'file',
      validation: 'optional',
      notes: '网站图标上传'
    },

    category: {
      selectors: [
        'select[name="category"]',
        '#post_cat',
        '.form-select select'
      ],
      method: 'select',
      validation: 'required',
      defaultValue: '216',
      notes: '分类选择，默认其他'
    },

    keywords: {
      selectors: [
        'textarea[name="tags"]',
        '.sites-keywords',
        'textarea[placeholder="输入标签"]'
      ],
      method: 'value',
      validation: 'optional',
      notes: '标签，用逗号分隔'
    },

    fullName: {
      selectors: [
        'input[name="user_name"]',
        'input[placeholder="请输入昵称"]'
      ],
      method: 'value',
      validation: 'optional',
      notes: '提交者昵称'
    },

    contactEmail: {
      selectors: [
        'input[name="contact_details"]',
        'input[placeholder="输入联系方式"]'
      ],
      method: 'value',
      validation: 'optional',
      notes: '联系方式，可以是邮箱或电话'
    }
  },

  submitConfig: {
    submitButton: '#submit, .btn-submit',
    submitMethod: 'click',
    successIndicators: ['.success-message'],
    errorIndicators: ['.error-message']
  },

  specialHandling: {
    requiresLogin: false,
    hasCaptcha: false,
    hasFileUpload: true,
    customScript: 'handleAiNavProSubmission',
    formValidation: {
      requiredFields: ['siteName', 'siteUrl', 'siteDescription', 'detailedIntro', 'category'],
      emailValidation: false,
      urlValidation: true
    },
    notes: [
      'AI导航站投稿平台',
      '支持网址和公众号两种类型',
      '有图标上传功能',
      '一键填写功能',
      '字符数限制提示',
      '中文界面'
    ]
  }
};

export function handleAiNavProSubmission(data, rule) {
  const processedData = { ...data };

  if (processedData.siteUrl && !processedData.siteUrl.startsWith('http')) {
    processedData.siteUrl = 'https://' + processedData.siteUrl;
  }

  // 自动选择网址类型
  const sitesTab = document.querySelector('a[data-type="sites"]');
  if (sitesTab) {
    sitesTab.click();
  }

  // 自动选择默认分类
  const categorySelect = document.querySelector('#post_cat');
  if (categorySelect) {
    categorySelect.value = '216';
  }

  return processedData;
}

export async function customFillElement(element, value, config) {
  // 处理下拉选择框
  if (element.tagName === 'SELECT') {
    const options = element.querySelectorAll('option');
    const option = Array.from(options).find(opt => opt.value === '216');
    if (option) {
      element.value = option.value;
      element.dispatchEvent(new Event('change', { bubbles: true }));
      return true;
    }
  }

  // 处理文件上传字段
  if (element.type === 'file') {
    console.warn('文件上传需要手动操作');
    return false;
  }

  // 处理隐藏的微信公众号字段
  if (element.name === 'wechat_id') {
    const wechatDiv = element.closest('.tg-wechat-id');
    if (wechatDiv && wechatDiv.style.display === 'none') {
      // 如果是公众号字段但当前是网址模式，跳过
      return false;
    }
  }

  if (element.tagName === 'INPUT' || element.tagName === 'TEXTAREA') {
    element.value = value;
    element.dispatchEvent(new Event('input', { bubbles: true }));
    element.dispatchEvent(new Event('change', { bubbles: true }));
    return true;
  }

  return false;
}