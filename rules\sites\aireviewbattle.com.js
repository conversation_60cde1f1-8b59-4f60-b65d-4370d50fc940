// AIReviewBattle.com 网站规则配置
// 网站: https://aireviewbattle.com/submit-your-ai-tool/
// 最后更新: 2025-07-06

export const SITE_RULE = {
  // 基本信息
  domain: 'aireviewbattle.com',
  siteName: 'AI Review Battle',
  priority: 1,
  lastUpdated: '2025-07-06',
  
  // 字段映射规则
  fieldMappings: {
    // 工具名称 -> Tool Name (使用siteName)
    siteName: {
      selectors: [
        '#wpforms-1707-field_1',
        'input[name="wpforms[fields][1]"]',
        'input.wpforms-field-required[id*="field_1"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '工具名称，使用website-info.js中的siteName字段'
    },

    // 工具网站URL -> Tool Website URL (使用siteUrl)
    siteUrl: {
      selectors: [
        '#wpforms-1707-field_2',
        'input[name="wpforms[fields][2]"]',
        'input.wpforms-field-required[id*="field_2"]'
      ],
      method: 'value',
      validation: 'required|url',
      notes: '工具网站URL，使用website-info.js中的siteUrl字段'
    },

    // 简短描述 -> Short Description About Your Tool (使用siteDescription)
    siteDescription: {
      selectors: [
        '#wpforms-1707-field_3',
        'textarea[name="wpforms[fields][3]"]',
        'textarea.wpforms-field-required[id*="field_3"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '工具简短描述，使用website-info.js中的siteDescription字段'
    },

    // 分类 -> Category
    category: {
      selectors: [
        '#wpforms-1707-field_4',
        'select[name="wpforms[fields][4]"]',
        'select.wpforms-field-required[id*="field_4"]'
      ],
      method: 'select',
      validation: 'required',
      options: [
        'AI Writing Tool',
        'Art',
        'Image Generator',
        'Video Generator',
        'Code Generator',
        'Chatbot',
        'Others'
      ],
      defaultValue: 'AI Writing Tool',
      notes: '工具分类，默认选择AI Writing Tool'
    },

    // 定价模式 -> Pricing
    pricing: {
      selectors: [
        '#wpforms-1707-field_5',
        'select[name="wpforms[fields][5]"]',
        'select.wpforms-field-required[id*="field_5"]'
      ],
      method: 'select',
      validation: 'required',
      options: ['Free', 'Paid', 'Freemium', 'Subscription'],
      defaultValue: 'Free',
      notes: '定价模式，默认选择Free'
    },

    // 联系邮箱 -> Your Email
    contactEmail: {
      selectors: [
        '#wpforms-1707-field_6',
        'input[name="wpforms[fields][6]"]',
        'input[type="email"].wpforms-field-required'
      ],
      method: 'value',
      validation: 'required|email',
      notes: '联系邮箱地址'
    },

    // 同意条款复选框 -> Agreement
    agreement: {
      selectors: [
        '#wpforms-1707-field_7_1',
        'input[name="wpforms[fields][7][]"]',
        'input[type="checkbox"][value*="confirm"]'
      ],
      method: 'checkbox',
      validation: 'required',
      defaultValue: true,
      notes: '同意条款复选框，必须勾选'
    }
  },
  
  // 自定义填写函数
  customFillElement: async function(element, value, config) {
    console.log(`AIReviewBattle自定义填写: ${element.name || element.id}, 方法: ${config.method}`);

    switch (config.method) {
      case 'value':
        // WPForms表单处理
        element.focus();
        await new Promise(resolve => setTimeout(resolve, 100));

        element.value = value;
        element.dispatchEvent(new Event('input', { bubbles: true }));
        element.dispatchEvent(new Event('change', { bubbles: true }));
        element.dispatchEvent(new Event('blur', { bubbles: true }));

        // WPForms可能需要额外的事件
        if (element.classList.contains('wpforms-field-required')) {
          element.dispatchEvent(new Event('keyup', { bubbles: true }));
        }
        break;

      case 'select':
        // WPForms下拉选择处理
        if (element.tagName.toLowerCase() === 'select') {
          const targetValue = config.defaultValue || value;

          // 尝试精确匹配
          let option = Array.from(element.options).find(opt =>
            opt.value === targetValue || opt.text === targetValue
          );

          // 如果没找到，尝试部分匹配
          if (!option) {
            option = Array.from(element.options).find(opt =>
              opt.text.includes(targetValue) || targetValue.includes(opt.text)
            );
          }

          if (option) {
            element.value = option.value;
            element.dispatchEvent(new Event('change', { bubbles: true }));
            element.dispatchEvent(new Event('blur', { bubbles: true }));
            console.log(`✓ 选择选项: ${option.text || option.value}`);
          }
        }
        break;

      case 'checkbox':
        // WPForms复选框处理
        if (element.type === 'checkbox') {
          element.checked = true;
          element.dispatchEvent(new Event('change', { bubbles: true }));
          element.dispatchEvent(new Event('click', { bubbles: true }));
          console.log(`✓ 勾选复选框: ${element.value || element.id}`);
        }
        break;

      default:
        element.value = value;
        element.dispatchEvent(new Event('input', { bubbles: true }));
        element.dispatchEvent(new Event('change', { bubbles: true }));
    }
  },
  
  // 提交流程配置
  submitConfig: {
    submitButton: '#wpforms-submit-1707, button[name="wpforms[submit]"], .wpforms-submit',
    submitMethod: 'click',
    waitAfterFill: 2000,
    waitAfterSubmit: 5000,
    successIndicators: [
      '.wpforms-confirmation-container',
      '.wpforms-success',
      '[class*="success"]'
    ],
    errorIndicators: [
      '.wpforms-error',
      '.wpforms-field-error',
      '[class*="error"]'
    ]
  },

  // 特殊处理
  specialHandling: {
    requiresLogin: false,
    hasCaptcha: false,
    hasFileUpload: false,
    hasAgreementCheckbox: true, // 有同意条款复选框

    // 表单验证规则
    formValidation: {
      requiredFields: ['siteName', 'siteUrl', 'siteDescription', 'category', 'pricing', 'contactEmail', 'agreement'],
      emailValidation: true,
      urlValidation: true
    },

    // 特殊注意事项
    notes: [
      '这是WPForms表单，使用精确的字段ID',
      '有7个字段：6个必填 + 1个同意复选框',
      '使用website-info.js中的siteName、siteUrl、siteDescription字段',
      '分类默认选择AI Writing Tool',
      '定价默认选择Free',
      '必须勾选同意条款复选框',
      '审核时间2-5个工作日'
    ]
  }
};

// 自定义处理函数
export function handleAIReviewBattleSubmission(data, rule) {
  console.log('Processing AI Review Battle submission...');
  
  const processedData = { ...data };
  
  // 确保URL格式正确
  if (processedData.siteUrl && !processedData.siteUrl.startsWith('http')) {
    processedData.siteUrl = 'https://' + processedData.siteUrl;
  }
  
  // 设置默认分类
  if (!processedData.category) {
    processedData.category = 'AI Writing Tool';
  }
  
  // 设置默认定价
  if (!processedData.pricing) {
    processedData.pricing = 'Free';
  }
  
  return processedData;
}
