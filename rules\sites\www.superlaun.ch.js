// SuperLaunch 网站规则配置
// 网站: https://www.superlaun.ch/dashboard/submit
// 最后更新: 2025-08-01

export const SITE_RULE = {
  // 基本信息
  domain: 'www.superlaun.ch',
  siteName: 'SuperLaunch',
  priority: 1,
  lastUpdated: '2025-08-01',
  
  // 字段映射规则
  fieldMappings: {
    // 产品名称 -> Product Name
    siteName: {
      selectors: [
        'input[name="name"]',
        'input#name',
        'input[placeholder*="Enter product name"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '产品名称，必填字段'
    },

    // 标语 -> Tagline
    siteDescription: {
      selectors: [
        'input[name="tagline"]',
        'input#tagline',
        'input[placeholder*="Enter a short tagline"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '产品标语，必填字段'
    },

    // 网站URL -> Website
    siteUrl: {
      selectors: [
        'input[name="website"]',
        'input#website',
        'input[placeholder*="https://example.com"]'
      ],
      method: 'value',
      validation: 'required|url',
      notes: '网站URL地址，必填字段'
    },

    // 详细描述 -> Description (Quill富文本编辑器)
    detailedIntro: {
      selectors: [
        '.ql-editor[contenteditable="true"]',
        'input[name="description"][type="hidden"]',
        '.quill .ql-editor'
      ],
      method: 'quill',
      validation: 'optional',
      notes: '详细描述，使用Quill富文本编辑器'
    }
  },
  
  // 提交流程配置
  submitConfig: {
    submitButton: 'button[type="submit"], .submit-button, button:contains("Submit")',
    submitMethod: 'click',
    waitAfterFill: 2000,
    waitAfterSubmit: 5000,
    successIndicators: [
      '.success-message',
      '.thank-you',
      '.submission-success'
    ],
    errorIndicators: [
      '.error-message',
      '.validation-error',
      '.form-error'
    ]
  },
  
  // 特殊处理
  specialHandling: {
    requiresLogin: true, // 需要登录
    hasCaptcha: false,
    hasFileUpload: true, // 有Logo上传
    hasQuillEditor: true, // 使用Quill编辑器
    hasRadioButtons: true, // 有Launch Type单选按钮
    
    // 表单验证规则
    formValidation: {
      requiredFields: ['siteName', 'siteDescription', 'siteUrl'],
      optionalFields: ['detailedIntro'],
      emailValidation: false,
      urlValidation: true,
      fileUploadValidation: true
    },
    
    // 特殊注意事项
    notes: [
      '需要登录才能访问表单',
      '包含Logo文件上传功能',
      '使用Quill富文本编辑器',
      'Launch Type默认选择Premium ($1)',
      '现代化的UI设计'
    ]
  }
};

// 自定义处理函数
export function handleSuperLaunchSubmission(data, rule) {
  console.log('Processing SuperLaunch submission...');
  
  const processedData = { ...data };
  
  // 确保URL格式正确
  if (processedData.siteUrl && !processedData.siteUrl.startsWith('http')) {
    processedData.siteUrl = 'https://' + processedData.siteUrl;
  }
  
  return processedData;
}

// 自定义元素填写函数
export async function customFillElement(element, value, config) {
  console.log(`SuperLaunch自定义填写: ${element.tagName}, 方法: ${config.method}`);

  switch (config.method) {
    case 'quill':
      // Quill富文本编辑器处理
      const quillEditor = element.closest('.ql-editor') || element;
      if (quillEditor && quillEditor.contentEditable === 'true') {
        quillEditor.focus();
        await new Promise(resolve => setTimeout(resolve, 300));

        // 清空现有内容
        quillEditor.innerHTML = '<p><br></p>';
        
        // 设置新内容
        quillEditor.innerHTML = `<p>${value}</p>`;
        
        // 更新隐藏字段
        const hiddenInput = document.querySelector('input[name="description"][type="hidden"]');
        if (hiddenInput) {
          hiddenInput.value = value;
        }
        
        // 触发输入事件
        quillEditor.dispatchEvent(new Event('input', { bubbles: true }));
        quillEditor.dispatchEvent(new Event('change', { bubbles: true }));
        
        console.log(`✓ 填写Quill字段: "${value}"`);
        return true;
      }
      return false;

    case 'value':
      // 标准输入框处理
      element.focus();
      await new Promise(resolve => setTimeout(resolve, 200));

      element.value = value;
      element.dispatchEvent(new Event('input', { bubbles: true }));
      element.dispatchEvent(new Event('change', { bubbles: true }));
      element.dispatchEvent(new Event('blur', { bubbles: true }));

      console.log(`✓ 填写字段: ${element.name || element.id} = "${value}"`);
      return true;

    default:
      element.value = value;
      element.dispatchEvent(new Event('input', { bubbles: true }));
      return true;
  }

  return false;
}

// 特殊处理：自动选择Launch Type
export async function handleSpecialFields() {
  console.log('处理特殊字段...');
  
  // 默认选择Premium ($1)单选按钮
  const premiumRadio = document.querySelector('input[type="radio"][value="paid"]');
  if (premiumRadio && !premiumRadio.checked) {
    const radioButton = document.querySelector('button[role="radio"][value="paid"]');
    if (radioButton) {
      radioButton.click();
      console.log('✓ 已选择Premium ($1)');
    }
  }
  
  return true;
}
