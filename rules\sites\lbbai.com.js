// Lbbai.com 网站规则配置
// 网站: https://lbbai.com/contribute
// 最后更新: 2025-07-24

export const SITE_RULE = {
  // 基本信息
  domain: 'lbbai.com',
  siteName: 'Lbbai',
  priority: 1,
  lastUpdated: '2025-07-24',

  // 字段映射规则
  fieldMappings: {
    // 网站名称 -> 名称
    siteName: {
      selectors: [
        'input[name="post_title"]',
        '.sites-title',
        'input[placeholder="名称"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '网站名称，最多30字符'
    },

    // 网站URL -> 链接
    siteUrl: {
      selectors: [
        'input[name="link"]',
        '.sites-link',
        'input[placeholder="链接"]'
      ],
      method: 'value',
      validation: 'required|url',
      notes: '网站链接'
    },

    // 网站描述 -> 简介
    siteDescription: {
      selectors: [
        'textarea[name="describe"]',
        '.sites-desc',
        'textarea[placeholder="简介"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '网站简介，最多150字符'
    },

    // 详细介绍 -> WordPress富文本编辑器
    detailedIntro: {
      selectors: [
        'textarea[name="post_content"]',
        '#post_content',
        'iframe#post_content_ifr'
      ],
      method: 'richtext',
      validation: 'required',
      notes: '详细介绍，WordPress富文本编辑器'
    },

    // 分类选择 -> 多选下拉框
    category: {
      selectors: [
        'select[name="category"]',
        '#post_cat',
        '.io-multiple-dropdown'
      ],
      method: 'multiselect',
      validation: 'required',
      notes: '分类选择，最多可选2个'
    },

    // 关键词标签 -> 标签
    keywords: {
      selectors: [
        'textarea[name="tags"]',
        '.sites-keywords',
        'textarea[placeholder="输入标签"]'
      ],
      method: 'value',
      validation: 'optional',
      notes: '标签，用逗号分隔'
    },

    // 提交者姓名 -> 昵称
    fullName: {
      selectors: [
        'input[name="guest_info[name]"]',
        'input[placeholder="请输入昵称"]'
      ],
      method: 'value',
      validation: 'optional',
      notes: '提交者昵称'
    },

    // 联系方式 -> 联系方式
    contactEmail: {
      selectors: [
        'input[name="guest_info[contact]"]',
        'input[placeholder="输入联系方式"]'
      ],
      method: 'value',
      validation: 'optional',
      notes: '联系方式，可以是邮箱或电话'
    }
  },

  // 提交流程配置
  submitConfig: {
    submitButton: '#submit, .btn-submit',
    submitMethod: 'click',
    waitAfterFill: 2000,
    waitAfterSubmit: 5000,
    successIndicators: ['.success-message'],
    errorIndicators: ['.error-message']
  },

  // 特殊处理
  specialHandling: {
    requiresLogin: false,
    hasCaptcha: false,
    hasFileUpload: false,
    customScript: 'handleLbbaiSubmission',
    formValidation: {
      requiredFields: ['siteName', 'siteUrl', 'siteDescription', 'detailedIntro', 'category'],
      emailValidation: false,
      urlValidation: true
    },
    notes: [
      'AI工具收录投稿平台',
      '支持多种AI工具分类',
      '有详细的分类选项',
      '中文界面',
      '字符数限制提示'
    ]
  }
};

// 自定义处理函数
export function handleLbbaiSubmission(data, rule) {
  console.log('Processing Lbbai form submission...');

  const processedData = { ...data };

  // 确保URL格式正确
  if (processedData.siteUrl && !processedData.siteUrl.startsWith('http')) {
    processedData.siteUrl = 'https://' + processedData.siteUrl;
  }

  // 处理关键词格式
  if (processedData.keywords) {
    // 如果是数组，转换为逗号分隔的字符串
    if (Array.isArray(processedData.keywords)) {
      processedData.keywords = processedData.keywords.join(', ');
    }
    processedData.keywords = processedData.keywords.trim();
  }

  // 处理描述长度限制（150字符）
  if (processedData.siteDescription && processedData.siteDescription.length > 150) {
    processedData.siteDescription = processedData.siteDescription.substring(0, 147) + '...';
  }

  // 处理网站名称长度限制
  if (processedData.siteName && processedData.siteName.length > 30) {
    processedData.siteName = processedData.siteName.substring(0, 30);
  }

  return processedData;
}

// 自定义元素填写函数
export async function customFillElement(element, value, config) {
  // 处理多选下拉框
  if (element.classList.contains('io-multiple-dropdown')) {
    const dropdownItems = element.querySelectorAll('.dropdown-item');
    let selectedItems = [];

    // 智能匹配分类
    for (const item of dropdownItems) {
      const itemText = item.textContent.trim();
      if (itemText.includes('AI智能助手') || itemText.includes('AI写作工具')) {
        selectedItems.push(item);
        if (selectedItems.length >= 2) break; // 最多选2个
      }
    }

    // 如果没找到合适的，选择前两个
    if (selectedItems.length === 0) {
      selectedItems = Array.from(dropdownItems).slice(0, 2);
    }

    // 点击选中的项目
    selectedItems.forEach(item => {
      item.click();
    });

    return true;
  }

  // 处理WordPress富文本编辑器
  if (element.tagName === 'IFRAME' && element.id === 'post_content_ifr') {
    try {
      const iframeDoc = element.contentDocument || element.contentWindow.document;
      const body = iframeDoc.body;
      if (body) {
        body.innerHTML = value;
        // 触发WordPress编辑器更新
        const textarea = document.getElementById('post_content');
        if (textarea) {
          textarea.value = value;
          textarea.dispatchEvent(new Event('change', { bubbles: true }));
        }
        return true;
      }
    } catch (e) {
      console.warn('无法访问iframe内容，尝试直接设置textarea');
    }
  }

  // 处理隐藏的textarea（富文本编辑器后端）
  if (element.tagName === 'TEXTAREA' && element.name === 'post_content') {
    element.value = value;
    element.dispatchEvent(new Event('input', { bubbles: true }));
    element.dispatchEvent(new Event('change', { bubbles: true }));

    // 尝试更新TinyMCE编辑器
    if (window.tinymce && window.tinymce.get('post_content')) {
      window.tinymce.get('post_content').setContent(value);
    }
    return true;
  }

  // 处理标准输入框和文本域
  if (element.tagName === 'INPUT' || element.tagName === 'TEXTAREA') {
    element.value = value;
    element.dispatchEvent(new Event('input', { bubbles: true }));
    element.dispatchEvent(new Event('change', { bubbles: true }));
    return true;
  }

  return false;
}