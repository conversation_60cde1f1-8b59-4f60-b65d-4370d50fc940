// GTAWebDirectory.com 网站规则配置
// 网站: https://www.gtawebdirectory.com/submit.php?id=392
// 最后更新: 2025-07-24

export const SITE_RULE = {
  // 基本信息
  domain: 'www.gtawebdirectory.com',
  siteName: 'GTA Web Directory',
  priority: 1,
  lastUpdated: '2025-07-24',

  // 字段映射规则
  fieldMappings: {
    // 链接类型 -> Link Type Selection
    linkType: {
      selectors: [
        'input[name="LINK_TYPE"]',
        'input[value="featured"]',
        'input[value="normal"]',
        'input[value="reciprocal"]'
      ],
      method: 'radio',
      validation: 'required',
      notes: '链接类型选择：特色、普通、互惠'
    },

    // 网站标题 -> Title
    siteName: {
      selectors: [
        'input[name="TITLE"]',
        'input[type="text"][name="TITLE"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '网站标题，最多100字符'
    },

    // 网站URL -> URL
    siteUrl: {
      selectors: [
        'input[name="URL"]',
        'input[type="text"][name="URL"]'
      ],
      method: 'value',
      validation: 'optional|url',
      notes: '网站URL地址，最多255字符'
    },

    // 网站描述 -> Description
    siteDescription: {
      selectors: [
        'textarea[name="DESCRIPTION"]',
        'textarea[rows="3"]'
      ],
      method: 'value',
      validation: 'optional',
      notes: '网站描述，最多500字符'
    },

    // META关键词 -> META Keywords
    keywords: {
      selectors: [
        'input[name="META_KEYWORDS"]',
        'input[maxlength="2000"]'
      ],
      method: 'value',
      validation: 'optional',
      notes: 'META关键词，用逗号分隔，最多2000字符'
    },

    // META描述 -> META Description
    detailedIntro: {
      selectors: [
        'textarea[name="META_DESCRIPTION"]',
        'textarea[cols="30"]'
      ],
      method: 'value',
      validation: 'optional',
      notes: 'META描述，最多250字符'
    },

    // 所有者姓名 -> Your Name
    fullName: {
      selectors: [
        'input[name="OWNER_NAME"]',
        'input[maxlength="50"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '网站所有者姓名，最多50字符'
    },

    // 所有者邮箱 -> Your Email
    contactEmail: {
      selectors: [
        'input[name="OWNER_EMAIL"]',
        'input[type="text"][name="OWNER_EMAIL"]'
      ],
      method: 'value',
      validation: 'required|email',
      notes: '网站所有者邮箱，最多255字符'
    },

    // 分类选择 -> Category
    category: {
      selectors: [
        'select[name="CATEGORY_ID"]'
      ],
      method: 'select',
      validation: 'required',
      notes: '网站分类选择'
    },

    // 互惠链接URL -> Reciprocal Link URL
    reciprocalUrl: {
      selectors: [
        'input[name="RECPR_URL"]',
        'input[type="text"][name="RECPR_URL"]'
      ],
      method: 'value',
      validation: 'optional|url',
      notes: '互惠链接URL，最多255字符'
    },

    // 互惠链接代码 -> Reciprocal Link Code
    reciprocalCode: {
      selectors: [
        'textarea[name="RECPR_TEXT"]',
        'textarea[readonly]'
      ],
      method: 'value',
      validation: 'optional',
      notes: '互惠链接HTML代码'
    },

    // 验证码 -> Enter the code shown
    captcha: {
      selectors: [
        '#CAPTCHA',
        'input[name="CAPTCHA"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '验证码，4位字符'
    },

    // 同意规则 -> Submission Rules Agreement
    agreeRules: {
      selectors: [
        '#AGREERULES',
        'input[name="AGREERULES"]'
      ],
      method: 'checkbox',
      validation: 'required',
      notes: '同意提交规则'
    }
  },
  // 提交流程配置
  submitConfig: {
    submitButton: 'input[name="submit"], input[value="Continue"]',
    submitMethod: 'manual', // 手动提交
    waitAfterFill: 0,
    waitAfterSubmit: 0,
    successIndicators: ['.success-message'],
    errorIndicators: ['.error-message']
  },

  // 特殊处理
  specialHandling: {
    requiresLogin: false,
    hasCaptcha: true,
    hasFileUpload: false,
    customScript: 'handleGtaWebDirectorySubmission',
    formValidation: {
      requiredFields: ['linkType', 'siteName', 'fullName', 'contactEmail', 'category', 'captcha', 'agreeRules'],
      emailValidation: true,
      urlValidation: true
    },
    notes: [
      'GTA Web Directory 多伦多和大多伦多地区网站目录',
      '传统的网站目录服务',
      '提供三种提交选项：',
      '- 特色链接：$6.79/年，24小时审核',
      '- 普通链接：免费，4-6个月审核',
      '- 互惠链接：免费，4个月审核，需要反向链接',
      '包含验证码验证',
      '必须同意提交规则',
      '支持SEO相关字段',
      '反向链接格式：<a href="http://www.gtawebdirectory.com">Directory of Toronto and GTA</a>',
      '传统表格样式界面',
      '手动提交模式'
    ]
  }
};

// 自定义处理函数
export function handleGtaWebDirectorySubmission(data) {
  console.log('Processing GTA Web Directory form submission...');

  const processedData = { ...data };

  // 确保URL格式正确
  if (processedData.siteUrl && !processedData.siteUrl.startsWith('http')) {
    processedData.siteUrl = 'https://' + processedData.siteUrl;
  }

  // 处理互惠链接URL
  if (processedData.reciprocalUrl && !processedData.reciprocalUrl.startsWith('http')) {
    processedData.reciprocalUrl = 'https://' + processedData.reciprocalUrl;
  }

  return processedData;
}

// 自定义元素填写函数
export async function customFillElement(element, value, config) {
  console.log(`GTA Web Directory自定义填写: ${element.tagName}, 方法: ${config.method}`);

  switch (config.method) {
    case 'value':
      // 标准输入框和文本域处理
      element.focus();
      await new Promise(resolve => setTimeout(resolve, 200));

      element.value = value;
      element.dispatchEvent(new Event('input', { bubbles: true }));
      element.dispatchEvent(new Event('change', { bubbles: true }));
      element.dispatchEvent(new Event('blur', { bubbles: true }));

      // 触发字符计数器（如果是描述字段）
      if (element.name === 'DESCRIPTION' || element.name === 'META_DESCRIPTION') {
        const event = new Event('keyup', { bubbles: true });
        element.dispatchEvent(event);
      }

      console.log(`✓ 填写字段: ${element.name} = "${value}"`);
      return true;

    case 'select':
      // 下拉选择框处理
      if (element.tagName === 'SELECT') {
        const options = element.querySelectorAll('option');
        let selectedOption;

        // 智能匹配分类
        for (const option of options) {
          if (option.value && option.value !== '0') {
            const optionText = option.textContent.trim();
            if (optionText.includes('Arts') || optionText.includes('Business') || optionText.includes('Technology')) {
              selectedOption = option;
              break;
            }
          }
        }

        // 如果没找到合适的，选择第一个非禁用选项
        if (!selectedOption) {
          selectedOption = Array.from(options).find(opt => opt.value !== '0' && !opt.disabled);
        }

        if (selectedOption) {
          element.value = selectedOption.value;
          element.dispatchEvent(new Event('change', { bubbles: true }));
          console.log(`✓ 选择分类: ${selectedOption.textContent}`);
          return true;
        }
      }
      break;

    case 'radio':
      // 单选按钮处理 - 默认选择免费普通链接
      const normalRadio = document.querySelector('input[name="LINK_TYPE"][value="normal"]');
      if (normalRadio) {
        normalRadio.checked = true;
        normalRadio.dispatchEvent(new Event('change', { bubbles: true }));
        console.log(`✓ 选择链接类型: 免费普通链接`);
        return true;
      }
      break;

    case 'checkbox':
      // 复选框处理
      if (element.type === 'checkbox') {
        element.checked = true;
        element.dispatchEvent(new Event('change', { bubbles: true }));
        console.log(`✓ 复选框设置: ${element.name} = ${element.checked}`);
        return true;
      }
      break;

    default:
      element.value = value;
      element.dispatchEvent(new Event('input', { bubbles: true }));
      return true;
  }

  return false;
}