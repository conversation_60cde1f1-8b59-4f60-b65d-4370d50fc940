// AIToolsGuide.com 网站规则配置
// 网站: https://aitoolsguide.com/contact/
// 最后更新: 2025-07-24

export const SITE_RULE = {
  domain: 'aitoolsguide.com',
  siteName: 'AI Tools Guide',
  priority: 1,
  lastUpdated: '2025-07-24',

  fieldMappings: {
    firstName: {
      selectors: [
        '#wpforms-41-field_0',
        'input[name="wpforms[fields][0][first]"]',
        '.wpforms-field-name-first'
      ],
      method: 'value',
      validation: 'required',
      notes: '提交者名字'
    },

    lastName: {
      selectors: [
        '#wpforms-41-field_0-last',
        'input[name="wpforms[fields][0][last]"]',
        '.wpforms-field-name-last'
      ],
      method: 'value',
      validation: 'required',
      notes: '提交者姓氏'
    },

    contactEmail: {
      selectors: [
        '#wpforms-41-field_1',
        'input[name="wpforms[fields][1]"]',
        'input[type="email"]'
      ],
      method: 'value',
      validation: 'required|email',
      notes: '联系邮箱'
    },

    siteName: {
      selectors: [
        '#wpforms-41-field_4',
        'input[name="wpforms[fields][4]"]',
        '.wpforms-field-text:nth-of-type(1) input'
      ],
      method: 'value',
      validation: 'required',
      notes: '工具名称'
    },

    siteUrl: {
      selectors: [
        '#wpforms-41-field_5',
        'input[name="wpforms[fields][5]"]',
        '.wpforms-field-text:nth-of-type(2) input'
      ],
      method: 'value',
      validation: 'required|url',
      notes: '工具网站链接'
    },

    tags: {
      selectors: [
        '#wpforms-41-field_6',
        'input[name="wpforms[fields][6]"]',
        '.wpforms-field-text:nth-of-type(3) input'
      ],
      method: 'value',
      validation: 'required',
      notes: '标签/关键词'
    },

    siteDescription: {
      selectors: [
        '#wpforms-41-field_2',
        'textarea[name="wpforms[fields][2]"]',
        '.wpforms-field-textarea textarea'
      ],
      method: 'value',
      validation: 'required',
      notes: '工具描述'
    }
  },

  submitConfig: {
    submitButton: '#wpforms-submit-41, button[name="wpforms[submit]"]',
    submitMethod: 'click',
    successIndicators: ['.wpforms-confirmation-container'],
    errorIndicators: ['.wpforms-error']
  },

  specialHandling: {
    requiresLogin: false,
    hasCaptcha: false,
    hasFileUpload: false,
    customScript: 'handleAIToolsGuideSubmission',
    formValidation: {
      requiredFields: ['firstName', 'lastName', 'contactEmail', 'siteName', 'siteUrl', 'tags', 'siteDescription'],
      emailValidation: true,
      urlValidation: true
    },
    notes: [
      '使用WPForms表单',
      '所有字段都是必填的',
      '姓名分为First和Last两个字段',
      '包含标签/关键词字段'
    ]
  }
};

export function handleAIToolsGuideSubmission(data, rule) {
  const processedData = { ...data };

  if (processedData.siteUrl && !processedData.siteUrl.startsWith('http')) {
    processedData.siteUrl = 'https://' + processedData.siteUrl;
  }

  if (processedData.tags && typeof processedData.tags === 'string') {
    processedData.tags = processedData.tags.split(',').map(tag => tag.trim()).join(', ');
  }

  return processedData;
}

export async function customFillElement(element, value, config) {
  if (element.tagName === 'INPUT' || element.tagName === 'TEXTAREA') {
    element.value = value;
    element.dispatchEvent(new Event('input', { bubbles: true }));
    element.dispatchEvent(new Event('change', { bubbles: true }));
    return true;
  }

  return false;
}