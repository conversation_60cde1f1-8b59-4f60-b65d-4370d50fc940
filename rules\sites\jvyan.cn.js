// AI Site Submitter - <PERSON><PERSON><PERSON> 规则配置
// 自动生成于: 2025/7/19 16:56:26
// 域名: jvyan.cn

export const SITE_RULE = {
  "domain": "jvyan.cn",
  "siteName": "<PERSON>vyan",
  "lastUpdated": "2025-07-19T08:56:26.121Z",
  "fieldMappings": {
    "siteName": {
      "selectors": [
        "#name",
        "input[placeholder*='AI工具']",
        "input[name='text']"
      ],
      "type": "input",
      "fillMethod": "value",
      "required": false,
      "validation": "optional"
    },
    "siteUrl": {
      "selectors": [
        "#url",
        "input[placeholder*='URL地址']",
        "input[name='url']"
      ],
      "type": "input",
      "fillMethod": "value",
      "required": false,
      "validation": "optional"
    },
    "siteDescription": {
      "selectors": [
        "#description",
        "textarea[placeholder*='简短描述']",
        "textarea[name='description']"
      ],
      "type": "input",
      "fillMethod": "value",
      "required": false,
      "validation": "optional"
    },
    "detailedIntro": {
      "selectors": [
        "#details",
        "textarea[placeholder*='详细描述']",
        "textarea[name='details']"
      ],
      "type": "input",
      "fillMethod": "value",
      "required": false,
      "validation": "optional"
    },
    "tags": {
      "selectors": [
        "#features",
        "textarea[placeholder*='主要功能']",
        "textarea[name='features']"
      ],
      "type": "input",
      "fillMethod": "value",
      "required": false,
      "validation": "optional"
    },
    "fullName": {
      "selectors": [
        "#submitterName",
        "input[placeholder*='姓名']",
        "input[name='submitterName']"
      ],
      "type": "input",
      "fillMethod": "value",
      "required": false,
      "validation": "optional"
    },
    "contactEmail": {
      "selectors": [
        "#contact",
        "input[placeholder*='邮箱地址']",
        "input[name='contact']"
      ],
      "type": "input",
      "fillMethod": "value",
      "required": false,
      "validation": "optional"
    }
  },
  "formInfo": {
    "description": "AI工具提交表单，用于收集工具信息、功能介绍及提交者联系方式",
    "submitSelector": "#commentForm button[type='submit']",
    "totalFields": 11,
    "notes": [
      "成功映射7个核心字段，跳过icon/images等图片上传字段",
      "所有字段均为可选，无必填验证",
      "表单专注于AI工具信息收集，字段语义清晰"
    ]
  },
  "metadata": {
    "generatedBy": "AI",
    "generatedAt": "2025-07-19T08:56:26.121Z",
    "version": "3.0.0",
    "aiModel": "moonshotai/Kimi-K2-Instruct"
  }
};

// 自定义处理函数 (可选)
export function handleJvyanCnSubmission(data, rule) {
  console.log('Processing Jvyan form submission...');
  
  const processedData = { ...data };
  
  // 在这里添加特殊处理逻辑
  // 例如：URL格式化、字段验证、默认值设置等
  
  return processedData;
}

// 自定义元素填写函数 (可选)
export async function customFillElement(element, value, config) {
  console.log('🔧 Jvyan 自定义填写函数被调用:', element, value);
  
  // 在这里添加特殊的元素填写逻辑
  // 例如：处理特殊的UI组件、异步操作等
  
  return false; // 返回 false 使用默认填写方法
}