// AIToolly.com 网站规则配置
// 网站: https://aitoolly.com/zh/submit
// 最后更新: 2025-07-24

export const SITE_RULE = {
  // 基本信息
  domain: 'aitoolly.com',
  siteName: 'AIToolly',
  priority: 1,
  lastUpdated: '2025-07-24',
  
  // 字段映射规则
  fieldMappings: {
    // 工具名称 -> 工具名称
    siteName: {
      selectors: [
        'input[name="website"]',
        'input[placeholder="AIToolly"]',
        'label:contains("工具名称") + input'
      ],
      method: 'value',
      validation: 'required',
      notes: '工具名称'
    },

    // 网址 -> 网址
    siteUrl: {
      selectors: [
        'input[name="url"]',
        'input[placeholder="https://aitoolly.com"]',
        'label:contains("网址") + input'
      ],
      method: 'value',
      validation: 'required|url',
      notes: '网站URL地址'
    },

    // 类别 -> 类别
    category: {
      selectors: [
        'select[id*="form-item"]',
        'select.h-10.w-full.rounded-md',
        'label:contains("类别") + select'
      ],
      method: 'select',
      validation: 'required',
      notes: '工具分类选择'
    }
  },

  // 提交流程配置
  submitConfig: {
    submitButton: 'button[type="submit"], .bg-blue-500',
    submitMethod: 'manual', // 手动提交
    waitAfterFill: 0,
    waitAfterSubmit: 0,
    successIndicators: ['.success-message'],
    errorIndicators: ['.error-message']
  },

  // 特殊处理
  specialHandling: {
    requiresLogin: false,
    hasCaptcha: false,
    hasFileUpload: false,
    customScript: 'handleAiToollySubmission',
    formValidation: {
      requiredFields: ['siteName', 'siteUrl', 'category'],
      emailValidation: false,
      urlValidation: true
    },
    notes: [
      'AIToolly AI工具目录提交平台',
      '中文界面',
      '需要在网站首页添加反向链接',
      '反向链接格式：<a href="https://AIToolly.com/" title="Best AI Tools Directory">AIToolly</a>',
      '支持多种AI工具分类',
      '简洁的三字段表单',
      '手动提交模式'
    ]
  }
};

// 自定义处理函数
export function handleAiToollySubmission(data) {
  console.log('Processing AIToolly form submission...');
  
  const processedData = { ...data };

  // 确保URL格式正确
  if (processedData.siteUrl && !processedData.siteUrl.startsWith('http')) {
    processedData.siteUrl = 'https://' + processedData.siteUrl;
  }

  return processedData;
}

// 自定义元素填写函数
export async function customFillElement(element, value, config) {
  console.log(`AIToolly自定义填写: ${element.tagName}, 方法: ${config.method}`);

  switch (config.method) {
    case 'value':
      // 标准输入框处理
      element.focus();
      await new Promise(resolve => setTimeout(resolve, 200));

      element.value = value;
      element.dispatchEvent(new Event('input', { bubbles: true }));
      element.dispatchEvent(new Event('change', { bubbles: true }));
      element.dispatchEvent(new Event('blur', { bubbles: true }));

      console.log(`✓ 填写字段: ${element.name} = "${value}"`);
      return true;

    case 'select':
      // 下拉选择框处理
      if (element.tagName === 'SELECT') {
        const options = element.querySelectorAll('option');
        let selectedOption;

        // 智能匹配分类
        for (const option of options) {
          if (option.value && option.value !== '') {
            const optionText = option.textContent.trim();
            if (optionText.includes('生产率') || optionText.includes('写作助理') || optionText.includes('其他')) {
              selectedOption = option;
              break;
            }
          }
        }

        // 如果没找到合适的，选择第一个非空选项
        if (!selectedOption) {
          selectedOption = Array.from(options).find(opt => opt.value !== '');
        }

        if (selectedOption) {
          element.value = selectedOption.value;
          element.dispatchEvent(new Event('change', { bubbles: true }));
          console.log(`✓ 选择分类: ${selectedOption.textContent}`);
          return true;
        }
      }
      break;

    default:
      element.value = value;
      element.dispatchEvent(new Event('input', { bubbles: true }));
      return true;
  }

  return false;
}
