// free-ai-tools-directory.com 网站规则配置
// 网站: https://free-ai-tools-directory.com/submit-request/
// 表单技术: WordPress Form with CAPTCHA
// 最后更新: 2025-07-08

export const SITE_RULE = {
  // 基本信息
  domain: 'free-ai-tools-directory.com',
  siteName: 'Free AI Tools Directory',
  priority: 1,
  lastUpdated: '2025-07-08',
  
  // 字段映射规则
  fieldMappings: {
    // 工具名称 -> Tool Name
    siteName: {
      selectors: [
        'input[name="input_6"]',
        'input[id="input_1_6"]',
        'input.large:first-of-type',
        'div.ginput_container_post_title input'
      ],
      method: 'value',
      validation: 'required',
      notes: '工具名称，使用website-info.js中的siteName字段'
    },
    
    // 网站URL -> Site Url
    siteUrl: {
      selectors: [
        'input[name="input_7"]',
        'input[id="input_1_7"]',
        'div.ginput_container_text input',
        'input.large:nth-of-type(2)'
      ],
      method: 'value',
      validation: 'required|url',
      notes: '网站URL，使用website-info.js中的siteUrl字段'
    },
    
    // 工具描述 -> Tool Description
    detailedIntro: {
      selectors: [
        'textarea[name="input_8"]',
        'textarea[id="input_1_8"]',
        'div.ginput_container_textarea textarea',
        'textarea.large'
      ],
      method: 'value',
      validation: 'optional',
      notes: '工具详细描述，使用website-info.js中的detailedIntro字段'
    },
    
    // 简短描述 -> Short Description
    siteDescription: {
      selectors: [
        'input[name="input_10"]',
        'input[id="input_1_10"]',
        'div.ginput_container_text input:nth-of-type(2)',
        'input.large:nth-of-type(3)'
      ],
      method: 'value',
      validation: 'optional',
      notes: '简短描述，使用website-info.js中的siteDescription字段'
    },
    
    // 工具图片 -> Tool Image
    logoUpload: {
      selectors: [
        'input[name="input_5"]',
        'input[id="input_1_5"]',
        'input[type="file"]',
        'div.ginput_container_post_image input'
      ],
      method: 'file-upload',
      validation: 'optional',
      acceptedTypes: ['jpg', 'jpeg', 'png', 'gif'],
      notes: '工具图片上传，支持jpg、jpeg、png、gif格式，可选'
    }
  },
  
  // 自定义填写函数
  customFillElement: async function(element, value, config) {
    console.log(`Free AI Tools Directory自定义填写: ${element.name || element.type}, 方法: ${config.method}`);
    
    switch (config.method) {
      case 'value':
        // 标准输入框和文本域处理
        element.focus();
        await new Promise(resolve => setTimeout(resolve, 100));
        
        // 清空现有内容
        element.value = '';
        await new Promise(resolve => setTimeout(resolve, 50));
        
        // 设置新值
        element.value = value;
        
        // 触发事件
        element.dispatchEvent(new Event('input', { bubbles: true }));
        element.dispatchEvent(new Event('change', { bubbles: true }));
        element.dispatchEvent(new Event('blur', { bubbles: true }));
        
        console.log(`✓ 填写字段: ${element.name} = "${value.substring(0, 50)}..."`);
        break;
        
      case 'file-upload':
        // 文件上传处理
        console.log('⚠️ 文件上传字段需要手动处理');
        console.log('请手动上传工具图片（支持jpg、jpeg、png、gif格式）');
        console.log('文件上传是可选的，可以跳过');
        break;
        
      default:
        element.value = value;
        element.dispatchEvent(new Event('input', { bubbles: true }));
        element.dispatchEvent(new Event('change', { bubbles: true }));
    }
  },
  
  // 提交流程配置
  submitConfig: {
    submitButton: [
      'input[id="gform_submit_button_1"]',
      'input[type="submit"]',
      'input[value="Submit"]',
      '.gform_button'
    ],
    submitMethod: 'click',
    waitAfterFill: 2000,
    waitAfterSubmit: 5000,
    successIndicators: [
      'text:contains("submitted")',
      'text:contains("thank you")',
      'text:contains("success")',
      'text:contains("received")'
    ],
    errorIndicators: [
      'text:contains("error")',
      'text:contains("required")',
      'text:contains("invalid")',
      'text:contains("captcha")'
    ]
  },
  
  // 特殊处理
  specialHandling: {
    requiresLogin: false,
    hasCaptcha: true, // 有验证码
    hasFileUpload: true, // 有文件上传
    isGravityForm: true, // Gravity Forms表单
    isFreeToolsOnly: true, // 只收录免费工具
    hasImageUpload: true, // 有图片上传
    hasRecaptchaV3: true, // reCAPTCHA v3

    // 表单验证规则
    formValidation: {
      requiredFields: ['siteName', 'siteUrl'],
      optionalFields: ['detailedIntro', 'siteDescription', 'logoUpload'],
      emailValidation: false,
      urlValidation: true,
      fileUploadFields: ['logoUpload'],
      captchaRequired: true,
      gravityFormsValidation: true
    },
    
    // 特殊注意事项
    notes: [
      '这是Free AI Tools Directory的工具提交表单',
      '表单包含4个字段：2个必填，2个可选，1个文件上传',
      '专门收录免费的AI工具',
      '使用Gravity Forms表单系统',
      '有reCAPTCHA v3验证保护',
      '支持图片上传（jpg、jpeg、png、gif）',
      '工具名称和网站URL是必填的',
      '工具描述和简短描述是可选的',
      '专注于免费AI工具发现',
      '提供AI工具新闻和博客',
      '支持书签收藏功能',
      '口号：发现免费AI工具的力量',
      '使用WordPress + Gravity Forms架构'
    ]
  }
};

// 自定义处理函数
export function handleFreeAIToolsDirectorySubmission(data, _rule) {
  console.log('Processing Free AI Tools Directory form submission...');

  const processedData = { ...data };

  // 确保URL格式正确
  if (processedData.siteUrl && !processedData.siteUrl.startsWith('http')) {
    processedData.siteUrl = 'https://' + processedData.siteUrl;
  }

  return processedData;
}

// WordPress表单检测
export function detectWordPressForm() {
  console.log('检测WordPress表单...');
  
  // 检查WordPress特征
  const wpElements = document.querySelectorAll('[class*="wp-"], [id*="wp-"]');
  const wpContent = document.querySelector('#content, .content');
  
  if (wpElements.length > 0 || wpContent) {
    console.log('✓ 检测到WordPress表单');
    return true;
  }
  
  return false;
}

// Free AI Tools Directory信息提醒
export function showFreeAIToolsDirectoryInfo() {
  console.log('🆓 Free AI Tools Directory 信息:');
  console.log('');
  console.log('平台特色:');
  console.log('- 专门收录免费的AI工具');
  console.log('- 提供AI工具新闻和博客');
  console.log('- 支持书签收藏功能');
  console.log('- 简洁的提交流程');
  console.log('');
  console.log('提交要求:');
  console.log('- 工具必须是免费的');
  console.log('- 工具名称和URL是必填的');
  console.log('- 描述字段都是可选的');
  console.log('- 支持图片上传（可选）');
  console.log('- 需要通过CAPTCHA验证');
  console.log('');
  console.log('支持的图片格式:');
  console.log('- JPG / JPEG');
  console.log('- PNG');
  console.log('- GIF');
  console.log('');
  console.log('平台口号:');
  console.log('"Discover the Power of AI for Free with Our AI Tools Directory!"');
  console.log('');
  console.log('Free AI Tools Directory - 发现免费AI工具的力量！');
}

// CAPTCHA处理提醒
export function showCaptchaReminder() {
  console.log('🔒 CAPTCHA验证提醒:');
  console.log('');
  console.log('验证要求:');
  console.log('- 表单包含CAPTCHA验证码');
  console.log('- 需要手动完成验证');
  console.log('- 验证码用于防止垃圾提交');
  console.log('- 确保提交的是真实工具');
  console.log('');
  console.log('操作步骤:');
  console.log('1. 填写完所有字段');
  console.log('2. 完成CAPTCHA验证');
  console.log('3. 点击提交按钮');
  console.log('4. 等待确认消息');
  console.log('');
  console.log('请在提交前完成CAPTCHA验证！');
}

// 文件上传指南
export function showFileUploadGuide() {
  console.log('📁 文件上传指南:');
  console.log('');
  console.log('支持的格式:');
  console.log('- JPG / JPEG（推荐）');
  console.log('- PNG（支持透明背景）');
  console.log('- GIF（支持动画）');
  console.log('');
  console.log('图片建议:');
  console.log('- 尺寸：建议正方形或16:9比例');
  console.log('- 大小：建议小于2MB');
  console.log('- 内容：工具Logo或截图');
  console.log('- 质量：清晰、专业');
  console.log('');
  console.log('上传说明:');
  console.log('- 图片上传是可选的');
  console.log('- 有助于提高工具展示效果');
  console.log('- 可以在提交后补充');
  console.log('');
  console.log('注意：文件上传需要手动操作！');
}

// 表单验证
export function validateFreeAIToolsDirectoryForm() {
  console.log('验证Free AI Tools Directory表单...');

  const requiredFields = [
    { selector: 'input[name="input_6"]', label: '工具名称' },
    { selector: 'input[name="input_7"]', label: '网站URL' }
  ];

  let isValid = true;

  requiredFields.forEach(field => {
    const element = document.querySelector(field.selector);
    if (!element || !element.value.trim()) {
      console.log(`⚠️ 必填字段为空: ${field.label}`);
      isValid = false;
    }
  });

  // 检查reCAPTCHA
  const recaptcha = document.querySelector('#g-recaptcha-response');
  if (recaptcha && !recaptcha.value) {
    console.log('⚠️ 请完成reCAPTCHA验证');
  }

  if (isValid) {
    console.log('✓ 表单验证通过（除reCAPTCHA外）');
  }

  return isValid;
}

// 免费工具验证
export function validateFreeToolRequirement() {
  console.log('🆓 免费工具要求验证:');
  console.log('');
  console.log('收录标准:');
  console.log('- 工具必须完全免费使用');
  console.log('- 或提供实质性的免费功能');
  console.log('- 不接受纯付费工具');
  console.log('- 免费试用期工具可以考虑');
  console.log('');
  console.log('请确认您的工具符合免费要求！');
}
