// ukinternetdirectory.net 网站规则配置
// 网站: https://www.ukinternetdirectory.net/submit.php
// 表单技术: PHP Form with CAPTCHA
// 最后更新: 2025-07-08

export const SITE_RULE = {
  // 基本信息
  domain: 'ukinternetdirectory.net',
  siteName: 'UK Internet Directory',
  priority: 1,
  lastUpdated: '2025-07-08',
  
  // 字段映射规则
  fieldMappings: {
    // 定价选项 -> Pricing
    linkType: {
      selectors: [
        'input[name="LINK_TYPE"][value="normal"]',
        'input[type="radio"][value="normal"]',
        'input[value="normal"]'
      ],
      method: 'radio',
      validation: 'required',
      defaultValue: 'normal',
      availableOptions: ['featured', 'normal', 'reciprocal'],
      notes: '定价选项，默认选择normal (Regular links，免费，4-5个月审核)'
    },
    
    // 标题 -> Title
    siteName: {
      selectors: [
        'input[name="TITLE"]',
        'input.text:first-of-type',
        'input[maxlength="100"]',
        'input[size="40"]:first-of-type'
      ],
      method: 'value',
      validation: 'optional',
      notes: '网站标题，使用website-info.js中的siteName字段'
    },
    
    // URL -> URL
    siteUrl: {
      selectors: [
        'input[name="URL"]',
        'input[maxlength="255"]',
        'input.text:nth-of-type(2)',
        'input[size="40"]:nth-of-type(2)'
      ],
      method: 'value',
      validation: 'optional|url',
      notes: '网站URL，使用website-info.js中的siteUrl字段'
    },
    
    // 描述 -> Description
    detailedIntro: {
      selectors: [
        'textarea[name="DESCRIPTION"]',
        'textarea.text:first-of-type',
        'textarea[rows="3"]',
        'textarea[style*="width: 440px"]'
      ],
      method: 'value',
      validation: 'optional',
      maxLength: 1000,
      notes: '网站详细描述，使用website-info.js中的detailedIntro字段，限制1000字符'
    },
    
    // META关键词 -> META Keywords
    keywords: {
      selectors: [
        'input[name="META_KEYWORDS"]',
        'input[maxlength="2000"]',
        'input.text:nth-of-type(3)',
        'input[size="40"]:nth-of-type(3)'
      ],
      method: 'value',
      validation: 'optional',
      notes: 'META关键词，使用website-info.js中的keywords字段'
    },
    
    // META描述 -> META Description
    siteDescription: {
      selectors: [
        'textarea[name="META_DESCRIPTION"]',
        'textarea.text:nth-of-type(2)',
        'textarea[rows="3"]:nth-of-type(2)',
        'textarea[cols="30"]'
      ],
      method: 'value',
      validation: 'optional',
      maxLength: 250,
      notes: 'META描述，使用website-info.js中的siteDescription字段，限制250字符'
    },
    
    // 您的姓名 -> Your Name
    fullName: {
      selectors: [
        'input[name="OWNER_NAME"]',
        'input[maxlength="50"]',
        'input.text:nth-of-type(4)',
        'input[size="40"]:nth-of-type(4)'
      ],
      method: 'value',
      validation: 'required',
      notes: '您的姓名，使用website-info.js中的fullName字段'
    },
    
    // 您的邮箱 -> Your Email
    contactEmail: {
      selectors: [
        'input[name="OWNER_EMAIL"]',
        'input.text:nth-of-type(5)',
        'input[size="40"]:nth-of-type(5)',
        'input[maxlength="255"]:nth-of-type(2)'
      ],
      method: 'value',
      validation: 'required|email',
      notes: '您的邮箱，使用website-info.js中的contactEmail字段'
    }
  },
  
  // 自定义填写函数
  customFillElement: async function(element, value, config) {
    console.log(`UK Internet Directory自定义填写: ${element.name || element.type}, 方法: ${config.method}`);
    
    switch (config.method) {
      case 'value':
        // 标准输入框和文本域处理
        element.focus();
        await new Promise(resolve => setTimeout(resolve, 100));
        
        // 清空现有内容
        element.value = '';
        await new Promise(resolve => setTimeout(resolve, 50));
        
        // 处理字符限制
        let finalValue = value;
        if (config.maxLength && finalValue.length > config.maxLength) {
          finalValue = finalValue.substring(0, config.maxLength);
          console.log(`⚠️ 内容被截断到${config.maxLength}字符: ${finalValue}`);
        }
        
        // 设置新值
        element.value = finalValue;
        
        // 触发事件
        element.dispatchEvent(new Event('input', { bubbles: true }));
        element.dispatchEvent(new Event('change', { bubbles: true }));
        element.dispatchEvent(new Event('blur', { bubbles: true }));
        
        console.log(`✓ 填写字段: ${element.name} = "${finalValue.substring(0, 50)}..."`);
        break;
        
      case 'radio':
        // 单选按钮处理
        console.log(`处理定价选项，目标值: ${config.defaultValue}`);
        
        // 查找所有同名单选按钮
        const radioButtons = document.querySelectorAll('input[name="LINK_TYPE"]');
        
        // 先取消所有选择
        radioButtons.forEach(rb => {
          rb.checked = false;
        });
        
        // 选择目标选项
        const targetRadio = Array.from(radioButtons).find(rb => 
          rb.value === config.defaultValue
        );
        
        if (targetRadio) {
          targetRadio.checked = true;
          targetRadio.dispatchEvent(new Event('change', { bubbles: true }));
          console.log(`✓ 选择定价选项: Regular links (${config.defaultValue})`);
        } else {
          console.log(`⚠️ 未找到定价选项: ${config.defaultValue}`);
        }
        break;
        
      default:
        element.value = value;
        element.dispatchEvent(new Event('input', { bubbles: true }));
        element.dispatchEvent(new Event('change', { bubbles: true }));
    }
  },
  
  // 提交流程配置
  submitConfig: {
    submitButton: [
      'button[type="submit"]',
      'input[type="submit"]',
      'button:contains("Submit")',
      'input[value*="Submit"]'
    ],
    submitMethod: 'click',
    waitAfterFill: 2000,
    waitAfterSubmit: 5000,
    successIndicators: [
      'text:contains("submitted")',
      'text:contains("thank you")',
      'text:contains("success")',
      'text:contains("approved")'
    ],
    errorIndicators: [
      'text:contains("error")',
      'text:contains("required")',
      'text:contains("invalid")',
      'text:contains("captcha")'
    ]
  },
  
  // 特殊处理
  specialHandling: {
    requiresLogin: false,
    hasCaptcha: true, // 可能有验证码
    hasFileUpload: false,
    isPHPForm: true, // PHP表单
    isUKDirectory: true, // 英国目录
    hasMetaFields: true, // 有META字段
    hasPaidOptions: true, // 有付费选项
    hasReciprocalOption: true, // 有互惠链接选项
    hasLongestReviewTime: true, // 审核时间最长
    hasCharacterLimits: true, // 有字符限制
    
    // 表单验证规则
    formValidation: {
      requiredFields: ['linkType', 'fullName', 'contactEmail'],
      optionalFields: ['siteName', 'siteUrl', 'detailedIntro', 'keywords', 'siteDescription'],
      emailValidation: true,
      urlValidation: true,
      characterLimits: {
        detailedIntro: 1000,
        siteDescription: 250
      },
      radioGroups: ['linkType']
    },
    
    // 特殊注意事项
    notes: [
      '这是UK Internet Directory的网站提交表单',
      '表单包含8个字段：3个必填，5个可选',
      '英国互联网目录，专注于英国网站',
      '可能有验证码保护，需要手动处理',
      '默认选择Regular links（免费，4-5个月审核）',
      '有付费选项：Featured links $7.97/年（24小时审核）',
      '有互惠链接选项：Regular submissions with reciprocal（3-4周审核）',
      '包含META字段：关键词和描述',
      '描述限制1000字符，META描述限制250字符',
      '使用实际字段名：LINK_TYPE, TITLE, URL, DESCRIPTION, META_KEYWORDS, META_DESCRIPTION, OWNER_NAME, OWNER_EMAIL',
      '与其他目录网站类似的表单结构',
      '专注于英国和欧洲网站收录',
      '审核时间最长（4-5个月）',
      '互惠链接需要在首页或index页面'
    ]
  }
};

// 自定义处理函数
export function handleUKInternetDirectorySubmission(data, _rule) {
  console.log('Processing UK Internet Directory form submission...');

  const processedData = { ...data };

  // 确保URL格式正确
  if (processedData.siteUrl && !processedData.siteUrl.startsWith('http')) {
    processedData.siteUrl = 'https://' + processedData.siteUrl;
  }

  // 处理字符限制
  if (processedData.detailedIntro && processedData.detailedIntro.length > 1000) {
    processedData.detailedIntro = processedData.detailedIntro.substring(0, 1000);
  }

  if (processedData.siteDescription && processedData.siteDescription.length > 250) {
    processedData.siteDescription = processedData.siteDescription.substring(0, 250);
  }

  // 设置默认值
  processedData.linkType = 'normal'; // Regular links

  return processedData;
}

// UK Internet Directory信息提醒
export function showUKInternetDirectoryInfo() {
  console.log('🇬🇧 UK Internet Directory 信息:');
  console.log('');
  console.log('平台特色:');
  console.log('- 英国互联网目录');
  console.log('- 专注于英国和欧洲网站');
  console.log('- 包含META字段优化');
  console.log('- 支持互惠链接选项');
  console.log('- 描述字段限制1000字符');
  console.log('');
  console.log('提交选项 (3种):');
  console.log('1. Featured links - $7.97/年');
  console.log('   - 24小时审核');
  console.log('   - 快速收录');
  console.log('   - 价格适中');
  console.log('');
  console.log('2. Regular links - 免费 ✅ 默认选择');
  console.log('   - 4-5个月审核 ⚠️ 最长');
  console.log('   - 完全免费');
  console.log('   - 需要耐心等待');
  console.log('');
  console.log('3. Regular submissions with reciprocal - 免费');
  console.log('   - 3-4周审核');
  console.log('   - 需要在首页或index页面放置互惠链接');
  console.log('   - 免费快速选项');
  console.log('');
  console.log('字段特点:');
  console.log('- 包含META关键词和描述');
  console.log('- 描述限制1000字符（较宽松）');
  console.log('- META描述限制250字符');
  console.log('- 支持SEO优化');
  console.log('- Title和URL字段为可选');
  console.log('');
  console.log('UK Internet Directory - 英国权威互联网目录！');
}

// 最长审核时间分析
export function showLongestReviewTimeAnalysis() {
  console.log('⏰ 最长审核时间分析:');
  console.log('');
  console.log('免费选项审核时间排序:');
  console.log('1. Free Internet: 2-3个月');
  console.log('2. Quality Internet: 2-3个月');
  console.log('3. Australia Web: 3-4个月');
  console.log('4. Free PR Web: 3-4个月');
  console.log('5. Info Listings: 3-6个月');
  console.log('6. UK Internet: 4-5个月 ⚠️ 最长');
  console.log('7. All States USA: 8-10周');
  console.log('');
  console.log('UK Internet Directory特点:');
  console.log('- 审核时间最长（4-5个月）');
  console.log('- 审核标准可能更严格');
  console.log('- 适合不急于上线的网站');
  console.log('- 英国本土网站可能优先');
  console.log('');
  console.log('建议:');
  console.log('- 如果急需收录，选择付费或互惠选项');
  console.log('- 免费选项适合长期规划');
  console.log('- 可以同时提交多个目录');
  console.log('- 考虑地理位置因素');
}

// 英国目录优势
export function showUKDirectoryAdvantages() {
  console.log('🏴󠁧󠁢󠁥󠁮󠁧󠁿 英国目录优势:');
  console.log('');
  console.log('地理优势:');
  console.log('- 英国本土权威目录');
  console.log('- 欧洲用户访问量高');
  console.log('- 时区友好');
  console.log('- 文化背景相近');
  console.log('');
  console.log('SEO优势:');
  console.log('- 英国本土链接权重高');
  console.log('- 提升英国搜索排名');
  console.log('- 增加本土流量');
  console.log('- 建立地域权威');
  console.log('');
  console.log('适合网站类型:');
  console.log('- 英国本土企业');
  console.log('- 欧洲市场网站');
  console.log('- 英语服务网站');
  console.log('- 国际化网站');
  console.log('');
  console.log('商业价值:');
  console.log('- 提高品牌知名度');
  console.log('- 增加商业机会');
  console.log('- 建立信任度');
  console.log('- 扩大市场覆盖');
}

// 互惠链接详细说明
export function showUKReciprocalLinkDetails() {
  console.log('🔗 UK目录互惠链接详细说明:');
  console.log('');
  console.log('互惠链接要求:');
  console.log('- 必须放置在首页（Home page）');
  console.log('- 或者放置在index页面');
  console.log('- 链接必须可见且可点击');
  console.log('- 使用指定的HTML代码');
  console.log('');
  console.log('审核优势:');
  console.log('- 审核时间大幅缩短（4-5个月 → 3-4周）');
  console.log('- 提高审核通过率');
  console.log('- 建立双向链接关系');
  console.log('- 增加网站权重');
  console.log('');
  console.log('互惠链接代码示例:');
  console.log('<a href="https://www.ukinternetdirectory.net/">UK Internet Directory</a>');
  console.log('');
  console.log('注意事项:');
  console.log('- 互惠链接必须持续有效');
  console.log('- 定期检查链接状态');
  console.log('- 不要隐藏或移除链接');
  console.log('- 保持链接的可访问性');
}

// 表单验证
export function validateUKInternetDirectoryForm() {
  console.log('验证UK Internet Directory表单...');

  const requiredFields = [
    { selector: 'input[name="OWNER_NAME"]', label: '您的姓名' },
    { selector: 'input[name="OWNER_EMAIL"]', label: '您的邮箱' }
  ];

  let isValid = true;

  requiredFields.forEach(field => {
    const element = document.querySelector(field.selector);
    if (!element || !element.value.trim()) {
      console.log(`⚠️ 必填字段为空: ${field.label}`);
      isValid = false;
    }
  });

  // 检查定价选项
  const radioButtons = document.querySelectorAll('input[name="LINK_TYPE"]:checked');
  if (radioButtons.length === 0) {
    console.log('⚠️ 请选择定价选项');
    isValid = false;
  }

  // 检查字符限制
  const description = document.querySelector('textarea[name="DESCRIPTION"]');
  if (description && description.value.length > 1000) {
    console.log('⚠️ 描述超过1000字符限制');
  }

  const metaDescription = document.querySelector('textarea[name="META_DESCRIPTION"]');
  if (metaDescription && metaDescription.value.length > 250) {
    console.log('⚠️ META描述超过250字符限制');
  }

  if (isValid) {
    console.log('✓ 表单验证通过');
  }

  return isValid;
}

// 目录网站地理分布
export function showDirectoryGeographicDistribution() {
  console.log('🌍 目录网站地理分布:');
  console.log('');
  console.log('已配置的目录网站 (7个):');
  console.log('1. 🇦🇺 australiawebdirectory.net - 澳大利亚');
  console.log('2. 🇺🇸 allstatesusadirectory.com - 美国各州');
  console.log('3. 🌐 freeprwebdirectory.com - PR公关（全球）');
  console.log('4. 🌐 freeinternetwebdirectory.com - 互联网（全球）');
  console.log('5. 🌐 info-listings.com - 信息列表（全球）');
  console.log('6. 🌐 qualityinternetdirectory.com - 质量目录（全球）');
  console.log('7. 🇬🇧 ukinternetdirectory.net - 英国 ✅ 新增');
  console.log('');
  console.log('地理覆盖:');
  console.log('- 北美洲: 美国');
  console.log('- 欧洲: 英国 ✅');
  console.log('- 大洋洲: 澳大利亚');
  console.log('- 全球: 4个专业目录');
  console.log('');
  console.log('战略意义:');
  console.log('- 覆盖主要英语国家');
  console.log('- 建立全球链接网络');
  console.log('- 提高国际知名度');
  console.log('- 分散地理风险');
}

// 最佳提交策略更新
export function showUpdatedBestSubmissionStrategy() {
  console.log('🎯 更新的最佳提交策略:');
  console.log('');
  console.log('按价格排序 (7个目录):');
  console.log('1. 💰 Quality Internet: $5.79/年 (最便宜)');
  console.log('2. 💰 All States USA: $6.79');
  console.log('3. 💰 Free Internet: $6.97/年');
  console.log('4. 💰 UK Internet: $7.97/年 ✅ 新增');
  console.log('5. 💰 Free PR Web: $9.99/年');
  console.log('6. 💰 Australia Web: $12.95/年');
  console.log('7. 💰 Info Listings: $29.99终身');
  console.log('');
  console.log('按审核时间排序 (免费选项):');
  console.log('1. ⚡ Free Internet: 2-3个月');
  console.log('2. ⚡ Quality Internet: 2-3个月');
  console.log('3. ⚡ Australia Web: 3-4个月');
  console.log('4. ⚡ Free PR Web: 3-4个月');
  console.log('5. ⚡ Info Listings: 3-6个月');
  console.log('6. ⚡ UK Internet: 4-5个月 ⚠️ 最长');
  console.log('7. ⚡ All States USA: 8-10周');
  console.log('');
  console.log('推荐组合策略:');
  console.log('- 预算有限: Quality + Free Internet');
  console.log('- 地理覆盖: US + UK + Australia');
  console.log('- 快速收录: 选择付费选项');
  console.log('- 长期投资: Info Listings终身');
}
