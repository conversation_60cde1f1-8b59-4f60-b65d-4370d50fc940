// FutureAGITools.com 网站规则配置
// 网站: https://www.futureagitools.com/submit-a-site
// 最后更新: 2025-07-06

export const SITE_RULE = {
  // 基本信息
  domain: 'futureagitools.com',
  siteName: 'Future AGI Tools',
  priority: 1,
  lastUpdated: '2025-07-06',
  
  // 字段映射规则
  fieldMappings: {
    // 工具名称 -> Name
    siteName: {
      selectors: [
        'input[name*="name"]:not([name*="your"])',
        'input[placeholder*="Name"]:not([placeholder*="Your"])',
        'input[aria-label*="Name"]:not([aria-label*="Your"])',
        '#name'
      ],
      method: 'value',
      validation: 'required',
      notes: '工具名称'
    },
    
    // 工具URL -> URL
    siteUrl: {
      selectors: [
        'input[name*="url"]',
        'input[type="url"]',
        'input[placeholder*="URL"]',
        '#url'
      ],
      method: 'value',
      validation: 'required|url',
      notes: '工具网站URL'
    },
    
    // 工具描述 -> Description
    siteDescription: {
      selectors: [
        'textarea[name*="description"]',
        'textarea[placeholder*="Description"]',
        'textarea[aria-label*="Description"]',
        '#description'
      ],
      method: 'value',
      validation: 'required',
      notes: '工具描述'
    },
    
    // 提交者姓名 -> Your name
    fullName: {
      selectors: [
        'input[name*="your"][name*="name"]',
        'input[placeholder*="Your name"]',
        'input[aria-label*="Your name"]',
        '#your-name'
      ],
      method: 'value',
      validation: 'required',
      notes: '提交者姓名'
    },
    
    // 联系邮箱 -> Email
    contactEmail: {
      selectors: [
        'input[name*="email"]',
        'input[type="email"]',
        'input[placeholder*="Email"]',
        '#email'
      ],
      method: 'value',
      validation: 'required|email',
      notes: '联系邮箱地址'
    },
    
    // 隐私政策同意 -> Privacy policy checkbox
    privacyPolicy: {
      selectors: [
        'input[type="checkbox"][name*="privacy"]',
        'input[type="checkbox"][value*="privacy"]',
        'input[type="checkbox"] + label:contains("privacy")',
        '#privacy-policy'
      ],
      method: 'checkbox',
      validation: 'required',
      defaultValue: true,
      notes: '隐私政策同意复选框，必须勾选'
    }
  },
  
  // 自定义填写函数
  customFillElement: async function(element, value, config) {
    console.log(`FutureAGITools自定义填写: ${element.name || element.id}, 方法: ${config.method}`);
    
    switch (config.method) {
      case 'value':
        // WordPress表单处理
        element.focus();
        await new Promise(resolve => setTimeout(resolve, 100));
        
        element.value = value;
        element.dispatchEvent(new Event('input', { bubbles: true }));
        element.dispatchEvent(new Event('change', { bubbles: true }));
        element.dispatchEvent(new Event('blur', { bubbles: true }));
        break;
        
      case 'checkbox':
        // 复选框处理
        if (element.type === 'checkbox') {
          element.checked = true;
          element.dispatchEvent(new Event('change', { bubbles: true }));
          element.dispatchEvent(new Event('click', { bubbles: true }));
          console.log(`✓ 勾选复选框: ${element.name || element.id}`);
        }
        break;
        
      default:
        element.value = value;
        element.dispatchEvent(new Event('input', { bubbles: true }));
        element.dispatchEvent(new Event('change', { bubbles: true }));
    }
  },
  
  // 提交流程配置
  submitConfig: {
    submitButton: 'input[type="submit"], button[type="submit"], .submit-button, #submit',
    submitMethod: 'click',
    waitAfterFill: 2000,
    waitAfterSubmit: 5000,
    successIndicators: [
      '.success-message',
      '.thank-you',
      '[class*="success"]'
    ],
    errorIndicators: [
      '.error-message',
      '.validation-error',
      '[class*="error"]'
    ]
  },
  
  // 特殊处理
  specialHandling: {
    requiresLogin: false,
    hasCaptcha: false,
    hasFileUpload: false,
    hasPrivacyCheckbox: true, // 有隐私政策复选框
    
    // 表单验证规则
    formValidation: {
      requiredFields: ['siteName', 'siteUrl', 'siteDescription', 'fullName', 'contactEmail', 'privacyPolicy'],
      emailValidation: true,
      urlValidation: true
    },
    
    // 特殊注意事项
    notes: [
      '简洁的WordPress表单设计',
      '有6个字段，全部必填',
      '包含隐私政策同意复选框',
      '目标是建立最大的AI工具目录',
      '表单提交后需要人工审核'
    ]
  }
};

// 自定义处理函数
export function handleFutureAGIToolsSubmission(data, rule) {
  console.log('Processing Future AGI Tools submission...');
  
  const processedData = { ...data };
  
  // 确保URL格式正确
  if (processedData.siteUrl && !processedData.siteUrl.startsWith('http')) {
    processedData.siteUrl = 'https://' + processedData.siteUrl;
  }
  
  // 确保隐私政策同意
  processedData.privacyPolicy = true;
  
  return processedData;
}
