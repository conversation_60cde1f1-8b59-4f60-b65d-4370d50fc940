// comparebiztech.com 网站规则配置
// 网站: https://www.comparebiztech.com/submit-ai-tool/
// 表单技术: Contact Form 7 with reCAPTCHA and Akismet
// 最后更新: 2025-07-08

export const SITE_RULE = {
  // 基本信息
  domain: 'comparebiztech.com',
  siteName: 'Compare Biz Tech',
  priority: 1,
  lastUpdated: '2025-07-08',
  
  // 字段映射规则
  fieldMappings: {
    // 提交者姓名 -> Name
    fullName: {
      selectors: [
        'input[name="your-name"]',
        '.wpcf7-form-control-wrap[data-name="your-name"] input',
        'input[type="text"][maxlength="400"]:first-of-type'
      ],
      method: 'value',
      validation: 'required',
      notes: '提交者姓名，使用website-info.js中的fullName字段'
    },
    
    // 商业域名邮箱 -> Please Enter Only Your Business Domain Email
    contactEmail: {
      selectors: [
        'input[name="your-email"]',
        '.wpcf7-form-control-wrap[data-name="your-email"] input',
        'input[type="email"]'
      ],
      method: 'value',
      validation: 'required|email|business',
      notes: '商业域名邮箱，使用website-info.js中的contactEmail字段，要求商业域名'
    },
    
    // AI工具名称 -> AI Tool Name
    siteName: {
      selectors: [
        'input[name="tool-name"]',
        '.wpcf7-form-control-wrap[data-name="tool-name"] input',
        'input[type="text"][maxlength="400"]:nth-of-type(2)'
      ],
      method: 'value',
      validation: 'required',
      notes: 'AI工具名称，使用website-info.js中的siteName字段'
    },
    
    // AI工具URL -> AI Tool URL
    siteUrl: {
      selectors: [
        'input[name="tool-url"]',
        '.wpcf7-form-control-wrap[data-name="tool-url"] input',
        'input[type="url"]'
      ],
      method: 'value',
      validation: 'required|url',
      notes: 'AI工具URL，使用website-info.js中的siteUrl字段'
    },
    
    // AI工具标签 -> AI Tool Tags
    keywords: {
      selectors: [
        'input[name="tool-tags"]',
        '.wpcf7-form-control-wrap[data-name="tool-tags"] input',
        'input[type="text"][maxlength="400"]:nth-of-type(3)'
      ],
      method: 'value',
      validation: 'required',
      notes: 'AI工具标签，使用website-info.js中的keywords字段'
    },
    
    // AI工具描述 -> AI Tool Description
    siteDescription: {
      selectors: [
        'textarea[name="tool-description"]',
        '.wpcf7-form-control-wrap[data-name="tool-description"] textarea',
        'textarea[maxlength="2000"]:first-of-type'
      ],
      method: 'value',
      validation: 'required',
      notes: 'AI工具描述，使用website-info.js中的siteDescription字段，最大2000字符'
    },
    
    // 其他详情 -> Any Other Details You'd Like To Share?
    faqs: {
      selectors: [
        'textarea[name="other-details"]',
        '.wpcf7-form-control-wrap[data-name="other-details"] textarea',
        'textarea[maxlength="2000"]:last-of-type'
      ],
      method: 'value',
      validation: 'optional',
      notes: '其他详情，使用website-info.js中的faqs字段'
    }
  },
  
  // 自定义填写函数
  customFillElement: async function(element, value, config) {
    console.log(`Compare Biz Tech自定义填写: ${element.name || element.tagName}, 方法: ${config.method}`);
    
    switch (config.method) {
      case 'value':
        // 标准输入框和文本域处理
        element.focus();
        await new Promise(resolve => setTimeout(resolve, 100));
        
        // 清空现有内容
        element.value = '';
        await new Promise(resolve => setTimeout(resolve, 50));
        
        // 处理特殊字段
        let finalValue = value;
        if (element.name === 'tool-tags' && Array.isArray(value)) {
          // 标签转换为逗号分隔的字符串
          finalValue = value.join(', ');
        }
        
        // 设置新值
        element.value = finalValue;
        
        // 触发Contact Form 7事件
        element.dispatchEvent(new Event('input', { bubbles: true }));
        element.dispatchEvent(new Event('change', { bubbles: true }));
        element.dispatchEvent(new Event('blur', { bubbles: true }));
        
        console.log(`✓ 填写字段: ${element.name} = "${finalValue.substring(0, 50)}..."`);
        break;
        
      default:
        element.value = value;
        element.dispatchEvent(new Event('input', { bubbles: true }));
        element.dispatchEvent(new Event('change', { bubbles: true }));
    }
  },
  
  // 提交流程配置
  submitConfig: {
    submitButton: [
      'input[type="submit"]',
      '.wpcf7-submit',
      'input[value="Submit"]'
    ],
    submitMethod: 'click',
    waitAfterFill: 3000, // 等待reCAPTCHA和Akismet处理
    waitAfterSubmit: 5000,
    
    // 提交前检查
    preSubmitChecks: [
      {
        type: 'recaptcha',
        selector: 'input[name="_wpcf7_recaptcha_response"]',
        action: 'wait',
        description: '等待reCAPTCHA验证'
      },
      {
        type: 'akismet',
        selector: '.akismet-fields-container',
        action: 'verify',
        description: '等待Akismet反垃圾邮件检查'
      }
    ],
    
    successIndicators: [
      '.wpcf7-mail-sent-ok',
      '.wpcf7-response-output[role="alert"]',
      'text:contains("Thank you")',
      'text:contains("sent successfully")'
    ],
    errorIndicators: [
      '.wpcf7-validation-errors',
      '.wpcf7-mail-sent-ng',
      '.wpcf7-response-output[role="alert"]',
      'text:contains("error")'
    ]
  },
  
  // 特殊处理
  specialHandling: {
    requiresLogin: false,
    hasCaptcha: true, // 有reCAPTCHA验证
    hasFileUpload: false,
    isContactForm7: true, // 使用Contact Form 7
    hasAkismet: true, // 有Akismet反垃圾邮件
    hasNewsletter: true, // 有邮件订阅
    
    // 表单验证规则
    formValidation: {
      requiredFields: ['fullName', 'contactEmail', 'siteName', 'siteUrl', 'keywords', 'siteDescription'],
      emailValidation: true,
      urlValidation: true,
      businessEmailRequired: true, // 要求商业域名邮箱
      characterLimits: {
        fullName: 400,
        contactEmail: 400,
        siteName: 400,
        siteUrl: 400,
        keywords: 400,
        siteDescription: 2000,
        additionalDetails: 2000
      }
    },
    
    // 特殊注意事项
    notes: [
      '这是WordPress网站，使用Contact Form 7插件',
      '表单包含7个字段，6个必填，1个可选',
      '有reCAPTCHA验证，需要等待验证完成',
      '有Akismet反垃圾邮件检查',
      '要求商业域名邮箱（不接受个人邮箱）',
      '提交后会订阅邮件通讯',
      '字段最大长度：文本400字符，文本域2000字符',
      '表单使用wpcf7-form-control类名',
      '字段包装在wpcf7-form-control-wrap中',
      '有隐私政策和数据处理说明'
    ]
  }
};

// 自定义处理函数
export function handleCompareBizTechSubmission(data, _rule) {
  console.log('Processing Compare Biz Tech form submission...');

  const processedData = { ...data };

  // 确保URL格式正确
  if (processedData.siteUrl && !processedData.siteUrl.startsWith('http')) {
    processedData.siteUrl = 'https://' + processedData.siteUrl;
  }

  // 处理标签格式
  if (processedData.keywords) {
    if (Array.isArray(processedData.keywords)) {
      processedData.keywords = processedData.keywords.join(', ');
    }
  }

  // faqs字段已通过字段映射处理，无需额外设置

  return processedData;
}

// reCAPTCHA处理函数
export async function handleRecaptcha() {
  console.log('等待reCAPTCHA验证...');
  
  // 检查reCAPTCHA是否存在
  const recaptchaResponse = document.querySelector('input[name="_wpcf7_recaptcha_response"]');
  if (recaptchaResponse) {
    console.log('检测到reCAPTCHA，需要手动验证');
    // 这里需要用户手动完成验证
    return new Promise((resolve) => {
      const checkRecaptcha = setInterval(() => {
        if (recaptchaResponse.value && recaptchaResponse.value.length > 0) {
          clearInterval(checkRecaptcha);
          console.log('✓ reCAPTCHA验证完成');
          resolve();
        }
      }, 1000);
    });
  }
}

// Akismet反垃圾邮件处理
export async function handleAkismet() {
  console.log('处理Akismet反垃圾邮件检查...');
  
  // 更新Akismet时间戳
  const akismetJs = document.querySelector('input[name="_wpcf7_ak_js"]');
  if (akismetJs) {
    akismetJs.value = new Date().getTime();
    console.log('✓ Akismet时间戳已更新');
  }
}
