// Launched.io 网站规则配置
// 网站: https://launched.io/SubmitStartup
// 最后更新: 2025-07-24

export const SITE_RULE = {
  domain: 'launched.io',
  siteName: 'Launched',
  priority: 1,
  lastUpdated: '2025-07-24',

  fieldMappings: {
    siteName: {
      selectors: [
        'input[name="productname"]',
        '#myform input[name="productname"]',
        'label:contains("Product Name") + input'
      ],
      method: 'value',
      validation: 'required',
      notes: '产品名称'
    },

    siteDescription: {
      selectors: [
        'input[name="pitch"]',
        '#myform input[name="pitch"]',
        'label:contains("Your Pitch") + input'
      ],
      method: 'value',
      validation: 'required',
      notes: '一句话产品描述'
    },

    siteUrl: {
      selectors: [
        'input[name="website"]',
        '#myform input[name="website"]',
        'label:contains("Website url") + input'
      ],
      method: 'value',
      validation: 'required|url',
      notes: '网站URL，需要https://开头'
    },

    detailedIntro: {
      selectors: [
        'textarea[name="description"]',
        '#myform textarea[name="description"]',
        'label:contains("Describe your product") + textarea'
      ],
      method: 'value',
      validation: 'required',
      notes: '详细产品描述，1-2段'
    },

    logoUrl: {
      selectors: [
        'input[name="screenshot"]',
        'input[type="file"]',
        'label:contains("Upload your product screenshot") + div input'
      ],
      method: 'file',
      validation: 'required',
      notes: '产品截图，1088x816像素'
    },

    twitterUrl: {
      selectors: [
        'input[name="twitter"]',
        '#myform input[name="twitter"]',
        'label:contains("Twitter account") + input'
      ],
      method: 'value',
      validation: 'optional|url',
      notes: 'Twitter账号ID'
    },

    videoUrl: {
      selectors: [
        'input[name="pitchVideo"]',
        '#myform input[name="pitchVideo"]',
        'label:contains("YouTube video pitch") + input'
      ],
      method: 'value',
      validation: 'optional|url',
      notes: 'YouTube视频推介URL'
    },

    country: {
      selectors: [
        'input[name="location"]',
        '#myform input[name="location"]',
        'label:contains("Where is your startup based") + input'
      ],
      method: 'value',
      validation: 'required',
      notes: '创业公司所在国家地区'
    },

    category: {
      selectors: [
        'input[name="industry"]',
        '#myform input[name="industry"]',
        'label:contains("What industry") + input'
      ],
      method: 'value',
      validation: 'required',
      notes: '所属行业'
    },

    contactEmail: {
      selectors: [
        'input[name="email"]',
        '#myform input[name="email"]',
        'label:contains("Your Email address") + input'
      ],
      method: 'value',
      validation: 'required|email',
      notes: '联系邮箱'
    },

    startupStage: {
      selectors: [
        'textarea[name="startupstage"]',
        '#myform textarea[name="startupstage"]',
        'label:contains("describe the stage") + textarea'
      ],
      method: 'value',
      validation: 'optional',
      notes: '创业阶段和支持需求'
    },

    affiliateUrl: {
      selectors: [
        'input[name="affiliateProgram"]',
        '#myform input[name="affiliateProgram"]',
        'label:contains("Affiliate Program") + input'
      ],
      method: 'value',
      validation: 'optional|url',
      notes: '联盟计划URL'
    }
  },

  submitConfig: {
    submitButton: 'input[type="submit"], .button',
    submitMethod: 'click',
    successIndicators: ['.success-message'],
    errorIndicators: ['.error-message']
  },

  specialHandling: {
    requiresLogin: false,
    hasCaptcha: false,
    hasFileUpload: true,
    customScript: 'handleLaunchedSubmission',
    formValidation: {
      requiredFields: ['siteName', 'siteDescription', 'siteUrl', 'detailedIntro', 'logoUrl', 'country', 'category', 'contactEmail'],
      emailValidation: true,
      urlValidation: true
    },
    notes: [
      '创业项目展示平台',
      '需要上传产品截图',
      '有出售意向复选框',
      '支持YouTube视频推介',
      '需要详细的创业阶段描述',
      '支持联盟计划'
    ]
  }
};

export function handleLaunchedSubmission(data, rule) {
  const processedData = { ...data };

  // 确保URL以https://开头
  if (processedData.siteUrl && !processedData.siteUrl.startsWith('http')) {
    processedData.siteUrl = 'https://' + processedData.siteUrl;
  }

  if (processedData.affiliateUrl && !processedData.affiliateUrl.startsWith('http')) {
    processedData.affiliateUrl = 'https://' + processedData.affiliateUrl;
  }

  if (processedData.videoUrl && !processedData.videoUrl.startsWith('http')) {
    processedData.videoUrl = 'https://' + processedData.videoUrl;
  }

  return processedData;
}

export async function customFillElement(element, value, config) {
  // 处理文件上传字段
  if (element.type === 'file') {
    console.warn('文件上传字段需要手动处理');
    return false;
  }

  // 处理复选框
  if (element.type === 'checkbox') {
    element.checked = Boolean(value);
    element.dispatchEvent(new Event('change', { bubbles: true }));
    return true;
  }

  if (element.tagName === 'INPUT' || element.tagName === 'TEXTAREA') {
    element.value = value;
    element.dispatchEvent(new Event('input', { bubbles: true }));
    element.dispatchEvent(new Event('change', { bubbles: true }));
    return true;
  }

  return false;
}