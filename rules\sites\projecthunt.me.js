// AI Site Submitter - ProjectHunt 规则配置
// 自动生成于: 2025/7/16 10:07:57
// 域名: projecthunt.me

export const SITE_RULE = {
  "domain": "projecthunt.me",
  "siteName": "ProjectHunt",
  "lastUpdated": "2025-07-16T02:07:57.400Z",
  "fieldMappings": {
    "siteName": {
      "selectors": [
        "#projectName",
        "input[name='projectName']",
        "input[aria-label*='Project name']"
      ],
      "type": "input",
      "fillMethod": "value",
      "required": false,
      "validation": "required"
    },
    "siteUrl": {
      "selectors": [
        "#projectUrl",
        "input[name='projectUrl']",
        "input[type='url']"
      ],
      "type": "input",
      "fillMethod": "value",
      "required": false,
      "validation": "required"
    },
    "contactEmail": {
      "selectors": [
        "#email",
        "input[name='email']",
        "input[type='email']"
      ],
      "type": "input",
      "fillMethod": "value",
      "required": false,
      "validation": "required"
    },
    "logo": {
      "selectors": [
        "#logo",
        "input[name='logo']",
        "input[type='file']"
      ],
      "type": "input",
      "fillMethod": "value",
      "required": false,
      "validation": "required"
    }
  },
  "formInfo": {
    "description": "ProjectHunt 项目提交表单，用于收录新工具/产品",
    "submitSelector": ".flex-grow.py-2.px-4.border.border-transparent.rounded-md.shadow-sm.text-sm.font-medium.text-white.bg-[#e66418].hover:bg-[#d65702].focus:outline-none.focus:ring-2.focus:ring-offset-1.focus:ring-[#f77925].disabled:opacity-50.disabled:cursor-not-allowed",
    "totalFields": 4,
    "notes": [
      "表单通过GET方法提交到带UTM参数的URL",
      "所有字段均为必填",
      "缺少描述字段，需额外补充"
    ]
  },
  "metadata": {
    "generatedBy": "AI",
    "generatedAt": "2025-07-16T02:07:57.400Z",
    "version": "3.0.0",
    "aiModel": "moonshotai/Kimi-K2-Instruct"
  }
};

// 自定义处理函数 (可选)
export function handleProjecthuntMeSubmission(data, rule) {
  console.log('Processing ProjectHunt form submission...');
  
  const processedData = { ...data };
  
  // 在这里添加特殊处理逻辑
  // 例如：URL格式化、字段验证、默认值设置等
  
  return processedData;
}

// 自定义元素填写函数 (可选)
export async function customFillElement(element, value, config) {
  console.log('🔧 ProjectHunt 自定义填写函数被调用:', element, value);
  
  // 在这里添加特殊的元素填写逻辑
  // 例如：处理特殊的UI组件、异步操作等
  
  return false; // 返回 false 使用默认填写方法
}