// UBC CTLT AI 网站规则配置
// 网站: https://ai.ctlt.ubc.ca/submit-an-event-resource-or-tool/
// 最后更新: 2025-07-24

export const SITE_RULE = {
  domain: 'ai.ctlt.ubc.ca',
  siteName: 'UBC CTLT AI',
  priority: 1,
  lastUpdated: '2025-07-24',

  fieldMappings: {
    firstName: {
      selectors: [
        '#input_2_1_3',
        'input[name="input_1.3"]',
        '.name_first input'
      ],
      method: 'value',
      validation: 'optional',
      notes: '名字'
    },

    lastName: {
      selectors: [
        '#input_2_1_6',
        'input[name="input_1.6"]',
        '.name_last input'
      ],
      method: 'value',
      validation: 'optional',
      notes: '姓氏'
    },

    contactEmail: {
      selectors: [
        '#input_2_3',
        'input[name="input_3"]',
        'input[type="email"]'
      ],
      method: 'value',
      validation: 'required|email',
      notes: '联系邮箱'
    },

    siteUrl: {
      selectors: [
        '#input_2_4',
        'input[name="input_4"]',
        'input[type="url"]'
      ],
      method: 'value',
      validation: 'required|url',
      notes: '事件/资源/工具的URL'
    },

    siteName: {
      selectors: [
        '#input_2_7',
        'input[name="input_7"]',
        '.ginput_container_post_title input'
      ],
      method: 'value',
      validation: 'required',
      notes: '标题'
    },

    siteDescription: {
      selectors: [
        '#input_2_9',
        'textarea[name="input_9"]',
        '.ginput_container_post_excerpt textarea'
      ],
      method: 'value',
      validation: 'required',
      notes: '简短描述，最多20个词'
    },

    detailedIntro: {
      selectors: [
        '#input_2_8',
        'textarea[name="input_8"]',
        '.ginput_container_textarea textarea'
      ],
      method: 'value',
      validation: 'optional',
      notes: '详细描述，可选'
    },

    category: {
      selectors: [
        'input[name="input_6"][value="409618"]',
        '#choice_2_6_2',
        'input[type="radio"]:last-of-type'
      ],
      method: 'radio',
      validation: 'required',
      defaultValue: '409618',
      notes: '分类选择，默认选择Tools'
    }
  },

  submitConfig: {
    submitButton: '#gform_submit_button_2, .gform_button',
    submitMethod: 'click',
    successIndicators: ['.gform_confirmation_message'],
    errorIndicators: ['.gfield_error']
  },

  specialHandling: {
    requiresLogin: false,
    hasCaptcha: true,
    hasFileUpload: false,
    customScript: 'handleUBCCTLTSubmission',
    formValidation: {
      requiredFields: ['contactEmail', 'siteUrl', 'siteName', 'siteDescription', 'category'],
      emailValidation: true,
      urlValidation: true
    },
    notes: [
      '使用Gravity Forms构建',
      '有reCAPTCHA v3验证',
      '有Akismet反垃圾邮件',
      '简短描述限制20个词',
      '详细描述为可选',
      '学术机构表单'
    ]
  }
};

export function handleUBCCTLTSubmission(data, rule) {
  const processedData = { ...data };

  if (processedData.siteUrl && !processedData.siteUrl.startsWith('http')) {
    processedData.siteUrl = 'https://' + processedData.siteUrl;
  }

  // 自动选择Tools分类
  const toolsRadio = document.querySelector('#choice_2_6_2');
  if (toolsRadio) {
    toolsRadio.checked = true;
  }

  return processedData;
}

export async function customFillElement(element, value, config) {
  if (element.type === 'radio') {
    element.checked = true;
    element.dispatchEvent(new Event('change', { bubbles: true }));
    return true;
  }

  if (element.tagName === 'INPUT' || element.tagName === 'TEXTAREA') {
    element.value = value;
    element.dispatchEvent(new Event('input', { bubbles: true }));
    element.dispatchEvent(new Event('change', { bubbles: true }));
    return true;
  }

  return false;
}