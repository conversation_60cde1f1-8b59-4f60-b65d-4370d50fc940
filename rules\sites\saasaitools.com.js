// SaaSAITools.com 网站规则配置
// 网站: https://saasaitools.com/submit/
// 最后更新: 2025-07-24

export const SITE_RULE = {
  // 基本信息
  domain: 'saasaitools.com',
  siteName: 'SaaSAITools',
  priority: 1,
  lastUpdated: '2025-07-24',

  // 字段映射规则
  fieldMappings: {
    // 姓名 -> fullName
    fullName: {
      selectors: [
        'input[name="input_text"]',
        '#ff_6_input_text',
        'input[placeholder="Enter your first name"]'
      ],
      method: 'value',
      validation: 'optional',
      notes: '提交者姓名'
    },

    // 邮箱 -> contactEmail
    contactEmail: {
      selectors: [
        'input[name="email"]',
        '#ff_6_email',
        'input[type="email"]'
      ],
      method: 'value',
      validation: 'optional|email',
      notes: '联系邮箱'
    },

    // 标签 -> keywords
    keywords: {
      selectors: [
        'input[name="post_tag"]',
        '#ff_6_post_tag',
        'input[placeholder="Type..."]'
      ],
      method: 'value',
      validation: 'optional',
      notes: '产品标签'
    },

    // 产品名称 -> siteName
    siteName: {
      selectors: [
        'input[name="post_title"]',
        '#ff_6_post_title',
        'input[placeholder*="name of your product"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '产品名称'
    },

    // 分类选择 -> category
    category: {
      selectors: [
        'select[name="listing-category"]',
        '#ff_6_listing-category'
      ],
      method: 'select',
      validation: 'required',
      notes: '产品分类选择'
    },

    // 产品标语 -> siteDescription
    siteDescription: {
      selectors: [
        'input[name="input_text_1"]',
        '#ff_6_input_text_1',
        'input[placeholder*="short product tagline"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '产品简短标语'
    },

    // 产品描述 -> detailedIntro
    detailedIntro: {
      selectors: [
        'textarea[name="description"]',
        '#ff_6_description',
        'textarea[placeholder*="overview and description"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '产品详细描述'
    },

    // 网站URL -> siteUrl
    siteUrl: {
      selectors: [
        'input[name="url"]',
        '#ff_6_url',
        'input[type="url"]'
      ],
      method: 'value',
      validation: 'required|url',
      notes: '网站URL'
    },

    // Twitter用户名 -> twitterUrl
    twitterUrl: {
      selectors: [
        'input[name="input_text_2"]',
        '#ff_6_input_text_2',
        'input[placeholder="@username"]'
      ],
      method: 'value',
      validation: 'required',
      notes: 'Twitter用户名'
    },

    // 产品图片 -> logoFile
    logoFile: {
      selectors: [
        'input[name="featured_image"]',
        '#ff_6_featured_image',
        'input[type="file"]'
      ],
      method: 'file',
      validation: 'required',
      notes: '产品图片上传，理想尺寸1366x768'
    },

    // 付费类型 -> pricing (复选框)
    pricing: {
      selectors: [
        'input[name="checkbox[]"]',
        'input[value="Free"]',
        'input[value="Freemium (you have both free and paid options available)"]',
        'input[value="Paid"]'
      ],
      method: 'checkbox',
      validation: 'required',
      notes: '付费类型选择'
    },

    // 关键特性 -> features
    features: {
      selectors: [
        'textarea[name="description_1"]',
        '#ff_6_description_1',
        'textarea[placeholder*="key features"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '产品关键特性'
    },

    // 使用场景 -> targetAudience
    targetAudience: {
      selectors: [
        'textarea[name="description_2"]',
        '#ff_6_description_2',
        'textarea[placeholder*="use cases"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '产品使用场景'
    }
  },

  // 提交流程配置
  submitConfig: {
    submitButton: 'button[type="submit"], .ff-btn-submit',
    submitMethod: 'click',
    waitAfterFill: 2000,
    waitAfterSubmit: 5000,
    successIndicators: ['.success-message'],
    errorIndicators: ['.ff-errors-in-stack']
  },

  // 特殊处理
  specialHandling: {
    requiresLogin: false,
    hasCaptcha: false,
    hasFileUpload: true,
    customScript: 'handleSaasaitoolsSubmission',
    formValidation: {
      requiredFields: ['siteName', 'category', 'siteDescription', 'detailedIntro', 'siteUrl', 'twitterUrl', 'logoFile', 'pricing', 'features', 'targetAudience'],
      emailValidation: true,
      urlValidation: true
    },
    notes: [
      'SaaS AI工具提交平台',
      'FluentForm表单系统',
      '需要上传产品图片',
      '复选框付费类型选择',
      '英文界面',
      'Twitter用户名需要@前缀'
    ]
  }
};

// 自定义处理函数
export function handleSaasaitoolsSubmission(data, rule) {
  console.log('Processing SaaSAITools form submission...');

  const processedData = { ...data };

  // 确保URL格式正确
  if (processedData.siteUrl && !processedData.siteUrl.startsWith('http')) {
    processedData.siteUrl = 'https://' + processedData.siteUrl;
  }

  // 处理Twitter用户名格式
  if (processedData.twitterUrl) {
    // 如果包含完整URL，提取用户名
    if (processedData.twitterUrl.includes('twitter.com/') || processedData.twitterUrl.includes('x.com/')) {
      const match = processedData.twitterUrl.match(/(?:twitter\.com\/|x\.com\/)([^\/\?]+)/);
      if (match) {
        processedData.twitterUrl = '@' + match[1];
      }
    } else if (!processedData.twitterUrl.startsWith('@')) {
      // 如果没有@前缀，添加@
      processedData.twitterUrl = '@' + processedData.twitterUrl.replace(/^@/, '');
    }
  }

  return processedData;
}

// 自定义元素填写函数
export async function customFillElement(element, value, config) {
  // 处理下拉选择框
  if (element.tagName === 'SELECT') {
    const options = element.querySelectorAll('option');
    let selectedOption;

    // 智能匹配分类
    for (const option of options) {
      if (option.value) {
        const optionText = option.textContent.trim();
        if (optionText.includes('Business') || optionText.includes('Writing') || optionText.includes('Text & Speech')) {
          selectedOption = option;
          break;
        }
      }
    }

    // 如果没找到合适的，选择第一个非空选项
    if (!selectedOption) {
      selectedOption = Array.from(options).find(opt => opt.value !== '');
    }

    if (selectedOption) {
      element.value = selectedOption.value;
      element.dispatchEvent(new Event('change', { bubbles: true }));
      return true;
    }
  }

  // 处理复选框
  if (element.type === 'checkbox') {
    // 默认选择Freemium选项
    if (element.value.includes('Freemium')) {
      element.checked = true;
      element.dispatchEvent(new Event('change', { bubbles: true }));
      return true;
    }
  }

  // 处理文件上传
  if (element.type === 'file') {
    console.warn('文件上传需要手动操作');
    return false;
  }

  // 处理标准输入框和文本域
  if (element.tagName === 'INPUT' || element.tagName === 'TEXTAREA') {
    element.value = value;
    element.dispatchEvent(new Event('input', { bubbles: true }));
    element.dispatchEvent(new Event('change', { bubbles: true }));
    return true;
  }

  return false;
}