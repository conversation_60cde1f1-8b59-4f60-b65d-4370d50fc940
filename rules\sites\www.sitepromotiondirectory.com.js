// SitePromotionDirectory.com 网站规则配置
// 网站: https://www.sitepromotiondirectory.com/submit.php
// 最后更新: 2025-07-24

export const SITE_RULE = {
  domain: 'www.sitepromotiondirectory.com',
  siteName: 'Site Promotion Directory',
  priority: 1,
  lastUpdated: '2025-07-24',

  fieldMappings: {
    linkType: {
      selectors: [
        'input[name="LINK_TYPE"][value="normal"]',
        'input[type="radio"][value="normal"]',
        'input[value="normal"]'
      ],
      method: 'radio',
      validation: 'required',
      defaultValue: 'normal',
      notes: '链接类型，默认选择免费Regular links'
    },

    siteName: {
      selectors: [
        'input[name="TITLE"]',
        '.text[name="TITLE"]',
        'td.label:contains("Title") + td input'
      ],
      method: 'value',
      validation: 'required',
      notes: '网站标题，最多100字符'
    },

    siteUrl: {
      selectors: [
        'input[name="URL"]',
        '.text[name="URL"]',
        'td.label:contains("URL") + td input'
      ],
      method: 'value',
      validation: 'optional|url',
      notes: '网站URL，最多255字符'
    },

    siteDescription: {
      selectors: [
        'textarea[name="DESCRIPTION"]',
        '.text[name="DESCRIPTION"]',
        'td.label:contains("Description") textarea'
      ],
      method: 'value',
      validation: 'optional',
      notes: '网站描述，最多1000字符'
    },

    keywords: {
      selectors: [
        'input[name="META_KEYWORDS"]',
        '.text[name="META_KEYWORDS"]',
        'td.label:contains("META Keywords") + td input'
      ],
      method: 'value',
      validation: 'optional',
      notes: 'META关键词，用逗号分隔'
    },

    metaDescription: {
      selectors: [
        'textarea[name="META_DESCRIPTION"]',
        '.text[name="META_DESCRIPTION"]',
        'td.label:contains("META Description") + td textarea'
      ],
      method: 'value',
      validation: 'optional',
      notes: 'META描述，最多250字符'
    },

    fullName: {
      selectors: [
        'input[name="OWNER_NAME"]',
        '.text[name="OWNER_NAME"]',
        'td.label:contains("Your Name") + td input'
      ],
      method: 'value',
      validation: 'required',
      notes: '提交者姓名，最多50字符'
    },

    contactEmail: {
      selectors: [
        'input[name="OWNER_EMAIL"]',
        '.text[name="OWNER_EMAIL"]',
        'td.label:contains("Your Email") + td input'
      ],
      method: 'value',
      validation: 'required|email',
      notes: '联系邮箱，最多255字符'
    },

    category: {
      selectors: [
        'select[name="CATEGORY_ID"]',
        'td.label:contains("Category") + td select'
      ],
      method: 'select',
      validation: 'required',
      defaultValue: '1',
      notes: '分类选择，默认Arts & Humanities'
    },

    reciprocalUrl: {
      selectors: [
        'input[name="RECPR_URL"]',
        '.text[name="RECPR_URL"]',
        'td.label:contains("Reciprocal Link URL") + td input'
      ],
      method: 'value',
      validation: 'optional|url',
      notes: '互惠链接URL'
    },

    captcha: {
      selectors: [
        'input[name="CAPTCHA"]',
        '#CAPTCHA',
        'td.label:contains("Enter the code") + td input'
      ],
      method: 'value',
      validation: 'required',
      notes: '验证码，需要手动输入'
    },

    agreeRules: {
      selectors: [
        'input[name="AGREERULES"]',
        '#AGREERULES',
        'input[type="checkbox"]'
      ],
      method: 'checkbox',
      validation: 'required',
      defaultValue: true,
      notes: '同意提交规则'
    }
  },

  submitConfig: {
    submitButton: 'input[name="submit"], .btn',
    submitMethod: 'click',
    successIndicators: ['.success-message'],
    errorIndicators: ['.error-message']
  },

  specialHandling: {
    requiresLogin: false,
    hasCaptcha: true,
    hasFileUpload: false,
    customScript: 'handleSitePromotionDirectorySubmission',
    formValidation: {
      requiredFields: ['linkType', 'siteName', 'fullName', 'contactEmail', 'category', 'captcha', 'agreeRules'],
      emailValidation: true,
      urlValidation: true
    },
    notes: [
      '网站目录提交平台',
      '有付费和免费选项',
      '包含SEO元数据字段',
      '支持互惠链接',
      '有图片验证码',
      '需要同意提交规则',
      '字符数限制严格'
    ]
  }
};

export function handleSitePromotionDirectorySubmission(data, rule) {
  const processedData = { ...data };

  // URL格式化
  if (processedData.siteUrl && !processedData.siteUrl.startsWith('http')) {
    processedData.siteUrl = 'https://' + processedData.siteUrl;
  }

  if (processedData.reciprocalUrl && !processedData.reciprocalUrl.startsWith('http')) {
    processedData.reciprocalUrl = 'https://' + processedData.reciprocalUrl;
  }

  // 自动选择免费Regular links
  const normalRadio = document.querySelector('input[name="LINK_TYPE"][value="normal"]');
  if (normalRadio) {
    normalRadio.checked = true;
  }

  // 自动选择默认分类
  const categorySelect = document.querySelector('select[name="CATEGORY_ID"]');
  if (categorySelect) {
    categorySelect.value = '1';
  }

  // 自动勾选同意规则
  const agreeCheckbox = document.querySelector('#AGREERULES');
  if (agreeCheckbox) {
    agreeCheckbox.checked = true;
  }

  return processedData;
}

export async function customFillElement(element, value, config) {
  // 处理单选按钮
  if (element.type === 'radio' && element.value === 'normal') {
    element.checked = true;
    element.dispatchEvent(new Event('change', { bubbles: true }));
    return true;
  }

  // 处理下拉选择框
  if (element.tagName === 'SELECT') {
    const options = element.querySelectorAll('option');
    const option = Array.from(options).find(opt => opt.value === '1');
    if (option) {
      element.value = option.value;
      element.dispatchEvent(new Event('change', { bubbles: true }));
      return true;
    }
  }

  // 处理复选框
  if (element.type === 'checkbox') {
    element.checked = Boolean(value);
    element.dispatchEvent(new Event('change', { bubbles: true }));
    return true;
  }

  // 处理验证码字段
  if (element.name === 'CAPTCHA') {
    console.warn('验证码需要手动输入');
    return false;
  }

  if (element.tagName === 'INPUT' || element.tagName === 'TEXTAREA') {
    element.value = value;
    element.dispatchEvent(new Event('input', { bubbles: true }));
    element.dispatchEvent(new Event('change', { bubbles: true }));
    return true;
  }

  return false;
}