// AI Site Submitter - ToolsApp 规则配置
// 自动生成于: 2025/7/16 09:45:30
// 域名: toolsapp.cc

export const SITE_RULE = {
  "domain": "toolsapp.cc",
  "siteName": "ToolsApp",
  "lastUpdated": "2025-07-16T01:45:30.604Z",
  "fieldMappings": {
    "siteName": {
      "selectors": [
        "input[name='website']",
        "input[placeholder*='ToolsApp']",
        "label:contains('Website Name') + input"
      ],
      "type": "input",
      "fillMethod": "value",
      "required": false,
      "validation": "required"
    },
    "siteUrl": {
      "selectors": [
        "input[name='url']",
        "input[placeholder*='https://']",
        "label:contains('Website URL') + input"
      ],
      "type": "input",
      "fillMethod": "value",
      "required": false,
      "validation": "required"
    }
  },
  "formInfo": {
    "description": "ToolsApp AI工具目录提交表单",
    "submitSelector": "button[type='submit']",
    "totalFields": 2,
    "notes": [
      "表单仅包含网站名称和URL两个必填字段",
      "提交前需要在网站首页添加指向https://toolsapp.cc/的反向链接"
    ]
  },
  "metadata": {
    "generatedBy": "AI",
    "generatedAt": "2025-07-16T01:45:30.604Z",
    "version": "3.0.0",
    "aiModel": "moonshotai/Kimi-K2-Instruct"
  }
};

// 自定义处理函数 (可选)
export function handleToolsappCcSubmission(data, rule) {
  console.log('Processing ToolsApp form submission...');
  
  const processedData = { ...data };
  
  // 在这里添加特殊处理逻辑
  // 例如：URL格式化、字段验证、默认值设置等
  
  return processedData;
}

// 自定义元素填写函数 (可选)
export async function customFillElement(element, value, config) {
  console.log('🔧 ToolsApp 自定义填写函数被调用:', element, value);
  
  // 在这里添加特殊的元素填写逻辑
  // 例如：处理特殊的UI组件、异步操作等
  
  return false; // 返回 false 使用默认填写方法
}