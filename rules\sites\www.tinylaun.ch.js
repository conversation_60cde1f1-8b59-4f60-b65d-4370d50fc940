// TinyLaunch 网站规则配置
// 网站: https://www.tinylaun.ch/dashboard/startups/new
// 最后更新: 2025-08-01

export const SITE_RULE = {
  // 基本信息
  domain: 'www.tinylaun.ch',
  siteName: 'TinyLaunch',
  priority: 1,
  lastUpdated: '2025-08-01',
  
  // 字段映射规则
  fieldMappings: {
    // 创业公司名称 -> Startup Name
    siteName: {
      selectors: [
        'input[name="name"]',
        'input#name',
        'input[placeholder*="Your startup\'s name"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '创业公司名称，必填字段'
    },

    // 标语 -> Tagline
    siteDescription: {
      selectors: [
        'input[name="tagline"]',
        'input#tagline',
        'input[placeholder*="One-line description"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '一句话描述，必填字段'
    },

    // 网站URL -> Website URL
    siteUrl: {
      selectors: [
        'input[name="url"]',
        'input#url',
        'input[placeholder*="https://example.com"]'
      ],
      method: 'value',
      validation: 'required|url',
      notes: '网站URL地址，必填字段'
    },

    // 详细描述 -> Description (TipTap富文本编辑器)
    detailedIntro: {
      selectors: [
        '.tiptap.ProseMirror[contenteditable="true"]',
        'div[contenteditable="true"].tiptap',
        'input[name="description"][type="hidden"]'
      ],
      method: 'tiptap',
      validation: 'required',
      notes: '详细描述，使用TipTap富文本编辑器，必填字段'
    },

    // 分类 -> Category
    category: {
      selectors: [
        'select[name="category_id"]',
        'select#category'
      ],
      method: 'select',
      validation: 'required',
      options: [
        '1', '2', '3', '4', '5', '6', '7', '8', '9', '10',
        '11', '12', '13', '14', '15', '16', '17', '18', '19', '20',
        '21', '22', '23', '24', '25', '26', '27', '28', '29', '30',
        '31', '32', '33', '34', '35', '36'
      ],
      defaultValue: '1', // SaaS & Tools
      notes: '产品分类，下拉选择，必填字段'
    }
  },
  
  // 提交流程配置
  submitConfig: {
    submitButton: 'button[type="submit"], button:contains("Create Startup")',
    submitMethod: 'click',
    waitAfterFill: 2000,
    waitAfterSubmit: 5000,
    successIndicators: [
      '.success-message',
      '.thank-you',
      '.submission-success'
    ],
    errorIndicators: [
      '.error-message',
      '.validation-error',
      '.form-error'
    ]
  },
  
  // 特殊处理
  specialHandling: {
    requiresLogin: true, // 需要登录
    hasCaptcha: false,
    hasFileUpload: true, // 有Logo上传
    hasTiptapEditor: true,
    
    // 表单验证规则
    formValidation: {
      requiredFields: ['siteName', 'siteDescription', 'siteUrl', 'detailedIntro', 'category'],
      emailValidation: false,
      urlValidation: true,
      fileUploadValidation: true
    },
    
    // 特殊注意事项
    notes: [
      '需要登录才能访问表单',
      '包含Logo文件上传功能',
      '使用TipTap富文本编辑器',
      '分类选项丰富，包含多个行业',
      '所有字段都是必填项'
    ]
  }
};

// 自定义处理函数
export function handleTinyLaunchSubmission(data, rule) {
  console.log('Processing TinyLaunch submission...');
  
  const processedData = { ...data };
  
  // 确保URL格式正确
  if (processedData.siteUrl && !processedData.siteUrl.startsWith('http')) {
    processedData.siteUrl = 'https://' + processedData.siteUrl;
  }
  
  // 处理分类选择
  if (!processedData.category) {
    processedData.category = '1'; // 默认选择SaaS & Tools
  }
  
  return processedData;
}

// 自定义元素填写函数
export async function customFillElement(element, value, config) {
  console.log(`TinyLaunch自定义填写: ${element.tagName}, 方法: ${config.method}`);

  switch (config.method) {
    case 'tiptap':
      // TipTap富文本编辑器处理
      const tiptapEditor = element.closest('.tiptap') || element;
      if (tiptapEditor && tiptapEditor.contentEditable === 'true') {
        tiptapEditor.focus();
        await new Promise(resolve => setTimeout(resolve, 300));

        // 清空现有内容
        tiptapEditor.innerHTML = '';
        
        // 创建段落元素并插入内容
        const p = document.createElement('p');
        p.textContent = value;
        p.className = 'mb-4';
        tiptapEditor.appendChild(p);
        
        // 更新隐藏字段
        const hiddenInput = document.querySelector('input[name="description"][type="hidden"]');
        if (hiddenInput) {
          hiddenInput.value = value;
        }
        
        // 触发输入事件
        tiptapEditor.dispatchEvent(new Event('input', { bubbles: true }));
        tiptapEditor.dispatchEvent(new Event('change', { bubbles: true }));
        
        console.log(`✓ 填写TipTap字段: "${value}"`);
        return true;
      }
      return false;

    case 'value':
      // 标准输入框处理
      element.focus();
      await new Promise(resolve => setTimeout(resolve, 200));

      element.value = value;
      element.dispatchEvent(new Event('input', { bubbles: true }));
      element.dispatchEvent(new Event('change', { bubbles: true }));
      element.dispatchEvent(new Event('blur', { bubbles: true }));

      console.log(`✓ 填写字段: ${element.name || element.id} = "${value}"`);
      return true;

    case 'select':
      // 下拉选择处理
      element.focus();
      await new Promise(resolve => setTimeout(resolve, 200));

      element.value = value;
      element.dispatchEvent(new Event('change', { bubbles: true }));

      console.log(`✓ 选择分类: ${value}`);
      return true;

    default:
      element.value = value;
      element.dispatchEvent(new Event('input', { bubbles: true }));
      return true;
  }

  return false;
}
