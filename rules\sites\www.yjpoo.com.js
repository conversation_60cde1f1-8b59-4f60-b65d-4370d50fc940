// Yjpoo.com 网站规则配置
// 网站: https://www.yjpoo.com/submit-ai-tool/
// 最后更新: 2025-07-24

export const SITE_RULE = {
  // 基本信息
  domain: 'www.yjpoo.com',
  siteName: 'Yjpoo',
  priority: 1,
  lastUpdated: '2025-07-24',

  // 字段映射规则
  fieldMappings: {
    // 网站名称 -> siteName
    siteName: {
      selectors: [
        'input[name="attr_28"]',
        '#attr_28',
        'input[placeholder="网站名称"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '网站名称'
    },

    // 网站链接 -> siteUrl
    siteUrl: {
      selectors: [
        'input[name="attr_30"]',
        '#attr_30',
        'input[placeholder="网站链接"]'
      ],
      method: 'value',
      validation: 'required|url',
      notes: '网站URL地址'
    },

    // 详细介绍 -> detailedIntro
    detailedIntro: {
      selectors: [
        'textarea[name="attr_38"]',
        '#attr_38',
        'textarea[placeholder*="详细介绍"]'
      ],
      method: 'value',
      validation: 'required',
      notes: 'AI产品详细介绍，包括功能特色、应用等'
    }
  },

  // 提交流程配置
  submitConfig: {
    submitButton: 'input[type="submit"][value="提交"]',
    submitMethod: 'click',
    waitAfterFill: 2000,
    waitAfterSubmit: 5000,
    successIndicators: ['.success-message'],
    errorIndicators: ['.error-message']
  },

  // 特殊处理
  specialHandling: {
    requiresLogin: false,
    hasCaptcha: false,
    hasFileUpload: false,
    customScript: 'handleYjpooSubmission',
    formValidation: {
      requiredFields: ['siteName', 'siteUrl', 'detailedIntro'],
      emailValidation: false,
      urlValidation: true
    },
    notes: [
      '映技派AI工具收录平台',
      '简洁的三字段表单',
      '24小时内审核',
      '需要详细介绍',
      '中文界面'
    ]
  }
};

// 自定义处理函数
export function handleYjpooSubmission(data, rule) {
  console.log('Processing Yjpoo form submission...');

  const processedData = { ...data };

  // 确保URL格式正确
  if (processedData.siteUrl && !processedData.siteUrl.startsWith('http')) {
    processedData.siteUrl = 'https://' + processedData.siteUrl;
  }

  return processedData;
}

// 自定义元素填写函数
export async function customFillElement(element, value, config) {
  // 处理标准输入框和文本域
  if (element.tagName === 'INPUT' || element.tagName === 'TEXTAREA') {
    element.value = value;
    element.dispatchEvent(new Event('input', { bubbles: true }));
    element.dispatchEvent(new Event('change', { bubbles: true }));
    return true;
  }

  return false;
}