// AIValley.ai 网站规则配置
// 网站: https://aivalley.ai/submit-tool/
// 最后更新: 2025-07-06

export const SITE_RULE = {
  // 基本信息
  domain: 'aivalley.ai',
  siteName: 'AI Valley',
  priority: 1,
  lastUpdated: '2025-07-06',
  
  // 字段映射规则
  fieldMappings: {
    // 联系人姓名 -> Name
    fullName: {
      selectors: [
        'input[name*="name"]:not([name*="tool"])',
        'input[placeholder*="Name"]',
        'input[aria-label*="Name"]',
        '#name'
      ],
      method: 'value',
      validation: 'required',
      notes: '提交者姓名'
    },
    
    // 联系邮箱 -> Email
    contactEmail: {
      selectors: [
        'input[name*="email"]',
        'input[type="email"]',
        'input[placeholder*="Email"]',
        '#email'
      ],
      method: 'value',
      validation: 'required|email',
      notes: '联系邮箱地址'
    },
    
    // 工具名称 -> Tool Name
    siteName: {
      selectors: [
        'input[name*="tool"][name*="name"]',
        'input[placeholder*="Tool Name"]',
        'input[aria-label*="Tool Name"]',
        '#tool-name'
      ],
      method: 'value',
      validation: 'required',
      notes: '工具名称'
    },
    
    // 工具URL -> Tool URL
    siteUrl: {
      selectors: [
        'input[name*="tool"][name*="url"]',
        'input[placeholder*="Tool URL"]',
        'input[type="url"]',
        '#tool-url'
      ],
      method: 'value',
      validation: 'required|url',
      notes: '工具网站URL'
    },
    
    // 工具详细描述 -> Tool Description (使用detailedIntro)
    detailedIntro: {
      selectors: [
        'textarea[name="your-message"]:first-of-type',
        'textarea.wpcf7-textarea:first-of-type',
        'textarea[cols="40"][rows="10"]:first-of-type'
      ],
      method: 'value',
      validation: 'optional',
      notes: '工具详细描述，使用website-info.js中的detailedIntro字段'
    },

    // 工具简短描述 -> Tool Short Description (使用siteDescription)
    siteDescription: {
      selectors: [
        'textarea[name="your-message"]:last-of-type',
        'textarea.wpcf7-textarea:last-of-type',
        'textarea[cols="40"][rows="10"]:last-of-type'
      ],
      method: 'value',
      validation: 'optional',
      notes: '工具简短描述，使用website-info.js中的siteDescription字段'
    },
    
    // 分类 -> Categories
    category: {
      selectors: [
        'select[name*="categor"]',
        'select[name*="category"]',
        'select[aria-label*="Categories"]',
        '#categories'
      ],
      method: 'select',
      validation: 'required',
      options: [
        'AI Tools', 'AI Assistant', 'Art', 'Audio', 'Automation', 'Avatar', 
        'Build AI', 'Career', 'ChatBots', 'Code Assistant', 'Content Creation', 
        'Copywriting', 'Data Analyst', 'Design', 'Developer Tools', 'Ecommerce', 
        'Educational', 'Email Assistant', 'Experiments', 'Fashion', 'Finance', 
        'Fun Tools', 'Gaming', 'Gift Ideas', 'Healthcare', 'Image Editing', 
        'Image Generator', 'Lead Generation', 'Legal Assistant', 'Life Assistant', 
        'Logo Generator', 'Marketing', 'Music', 'Open-Source', 'Presentations', 
        'Productivity', 'Prompt Tool', 'Real Estate', 'Research', 'Resources', 
        'Search Engine', 'SEO', 'Social Media Assistant', 'Spreadsheets', 
        'Startup Tools', 'Storytelling', 'Summarizer', 'Text-to-image', 
        'Text-to-speech', 'Travel', 'Video Editing', 'Video Generator', 'Writing'
      ],
      defaultValue: 'AI Tools',
      notes: '工具分类，默认选择AI Tools'
    }
  },
  
  // 自定义填写函数
  customFillElement: async function(element, value, config) {
    console.log(`AIValley自定义填写: ${element.name || element.id}, 方法: ${config.method}`);

    switch (config.method) {
      case 'value':
        // Contact Form 7 特殊处理
        element.focus();
        await new Promise(resolve => setTimeout(resolve, 100));

        // 对于textarea字段，需要特殊处理
        if (element.tagName.toLowerCase() === 'textarea') {
          // 检查是否已经填写过，避免重复填写同名字段
          if (element.value && element.value.trim() !== '') {
            console.log(`跳过已填写的textarea: ${element.name}`);
            return;
          }
        }

        element.value = value;
        element.dispatchEvent(new Event('input', { bubbles: true }));
        element.dispatchEvent(new Event('change', { bubbles: true }));
        element.dispatchEvent(new Event('blur', { bubbles: true }));
        break;

      case 'select':
        // WordPress下拉选择处理
        if (element.tagName.toLowerCase() === 'select') {
          const targetValue = config.defaultValue || value;

          // 尝试精确匹配
          let option = Array.from(element.options).find(opt =>
            opt.value === targetValue || opt.text === targetValue
          );

          // 如果没找到，尝试部分匹配
          if (!option) {
            option = Array.from(element.options).find(opt =>
              opt.text.includes(targetValue) || targetValue.includes(opt.text)
            );
          }

          if (option) {
            element.value = option.value;
            element.dispatchEvent(new Event('change', { bubbles: true }));
            console.log(`✓ 选择分类: ${option.text || option.value}`);
          }
        }
        break;

      default:
        element.value = value;
        element.dispatchEvent(new Event('input', { bubbles: true }));
        element.dispatchEvent(new Event('change', { bubbles: true }));
    }
  },
  
  // 提交流程配置
  submitConfig: {
    submitButton: 'input[type="submit"], button[type="submit"], .submit-button, #submit',
    submitMethod: 'click',
    waitAfterFill: 2000,
    waitAfterSubmit: 5000,
    successIndicators: [
      '.success-message',
      '.thank-you',
      '[class*="success"]'
    ],
    errorIndicators: [
      '.error-message',
      '.validation-error',
      '[class*="error"]'
    ]
  },
  
  // 特殊处理
  specialHandling: {
    requiresLogin: false,
    hasCaptcha: false,
    hasFileUpload: false,
    
    // 表单验证规则
    formValidation: {
      requiredFields: ['fullName', 'contactEmail', 'siteName', 'siteUrl', 'category'],
      emailValidation: true,
      urlValidation: true
    },
    
    // 特殊注意事项
    notes: [
      '这是WordPress网站，使用标准表单',
      '有6个字段，其中4个必填，2个可选',
      '分类选项非常丰富，包含50+个选项',
      '默认分类选择AI Tools',
      '支持工具描述和简短描述两个描述字段'
    ]
  }
};

// 自定义处理函数
export function handleAIValleySubmission(data, rule) {
  console.log('Processing AI Valley submission...');

  const processedData = { ...data };

  // 确保URL格式正确
  if (processedData.siteUrl && !processedData.siteUrl.startsWith('http')) {
    processedData.siteUrl = 'https://' + processedData.siteUrl;
  }

  // 设置默认分类
  if (!processedData.category) {
    processedData.category = 'AI Tools';
  }

  return processedData;
}
