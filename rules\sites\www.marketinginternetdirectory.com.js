// AI Site Submitter - MarketingInternetDirectory 规则配置
// 自动生成于: 2025/7/15 19:19:08
// 域名: www.marketinginternetdirectory.com

export const SITE_RULE = {
  "domain": "www.marketinginternetdirectory.com",
  "siteName": "MarketingInternetDirectory",
  "lastUpdated": "2025-07-15T11:19:08.246Z",
  "fieldMappings": {
    "siteTitle": {
      "selectors": [
        "[name='TITLE']",
        "input[name='TITLE']"
      ],
      "type": "input",
      "fillMethod": "value",
      "required": false,
      "validation": "required"
    },
    "siteUrl": {
      "selectors": [
        "[name='URL']",
        "input[name='URL']"
      ],
      "type": "input",
      "fillMethod": "value",
      "required": false,
      "validation": "required"
    },
    "siteDescription": {
      "selectors": [
        "[name='DESCRIPTION']",
        "textarea[name='DESCRIPTION']"
      ],
      "type": "input",
      "fillMethod": "value",
      "required": false,
      "validation": "required"
    },
    "keywords": {
      "selectors": [
        "[name='META_KEYWORDS']",
        "input[name='META_KEYWORDS']"
      ],
      "type": "input",
      "fillMethod": "value",
      "required": false,
      "validation": "optional"
    },
    "detailedIntro": {
      "selectors": [
        "[name='META_DESCRIPTION']",
        "textarea[name='META_DESCRIPTION']"
      ],
      "type": "input",
      "fillMethod": "value",
      "required": false,
      "validation": "optional"
    },
    "fullName": {
      "selectors": [
        "[name='OWNER_NAME']",
        "input[name='OWNER_NAME']"
      ],
      "type": "input",
      "fillMethod": "value",
      "required": false,
      "validation": "optional"
    },
    "contactEmail": {
      "selectors": [
        "[name='OWNER_EMAIL']",
        "input[name='OWNER_EMAIL']"
      ],
      "type": "input",
      "fillMethod": "value",
      "required": false,
      "validation": "optional"
    },
    "agreeTerms": {
      "selectors": [
        "#AGREERULES",
        "input[type='checkbox']#AGREERULES"
      ],
      "type": "input",
      "fillMethod": "value",
      "defaultValue": true,
      "required": false,
      "validation": "required"
    }
  },
  "formInfo": {
    "description": "Marketing Internet Directory网站提交表单",
    "submitSelector": "[name='submit']",
    "totalFields": 12,
    "notes": [
      "表单包含网站基本信息、SEO元数据、所有者信息及条款同意",
      "无分类选择器，为通用目录提交",
      "LINK_TYPE为付费选项，默认不选择"
    ]
  },
  "metadata": {
    "generatedBy": "AI",
    "generatedAt": "2025-07-15T11:19:08.246Z",
    "version": "3.0.0",
    "aiModel": "moonshotai/Kimi-K2-Instruct"
  }
};

// 自定义处理函数 (可选)
export function handleWwwMarketinginternetdirectoryComSubmission(data, rule) {
  console.log('Processing MarketingInternetDirectory form submission...');
  
  const processedData = { ...data };
  
  // 在这里添加特殊处理逻辑
  // 例如：URL格式化、字段验证、默认值设置等
  
  return processedData;
}

// 自定义元素填写函数 (可选)
export async function customFillElement(element, value, config) {
  console.log('🔧 MarketingInternetDirectory 自定义填写函数被调用:', element, value);
  
  // 在这里添加特殊的元素填写逻辑
  // 例如：处理特殊的UI组件、异步操作等
  
  return false; // 返回 false 使用默认填写方法
}