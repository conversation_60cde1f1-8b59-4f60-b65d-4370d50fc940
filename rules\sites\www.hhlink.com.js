// Hhlink.com 网站规则配置
// 网站: https://www.hhlink.com/提交新网站
// 最后更新: 2025-07-24

export const SITE_RULE = {
  // 基本信息
  domain: 'www.hhlink.com',
  siteName: 'Hhlink',
  priority: 1,
  lastUpdated: '2025-07-24',

  // 字段映射规则
  fieldMappings: {
    // 网站域名 -> siteUrl
    siteUrl: {
      selectors: [
        'input[name="ctl00$cphContent$tbLinkURL"]',
        '#ctl00_cphContent_tbLinkURL'
      ],
      method: 'value',
      validation: 'required|url',
      notes: '网站域名，不包含http://'
    },

    // 所属分类 -> category
    category: {
      selectors: [
        'select[name="ctl00$cphContent$ddLinkRoot"]',
        '#ctl00_cphContent_ddLinkRoot'
      ],
      method: 'select',
      validation: 'required',
      notes: '网站分类选择'
    },

    // 所在地区 -> region
    region: {
      selectors: [
        'select[name="ctl00$cphContent$ddArea"]',
        '#ctl00_cphContent_ddArea'
      ],
      method: 'select',
      validation: 'required',
      notes: '网站所在地区'
    },

    // 语言版本 -> 复选框
    languageChinese: {
      selectors: [
        'input[name="ctl00$cphContent$cbEn2"]',
        '#ctl00_cphContent_cbEn2'
      ],
      method: 'checkbox',
      validation: 'optional',
      notes: '中文版本'
    },

    languageEnglish: {
      selectors: [
        'input[name="ctl00$cphContent$cbEn1"]',
        '#ctl00_cphContent_cbEn1'
      ],
      method: 'checkbox',
      validation: 'optional',
      notes: '英文版本'
    },

    // 网站中文名称 -> siteName
    siteName: {
      selectors: [
        'input[name="ctl00$cphContent$tbLinkName2"]',
        '#ctl00_cphContent_tbLinkName2'
      ],
      method: 'value',
      validation: 'required',
      notes: '网站中文名称，最多50字符'
    },

    // 中文详细介绍 -> detailedIntro
    detailedIntro: {
      selectors: [
        'textarea[name="ctl00$cphContent$tbLinkText2"]',
        '#ctl00_cphContent_tbLinkText2'
      ],
      method: 'value',
      validation: 'required',
      notes: '中文详细介绍'
    },

    // 网站英文名称 -> siteTitle
    siteTitle: {
      selectors: [
        'input[name="ctl00$cphContent$tbLinkName1"]',
        '#ctl00_cphContent_tbLinkName1'
      ],
      method: 'value',
      validation: 'required',
      notes: '网站英文名称，最多100字符'
    },

    // 英文详细介绍 -> siteDescription
    siteDescription: {
      selectors: [
        'textarea[name="ctl00$cphContent$tbLinkText1"]',
        '#ctl00_cphContent_tbLinkText1'
      ],
      method: 'value',
      validation: 'required',
      notes: '英文详细介绍'
    },

    // 验证代码 -> captcha
    captcha: {
      selectors: [
        'input[name="ctl00$cphContent$tbPassGif"]',
        '#ctl00_cphContent_tbPassGif'
      ],
      method: 'value',
      validation: 'required',
      notes: '验证码，需要手动输入'
    },

    // 免责声明同意 -> agreement
    agreement: {
      selectors: [
        'input[name="ctl00$cphContent$rbLegal"]',
        '#ctl00_cphContent_rbLegal_0'
      ],
      method: 'radio',
      validation: 'required',
      notes: '同意免责声明'
    }
  },

  // 提交流程配置
  submitConfig: {
    submitButton: 'input[name="ctl00$cphContent$btAddNew"], #ctl00_cphContent_btAddNew',
    submitMethod: 'click',
    waitAfterFill: 2000,
    waitAfterSubmit: 5000,
    successIndicators: ['.success-message'],
    errorIndicators: ['.error-message', 'span[style*="color:Red"]']
  },

  // 特殊处理
  specialHandling: {
    requiresLogin: false,
    hasCaptcha: true,
    hasFileUpload: false,
    customScript: 'handleHhlinkSubmission',
    formValidation: {
      requiredFields: ['siteUrl', 'category', 'region', 'siteName', 'detailedIntro', 'siteTitle', 'siteDescription', 'captcha', 'agreement'],
      emailValidation: false,
      urlValidation: true
    },
    notes: [
      '海外导航网站提交平台',
      '双语表单（中英文）',
      '需要验证码',
      '需要同意免责声明',
      'ASP.NET WebForms架构',
      '复杂的表单验证'
    ]
  }
};

// 自定义处理函数
export function handleHhlinkSubmission(data, rule) {
  console.log('Processing Hhlink form submission...');

  const processedData = { ...data };

  // 处理URL格式 - 移除http://前缀
  if (processedData.siteUrl) {
    processedData.siteUrl = processedData.siteUrl.replace(/^https?:\/\//, '');
  }

  // 处理中文名称长度限制
  if (processedData.siteName && processedData.siteName.length > 50) {
    processedData.siteName = processedData.siteName.substring(0, 50);
  }

  // 处理英文名称长度限制
  if (processedData.siteTitle && processedData.siteTitle.length > 100) {
    processedData.siteTitle = processedData.siteTitle.substring(0, 100);
  }

  return processedData;
}

// 自定义元素填写函数
export async function customFillElement(element, value, config) {
  // 处理下拉选择框
  if (element.tagName === 'SELECT') {
    const options = element.querySelectorAll('option');
    let selectedOption;

    // 智能匹配分类
    if (element.name.includes('ddLinkRoot')) {
      for (const option of options) {
        if (option.value !== '0') {
          const optionText = option.textContent.trim();
          if (optionText.includes('门户导航') || optionText.includes('商业专业')) {
            selectedOption = option;
            break;
          }
        }
      }
    }

    // 智能匹配地区
    if (element.name.includes('ddArea')) {
      for (const option of options) {
        if (option.value !== '0') {
          const optionText = option.textContent.trim();
          if (optionText.includes('中国大陆') || optionText.includes('美国')) {
            selectedOption = option;
            break;
          }
        }
      }
    }

    // 如果没找到合适的，选择第一个非默认选项
    if (!selectedOption) {
      selectedOption = Array.from(options).find(opt => opt.value !== '0');
    }

    if (selectedOption) {
      element.value = selectedOption.value;
      element.dispatchEvent(new Event('change', { bubbles: true }));
      return true;
    }
  }

  // 处理复选框
  if (element.type === 'checkbox') {
    element.checked = true;
    element.dispatchEvent(new Event('change', { bubbles: true }));
    return true;
  }

  // 处理单选框
  if (element.type === 'radio') {
    element.checked = true;
    element.dispatchEvent(new Event('change', { bubbles: true }));
    return true;
  }

  // 处理验证码字段
  if (element.name && element.name.includes('tbPassGif')) {
    console.warn('验证码需要手动输入');
    return false;
  }

  // 处理标准输入框和文本域
  if (element.tagName === 'INPUT' || element.tagName === 'TEXTAREA') {
    element.value = value;
    element.dispatchEvent(new Event('input', { bubbles: true }));
    element.dispatchEvent(new Event('change', { bubbles: true }));
    return true;
  }

  return false;
}