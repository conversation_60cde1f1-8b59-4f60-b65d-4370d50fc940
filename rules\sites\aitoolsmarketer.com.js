// AiToolsMarketer.com 网站规则配置
// 网站: https://aitoolsmarketer.com/submit/
// 最后更新: 2025-07-09

export const SITE_RULE = {
  // 基本信息
  domain: 'aitoolsmarketer.com',
  siteName: 'AiToolsMarketer',
  priority: 1,
  lastUpdated: '2025-07-09',
  
  // 字段映射规则
  fieldMappings: {
    // 邮箱 -> Your Email
    contactEmail: {
      selectors: [
        'input[name="email"]',
        '#ff_5_email',
        'input[type="email"]'
      ],
      method: 'value',
      validation: 'required|email',
      notes: '联系邮箱地址'
    },
    
    // 工具名称 -> Tool Name
    siteName: {
      selectors: [
        'input[name="input_text"]',
        '#ff_5_input_text',
        'input[placeholder="Enter your tool name"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '工具名称'
    },
    
    // 网站URL -> Website URL
    siteUrl: {
      selectors: [
        'input[name="input_text_1"]',
        '#ff_5_input_text_1',
        'input[placeholder="Enter your website URL"]'
      ],
      method: 'value',
      validation: 'required|url',
      notes: '网站URL地址'
    },
    
    // AI分类 -> AI Category (选择Marketing AI)
    category: {
      selectors: [
        'select[name="multi_select[]"]',
        '#ff_5_multi_select',
        '.choices__input'
      ],
      method: 'select',
      validation: 'required',
      targetValue: 'Marketing AI',
      defaultValue: 'Marketing AI',
      notes: 'AI工具分类，选择Marketing AI'
    },
    
    // 工具定价 -> Tool Pricing (选择Free)
    pricing: {
      selectors: [
        'select[name="dropdown_1"]',
        '#ff_5_dropdown_1'
      ],
      method: 'select',
      validation: 'required',
      targetValue: 'Free',
      defaultValue: 'Free',
      notes: '工具定价，选择Free'
    },
    
    // 联盟计划URL -> Affiliate Program URL (可选)
    affiliateUrl: {
      selectors: [
        'input[name="input_text_2"]',
        '#ff_5_input_text_2'
      ],
      method: 'value',
      validation: 'optional',
      notes: '联盟计划URL（可选）'
    },
    
    // 工具描述 -> Tool Description
    detailedIntro: {
      selectors: [
        'textarea[name="description"]',
        '#ff_5_description',
        'textarea[placeholder*="Write all tool details"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '工具详细描述'
    }
  },
  
  // 提交流程配置
  submitConfig: {
    submitButton: 'button[type="submit"], .ff-btn-submit',
    submitMethod: 'click',
    waitAfterFill: 3000, // 填写后等待3秒
    waitAfterSubmit: 5000, // 提交后等待5秒
    successIndicators: [
      '.ff-message-success',
      '.success-message',
      '.alert-success'
    ],
    errorIndicators: [
      '.ff-errors-in-stack',
      '.error-message',
      '.alert-error'
    ]
  },
  
  // 特殊处理
  specialHandling: {
    requiresLogin: false,
    hasCaptcha: true, // 有reCAPTCHA和Turnstile验证
    hasFileUpload: false,
    
    // 表单验证规则
    formValidation: {
      requiredFields: ['contactEmail', 'siteName', 'siteUrl', 'category', 'pricing', 'detailedIntro'],
      emailValidation: true,
      urlValidation: true
    },
    
    // 特殊注意事项
    notes: [
      '使用FluentForm构建的WordPress表单',
      '包含reCAPTCHA和Cloudflare Turnstile双重验证',
      '表单包含7个字段：邮箱、工具名称、URL、分类、定价、联盟URL、描述',
      '使用Choices.js多选组件处理分类选择',
      '有严格的内容审核政策',
      '24-48小时内人工审核',
      '需要处理验证码挑战'
    ]
  }
};

// 自定义处理函数
export function handleAiToolsMarketerSubmission(data, rule) {
  console.log('Processing AiToolsMarketer.com submission...');
  
  // 特殊处理逻辑
  const processedData = { ...data };
  
  // 确保URL格式正确
  if (processedData.siteUrl && !processedData.siteUrl.startsWith('http')) {
    processedData.siteUrl = 'https://' + processedData.siteUrl;
  }
  
  // 设置默认分类为Marketing AI
  processedData.category = 'Marketing AI';
  
  // 设置默认定价为Free
  processedData.pricing = 'Free';
  
  // 处理联盟URL（可选）
  if (processedData.affiliateUrl && !processedData.affiliateUrl.startsWith('http')) {
    processedData.affiliateUrl = 'https://' + processedData.affiliateUrl;
  }
  
  return processedData;
}

// 自定义元素填写函数，专门处理FluentForm和Choices.js组件
export async function customFillElement(element, value, config) {
  console.log('🔧 AiToolsMarketer自定义填写函数被调用:', element, value);
  
  // 处理FluentForm输入字段
  if (element.classList.contains('ff-el-form-control')) {
    try {
      element.value = value;
      element.dispatchEvent(new Event('input', { bubbles: true }));
      element.dispatchEvent(new Event('change', { bubbles: true }));
      element.dispatchEvent(new Event('blur', { bubbles: true }));
      console.log('✅ 使用FluentForm字段填写:', value);
      return true;
    } catch (error) {
      console.warn('FluentForm字段填写失败:', error);
    }
  }
  
  // 处理Choices.js多选组件
  if (element.classList.contains('choices__input') || element.closest('.choices')) {
    try {
      // 查找并点击对应的选项
      const choiceItem = document.querySelector(`[data-value="${config.targetValue}"]`);
      if (choiceItem) {
        choiceItem.click();
        console.log('✅ 点击Choices.js选项:', config.defaultValue);
        return true;
      }
      
      // 备用方法：通过文本匹配
      const textChoice = Array.from(document.querySelectorAll('.choices__item--choice')).find(
        item => item.textContent.includes(config.targetValue)
      );
      if (textChoice) {
        textChoice.click();
        console.log('✅ 通过文本匹配点击Choices.js选项:', config.defaultValue);
        return true;
      }
    } catch (error) {
      console.warn('Choices.js组件处理失败:', error);
    }
  }
  
  // 处理普通select下拉框
  if (element.tagName === 'SELECT') {
    try {
      element.value = config.targetValue;
      element.dispatchEvent(new Event('change', { bubbles: true }));
      console.log('✅ 选择下拉选项:', config.defaultValue);
      return true;
    } catch (error) {
      console.warn('Select下拉框处理失败:', error);
    }
  }
  
  // 处理验证码提示
  if (element.classList.contains('g-recaptcha') || element.id.includes('turnstile')) {
    console.log('⚠️ 检测到验证码，需要用户手动完成验证');
    return false; // 让用户手动处理验证码
  }
  
  // 默认处理
  return false;
}
