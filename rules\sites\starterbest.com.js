// StarterBest 网站规则配置
// 网站: https://starterbest.com/submit
// 最后更新: 2025-08-01

export const SITE_RULE = {
  // 基本信息
  domain: 'starterbest.com',
  siteName: 'StarterBest',
  priority: 1,
  lastUpdated: '2025-08-01',
  
  // 字段映射规则
  fieldMappings: {
    // 产品链接 -> Link
    siteUrl: {
      selectors: [
        'input[name="link"]',
        'input[placeholder*="Enter the link to your product"]'
      ],
      method: 'value',
      validation: 'required|url',
      notes: '产品链接，必填字段'
    },

    // 产品名称 -> Name
    siteName: {
      selectors: [
        'input[name="name"]',
        'input[placeholder*="Enter the name of your product"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '产品名称，必填字段'
    },

    // YouTube链接 -> YouToBeLink (可选)
    youtubeUrl: {
      selectors: [
        'input[name="youToBeLink"]',
        'input[placeholder*="Enter the youToBeLink of your product"]'
      ],
      method: 'value',
      validation: 'optional|url',
      notes: 'YouTube链接，可选字段'
    },

    // GitHub链接 -> GithubLink (可选)
    githubUrl: {
      selectors: [
        'input[name="githubLink"]',
        'input[placeholder*="Enter the githubLink of your product"]'
      ],
      method: 'value',
      validation: 'optional|url',
      notes: 'GitHub链接，可选字段'
    },

    // 简短描述 -> Description
    siteDescription: {
      selectors: [
        'textarea[name="description"]',
        'textarea[placeholder*="Enter a brief description of your product"]'
      ],
      method: 'textarea',
      validation: 'required',
      notes: '产品简短描述，必填字段'
    },

    // 详细介绍 -> Introduction (EasyMDE Markdown编辑器)
    detailedIntro: {
      selectors: [
        '.CodeMirror textarea[tabindex="0"]',
        '#simplemde-editor-2',
        '.EasyMDEContainer .CodeMirror textarea'
      ],
      method: 'easymde',
      validation: 'optional',
      notes: '详细介绍，使用EasyMDE Markdown编辑器'
    }
  },
  
  // 提交流程配置
  submitConfig: {
    submitButton: 'button[type="submit"], button:contains("Submit")',
    submitMethod: 'click',
    waitAfterFill: 2000,
    waitAfterSubmit: 5000,
    successIndicators: [
      '.success-message',
      '.thank-you',
      '.submission-success'
    ],
    errorIndicators: [
      '.error-message',
      '.validation-error',
      '.form-error'
    ]
  },
  
  // 特殊处理
  specialHandling: {
    requiresLogin: false,
    hasCaptcha: false,
    hasFileUpload: true, // 有Icon和Image上传
    hasEasyMDEEditor: true, // 使用EasyMDE编辑器
    hasDropdowns: true, // 有Categories和Tags下拉选择
    hasAIAutofill: true, // 有AI自动填充功能
    
    // 表单验证规则
    formValidation: {
      requiredFields: ['siteUrl', 'siteName', 'siteDescription'],
      optionalFields: ['youtubeUrl', 'githubUrl', 'detailedIntro'],
      emailValidation: false,
      urlValidation: true,
      fileUploadValidation: true
    },
    
    // 特殊注意事项
    notes: [
      '支持AI自动填充功能',
      '包含Icon (1:1) 和 Image (16:9) 上传',
      '使用EasyMDE Markdown编辑器',
      '有Categories和Tags下拉选择',
      '支持YouTube和GitHub链接'
    ]
  }
};

// 自定义处理函数
export function handleStarterBestSubmission(data, rule) {
  console.log('Processing StarterBest submission...');
  
  const processedData = { ...data };
  
  // 确保URL格式正确
  if (processedData.siteUrl && !processedData.siteUrl.startsWith('http')) {
    processedData.siteUrl = 'https://' + processedData.siteUrl;
  }
  
  if (processedData.youtubeUrl && !processedData.youtubeUrl.startsWith('http')) {
    processedData.youtubeUrl = 'https://' + processedData.youtubeUrl;
  }
  
  if (processedData.githubUrl && !processedData.githubUrl.startsWith('http')) {
    processedData.githubUrl = 'https://' + processedData.githubUrl;
  }
  
  return processedData;
}

// 自定义元素填写函数
export async function customFillElement(element, value, config) {
  console.log(`StarterBest自定义填写: ${element.tagName}, 方法: ${config.method}`);

  switch (config.method) {
    case 'easymde':
      // EasyMDE Markdown编辑器处理
      const easyMDEContainer = element.closest('.EasyMDEContainer');
      if (easyMDEContainer) {
        const textarea = easyMDEContainer.querySelector('.CodeMirror textarea[tabindex="0"]');
        if (textarea) {
          textarea.focus();
          await new Promise(resolve => setTimeout(resolve, 300));
          
          textarea.value = value;
          textarea.dispatchEvent(new Event('input', { bubbles: true }));
          textarea.dispatchEvent(new Event('change', { bubbles: true }));
          
          console.log(`✓ 填写EasyMDE字段: "${value}"`);
          return true;
        }
      }
      return false;

    case 'textarea':
      // 文本域处理
      element.focus();
      await new Promise(resolve => setTimeout(resolve, 200));

      element.value = value;
      element.dispatchEvent(new Event('input', { bubbles: true }));
      element.dispatchEvent(new Event('change', { bubbles: true }));

      console.log(`✓ 填写文本域: "${value}"`);
      return true;

    case 'value':
      // 标准输入框处理
      element.focus();
      await new Promise(resolve => setTimeout(resolve, 200));

      element.value = value;
      element.dispatchEvent(new Event('input', { bubbles: true }));
      element.dispatchEvent(new Event('change', { bubbles: true }));
      element.dispatchEvent(new Event('blur', { bubbles: true }));

      console.log(`✓ 填写字段: ${element.name || element.id} = "${value}"`);
      return true;

    default:
      element.value = value;
      element.dispatchEvent(new Event('input', { bubbles: true }));
      return true;
  }

  return false;
}
