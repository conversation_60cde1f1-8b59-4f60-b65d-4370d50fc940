// Seminal.ai 网站规则配置
// 网站: https://seminal.ai/submit-tool/
// 最后更新: 2025-07-06

export const SITE_RULE = {
  // 基本信息
  domain: 'seminal.ai',
  siteName: 'Seminal AI',
  priority: 1,
  lastUpdated: '2025-07-06',
  
  // 字段映射规则
  fieldMappings: {
    // 工具名称 -> Tool Name
    siteName: {
      selectors: [
        'input[name*="tool"][name*="name"]',
        'input[placeholder*="Tool Name"]',
        'input[aria-label*="Tool Name"]',
        '#tool-name'
      ],
      method: 'value',
      validation: 'required',
      notes: '工具名称'
    },
    
    // 网站URL -> Website URL
    siteUrl: {
      selectors: [
        'input[name*="website"]',
        'input[name*="url"]',
        'input[type="url"]',
        'input[placeholder*="Website URL"]'
      ],
      method: 'value',
      validation: 'required|url',
      notes: '工具网站URL'
    },
    
    // 分类 -> Category
    category: {
      selectors: [
        'select[name*="category"]',
        'select[aria-label*="Category"]',
        '#category'
      ],
      method: 'select',
      validation: 'required',
      options: [
        'Productivity',
        'Creativity', 
        'Writing',
        'Coding',
        'Business'
      ],
      defaultValue: 'Productivity',
      notes: '工具分类，默认选择Productivity'
    },
    
    // 工具描述 -> Description
    siteDescription: {
      selectors: [
        'textarea[name*="description"]',
        'textarea[placeholder*="Description"]',
        'textarea[aria-label*="Description"]',
        '#description'
      ],
      method: 'value',
      validation: 'required',
      notes: '工具描述'
    },
    
    // 提交者姓名 -> Your Name
    fullName: {
      selectors: [
        'input[name*="name"]:not([name*="tool"])',
        'input[placeholder*="Your Name"]',
        'input[aria-label*="Your Name"]',
        '#your-name'
      ],
      method: 'value',
      validation: 'required',
      notes: '提交者姓名'
    },
    
    // 联系邮箱 -> Your Email
    contactEmail: {
      selectors: [
        'input[name*="email"]',
        'input[type="email"]',
        'input[placeholder*="Your Email"]',
        '#your-email'
      ],
      method: 'value',
      validation: 'required|email',
      notes: '联系邮箱地址'
    }
  },
  
  // 自定义填写函数
  customFillElement: async function(element, value, config) {
    console.log(`Seminal AI自定义填写: ${element.name || element.id}, 方法: ${config.method}`);
    
    switch (config.method) {
      case 'value':
        // 标准表单处理
        element.focus();
        await new Promise(resolve => setTimeout(resolve, 100));
        
        element.value = value;
        element.dispatchEvent(new Event('input', { bubbles: true }));
        element.dispatchEvent(new Event('change', { bubbles: true }));
        element.dispatchEvent(new Event('blur', { bubbles: true }));
        break;
        
      case 'select':
        // 下拉选择处理
        if (element.tagName.toLowerCase() === 'select') {
          const targetValue = config.defaultValue || value;
          
          // 尝试精确匹配
          let option = Array.from(element.options).find(opt => 
            opt.value === targetValue || opt.text === targetValue
          );
          
          // 如果没找到，尝试部分匹配
          if (!option) {
            option = Array.from(element.options).find(opt => 
              opt.text.includes(targetValue) || targetValue.includes(opt.text)
            );
          }
          
          if (option) {
            element.value = option.value;
            element.dispatchEvent(new Event('change', { bubbles: true }));
            console.log(`✓ 选择分类: ${option.text || option.value}`);
          }
        }
        break;
        
      default:
        element.value = value;
        element.dispatchEvent(new Event('input', { bubbles: true }));
        element.dispatchEvent(new Event('change', { bubbles: true }));
    }
  },
  
  // 提交流程配置
  submitConfig: {
    submitButton: 'button[type="submit"], input[type="submit"], .submit-button, #submit',
    submitMethod: 'click',
    waitAfterFill: 2000,
    waitAfterSubmit: 5000,
    successIndicators: [
      '.success-message',
      '.thank-you',
      '[class*="success"]'
    ],
    errorIndicators: [
      '.error-message',
      '.validation-error',
      '[class*="error"]'
    ]
  },
  
  // 特殊处理
  specialHandling: {
    requiresLogin: false,
    hasCaptcha: false,
    hasFileUpload: false,
    
    // 表单验证规则
    formValidation: {
      requiredFields: ['siteName', 'siteUrl', 'category', 'siteDescription', 'fullName', 'contactEmail'],
      emailValidation: true,
      urlValidation: true
    },
    
    // 特殊注意事项
    notes: [
      '简洁的表单设计，6个必填字段',
      '分类选项较少，只有5个选项',
      '默认分类选择Productivity',
      '审核时间5-7个工作日',
      '由The Seminal Church运营'
    ]
  }
};

// 自定义处理函数
export function handleSeminalAISubmission(data, rule) {
  console.log('Processing Seminal AI submission...');
  
  const processedData = { ...data };
  
  // 确保URL格式正确
  if (processedData.siteUrl && !processedData.siteUrl.startsWith('http')) {
    processedData.siteUrl = 'https://' + processedData.siteUrl;
  }
  
  // 设置默认分类
  if (!processedData.category) {
    processedData.category = 'Productivity';
  }
  
  return processedData;
}
