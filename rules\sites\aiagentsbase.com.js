// AiAgentsBase.com 网站规则配置
// 网站: https://aiagentsbase.com/submit
// 最后更新: 2025-07-09

export const SITE_RULE = {
  // 基本信息
  domain: 'aiagentsbase.com',
  siteName: 'AiAgentsBase',
  priority: 1,
  lastUpdated: '2025-07-09',
  
  // 字段映射规则
  fieldMappings: {
    // 邮箱地址 -> Email address
    contactEmail: {
      selectors: [
        'input[name="Email address"]',
        'input[type="email"]',
        'input[placeholder*="email"]'
      ],
      method: 'value',
      validation: 'required|email',
      notes: '联系邮箱地址'
    },
    
    // 代理名称 -> Agent name
    siteName: {
      selectors: [
        'input[name="Tool name"]',
        'input[placeholder*="agent name"]',
        'input[placeholder*="Enter agent name"]'
      ],
      method: 'value',
      validation: 'required',
      notes: 'AI代理名称'
    },
    
    // 网站 -> Website
    siteUrl: {
      selectors: [
        'input[name="Website"]',
        'input[type="url"]',
        'input[placeholder="https://domain.com"]'
      ],
      method: 'value',
      validation: 'required|url',
      notes: '网站URL地址'
    },
    
    // 分类 -> Category (选择Content & Creation)
    category: {
      selectors: [
        'select[name="Category"]',
        'select.framer-form-input'
      ],
      method: 'select',
      validation: 'required',
      targetValue: 'content-creation',
      defaultValue: 'Content & Creation',
      notes: '产品分类，选择Content & Creation'
    },
    
    // 简短标语 -> Short tagline
    tagline: {
      selectors: [
        'input[name="Tagline"]',
        'input[placeholder*="short explainer"]',
        'input[placeholder*="10-12 words"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '简短标语（10-12个词）'
    },
    
    // 中等标语 -> Medium tagline
    siteDescription: {
      selectors: [
        'textarea[name="Description"]',
        'textarea[placeholder*="more details"]',
        'textarea[placeholder*="110 words"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '详细描述（最多110个词）'
    }
  },
  
  // 提交流程配置
  submitConfig: {
    submitButton: 'button[type="submit"], button[name="form-button"]',
    submitMethod: 'click',
    waitAfterFill: 2000, // 填写后等待2秒
    waitAfterSubmit: 3000, // 提交后等待3秒
    successIndicators: [
      '.success-message',
      '.alert-success',
      '.notification-success',
      '[class*="success"]'
    ],
    errorIndicators: [
      '.error-message',
      '.alert-error',
      '.alert-danger',
      '[class*="error"]'
    ]
  },
  
  // 特殊处理
  specialHandling: {
    requiresLogin: false,
    hasCaptcha: false,
    hasFileUpload: false,
    
    // 表单验证规则
    formValidation: {
      requiredFields: ['contactEmail', 'siteName', 'siteUrl', 'category', 'tagline', 'siteDescription'],
      emailValidation: true,
      urlValidation: true
    },
    
    // 特殊注意事项
    notes: [
      '使用Framer构建的现代化表单',
      '表单包含6个字段：邮箱、代理名称、网站、分类、简短标语、详细描述',
      '所有字段都是必填的',
      '分类自动选择Content & Creation',
      '简短标语限制10-12个词',
      '详细描述限制110个词',
      '使用自定义CSS变量和样式'
    ]
  }
};

// 自定义处理函数
export function handleAiAgentsBaseSubmission(data, rule) {
  console.log('Processing AiAgentsBase.com submission...');
  
  // 特殊处理逻辑
  const processedData = { ...data };
  
  // 确保URL格式正确
  if (processedData.siteUrl && !processedData.siteUrl.startsWith('http')) {
    processedData.siteUrl = 'https://' + processedData.siteUrl;
  }
  
  // 确保分类设置为Content & Creation
  processedData.category = 'content-creation';
  
  // 处理简短标语，确保不超过10-12个词
  if (processedData.tagline && processedData.tagline.split(' ').length > 12) {
    const words = processedData.tagline.split(' ');
    processedData.tagline = words.slice(0, 12).join(' ');
  }
  
  // 处理详细描述，确保不超过110个词
  if (processedData.siteDescription && processedData.siteDescription.split(' ').length > 110) {
    const words = processedData.siteDescription.split(' ');
    processedData.siteDescription = words.slice(0, 110).join(' ');
  }
  
  return processedData;
}

// 自定义元素填写函数，专门处理Framer组件
export async function customFillElement(element, value, config) {
  console.log('🔧 AiAgentsBase自定义填写函数被调用:', element, value);
  
  // 处理Framer表单输入组件
  if (element.classList.contains('framer-form-input')) {
    try {
      // 设置值
      element.value = value;
      
      // 移除empty类
      element.classList.remove('framer-form-input-empty');
      
      // 触发事件
      element.dispatchEvent(new Event('input', { bubbles: true }));
      element.dispatchEvent(new Event('change', { bubbles: true }));
      element.dispatchEvent(new Event('blur', { bubbles: true }));
      
      console.log('✅ 使用Framer组件填写:', value);
      return true;
    } catch (error) {
      console.warn('Framer组件填写失败:', error);
    }
  }
  
  // 处理select下拉选择
  if (element.tagName === 'SELECT' && config.targetValue) {
    try {
      element.value = config.targetValue;
      element.dispatchEvent(new Event('change', { bubbles: true }));
      console.log('✅ 选择下拉选项:', config.defaultValue);
      return true;
    } catch (error) {
      console.warn('下拉选择失败:', error);
    }
  }
  
  // 处理textarea
  if (element.tagName === 'TEXTAREA') {
    try {
      element.value = value;
      element.classList.remove('framer-form-input-empty');
      element.dispatchEvent(new Event('input', { bubbles: true }));
      element.dispatchEvent(new Event('change', { bubbles: true }));
      console.log('✅ 使用textarea填写:', value);
      return true;
    } catch (error) {
      console.warn('Textarea填写失败:', error);
    }
  }
  
  // 默认处理
  return false;
}
