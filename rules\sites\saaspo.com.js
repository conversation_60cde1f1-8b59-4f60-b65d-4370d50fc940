// SaaSPO.com 网站规则配置
// 网站: https://saaspo.com/submit
// 最后更新: 2025-07-24

export const SITE_RULE = {
  domain: 'saaspo.com',
  siteName: 'SaaSPO',
  priority: 1,
  lastUpdated: '2025-07-24',

  fieldMappings: {
    fullName: {
      selectors: [
        '#Name',
        'input[name="Name"]',
        'input[placeholder="Your name"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '提交者姓名'
    },

    contactEmail: {
      selectors: [
        '#Email',
        'input[name="Email"]',
        'input[placeholder="Your email"]'
      ],
      method: 'value',
      validation: 'required|email',
      notes: '联系邮箱'
    },

    twitterUrl: {
      selectors: [
        '#Twitter',
        'input[name="Twitter"]',
        'input[placeholder="Your Twitter @"]'
      ],
      method: 'value',
      validation: 'optional',
      notes: 'Twitter用户名，含@符号'
    },

    siteUrl: {
      selectors: [
        '#Website-URL',
        'input[name="Website-URL"]',
        'input[placeholder="Website URL"]'
      ],
      method: 'value',
      validation: 'required|url',
      notes: '网站URL'
    }
  },

  submitConfig: {
    submitButton: 'input[type="submit"], .button',
    submitMethod: 'click',
    successIndicators: ['.success-message', '.w-form-done'],
    errorIndicators: ['.error-message', '.w-form-fail']
  },

  specialHandling: {
    requiresLogin: false,
    hasCaptcha: true,
    hasFileUpload: false,
    customScript: 'handleSaaSPOSubmission',
    formValidation: {
      requiredFields: ['fullName', 'contactEmail', 'siteUrl'],
      emailValidation: true,
      urlValidation: true
    },
    notes: [
      '设计展示平台',
      '使用Webflow构建',
      '有Cloudflare Turnstile验证',
      '简洁的4字段表单',
      'Twitter关注推广',
      '专注优秀设计收录'
    ]
  }
};

export function handleSaaSPOSubmission(data, rule) {
  const processedData = { ...data };

  if (processedData.siteUrl && !processedData.siteUrl.startsWith('http')) {
    processedData.siteUrl = 'https://' + processedData.siteUrl;
  }

  // Twitter用户名处理，确保有@符号
  if (processedData.twitterUrl && !processedData.twitterUrl.startsWith('@')) {
    processedData.twitterUrl = '@' + processedData.twitterUrl;
  }

  return processedData;
}

export async function customFillElement(element, value, config) {
  // 处理Webflow表单输入
  if (element.tagName === 'INPUT') {
    element.value = value;
    element.dispatchEvent(new Event('input', { bubbles: true }));
    element.dispatchEvent(new Event('change', { bubbles: true }));
    return true;
  }

  return false;
}