// AIToGrow.com 网站规则配置
// 网站: https://aitogrow.com/#send-your-tool
// 最后更新: 2025-07-24

export const SITE_RULE = {
  domain: 'aitogrow.com',
  siteName: 'AI To Grow',
  priority: 1,
  lastUpdated: '2025-07-24',

  fieldMappings: {
    fullName: {
      selectors: [
        'input[name="your-name"]',
        'input[placeholder="Name"]',
        '.wpcf7-text:first-of-type'
      ],
      method: 'value',
      validation: 'required',
      notes: '提交者姓名'
    },

    contactEmail: {
      selectors: [
        'input[name="email"]',
        '.wpcf7-email',
        'input[type="email"]',
        'input[placeholder="Email"]'
      ],
      method: 'value',
      validation: 'required|email',
      notes: '联系邮箱'
    },

    siteUrl: {
      selectors: [
        'input[name="url"]',
        'input[type="url"]',
        'input[placeholder="Your Tool URL"]'
      ],
      method: 'value',
      validation: 'required|url',
      notes: '工具网站链接'
    },

    siteDescription: {
      selectors: [
        'textarea[name="textarea"]',
        'textarea[placeholder="Message"]',
        '.wpcf7-textarea'
      ],
      method: 'value',
      validation: 'optional',
      notes: '工具描述信息'
    }
  },

  submitConfig: {
    submitButton: 'input[type="submit"], .wpcf7-submit',
    submitMethod: 'click',
    successIndicators: ['.wpcf7-mail-sent-ok'],
    errorIndicators: ['.wpcf7-validation-errors']
  },

  specialHandling: {
    requiresLogin: false,
    hasCaptcha: false,
    hasFileUpload: false,
    customScript: 'handleAIToGrowSubmission',
    formValidation: {
      requiredFields: ['fullName', 'contactEmail', 'siteUrl'],
      emailValidation: true,
      urlValidation: true
    },
    notes: [
      '使用Contact Form 7表单',
      '表单在页面锚点#send-your-tool',
      '所有字段使用placeholder而非label',
      '提交按钮文本为Send',
      '有spinner加载动画'
    ]
  }
};

export function handleAIToGrowSubmission(data, rule) {
  const processedData = { ...data };

  if (processedData.siteUrl && !processedData.siteUrl.startsWith('http')) {
    processedData.siteUrl = 'https://' + processedData.siteUrl;
  }

  return processedData;
}

export async function customFillElement(element, value, config) {
  if (element.tagName === 'INPUT' || element.tagName === 'TEXTAREA') {
    element.value = value;
    element.dispatchEvent(new Event('input', { bubbles: true }));
    element.dispatchEvent(new Event('change', { bubbles: true }));
    return true;
  }

  return false;
}