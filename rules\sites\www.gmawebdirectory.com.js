// AI Site Submitter - GmaWebDirectory 规则配置
// 自动生成于: 2025/7/15 19:31:11
// 域名: www.gmawebdirectory.com

export const SITE_RULE = {
  "domain": "www.gmawebdirectory.com",
  "siteName": "GmaWebDirectory",
  "lastUpdated": "2025-07-15T11:31:11.153Z",
  "fieldMappings": {
    "siteTitle": {
      "selectors": [
        "[name='TITLE']",
        "input[name='TITLE']",
        "input[type='text'][name='TITLE']"
      ],
      "type": "input",
      "fillMethod": "value",
      "required": false,
      "validation": "required"
    },
    "siteUrl": {
      "selectors": [
        "[name='URL']",
        "input[name='URL']",
        "input[type='text'][name='URL']"
      ],
      "type": "input",
      "fillMethod": "value",
      "required": false,
      "validation": "required"
    },
    "siteDescription": {
      "selectors": [
        "[name='DESCRIPTION']",
        "textarea[name='DESCRIPTION']",
        "textarea"
      ],
      "type": "input",
      "fillMethod": "value",
      "required": false,
      "validation": "required"
    },
    "contactEmail": {
      "selectors": [
        "[name='OWNER_EMAIL']",
        "input[name='OWNER_EMAIL']",
        "input[type='text'][name='OWNER_EMAIL']"
      ],
      "type": "input",
      "fillMethod": "value",
      "required": false,
      "validation": "required"
    },
    "fullName": {
      "selectors": [
        "[name='OWNER_NAME']",
        "input[name='OWNER_NAME']",
        "input[type='text'][name='OWNER_NAME']"
      ],
      "type": "input",
      "fillMethod": "value",
      "required": false,
      "validation": "required"
    },
    "reciprocalUrl": {
      "selectors": [
        "[name='RECPR_URL']",
        "input[name='RECPR_URL']",
        "input[type='text'][name='RECPR_URL']"
      ],
      "type": "input",
      "fillMethod": "value",
      "required": false,
      "validation": "optional"
    },
    "reciprocalText": {
      "selectors": [
        "[name='RECPR_TEXT']",
        "textarea[name='RECPR_TEXT']",
        "textarea"
      ],
      "type": "input",
      "fillMethod": "value",
      "required": false,
      "validation": "optional"
    },
    "agreeRules": {
      "selectors": [
        "#AGREERULES",
        "input[type='checkbox']#AGREERULES",
        "input[name='AGREERULES']"
      ],
      "type": "input",
      "fillMethod": "value",
      "required": false,
      "validation": "required"
    },
    "linkType": {
      "selectors": [
        "[name='LINK_TYPE']",
        "input[name='LINK_TYPE']",
        "input[type='radio'][name='LINK_TYPE']"
      ],
      "type": "input",
      "fillMethod": "value",
      "defaultValue": "Regular links",
      "required": false,
      "validation": "required"
    }
  },
  "formInfo": {
    "description": "网站目录提交表单，包含网站基本信息、联系人信息和互惠链接信息",
    "submitSelector": "[name='submit']",
    "totalFields": 11,
    "notes": [
      "表单包含互惠链接字段，可选填写",
      "必须勾选同意条款",
      "链接类型选择Regular links即可"
    ]
  },
  "metadata": {
    "generatedBy": "AI",
    "generatedAt": "2025-07-15T11:31:11.153Z",
    "version": "3.0.0",
    "aiModel": "moonshotai/Kimi-K2-Instruct"
  }
};

// 自定义处理函数 (可选)
export function handleWwwGmawebdirectoryComSubmission(data, rule) {
  console.log('Processing GmaWebDirectory form submission...');
  
  const processedData = { ...data };
  
  // 在这里添加特殊处理逻辑
  // 例如：URL格式化、字段验证、默认值设置等
  
  return processedData;
}

// 自定义元素填写函数 (可选)
export async function customFillElement(element, value, config) {
  console.log('🔧 GmaWebDirectory 自定义填写函数被调用:', element, value);
  
  // 在这里添加特殊的元素填写逻辑
  // 例如：处理特殊的UI组件、异步操作等
  
  return false; // 返回 false 使用默认填写方法
}