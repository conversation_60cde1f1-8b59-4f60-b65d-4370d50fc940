// Advanced Innovation KI Tool 规则配置
// 网站: https://www.advanced-innovation.io/ki-tool-einreichen
// 最后更新: 2025-07-24

export const SITE_RULE = {
  // 基本信息
  domain: 'www.advanced-innovation.io',
  siteName: 'Advanced Innovation',
  priority: 1,
  lastUpdated: '2025-07-24',

  // 字段映射规则
  fieldMappings: {
    // 名字 -> Vorname
    firstName: {
      selectors: [
        'input[name="Tool-First-Name"]',
        '#Tool-First-Name',
        'input[id="Tool-First-Name"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '提交者名字'
    },

    // 姓氏 -> Nachname
    lastName: {
      selectors: [
        'input[name="Tool-Last-Name"]',
        '#Tool-Last-Name',
        'input[id="Tool-Last-Name"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '提交者姓氏'
    },

    // 联系邮箱 -> E-Mail
    contactEmail: {
      selectors: [
        'input[name="Tool-Email"]',
        '#Tool-Email',
        'input[type="email"]'
      ],
      method: 'value',
      validation: 'required|email',
      notes: '联系邮箱地址'
    },

    // 工具名称 -> Tool Name
    siteName: {
      selectors: [
        'input[name="Tool-Tool-Name"]',
        '#Tool-Tool-Name',
        'input[id="Tool-Tool-Name"]'
      ],
      method: 'value',
      validation: 'required',
      maxLength: 100,
      notes: 'AI工具名称'
    },

    // 工具网站 -> Tool Website
    siteUrl: {
      selectors: [
        'input[name="Tool-Tool-Website"]',
        '#Tool-Tool-Website',
        'input[id="Tool-Tool-Website"]'
      ],
      method: 'value',
      validation: 'required|url',
      notes: '工具官方网站地址'
    },

    // 工具描述 -> Tool Beschreibung
    siteDescription: {
      selectors: [
        'textarea[name="Tool-Description"]',
        '#Tool-Tool-Description',
        'textarea[id="Tool-Tool-Description"]'
      ],
      method: 'value',
      validation: 'required',
      minLength: 50,
      maxLength: 1000,
      notes: '工具功能描述'
    },

    // 定价模式 -> Preismodell (下拉选择)
    pricing: {
      selectors: [
        'select[name="Tool-Preismodell"]',
        '#Tool-Preismodell',
        'select[id="Tool-Preismodell"]'
      ],
      method: 'select',
      validation: 'required',
      options: ['Freemium', 'Kostenpflichtig', 'Kostenlos', 'Github', 'Google Colab', 'MIT-Lizenz', 'Open Source', 'TBA'],
      optionTexts: ['Freemium', 'Kostenpflichtig', 'Kostenlos', 'Github', 'Google Colab', 'MIT-Lizenz', 'Open Source', 'TBA'],
      defaultValue: 'Freemium',
      notes: '根据website-info.js中的pricing字段智能匹配'
    }
  },

  // 提交流程配置
  submitConfig: {
    submitButton: 'input[type="submit"]#w-node-_68d17fad-a8b3-d3f0-f990-0f1392d3f785-3ae0aa49',
    submitMethod: 'click',
    successIndicators: [
      '.success-message',
      '.thank-you',
      '.form-success'
    ],
    errorIndicators: [
      '.error-message',
      '.form-error',
      '.validation-error'
    ]
  },

  // 特殊处理
  specialHandling: {
    requiresLogin: false,
    hasCaptcha: false,
    hasFileUpload: false,

    // 自定义处理脚本
    customScript: 'handleAdvancedInnovationSubmission',

    // 表单验证规则
    formValidation: {
      requiredFields: ['firstName', 'lastName', 'contactEmail', 'siteName', 'siteUrl', 'siteDescription', 'pricing'],
      emailValidation: true,
      urlValidation: true
    },

    // 特殊注意事项
    notes: [
      '表单使用德语标签',
      '工具描述需要详细说明功能',
      '定价模式需要准确选择',
      '提交前会进行人工审核'
    ]
  }
};

// 自定义处理函数
export function handleAdvancedInnovationSubmission(data, rule) {
  console.log('Processing Advanced Innovation KI Tool submission...');

  const processedData = { ...data };

  // 定价模式映射 - 将英文转换为德文
  if (processedData.pricing) {
    const pricingMap = {
      'Free': 'Kostenlos',
      'Freemium': 'Freemium',
      'Paid': 'Kostenpflichtig',
      'Open Source': 'Open Source',
      'Contact for Pricing': 'TBA'
    };

    processedData.pricing = pricingMap[processedData.pricing] || 'Freemium';
  }

  // 确保URL格式正确
  if (processedData.siteUrl && !processedData.siteUrl.startsWith('http')) {
    processedData.siteUrl = 'https://' + processedData.siteUrl;
  }

  // 确保描述长度合适
  if (processedData.siteDescription && processedData.siteDescription.length < 50) {
    processedData.siteDescription = processedData.detailedIntro || processedData.siteDescription;
  }

  console.log('Advanced Innovation 数据处理完成:', processedData);
  return processedData;
}