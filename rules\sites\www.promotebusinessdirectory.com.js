// AI Site Submitter - PromoteBusinessDirectory 规则配置
// 自动生成于: 2025/7/15 19:33:38
// 域名: www.promotebusinessdirectory.com

export const SITE_RULE = {
  "domain": "www.promotebusinessdirectory.com",
  "siteName": "PromoteBusinessDirectory",
  "lastUpdated": "2025-07-15T11:33:38.991Z",
  "fieldMappings": {
    "siteName": {
      "selectors": [
        "#TITLE",
        "input[name='TITLE']",
        "input[type='text']:first-of-type"
      ],
      "type": "input",
      "fillMethod": "value",
      "required": false,
      "validation": "required"
    },
    "siteUrl": {
      "selectors": [
        "#URL",
        "input[name='URL']",
        "input[type='text']:nth-of-type(2)"
      ],
      "type": "input",
      "fillMethod": "value",
      "required": false,
      "validation": "required"
    },
    "siteDescription": {
      "selectors": [
        "#DESCRIPTION",
        "textarea[name='DESCRIPTION']",
        "textarea"
      ],
      "type": "input",
      "fillMethod": "value",
      "required": false,
      "validation": "required"
    },
    "fullName": {
      "selectors": [
        "#OWNER_NAME",
        "input[name='OWNER_NAME']",
        "input[type='text']:nth-of-type(3)"
      ],
      "type": "input",
      "fillMethod": "value",
      "required": false,
      "validation": "optional"
    },
    "contactEmail": {
      "selectors": [
        "#OWNER_EMAIL",
        "input[name='OWNER_EMAIL']",
        "input[type='text']:nth-of-type(4)"
      ],
      "type": "input",
      "fillMethod": "value",
      "required": false,
      "validation": "required"
    },
    "agreeRules": {
      "selectors": [
        "#AGREERULES",
        "input[name='AGREERULES']",
        "input[type='checkbox']:first-of-type"
      ],
      "type": "input",
      "fillMethod": "value",
      "defaultValue": true,
      "required": false,
      "validation": "required"
    },
    "newsletterOptIn": {
      "selectors": [
        "[name='OWNER_NEWSLETTER_ALLOW']",
        "input[type='checkbox']:last-of-type"
      ],
      "type": "input",
      "fillMethod": "value",
      "required": false,
      "validation": "optional"
    }
  },
  "formInfo": {
    "description": "网站目录提交表单，用于向PromoteBusinessDirectory提交网站信息",
    "submitSelector": "[name='continue']",
    "totalFields": 7,
    "notes": [
      "表单包含基本网站信息、联系信息和同意条款",
      "所有字段均为非必填，但建议填写完整以提高收录率",
      "需要勾选同意规则才能继续提交"
    ]
  },
  "metadata": {
    "generatedBy": "AI",
    "generatedAt": "2025-07-15T11:33:38.991Z",
    "version": "3.0.0",
    "aiModel": "moonshotai/Kimi-K2-Instruct"
  }
};

// 自定义处理函数 (可选)
export function handleWwwPromotebusinessdirectoryComSubmission(data, rule) {
  console.log('Processing PromoteBusinessDirectory form submission...');
  
  const processedData = { ...data };
  
  // 在这里添加特殊处理逻辑
  // 例如：URL格式化、字段验证、默认值设置等
  
  return processedData;
}

// 自定义元素填写函数 (可选)
export async function customFillElement(element, value, config) {
  console.log('🔧 PromoteBusinessDirectory 自定义填写函数被调用:', element, value);
  
  // 在这里添加特殊的元素填写逻辑
  // 例如：处理特殊的UI组件、异步操作等
  
  return false; // 返回 false 使用默认填写方法
}