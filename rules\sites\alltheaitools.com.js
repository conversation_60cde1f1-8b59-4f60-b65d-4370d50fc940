// AllTheAiTools.com 网站规则配置
// 网站: https://alltheaitools.com/submit-tool
// 最后更新: 2025-07-09

export const SITE_RULE = {
  // 基本信息
  domain: 'alltheaitools.com',
  siteName: 'AllTheAiTools',
  priority: 1,
  lastUpdated: '2025-07-09',
  
  // 字段映射规则
  fieldMappings: {
    // 工具名称 -> Tool Name
    siteName: {
      selectors: [
        'input[name="name"]',
        'input[placeholder="Tool Name"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '工具名称'
    },
    
    // 分类 -> Select Category
    category: {
      selectors: [
        'button[role="combobox"]:contains("Select Category")',
        'button[aria-controls*="radix"]:first-of-type'
      ],
      method: 'select',
      validation: 'required',
      defaultValue: 'Productivity',
      notes: '产品分类'
    },
    
    // 标签 -> Select Tags
    tags: {
      selectors: [
        'button[role="combobox"]:contains("Select Tags")',
        'button[aria-controls*="radix"]:nth-of-type(2)'
      ],
      method: 'select',
      validation: 'optional',
      defaultValue: 'AI Tools',
      notes: '产品标签'
    },
    
    // 定价模式 -> Select Pricing Model
    pricing: {
      selectors: [
        'select[name="pricingModel"]',
        'button[role="combobox"]:contains("Select Pricing Model")'
      ],
      method: 'select',
      validation: 'required',
      targetValue: 'free',
      defaultValue: 'Free',
      notes: '定价模式，选择Free'
    },
    
    // 简短描述 -> Short Description
    siteDescription: {
      selectors: [
        'textarea[name="short_description"]',
        'textarea[placeholder="Short Description"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '简短描述'
    },
    
    // 完整描述 -> Full Description
    detailedIntro: {
      selectors: [
        'textarea[name="description"]',
        'textarea[placeholder="Full Description"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '完整描述'
    },
    
    // 网站URL -> Website URL
    siteUrl: {
      selectors: [
        'input[name="websiteUrl"]',
        'input[placeholder="Website URL"]'
      ],
      method: 'value',
      validation: 'required|url',
      notes: '网站URL地址'
    },
    
    // 联盟计划 -> Has Affiliate Program
    hasAffiliateProgram: {
      selectors: [
        'button[id="hasAffiliateProgram"]',
        'button[role="checkbox"]'
      ],
      method: 'checkbox',
      validation: 'optional',
      targetValue: false,
      notes: '是否有联盟计划'
    }
  },
  
  // 提交流程配置
  submitConfig: {
    submitButton: 'button[type="submit"], button:contains("Submit Tool")',
    submitMethod: 'click',
    waitAfterFill: 2000, // 填写后等待2秒
    waitAfterSubmit: 3000, // 提交后等待3秒
    successIndicators: [
      '.success-message',
      '.alert-success',
      '.notification-success',
      '[class*="success"]'
    ],
    errorIndicators: [
      '.error-message',
      '.alert-error',
      '.alert-danger',
      '[class*="error"]'
    ]
  },
  
  // 特殊处理
  specialHandling: {
    requiresLogin: false,
    hasCaptcha: false,
    hasFileUpload: true, // 有图标和图片上传
    hasTabs: true, // 有标签页切换
    
    // 表单验证规则
    formValidation: {
      requiredFields: ['siteName', 'category', 'pricing', 'siteDescription', 'detailedIntro', 'siteUrl'],
      emailValidation: false,
      urlValidation: true
    },
    
    // 特殊注意事项
    notes: [
      '包含两个标签页：Quick Submit 和 Detailed Submit',
      '当前使用Detailed Submit标签页',
      '表单包含8个主要字段：工具名称、分类、标签、定价、描述、URL、联盟计划',
      '有文件上传功能（图标和图片）',
      '支持添加优缺点和FAQ（可选）',
      '使用Radix UI组件库',
      '定价自动选择Free',
      '分类和标签使用下拉选择组件'
    ]
  }
};

// 自定义处理函数
export function handleAllTheAiToolsSubmission(data, rule) {
  console.log('Processing AllTheAiTools.com submission...');
  
  // 特殊处理逻辑
  const processedData = { ...data };
  
  // 确保URL格式正确
  if (processedData.siteUrl && !processedData.siteUrl.startsWith('http')) {
    processedData.siteUrl = 'https://' + processedData.siteUrl;
  }
  
  // 确保定价设置为Free
  processedData.pricing = 'free';
  
  // 设置默认分类
  if (!processedData.category) {
    processedData.category = 'Productivity';
  }
  
  // 设置默认标签
  if (!processedData.tags) {
    processedData.tags = 'AI Tools';
  }
  
  // 联盟计划默认为false
  processedData.hasAffiliateProgram = false;
  
  return processedData;
}

// 自定义元素填写函数，专门处理Radix UI组件
export async function customFillElement(element, value, config) {
  console.log('🔧 AllTheAiTools自定义填写函数被调用:', element, value);
  
  // 处理Radix UI下拉选择组件
  if (element.getAttribute('role') === 'combobox') {
    try {
      element.click();
      
      // 等待下拉菜单出现
      await new Promise(resolve => setTimeout(resolve, 500));
      
      // 根据不同的下拉选择进行处理
      if (element.textContent.includes('Select Category')) {
        // 查找并点击Productivity选项
        const option = document.querySelector('[role="option"]:contains("Productivity")');
        if (option) {
          option.click();
          console.log('✅ 选择分类: Productivity');
          return true;
        }
      } else if (element.textContent.includes('Select Tags')) {
        // 查找并点击AI Tools选项
        const option = document.querySelector('[role="option"]:contains("AI Tools")');
        if (option) {
          option.click();
          console.log('✅ 选择标签: AI Tools');
          return true;
        }
      } else if (element.textContent.includes('Select Pricing Model')) {
        // 查找并点击Free选项
        const option = document.querySelector('[role="option"]:contains("Free")');
        if (option) {
          option.click();
          console.log('✅ 选择定价: Free');
          return true;
        }
      }
    } catch (error) {
      console.warn('Radix UI下拉选择失败:', error);
    }
  }
  
  // 处理隐藏的select元素
  if (element.tagName === 'SELECT' && element.name === 'pricingModel') {
    try {
      element.value = config.targetValue || 'free';
      element.dispatchEvent(new Event('change', { bubbles: true }));
      console.log('✅ 设置隐藏select值:', config.targetValue || 'free');
      return true;
    } catch (error) {
      console.warn('隐藏select设置失败:', error);
    }
  }
  
  // 处理复选框按钮
  if (element.getAttribute('role') === 'checkbox') {
    try {
      if (config.targetValue === false) {
        // 确保复选框未选中
        element.setAttribute('aria-checked', 'false');
        element.setAttribute('data-state', 'unchecked');
        console.log('✅ 取消勾选复选框');
        return true;
      }
    } catch (error) {
      console.warn('复选框处理失败:', error);
    }
  }
  
  // 默认处理
  return false;
}
