// fastpedia.io 网站规则配置
// 网站: https://fastpedia.io/submit-tool/
// 表单技术: WordPress + 自定义表单
// 最后更新: 2025-07-07

export const SITE_RULE = {
  // 基本信息
  domain: 'fastpedia.io',
  siteName: 'FastPedia',
  priority: 1,
  lastUpdated: '2025-07-07',
  
  // 字段映射规则
  fieldMappings: {
    // 工具名称 -> Tool Name
    siteName: {
      selectors: [
        'input[name*="tool"][name*="name"]',
        'input[placeholder*="Tool Name"]',
        'input[name*="name"]',
        '#tool-name',
        '.tool-name-field'
      ],
      method: 'value',
      validation: 'required',
      notes: '工具名称，使用website-info.js中的siteName字段'
    },
    
    // 官方网站 -> Official Website
    siteUrl: {
      selectors: [
        'input[name="text-974"]',
        'input[placeholder="Official Website"]',
        'input[maxlength="100"][aria-required="true"]'
      ],
      method: 'value',
      validation: 'required|url',
      notes: '官方网站URL，使用website-info.js中的siteUrl字段'
    },
    
    // 联系邮箱 -> Contact Email
    contactEmail: {
      selectors: [
        'input[name*="email"]',
        'input[type="email"]',
        'input[placeholder*="Contact Email"]',
        '#contact-email'
      ],
      method: 'value',
      validation: 'required|email',
      notes: '联系邮箱，使用website-info.js中的contactEmail字段'
    },
    
    // 工具分类 -> Tool Category
    category: {
      selectors: [
        'select[name="menu-85"]',
        'select.wpcf7-select',
        'select[aria-required="true"]'
      ],
      method: 'select',
      validation: 'required',
      defaultValue: 'Design',
      notes: '工具分类，从下拉列表中选择Design'
    },
    
    // 定价模式 -> Pricing Model
    pricing: {
      selectors: [
        'input[name*="pricing"][value="Free"]',
        'input[name*="pricing"][value="Freemium"]',
        'input[name*="pricing"][value="Paid"]',
        'select[name*="pricing"]'
      ],
      method: 'radio',
      validation: 'required',
      defaultValue: 'Free',
      notes: '定价模式，根据website-info.js中的pricing字段选择'
    },
    
    // 付费定价详情 -> If Paid, provide pricing details
    priceAmount: {
      selectors: [
        'input[name="text-341"]',
        'input[placeholder*="pricing details"]',
        'input[maxlength="100"]'
      ],
      method: 'value',
      validation: 'optional',
      defaultValue: 'Free to use permanently',
      notes: '付费定价详情，使用固定英文回复：永久免费使用'
    },
    
    // 简短描述 -> Short Unique Description
    siteDescription: {
      selectors: [
        'textarea[name="textarea-671"]',
        'textarea[placeholder*="main function"]',
        'textarea[maxlength="2000"]:first-of-type'
      ],
      method: 'value',
      validation: 'required',
      notes: '简短描述，使用website-info.js中的siteDescription字段'
    },
    
    // 工具用途 -> Purpose of the Tool
    useCases: {
      selectors: [
        'textarea[name="textarea-676"]',
        'textarea[placeholder*="sets your tool apart"]',
        'textarea[maxlength="2000"]:nth-of-type(2)'
      ],
      method: 'value',
      validation: 'required',
      notes: '工具用途，使用website-info.js中的useCases字段'
    },
    
    // 相关标签 -> Relevant Tags, Keywords, Hashtags
    keywords: {
      selectors: [
        'input[name="text-959"]',
        'input[placeholder*="tags"]',
        'input[maxlength="400"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '相关标签，使用website-info.js中的keywords字段'
    },
    
    // 截图或视频链接 -> Screenshot or Video Link
    thumbnailUrl: {
      selectors: [
        'input[name*="screenshot"]',
        'input[name*="video"]',
        'input[placeholder*="Screenshot or Video Link"]',
        'input[type="url"]:not([name*="website"])'
      ],
      method: 'value',
      validation: 'required|url',
      notes: '截图或视频链接，使用website-info.js中的thumbnailUrl字段'
    },
    
    // 支持链接 -> Support Links
    supportLinks: {
      selectors: [
        'input[name="text-110"]',
        'input[placeholder*="FAQ"]',
        'input[placeholder*="Customer Support"]'
      ],
      method: 'value',
      validation: 'optional',
      notes: '支持链接，使用website-info.js中的siteUrl字段作为支持链接'
    },

    // 社交媒体 -> Social Media Profiles
    twitterUrl: {
      selectors: [
        'input[name="text-727"]',
        'input[placeholder*="social media profiles"]'
      ],
      method: 'value',
      validation: 'optional',
      notes: '社交媒体链接，使用website-info.js中的twitterUrl字段'
    }
  },
  
  // 自定义填写函数
  customFillElement: async function(element, value, config) {
    console.log(`FastPedia自定义填写: ${element.name || element.placeholder}, 方法: ${config.method}`);
    
    switch (config.method) {
      case 'value':
        // 标准输入框和文本域处理
        element.focus();
        await new Promise(resolve => setTimeout(resolve, 100));
        
        // 清空现有内容
        element.value = '';
        await new Promise(resolve => setTimeout(resolve, 50));
        
        // 处理特殊字段的默认值
        let finalValue = value;
        if (element.name === 'text-341') {
          // 付费定价详情字段，使用固定英文回复
          finalValue = 'Free to use permanently';
        }
        
        // 设置新值
        element.value = finalValue;
        
        // 触发事件
        element.dispatchEvent(new Event('input', { bubbles: true }));
        element.dispatchEvent(new Event('change', { bubbles: true }));
        element.dispatchEvent(new Event('blur', { bubbles: true }));
        
        console.log(`✓ 填写字段: ${element.placeholder || element.name} = "${finalValue.substring(0, 50)}..."`);
        break;
        
      case 'radio':
        // 单选按钮处理 - 定价模式
        const pricingMapping = {
          'Free': 'Free',
          'Freemium': 'Freemium',
          'Paid': 'Paid',
          'Subscription': 'Paid'
        };
        
        const targetPricing = pricingMapping[value] || 'Free';
        const radioButton = document.querySelector(`input[name*="pricing"][value="${targetPricing}"]`);
        
        if (radioButton) {
          radioButton.checked = true;
          radioButton.dispatchEvent(new Event('change', { bubbles: true }));
          console.log(`✓ 选择定价模式: ${targetPricing}`);
        }
        break;
        
      case 'select':
        // 下拉选择框处理
        if (element.tagName.toLowerCase() === 'select') {
          // 分类映射表
          const categoryMapping = {
            'AI Tools': 'Design',
            'Developer Tools': 'Developer Tools',
            'Content Creation': 'Copywriting',
            'Image Generation': 'Image generator',
            'Video Generation': 'Video Generator',
            'Audio Tools': 'Audio Editing',
            'Chatbot': 'Chatbot Development',
            'Design': 'Design',
            'Marketing': 'Social Media',
            'Productivity': 'Productivity',
            'Education': 'Education',
            'Finance': 'Finance',
            'Healthcare': 'Healthcare',
            'Writing': 'Writing'
          };
          
          // 尝试映射分类
          let targetValue = categoryMapping[value] || value;
          
          // 查找匹配的选项
          const option = Array.from(element.options).find(opt => 
            opt.value === targetValue || 
            opt.text === targetValue ||
            opt.text.toLowerCase().includes(targetValue.toLowerCase()) ||
            targetValue.toLowerCase().includes(opt.text.toLowerCase())
          );
          
          if (option) {
            element.value = option.value;
            element.dispatchEvent(new Event('change', { bubbles: true }));
            console.log(`✓ 选择分类: ${option.text}`);
          } else {
            // 使用默认值
            const defaultOption = Array.from(element.options).find(opt => 
              opt.text.includes('AI') || opt.text.includes('Tools')
            );
            if (defaultOption) {
              element.value = defaultOption.value;
              element.dispatchEvent(new Event('change', { bubbles: true }));
              console.log(`✓ 使用默认分类: ${defaultOption.text}`);
            }
          }
        }
        break;
        
      default:
        element.value = value;
        element.dispatchEvent(new Event('input', { bubbles: true }));
        element.dispatchEvent(new Event('change', { bubbles: true }));
    }
  },

  // 提交流程配置
  submitConfig: {
    submitButton: [
      'button[type="submit"]',
      'input[type="submit"]',
      '.submit-button',
      'button:contains("Submit")'
    ],
    submitMethod: 'click',
    waitAfterFill: 2000,
    waitAfterSubmit: 5000,
    successIndicators: [
      '.success-message',
      '.thank-you',
      '.confirmation',
      '[class*="success"]',
      'text:contains("submitted")'
    ],
    errorIndicators: [
      '.error-message',
      '.validation-error',
      '[class*="error"]'
    ]
  },

  // 特殊处理
  specialHandling: {
    requiresLogin: false,
    hasCaptcha: false,
    hasFileUpload: true, // 有Logo上传功能
    hasMediaUpload: true, // 有额外媒体上传

    // 表单验证规则
    formValidation: {
      requiredFields: ['siteName', 'siteUrl', 'contactEmail', 'category', 'pricing', 'siteDescription', 'useCases', 'keywords', 'thumbnailUrl'],
      emailValidation: true,
      urlValidation: true,
      characterLimits: {
        siteDescription: 300,
        detailedIntro: 300
      }
    },

    // 特殊注意事项
    notes: [
      '这是WordPress网站，使用Contact Form 7插件',
      '有文件上传功能（Logo和额外媒体），需要手动处理',
      '分类默认选择Design',
      '付费定价详情使用固定英文回复：Free to use permanently',
      '简短描述使用siteDescription字段',
      '工具用途使用useCases字段',
      '支持链接使用siteUrl字段',
      '社交媒体使用twitterUrl字段',
      '分类选项包含70+个选项',
      '字段名格式：menu-85, textarea-671, text-341等',
      '网站专注于AI工具目录和社区建设'
    ]
  }
};

// 自定义处理函数
export function handleFastPediaSubmission(data, _rule) {
  console.log('Processing FastPedia form submission...');

  const processedData = { ...data };

  // 确保URL格式正确
  if (processedData.siteUrl && !processedData.siteUrl.startsWith('http')) {
    processedData.siteUrl = 'https://' + processedData.siteUrl;
  }

  // 确保截图/视频URL格式正确
  if (processedData.thumbnailUrl && !processedData.thumbnailUrl.startsWith('http')) {
    processedData.thumbnailUrl = 'https://' + processedData.thumbnailUrl;
  }

  // 设置固定的定价详情
  processedData.priceAmount = 'Free to use permanently';

  return processedData;
}
