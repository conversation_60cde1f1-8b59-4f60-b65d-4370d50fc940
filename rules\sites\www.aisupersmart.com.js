// AI Site Submitter - AI Super Smart 规则配置
// 自动生成于: 2025/7/15 11:12:37
// 域名: www.aisupersmart.com

export const SITE_RULE = {
  "domain": "www.aisupersmart.com",
  "siteName": "AI Super Smart",
  "lastUpdated": "2025-07-15T03:12:37.637Z",
  "fieldMappings": {
    "siteName": {
      "selectors": [
        "#tool_name"
      ],
      "type": "input",
      "fillMethod": "value",
      "required": false,
      "validation": "required"
    },
    "siteUrl": {
      "selectors": [
        "#tool_url"
      ],
      "type": "input",
      "fillMethod": "value",
      "required": false,
      "validation": "url"
    },
    "siteDescription": {
      "selectors": [
        "#short_description"
      ],
      "type": "input",
      "fillMethod": "value",
      "required": false
    },
    "contactEmail": {
      "selectors": [
        "#email"
      ],
      "type": "input",
      "fillMethod": "value",
      "required": false,
      "validation": "email"
    },
    "thumbnailUrl": {
      "selectors": [
        "#featured_image"
      ],
      "type": "input",
      "fillMethod": "value",
      "required": false
    },
    "category": {
      "selectors": [
        "#category"
      ],
      "type": "input",
      "fillMethod": "value",
      "required": false
    },
    "pricing": {
      "selectors": [
        "input[name='pricing[]']"
      ],
      "type": "input",
      "fillMethod": "value",
      "required": false,
      "validation": "required"
    },
    "features": {
      "selectors": [
        "input[name='features[]']"
      ],
      "type": "input",
      "fillMethod": "value",
      "required": false,
      "validation": "required"
    }
  },
  "formInfo": {
    "description": "AI Super Smart 工具提交表单",
    "submitSelector": ".jet-form-builder__action-button.jet-form-builder__submit.submit-type-ajax",
    "totalFields": 25,
    "notes": [
      "多选框需根据实际值点击",
      "文件上传需本地路径"
    ]
  },
  "metadata": {
    "generatedBy": "AI",
    "generatedAt": "2025-07-15T03:12:37.637Z",
    "version": "3.0.0",
    "aiModel": "moonshotai/Kimi-K2-Instruct"
  }
};

// 自定义处理函数 (可选)
export function handleWwwAisupersmartComSubmission(data, rule) {
  console.log('Processing AI Super Smart form submission...');
  
  const processedData = { ...data };
  
  // 在这里添加特殊处理逻辑
  // 例如：URL格式化、字段验证、默认值设置等
  
  return processedData;
}

// 自定义元素填写函数 (可选)
export async function customFillElement(element, value, config) {
  console.log('🔧 AI Super Smart 自定义填写函数被调用:', element, value);
  
  // 在这里添加特殊的元素填写逻辑
  // 例如：处理特殊的UI组件、异步操作等
  
  return false; // 返回 false 使用默认填写方法
}