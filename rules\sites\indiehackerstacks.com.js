// IndieHackerStacks.com 网站规则配置
// 网站: https://indiehackerstacks.com/me/products/-1
// 最后更新: 2025-07-24

export const SITE_RULE = {
  domain: 'indiehackerstacks.com',
  siteName: 'Indie Hacker Stacks',
  priority: 1,
  lastUpdated: '2025-07-24',

  fieldMappings: {
    siteName: {
      selectors: [
        'input[name="productName"]',
        'input[id*="productName"]',
        'label:contains("Product name") + div input'
      ],
      method: 'value',
      validation: 'required',
      notes: '产品名称'
    },

    siteUrl: {
      selectors: [
        'input[name="productUrl"]',
        'input[id*="productUrl"]',
        'label:contains("Url") + div input'
      ],
      method: 'value',
      validation: 'required|url',
      notes: '产品URL，自动添加https://前缀'
    },

    tagline: {
      selectors: [
        'input[name="tagline"]',
        'input[id*="tagline"]',
        'label:contains("Tagline") + div input'
      ],
      method: 'value',
      validation: 'required',
      notes: '产品标语，2-70字符'
    },

    siteDescription: {
      selectors: [
        'textarea[name="description"]',
        'textarea[id*="description"]',
        'label:contains("Description") + div textarea'
      ],
      method: 'value',
      validation: 'required',
      notes: '产品描述，10-260字符'
    },

    twitterUrl: {
      selectors: [
        'input[name="twitterHandle"]',
        'input[id*="twitterHandle"]',
        'label:contains("Twitter handle") + div input'
      ],
      method: 'value',
      validation: 'optional',
      notes: 'Twitter用户名，不含@符号'
    }
  },

  submitConfig: {
    submitButton: 'button:contains("Next"), button[type="button"]',
    submitMethod: 'click',
    successIndicators: ['.success-message'],
    errorIndicators: ['.text-red-500']
  },

  specialHandling: {
    requiresLogin: false,
    hasCaptcha: false,
    hasFileUpload: false,
    customScript: 'handleIndieHackerStacksSubmission',
    formValidation: {
      requiredFields: ['siteName', 'siteUrl', 'tagline', 'siteDescription'],
      emailValidation: false,
      urlValidation: true
    },
    notes: [
      '使用React构建的现代表单',
      '动态生成的字段ID',
      'Tailwind CSS样式',
      '字符长度限制',
      'URL自动添加https://前缀',
      'Twitter用户名不含@符号'
    ]
  }
};

export function handleIndieHackerStacksSubmission(data, rule) {
  const processedData = { ...data };

  // URL字段不需要添加https://前缀，因为表单已有前缀显示
  if (processedData.siteUrl && processedData.siteUrl.startsWith('https://')) {
    processedData.siteUrl = processedData.siteUrl.replace('https://', '');
  }

  // Twitter用户名处理，移除@符号
  if (processedData.twitterUrl && processedData.twitterUrl.startsWith('@')) {
    processedData.twitterUrl = processedData.twitterUrl.substring(1);
  }

  return processedData;
}

export async function customFillElement(element, value, config) {
  // 处理React组件的输入框
  if (element.tagName === 'INPUT' || element.tagName === 'TEXTAREA') {
    // 清除现有值
    element.value = '';
    element.focus();

    // 模拟用户输入
    element.value = value;

    // 触发React事件
    const inputEvent = new Event('input', { bubbles: true });
    const changeEvent = new Event('change', { bubbles: true });

    element.dispatchEvent(inputEvent);
    element.dispatchEvent(changeEvent);

    // 失去焦点
    element.blur();

    return true;
  }

  return false;
}