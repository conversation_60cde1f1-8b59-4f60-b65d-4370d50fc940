// tools-ai.online 网站规则配置
// 网站: https://www.tools-ai.online/submit-tool
// 表单技术: 现代React表单
// 最后更新: 2025-07-07

export const SITE_RULE = {
  // 基本信息
  domain: 'tools-ai.online',
  siteName: 'Tools AI Online',
  priority: 1,
  lastUpdated: '2025-07-07',
  
  // 字段映射规则
  fieldMappings: {
    // 工具名称 -> Tool Name
    siteName: {
      selectors: [
        'input[name="toolName"]',
        '#toolName',
        'input[placeholder*="Enter tool name"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '工具名称，使用website-info.js中的siteName字段'
    },
    
    // 网站URL -> Website URL
    siteUrl: {
      selectors: [
        'input[name="toolUrl"]',
        '#toolUrl',
        'input[placeholder*="https://example.com"]'
      ],
      method: 'value',
      validation: 'required|url',
      notes: '网站URL，使用website-info.js中的siteUrl字段'
    },
    
    // 工具描述 -> Tool Description
    siteDescription: {
      selectors: [
        'textarea[id="toolDescription"]',
        'textarea[data-testid="text-area"]',
        'textarea[placeholder*="Describe your tool"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '工具描述，使用website-info.js中的siteDescription字段'
    },
    
    // 分类 -> Categories
    category: {
      selectors: [
        'div[class*="flex items-center flex-wrap"]',
        'div:contains("Select categories")',
        'label[for="categories"] + p + div'
      ],
      method: 'multiselect',
      validation: 'required',
      defaultValue: 'Productivity',
      notes: '分类选择，默认选择Productivity，最多3个分类'
    },
    
    // 标签 -> Tags
    tags: {
      selectors: [
        'div[class*="flex items-center flex-wrap"]:last-of-type',
        'div:contains("Select tags")',
        'label[for="tags"] + p + div'
      ],
      method: 'multiselect',
      validation: 'required',
      defaultValue: 'AI for fun',
      notes: '标签选择，默认选择AI for fun，最多5个标签'
    },
    
    // 定价 -> Pricing
    pricing: {
      selectors: [
        'select[name="pricing"]',
        '#pricing',
        'select option[value="31"]'
      ],
      method: 'select',
      validation: 'required',
      defaultValue: 'Free',
      notes: '定价模式，默认选择Free (value="31")'
    },
    
    // 联系邮箱 -> Contact Email
    contactEmail: {
      selectors: [
        'input[name="contactEmail"]',
        '#contactEmail',
        'input[type="email"]',
        'input[placeholder*="<EMAIL>"]'
      ],
      method: 'value',
      validation: 'required|email',
      notes: '联系邮箱，使用website-info.js中的contactEmail字段'
    }
  },
  
  // 自定义填写函数
  customFillElement: async function(element, value, config) {
    console.log(`Tools AI Online自定义填写: ${element.name || element.id}, 方法: ${config.method}`);
    
    switch (config.method) {
      case 'value':
        // 标准输入框和文本域处理
        element.focus();
        await new Promise(resolve => setTimeout(resolve, 100));
        
        // 清空现有内容
        element.value = '';
        await new Promise(resolve => setTimeout(resolve, 50));
        
        // 设置新值
        element.value = value;
        
        // 触发React事件
        element.dispatchEvent(new Event('input', { bubbles: true }));
        element.dispatchEvent(new Event('change', { bubbles: true }));
        element.dispatchEvent(new Event('blur', { bubbles: true }));
        
        console.log(`✓ 填写字段: ${element.placeholder || element.name} = "${value.substring(0, 50)}..."`);
        break;
        
      case 'select':
        // 下拉选择框处理
        if (element.tagName.toLowerCase() === 'select') {
          // 定价映射
          const pricingMapping = {
            'Free': '31',
            'Freemium': '32',
            'GitHub': '33',
            'Google Colab': '35',
            'Open Source': '34',
            'Other': '37',
            'Paid': '30'
          };
          
          const targetValue = pricingMapping[value] || pricingMapping[config.defaultValue];
          
          if (targetValue) {
            element.value = targetValue;
            element.dispatchEvent(new Event('change', { bubbles: true }));
            console.log(`✓ 选择定价: ${config.defaultValue} (value="${targetValue}")`);
          }
        }
        break;
        
      case 'multiselect':
        // 多选组件处理（Categories和Tags）
        try {
          // 点击多选容器打开选项
          const container = element.closest('div[class*="relative"]') || element;
          const clickableArea = container.querySelector('div[class*="flex items-center flex-wrap"]');
          
          if (clickableArea) {
            clickableArea.click();
            console.log('点击打开多选组件');
            
            // 等待选项加载
            await new Promise(resolve => setTimeout(resolve, 800));
            
            // 查找并点击目标选项
            let targetOption = null;
            
            if (config.defaultValue === 'Productivity') {
              // 查找Productivity选项
              targetOption = document.querySelector('[data-value*="productivity"], [data-value*="Productivity"], div:contains("Productivity")');
            } else if (config.defaultValue === 'AI for fun') {
              // 查找AI for fun选项
              targetOption = document.querySelector('[data-value*="ai"], [data-value*="fun"], div:contains("AI for fun")');
            }
            
            if (targetOption) {
              targetOption.click();
              console.log(`✓ 选择选项: ${config.defaultValue}`);
            } else {
              console.warn(`未找到选项: ${config.defaultValue}`);
            }
          }
        } catch (error) {
          console.error('多选组件处理出错:', error);
        }
        break;
        
      default:
        element.value = value;
        element.dispatchEvent(new Event('input', { bubbles: true }));
        element.dispatchEvent(new Event('change', { bubbles: true }));
    }
  },

  // 提交流程配置
  submitConfig: {
    submitButton: [
      'button[type="submit"]',
      'button:contains("Submit Tool")',
      '.submit-button',
      'button[class*="bg-red"]'
    ],
    submitMethod: 'click',
    waitAfterFill: 2000,
    waitAfterSubmit: 5000,
    successIndicators: [
      '.success-message',
      '.thank-you',
      '.confirmation',
      '[class*="success"]',
      'text:contains("submitted")',
      'text:contains("received")'
    ],
    errorIndicators: [
      '.error-message',
      '.validation-error',
      '[class*="error"]',
      '[class*="text-red"]'
    ]
  },

  // 特殊处理
  specialHandling: {
    requiresLogin: false,
    hasCaptcha: false,
    hasFileUpload: false,
    hasMultiSelect: true, // 有多选组件
    isModernUI: true, // 现代React UI

    // 表单验证规则
    formValidation: {
      requiredFields: ['siteName', 'siteUrl', 'siteDescription', 'category', 'tags', 'pricing', 'contactEmail'],
      emailValidation: true,
      urlValidation: true,
      multiSelectLimits: {
        category: 3, // 最多3个分类
        tags: 5     // 最多5个标签
      }
    },

    // 特殊注意事项
    notes: [
      '这是现代React表单，使用Tailwind CSS',
      '有多选组件用于分类和标签选择',
      '分类最多选择3个，标签最多选择5个',
      '定价使用数字值：Free=31, Freemium=32, Paid=30等',
      '默认选择：分类=Productivity, 标签=AI for fun, 定价=Free',
      '多选组件需要点击容器打开选项列表',
      '使用React事件系统，需要触发input/change事件',
      '表单设计现代化，用户体验良好'
    ]
  }
};

// 自定义处理函数
export function handleToolsAIOnlineSubmission(data, _rule) {
  console.log('Processing Tools AI Online form submission...');

  const processedData = { ...data };

  // 确保URL格式正确
  if (processedData.siteUrl && !processedData.siteUrl.startsWith('http')) {
    processedData.siteUrl = 'https://' + processedData.siteUrl;
  }

  // 设置默认选项
  processedData.category = 'Productivity';
  processedData.tags = 'AI for fun';
  processedData.pricing = 'Free';

  return processedData;
}
