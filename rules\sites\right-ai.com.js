// Right-AI.com 网站规则配置
// 网站: https://right-ai.com/submit
// 最后更新: 2025-07-24

export const SITE_RULE = {
  // 基本信息
  domain: 'right-ai.com',
  siteName: 'RightAI Tools Directory',
  priority: 1,
  lastUpdated: '2025-07-24',

  // 字段映射规则
  fieldMappings: {
    // 网站名称 -> Website Name
    siteName: {
      selectors: [
        'input[name="website"]',
        'input[placeholder="RightAI"]',
        'label:contains("Website Name") + input'
      ],
      method: 'value',
      validation: 'required',
      notes: '网站名称'
    },

    // 产品描述 -> Product Description
    siteDescription: {
      selectors: [
        'input[name="websiteDesc"]',
        'input[placeholder*="describe the product content"]',
        'label:contains("Product Description") + input'
      ],
      method: 'value',
      validation: 'required',
      notes: '产品简要描述'
    },

    // 网站URL -> Website URL
    siteUrl: {
      selectors: [
        'input[name="url"]',
        'input[placeholder="https://right-ai.com/"]',
        'label:contains("Website URL") + input'
      ],
      method: 'value',
      validation: 'required|url',
      notes: '网站URL地址'
    },

    // 联系邮箱 -> Email
    contactEmail: {
      selectors: [
        'input[name="email"]',
        'input[placeholder="Email"]',
        'label:contains("Email") + input'
      ],
      method: 'value',
      validation: 'required|email',
      notes: '联系邮箱'
    },

    // 反向链接页面 -> Please enter a link that includes a link to our site
    backlinkUrl: {
      selectors: [
        'input[name="addedUrl"]',
        'input[placeholder="Please enter the link"]',
        'label:contains("includes a link to our site") + input'
      ],
      method: 'value',
      validation: 'required|url',
      notes: '包含反向链接的页面URL'
    }
  },

  // 提交流程配置
  submitConfig: {
    submitButton: 'button[type="submit"], .bg-primary',
    submitMethod: 'manual', // 手动提交
    waitAfterFill: 0,
    waitAfterSubmit: 0,
    successIndicators: ['.success-message'],
    errorIndicators: ['.error-message']
  },

  // 特殊处理
  specialHandling: {
    requiresLogin: false,
    hasCaptcha: false,
    hasFileUpload: false,
    customScript: 'handleRightAiSubmission',
    formValidation: {
      requiredFields: ['siteName', 'siteDescription', 'siteUrl', 'contactEmail', 'backlinkUrl'],
      emailValidation: true,
      urlValidation: true
    },
    notes: [
      'RightAI AI工具目录提交平台',
      '需要在网站添加反向链接才能成功提交',
      '反向链接格式：<a href="https://right-ai.com/" title="RightAI Tools Directory">RightAI Tools Directory</a>',
      '专注AI相关产品',
      '手动审核',
      '不接受加密货币相关内容',
      '手动提交模式'
    ]
  }
};

// 自定义处理函数
export function handleRightAiSubmission(data) {
  console.log('Processing RightAI form submission...');

  const processedData = { ...data };

  // 确保URL格式正确
  if (processedData.siteUrl && !processedData.siteUrl.startsWith('http')) {
    processedData.siteUrl = 'https://' + processedData.siteUrl;
  }

  // 处理反向链接URL
  if (processedData.backlinkUrl && !processedData.backlinkUrl.startsWith('http')) {
    processedData.backlinkUrl = 'https://' + processedData.backlinkUrl;
  }

  // 如果没有提供反向链接URL，使用网站首页
  if (!processedData.backlinkUrl && processedData.siteUrl) {
    processedData.backlinkUrl = processedData.siteUrl;
  }

  return processedData;
}

// 自定义元素填写函数
export async function customFillElement(element, value, config) {
  console.log(`RightAI自定义填写: ${element.tagName}, 方法: ${config.method}`);

  switch (config.method) {
    case 'value':
      // 标准输入框处理
      element.focus();
      await new Promise(resolve => setTimeout(resolve, 200));

      element.value = value;
      element.dispatchEvent(new Event('input', { bubbles: true }));
      element.dispatchEvent(new Event('change', { bubbles: true }));
      element.dispatchEvent(new Event('blur', { bubbles: true }));

      console.log(`✓ 填写字段: ${element.name} = "${value}"`);
      return true;

    default:
      element.value = value;
      element.dispatchEvent(new Event('input', { bubbles: true }));
      return true;
  }

  return false;
}