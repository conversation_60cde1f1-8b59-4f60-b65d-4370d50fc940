// AI Site Submitter - Background Script
// 处理扩展的后台逻辑
// 最后更新: 2025-07-06

// 扩展安装时的初始化
chrome.runtime.onInstalled.addListener(() => {
    console.log('AI Site Submitter 已安装');
});

// 处理扩展图标点击
chrome.action.onClicked.addListener(async (tab) => {
    try {
        // 打开侧边栏
        await chrome.sidePanel.open({ tabId: tab.id });
    } catch (error) {
        console.error('打开侧边栏失败:', error);
    }
});

// 处理来自content script和sidepanel的消息
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
    if (request.action === 'getCurrentTab') {
        chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
            if (tabs[0]) {
                sendResponse({ 
                    success: true, 
                    tab: {
                        id: tabs[0].id,
                        url: tabs[0].url,
                        title: tabs[0].title,
                        hostname: new URL(tabs[0].url).hostname
                    }
                });
            } else {
                sendResponse({ success: false, error: '无法获取当前标签页' });
            }
        });
        return true; // 异步响应
    }
    
    if (request.action === 'executeContentScript') {
        chrome.tabs.query({ active: true, currentWindow: true }, async (tabs) => {
            if (tabs[0]) {
                try {
                    const response = await chrome.tabs.sendMessage(tabs[0].id, {
                        action: request.contentAction,
                        data: request.data
                    });
                    sendResponse(response);
                } catch (error) {
                    sendResponse({ success: false, error: error.message });
                }
            } else {
                sendResponse({ success: false, error: '无法找到活动标签页' });
            }
        });
        return true; // 异步响应
    }
    
    return true;
});

console.log('AI Site Submitter background script loaded');
