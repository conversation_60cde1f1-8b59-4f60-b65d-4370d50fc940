// SNKCreation.com 网站规则配置
// 网站: https://www.snkcreation.com/snk-free-site-submission
// 最后更新: 2025-07-24

export const SITE_RULE = {
  domain: 'www.snkcreation.com',
  siteName: 'SNK Creation',
  priority: 1,
  lastUpdated: '2025-07-24',

  fieldMappings: {
    fullName: {
      selectors: [
        '#input_comp-kt1li15k',
        'input[name="full-name *"]',
        'input[placeholder="Full Name *"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '提交者全名'
    },

    contactEmail: {
      selectors: [
        '#input_comp-kt1li15p',
        'input[name="email"]',
        'input[placeholder="Business Email ID *"]'
      ],
      method: 'value',
      validation: 'required|email',
      notes: '商务邮箱'
    },

    phone: {
      selectors: [
        '#input_comp-kt1li162',
        'input[name="phone"]',
        'input[placeholder="Contact No. *"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '联系电话'
    },

    country: {
      selectors: [
        '#input_comp-kt1li167',
        'input[name="country-*"]',
        'input[placeholder="Country *"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '国家'
    },

    siteUrl: {
      selectors: [
        '#input_comp-kt1li16l',
        'input[name="site-submission website / blog url"]',
        'input[placeholder="Site Submission Website / Blog URL"]'
      ],
      method: 'value',
      validation: 'optional|url',
      notes: '网站/博客URL'
    }
  },

  submitConfig: {
    submitButton: 'button[data-testid="buttonElement"], .uDW_Qe.wixui-button',
    submitMethod: 'click',
    successIndicators: ['.wixui-rich-text__text:contains("Thanks! Message sent.")'],
    errorIndicators: ['.error-message']
  },

  specialHandling: {
    requiresLogin: false,
    hasCaptcha: false,
    hasFileUpload: false,
    customScript: 'handleSNKCreationSubmission',
    formValidation: {
      requiredFields: ['fullName', 'contactEmail', 'phone', 'country'],
      emailValidation: true,
      urlValidation: true
    },
    notes: [
      '使用Wix表单系统',
      '有成功提交消息显示',
      '字段使用复杂的ID命名',
      '网站URL字段为可选',
      '有电话号码格式验证',
      '邮箱有正则验证'
    ]
  }
};

export function handleSNKCreationSubmission(data, rule) {
  const processedData = { ...data };

  if (processedData.siteUrl && !processedData.siteUrl.startsWith('http')) {
    processedData.siteUrl = 'https://' + processedData.siteUrl;
  }

  return processedData;
}

export async function customFillElement(element, value, config) {
  if (element.tagName === 'INPUT') {
    element.value = value;
    element.dispatchEvent(new Event('input', { bubbles: true }));
    element.dispatchEvent(new Event('change', { bubbles: true }));
    return true;
  }

  return false;
}