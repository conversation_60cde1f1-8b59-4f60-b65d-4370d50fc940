// freeprwebdirectory.com 网站规则配置
// 网站: https://www.freeprwebdirectory.com/submit.php
// 表单技术: PHP Form with CAPTCHA
// 最后更新: 2025-07-08

export const SITE_RULE = {
  // 基本信息
  domain: 'freeprwebdirectory.com',
  siteName: 'Free PR Web Directory',
  priority: 1,
  lastUpdated: '2025-07-08',
  
  // 字段映射规则
  fieldMappings: {
    // 定价选项 -> Pricing
    linkType: {
      selectors: [
        'input[name="LINK_TYPE"][value="normal"]',
        'input[type="radio"][value="normal"]',
        'input[value="normal"]'
      ],
      method: 'radio',
      validation: 'required',
      defaultValue: 'normal',
      availableOptions: ['featured', 'normal'],
      notes: '定价选项，默认选择normal (Regular links，免费，3-4个月审核)'
    },
    
    // 标题 -> Title
    siteName: {
      selectors: [
        'input[name="TITLE"]',
        'input.text:first-of-type',
        'input[maxlength="100"]',
        'input[size="40"]:first-of-type'
      ],
      method: 'value',
      validation: 'required',
      notes: '网站标题，使用website-info.js中的siteName字段'
    },
    
    // URL -> URL
    siteUrl: {
      selectors: [
        'input[name="URL"]',
        'input[maxlength="255"]',
        'input.text:nth-of-type(2)',
        'input[size="40"]:nth-of-type(2)'
      ],
      method: 'value',
      validation: 'optional|url',
      notes: '网站URL，使用website-info.js中的siteUrl字段'
    },
    
    // 描述 -> Description
    detailedIntro: {
      selectors: [
        'textarea[name="DESCRIPTION"]',
        'textarea.text:first-of-type',
        'textarea[rows="3"]:first-of-type',
        'textarea[cols="37"]'
      ],
      method: 'value',
      validation: 'optional',
      maxLength: 500,
      notes: '网站详细描述，使用website-info.js中的detailedIntro字段，限制500字符'
    },
    
    // META关键词 -> META Keywords
    keywords: {
      selectors: [
        'input[name="META_KEYWORDS"]',
        'input[maxlength="2000"]',
        'input.text:nth-of-type(3)',
        'input[size="40"]:nth-of-type(3)'
      ],
      method: 'value',
      validation: 'optional',
      notes: 'META关键词，使用website-info.js中的keywords字段'
    },
    
    // META描述 -> META Description
    siteDescription: {
      selectors: [
        'textarea[name="META_DESCRIPTION"]',
        'textarea.text:nth-of-type(2)',
        'textarea[rows="3"]:nth-of-type(2)',
        'textarea[cols="30"]'
      ],
      method: 'value',
      validation: 'optional',
      maxLength: 250,
      notes: 'META描述，使用website-info.js中的siteDescription字段，限制250字符'
    },
    
    // 您的姓名 -> Your Name
    fullName: {
      selectors: [
        'input[name="OWNER_NAME"]',
        'input[maxlength="50"]',
        'input.text:nth-of-type(4)',
        'input[size="40"]:nth-of-type(4)'
      ],
      method: 'value',
      validation: 'required',
      notes: '您的姓名，使用website-info.js中的fullName字段'
    },
    
    // 您的邮箱 -> Your Email
    contactEmail: {
      selectors: [
        'input[name="OWNER_EMAIL"]',
        'input.text:nth-of-type(5)',
        'input[size="40"]:nth-of-type(5)',
        'input[maxlength="255"]:nth-of-type(2)'
      ],
      method: 'value',
      validation: 'required|email',
      notes: '您的邮箱，使用website-info.js中的contactEmail字段'
    }
  },
  
  // 自定义填写函数
  customFillElement: async function(element, value, config) {
    console.log(`Free PR Web Directory自定义填写: ${element.name || element.type}, 方法: ${config.method}`);
    
    switch (config.method) {
      case 'value':
        // 标准输入框和文本域处理
        element.focus();
        await new Promise(resolve => setTimeout(resolve, 100));
        
        // 清空现有内容
        element.value = '';
        await new Promise(resolve => setTimeout(resolve, 50));
        
        // 处理字符限制
        let finalValue = value;
        if (config.maxLength && finalValue.length > config.maxLength) {
          finalValue = finalValue.substring(0, config.maxLength);
          console.log(`⚠️ 内容被截断到${config.maxLength}字符: ${finalValue}`);
        }
        
        // 设置新值
        element.value = finalValue;
        
        // 触发事件
        element.dispatchEvent(new Event('input', { bubbles: true }));
        element.dispatchEvent(new Event('change', { bubbles: true }));
        element.dispatchEvent(new Event('blur', { bubbles: true }));
        
        console.log(`✓ 填写字段: ${element.name} = "${finalValue.substring(0, 50)}..."`);
        break;
        
      case 'radio':
        // 单选按钮处理
        console.log(`处理定价选项，目标值: ${config.defaultValue}`);
        
        // 查找所有同名单选按钮
        const radioButtons = document.querySelectorAll('input[name="LINK_TYPE"]');
        
        // 先取消所有选择
        radioButtons.forEach(rb => {
          rb.checked = false;
        });
        
        // 选择目标选项
        const targetRadio = Array.from(radioButtons).find(rb => 
          rb.value === config.defaultValue
        );
        
        if (targetRadio) {
          targetRadio.checked = true;
          targetRadio.dispatchEvent(new Event('change', { bubbles: true }));
          console.log(`✓ 选择定价选项: Regular links (${config.defaultValue})`);
        } else {
          console.log(`⚠️ 未找到定价选项: ${config.defaultValue}`);
        }
        break;
        
      default:
        element.value = value;
        element.dispatchEvent(new Event('input', { bubbles: true }));
        element.dispatchEvent(new Event('change', { bubbles: true }));
    }
  },
  
  // 提交流程配置
  submitConfig: {
    submitButton: [
      'button[type="submit"]',
      'input[type="submit"]',
      'button:contains("Submit")',
      'input[value*="Submit"]'
    ],
    submitMethod: 'click',
    waitAfterFill: 2000,
    waitAfterSubmit: 5000,
    successIndicators: [
      'text:contains("submitted")',
      'text:contains("thank you")',
      'text:contains("success")',
      'text:contains("approved")'
    ],
    errorIndicators: [
      'text:contains("error")',
      'text:contains("required")',
      'text:contains("invalid")',
      'text:contains("captcha")'
    ]
  },
  
  // 特殊处理
  specialHandling: {
    requiresLogin: false,
    hasCaptcha: true, // 可能有验证码
    hasFileUpload: false,
    isPHPForm: true, // PHP表单
    isPRDirectory: true, // PR目录
    hasMetaFields: true, // 有META字段
    hasPaidOptions: true, // 有付费选项
    hasCharacterLimits: true, // 有字符限制
    
    // 表单验证规则
    formValidation: {
      requiredFields: ['linkType', 'siteName', 'fullName', 'contactEmail'],
      optionalFields: ['siteUrl', 'detailedIntro', 'keywords', 'siteDescription'],
      emailValidation: true,
      urlValidation: true,
      characterLimits: {
        detailedIntro: 500,
        siteDescription: 250
      },
      radioGroups: ['linkType']
    },
    
    // 特殊注意事项
    notes: [
      '这是Free PR Web Directory的网站提交表单',
      '表单包含8个字段：4个必填，4个可选',
      'PR（公关）网站目录，专注于公关和营销',
      '可能有验证码保护，需要手动处理',
      '默认选择Regular links（免费，3-4个月审核）',
      '有付费选项：Featured links $9.99/年（24-48小时审核）',
      '包含META字段：关键词和描述',
      '描述限制500字符，META描述限制250字符',
      '使用实际字段名：LINK_TYPE, TITLE, URL, DESCRIPTION, META_KEYWORDS, META_DESCRIPTION, OWNER_NAME, OWNER_EMAIL',
      '与其他目录网站类似的表单结构',
      '专注于PR和营销相关网站收录'
    ]
  }
};

// 自定义处理函数
export function handleFreePRWebDirectorySubmission(data, _rule) {
  console.log('Processing Free PR Web Directory form submission...');

  const processedData = { ...data };

  // 确保URL格式正确
  if (processedData.siteUrl && !processedData.siteUrl.startsWith('http')) {
    processedData.siteUrl = 'https://' + processedData.siteUrl;
  }

  // 处理字符限制
  if (processedData.detailedIntro && processedData.detailedIntro.length > 500) {
    processedData.detailedIntro = processedData.detailedIntro.substring(0, 500);
  }

  if (processedData.siteDescription && processedData.siteDescription.length > 250) {
    processedData.siteDescription = processedData.siteDescription.substring(0, 250);
  }

  // 设置默认值
  processedData.linkType = 'normal'; // Regular links

  return processedData;
}

// PR目录信息提醒
export function showFreePRWebDirectoryInfo() {
  console.log('📢 Free PR Web Directory 信息:');
  console.log('');
  console.log('平台特色:');
  console.log('- 专注于PR（公关）和营销网站');
  console.log('- 免费的公关网站目录');
  console.log('- 包含META字段优化');
  console.log('- 支持营销和推广网站');
  console.log('');
  console.log('提交选项 (2种):');
  console.log('1. Featured links - $9.99/年');
  console.log('   - 24-48小时内审核');
  console.log('   - 编辑人工紧急审核');
  console.log('   - 费用用于编辑时间和精力');
  console.log('');
  console.log('2. Regular links - 免费 ✅ 默认选择');
  console.log('   - 3-4个月审核');
  console.log('   - 标准审核流程');
  console.log('');
  console.log('字段特点:');
  console.log('- 包含META关键词和描述');
  console.log('- 描述限制500字符');
  console.log('- META描述限制250字符');
  console.log('- 支持SEO优化');
  console.log('');
  console.log('Free PR Web Directory - 专业的公关网站目录！');
}

// PR网站优化指南
export function showPROptimizationGuide() {
  console.log('📈 PR网站优化指南:');
  console.log('');
  console.log('PR网站特点:');
  console.log('- 专注于公关和营销');
  console.log('- 提高品牌知名度');
  console.log('- 增加媒体曝光');
  console.log('- 建立行业权威');
  console.log('');
  console.log('内容优化:');
  console.log('- 突出公关价值');
  console.log('- 强调媒体关系');
  console.log('- 展示成功案例');
  console.log('- 包含联系信息');
  console.log('');
  console.log('关键词策略:');
  console.log('- 使用PR相关关键词');
  console.log('- 包含行业术语');
  console.log('- 添加地理位置');
  console.log('- 突出服务特色');
  console.log('');
  console.log('描述写作:');
  console.log('- 简洁明了（500字符内）');
  console.log('- 突出核心优势');
  console.log('- 包含行动号召');
  console.log('- 专业的语言风格');
}

// 字符限制对比
export function showCharacterLimitsComparison() {
  console.log('📏 字符限制对比:');
  console.log('');
  console.log('Free PR Web Directory:');
  console.log('- Description: 500字符');
  console.log('- META Description: 250字符');
  console.log('');
  console.log('其他目录对比:');
  console.log('- All States USA: 1000字符');
  console.log('- Australia Web: 1000字符');
  console.log('- Free PR Web: 500字符 ⚠️ 更严格');
  console.log('');
  console.log('优化建议:');
  console.log('- 精简描述内容');
  console.log('- 突出核心信息');
  console.log('- 避免冗余表达');
  console.log('- 使用关键词密度');
}

// 付费选项分析
export function showPaidOptionAnalysis() {
  console.log('💰 付费选项分析:');
  console.log('');
  console.log('价格对比:');
  console.log('- Free PR Web: $9.99/年');
  console.log('- All States USA: $6.79');
  console.log('- Australia Web: $12.95/年');
  console.log('');
  console.log('审核时间:');
  console.log('- Free PR Web: 24-48小时');
  console.log('- All States USA: 24小时');
  console.log('- Australia Web: 几小时');
  console.log('');
  console.log('性价比分析:');
  console.log('- 中等价格水平');
  console.log('- 合理的审核时间');
  console.log('- 专业的编辑服务');
  console.log('- 适合PR网站');
}

// 表单验证
export function validateFreePRWebDirectoryForm() {
  console.log('验证Free PR Web Directory表单...');

  const requiredFields = [
    { selector: 'input[name="TITLE"]', label: '网站标题' },
    { selector: 'input[name="OWNER_NAME"]', label: '您的姓名' },
    { selector: 'input[name="OWNER_EMAIL"]', label: '您的邮箱' }
  ];

  let isValid = true;

  requiredFields.forEach(field => {
    const element = document.querySelector(field.selector);
    if (!element || !element.value.trim()) {
      console.log(`⚠️ 必填字段为空: ${field.label}`);
      isValid = false;
    }
  });

  // 检查定价选项
  const radioButtons = document.querySelectorAll('input[name="LINK_TYPE"]:checked');
  if (radioButtons.length === 0) {
    console.log('⚠️ 请选择定价选项');
    isValid = false;
  }

  // 检查字符限制
  const description = document.querySelector('textarea[name="DESCRIPTION"]');
  if (description && description.value.length > 500) {
    console.log('⚠️ 描述超过500字符限制');
  }

  const metaDescription = document.querySelector('textarea[name="META_DESCRIPTION"]');
  if (metaDescription && metaDescription.value.length > 250) {
    console.log('⚠️ META描述超过250字符限制');
  }

  if (isValid) {
    console.log('✓ 表单验证通过');
  }

  return isValid;
}

// PR目录网站类型
export function showPRWebsiteTypes() {
  console.log('🎯 适合PR目录的网站类型:');
  console.log('');
  console.log('公关公司:');
  console.log('- PR代理机构');
  console.log('- 媒体关系公司');
  console.log('- 危机管理公司');
  console.log('- 品牌咨询公司');
  console.log('');
  console.log('营销服务:');
  console.log('- 数字营销公司');
  console.log('- 社交媒体管理');
  console.log('- 内容营销服务');
  console.log('- 影响者营销');
  console.log('');
  console.log('媒体相关:');
  console.log('- 新闻发布服务');
  console.log('- 媒体监测工具');
  console.log('- 新闻稿写作');
  console.log('- 媒体数据库');
  console.log('');
  console.log('企业服务:');
  console.log('- 企业传播');
  console.log('- 投资者关系');
  console.log('- 政府关系');
  console.log('- 社区关系');
}

// 目录网站系列对比
export function showDirectorySeriesComparison() {
  console.log('🌐 目录网站系列对比:');
  console.log('');
  console.log('已配置的目录网站:');
  console.log('1. australiawebdirectory.net - 澳大利亚');
  console.log('2. allstatesusadirectory.com - 美国各州');
  console.log('3. freeprwebdirectory.com - PR公关 ✅ 当前');
  console.log('');
  console.log('共同特点:');
  console.log('- 相同的表单结构');
  console.log('- 相同的字段名称');
  console.log('- PHP表单系统');
  console.log('- 免费和付费选项');
  console.log('- META字段支持');
  console.log('');
  console.log('差异化定位:');
  console.log('- 地理定位 vs 行业定位');
  console.log('- 通用目录 vs 专业目录');
  console.log('- 不同的审核标准');
  console.log('- 不同的用户群体');
}
