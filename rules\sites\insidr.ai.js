// Insidr.ai 网站规则配置
// 网站: https://www.insidr.ai/submit-tools/
// 最后更新: 2025-07-06

export const SITE_RULE = {
  // 基本信息
  domain: 'insidr.ai',
  siteName: 'Insidr',
  priority: 1,
  lastUpdated: '2025-07-06',
  
  // 字段映射规则
  fieldMappings: {
    // 联系邮箱 -> Email
    contactEmail: {
      selectors: [
        'input[name="email"]',
        'input[type="email"]',
        'input[placeholder*="Enter your email"]',
        'input.sc-gZMcBi.hLeoHq'
      ],
      method: 'value',
      validation: 'required|email',
      notes: '联系邮箱地址'
    },

    // 工具描述 -> Message (what the AI tool can do)
    siteDescription: {
      selectors: [
        'textarea[name="form_fields[message]"]',
        '#form-field-message',
        'textarea[placeholder*="what the AI tool can do"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '简短描述AI工具的功能'
    },

    // 网站URL -> Link (name field实际是link)
    siteUrl: {
      selectors: [
        'input[name="form_fields[name]"]',
        '#form-field-name',
        'input[placeholder*="Link to the AI tool"]'
      ],
      method: 'value',
      validation: 'required|url',
      notes: '工具链接，如果可能请提供联盟注册链接'
    },

    // 工具分类 -> Tag (email field实际是category)
    category: {
      selectors: [
        'input[name="form_fields[email]"]',
        '#form-field-email',
        'input[placeholder*="Tool category"]'
      ],
      method: 'value',
      validation: 'required',
      defaultValue: 'AI Tools', // 默认分类
      notes: '工具分类标签'
    }
  },
  
  // 提交流程配置
  submitConfig: {
    submitButton: 'button.elementor-button[type="submit"]',
    submitMethod: 'click',
    waitAfterFill: 1500, // 填写后等待1.5秒
    waitAfterSubmit: 3000, // 提交后等待3秒
    successIndicators: [
      '.elementor-message.elementor-message-success',
      '.success-message',
      '.elementor-form-success'
    ],
    errorIndicators: [
      '.elementor-message.elementor-message-danger',
      '.error-message',
      '.elementor-form-error'
    ]
  },
  
  // 特殊处理
  specialHandling: {
    requiresLogin: false,
    hasCaptcha: false,
    hasFileUpload: false,
    
    // 表单验证规则
    formValidation: {
      requiredFields: ['contactEmail', 'siteDescription', 'siteUrl', 'category'],
      emailValidation: true,
      urlValidation: true
    },
    
    // 特殊注意事项
    notes: [
      '表单有4个字段：邮箱、工具描述、工具链接、工具分类',
      '表单字段名称与实际用途不匹配（name字段实际是链接，email字段实际是分类）',
      '需要简短描述工具功能',
      '如果可能，提供联盟注册链接',
      '工具分类要准确'
    ]
  }
};

// 自定义处理函数
export function handleInsidrSubmission(data, rule) {
  console.log('Processing Insidr.ai submission...');
  
  // 特殊处理逻辑
  const processedData = { ...data };
  
  // 确保描述简洁明了
  if (processedData.siteDescription) {
    processedData.siteDescription = processedData.siteDescription.trim();
    // 如果描述太长，截取前200字符
    if (processedData.siteDescription.length > 200) {
      processedData.siteDescription = processedData.siteDescription.substring(0, 200) + '...';
    }
  }
  
  // 确保URL格式正确
  if (processedData.siteUrl && !processedData.siteUrl.startsWith('http')) {
    processedData.siteUrl = 'https://' + processedData.siteUrl;
  }
  
  // 设置合适的分类（使用默认值）
  if (!processedData.category) {
    processedData.category = 'AI Tools'; // 默认分类
  }
  
  return processedData;
}
