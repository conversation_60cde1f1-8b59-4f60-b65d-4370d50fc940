// AIBestTop.com 网站规则配置
// 网站: https://www.aibesttop.com/submit
// 最后更新: 2025-07-24

export const SITE_RULE = {
  // 基本信息
  domain: 'www.aibesttop.com',
  siteName: 'AIBestTop',
  priority: 1,
  lastUpdated: '2025-07-24',

  // 字段映射规则
  fieldMappings: {
    // 网站名称 -> Website Name
    siteName: {
      selectors: [
        'input[name="website"]',
        'input[placeholder="aibesttop AI"]',
        'label:contains("Website Name") + input'
      ],
      method: 'value',
      validation: 'required',
      notes: '网站名称'
    },

    // 网站URL -> Website URL
    siteUrl: {
      selectors: [
        'input[name="url"]',
        'input[placeholder="https://aibesttop.com/"]',
        'label:contains("Website URL") + input'
      ],
      method: 'value',
      validation: 'required|url',
      notes: '网站URL地址'
    }
  },

  // 提交流程配置
  submitConfig: {
    submitButton: 'button[type="submit"], .bg-white',
    submitMethod: 'manual', // 手动提交
    waitAfterFill: 0,
    waitAfterSubmit: 0,
    successIndicators: ['.success-message'],
    errorIndicators: ['.error-message']
  },

  // 特殊处理
  specialHandling: {
    requiresLogin: false,
    hasCaptcha: false,
    hasFileUpload: false,
    customScript: 'handleAiBestTopSubmission',
    formValidation: {
      requiredFields: ['siteName', 'siteUrl'],
      emailValidation: false,
      urlValidation: true
    },
    notes: [
      'AIBestTop AI工具目录提交平台',
      '极简表单，只有网站名称和URL两个字段',
      '需要在网站首页添加反向链接',
      '反向链接格式：<a href="https://aibesttop.com/" title="aibesttop AI Tools Directory">aibesttop AI Tools Directory</a>',
      '深色主题界面',
      '手动提交模式'
    ]
  }
};

// 自定义处理函数
export function handleAiBestTopSubmission(data) {
  console.log('Processing AIBestTop form submission...');

  const processedData = { ...data };

  // 确保URL格式正确
  if (processedData.siteUrl && !processedData.siteUrl.startsWith('http')) {
    processedData.siteUrl = 'https://' + processedData.siteUrl;
  }

  return processedData;
}

// 自定义元素填写函数
export async function customFillElement(element, value, config) {
  console.log(`AIBestTop自定义填写: ${element.tagName}, 方法: ${config.method}`);

  switch (config.method) {
    case 'value':
      // 标准输入框处理
      element.focus();
      await new Promise(resolve => setTimeout(resolve, 200));

      element.value = value;
      element.dispatchEvent(new Event('input', { bubbles: true }));
      element.dispatchEvent(new Event('change', { bubbles: true }));
      element.dispatchEvent(new Event('blur', { bubbles: true }));

      console.log(`✓ 填写字段: ${element.name} = "${value}"`);
      return true;

    default:
      element.value = value;
      element.dispatchEvent(new Event('input', { bubbles: true }));
      return true;
  }

  return false;
}