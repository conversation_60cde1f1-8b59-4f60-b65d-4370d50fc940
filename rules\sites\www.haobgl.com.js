// Haobgl.com 网站规则配置
// 网站: https://www.haobgl.com/article-toadd.html
// 最后更新: 2025-07-24

export const SITE_RULE = {
  // 基本信息
  domain: 'www.haobgl.com',
  siteName: 'Haobgl',
  priority: 1,
  lastUpdated: '2025-07-24',

  // 字段映射规则
  fieldMappings: {
    // 网站名称 -> 名称
    siteName: {
      selectors: [
        'input[name="post_title"]',
        '.sites-title',
        'input[placeholder="名称"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '网站名称，最多30字符'
    },

    // 网站URL -> 链接
    siteUrl: {
      selectors: [
        'input[name="link"]',
        '.sites-link',
        'input[placeholder="链接"]'
      ],
      method: 'value',
      validation: 'required|url',
      notes: '网站链接'
    },

    // 网站描述 -> 简介
    siteDescription: {
      selectors: [
        'input[name="sescribe"]',
        '.sites-des',
        'input[placeholder="简介"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '网站简介，最多80字符'
    },

    // 详细介绍 -> 输入网址介绍
    detailedIntro: {
      selectors: [
        'textarea[name="post_content"]',
        'textarea[placeholder="输入网址介绍"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '详细介绍'
    },

    // 分类选择 -> 分类
    category: {
      selectors: [
        'select[name="category"]',
        '#post_cat',
        '.form-select select'
      ],
      method: 'select',
      validation: 'required',
      notes: '分类选择，根据网站类型选择合适分类'
    },

    // 关键词标签 -> 标签
    keywords: {
      selectors: [
        'textarea[name="tags"]',
        '.sites-keywords',
        'textarea[placeholder="输入标签"]'
      ],
      method: 'value',
      validation: 'optional',
      notes: '标签，用逗号分隔'
    },

    // 提交者姓名 -> 昵称
    fullName: {
      selectors: [
        'input[name="user_name"]',
        'input[placeholder="请输入昵称"]'
      ],
      method: 'value',
      validation: 'optional',
      notes: '提交者昵称'
    },

    // 联系方式 -> 联系方式
    contactEmail: {
      selectors: [
        'input[name="contact_details"]',
        'input[placeholder="输入联系方式"]'
      ],
      method: 'value',
      validation: 'optional',
      notes: '联系方式，可以是邮箱或电话'
    }
  },
  // 提交流程配置
  submitConfig: {
    submitButton: '#submit, .btn-submit',
    submitMethod: 'click',
    waitAfterFill: 2000,
    waitAfterSubmit: 5000,
    successIndicators: ['.success-message'],
    errorIndicators: ['.error-message']
  },

  // 特殊处理
  specialHandling: {
    requiresLogin: false,
    hasCaptcha: false,
    hasFileUpload: false,
    customScript: 'handleHaobglSubmission',
    formValidation: {
      requiredFields: ['siteName', 'siteUrl', 'siteDescription', 'detailedIntro', 'category'],
      emailValidation: false,
      urlValidation: true
    },
    notes: [
      'Haobgl 网站收录投稿平台',
      '支持多种网站分类',
      '有详细的分类选项',
      '中文界面',
      '字符数限制提示',
      '与zhexieai.com使用相同系统源码'
    ]
  }
};

// 自定义处理函数
export function handleHaobglSubmission(data, rule) {
  console.log('Processing Haobgl form submission...');

  const processedData = { ...data };

  // 确保URL格式正确
  if (processedData.siteUrl && !processedData.siteUrl.startsWith('http')) {
    processedData.siteUrl = 'https://' + processedData.siteUrl;
  }

  // 处理关键词格式
  if (processedData.keywords) {
    // 如果是数组，转换为逗号分隔的字符串
    if (Array.isArray(processedData.keywords)) {
      processedData.keywords = processedData.keywords.join(', ');
    }
    processedData.keywords = processedData.keywords.trim();
  }

  // 处理描述长度限制
  if (processedData.siteDescription && processedData.siteDescription.length > 80) {
    processedData.siteDescription = processedData.siteDescription.substring(0, 77) + '...';
  }

  // 处理网站名称长度限制
  if (processedData.siteName && processedData.siteName.length > 30) {
    processedData.siteName = processedData.siteName.substring(0, 30);
  }

  return processedData;
}

// 自定义元素填写函数
export async function customFillElement(element, value, config) {
  // 处理分类下拉选择框
  if (element.tagName === 'SELECT' && element.name === 'category') {
    const options = element.querySelectorAll('option');
    let selectedOption;

    // 根据网站分类智能选择
    const categoryMapping = {
      'Ai Tools': ['其他AI', '文本AI', '图片AI'],
      'Education': ['学习', 'AI课程'],
      'Productivity': ['办公', '自动化AI'],
      'Design': ['图片AI', '绘图AI'],
      'Writing': ['文本AI', '写作'],
      'Video': ['视频AI'],
      'Audio': ['音频AI'],
      'Chat': ['聊天AI'],
      'Code': ['编程AI']
    };

    // 尝试智能匹配分类
    for (const option of options) {
      if (option.value !== '0') {
        const optionText = option.textContent.trim();
        if (optionText.includes('其他AI') || optionText.includes('文本AI')) {
          selectedOption = option;
          break;
        }
      }
    }

    // 如果没找到合适的，选择第一个非默认选项
    if (!selectedOption) {
      selectedOption = Array.from(options).find(opt => opt.value !== '0');
    }

    if (selectedOption) {
      element.value = selectedOption.value;
      element.dispatchEvent(new Event('change', { bubbles: true }));
      return true;
    }
  }

  // 处理标准输入框和文本域
  if (element.tagName === 'INPUT' || element.tagName === 'TEXTAREA') {
    element.value = value;
    element.dispatchEvent(new Event('input', { bubbles: true }));
    element.dispatchEvent(new Event('change', { bubbles: true }));
    return true;
  }

  return false;
}