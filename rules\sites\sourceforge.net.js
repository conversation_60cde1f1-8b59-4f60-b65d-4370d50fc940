// sourceforge.net 网站规则配置
// 网站: https://sourceforge.net/software/vendors/new
// 表单技术: Professional Business Software Directory
// 最后更新: 2025-07-08

export const SITE_RULE = {
  // 基本信息
  domain: 'sourceforge.net',
  siteName: 'SourceForge Business Directory',
  priority: 1,
  lastUpdated: '2025-07-08',
  
  // 字段映射规则
  fieldMappings: {
    // 您的姓名 -> Your Name
    fullName: {
      selectors: [
        'input[name="name"]',
        'input[placeholder*="Your Name"]',
        'form input[type="text"]:first-of-type',
        'label:contains("Your Name") + input'
      ],
      method: 'value',
      validation: 'required',
      notes: '您的姓名，使用website-info.js中的fullName字段'
    },
    
    // 邮箱地址 -> Email Address
    contactEmail: {
      selectors: [
        'input[name="email"]',
        'input[type="email"]',
        'input[placeholder*="Email"]',
        'label:contains("Email Address") + input'
      ],
      method: 'value',
      validation: 'required|email',
      notes: '邮箱地址，使用website-info.js中的contactEmail字段'
    },
    
    // 公司名称 -> Company
    companyName: {
      selectors: [
        'input[name="company"]',
        'input[placeholder*="Company"]',
        'label:contains("Company") + input',
        'form input[type="text"]:nth-of-type(3)'
      ],
      method: 'value',
      validation: 'required',
      notes: '公司名称，使用website-info.js中的companyName字段'
    },
    
    // 职位 -> Your Title
    jobTitle: {
      selectors: [
        'input[name="title"]',
        'input[name="job_title"]',
        'input[placeholder*="Title"]',
        'label:contains("Your Title") + input'
      ],
      method: 'value',
      validation: 'required',
      defaultValue: 'Product Manager',
      notes: '职位，使用website-info.js中的jobTitle字段或默认值'
    },
    
    // 网站 -> Website
    siteUrl: {
      selectors: [
        'input[name="website"]',
        'input[name="url"]',
        'input[type="url"]',
        'label:contains("Website") + input'
      ],
      method: 'value',
      validation: 'required|url',
      notes: '网站URL，使用website-info.js中的siteUrl字段'
    },
    
    // 电话 -> Phone
    phone: {
      selectors: [
        'input[name="phone"]',
        'input[type="tel"]',
        'input[placeholder*="Phone"]',
        'label:contains("Phone") + input'
      ],
      method: 'value',
      validation: 'optional',
      defaultValue: '******-578-3358',
      notes: '电话号码，使用website-info.js中的phone字段或默认值'
    },
    
    // 公司成立年份 -> Company Founded
    foundedYear: {
      selectors: [
        'input[name="founded"]',
        'input[name="company_founded"]',
        'select[name="founded_year"]',
        'label:contains("Company Founded") + input'
      ],
      method: 'value',
      validation: 'optional',
      defaultValue: '2025',
      notes: '公司成立年份，默认2025'
    },
    
    // 公司位置 -> Company Location
    companyLocation: {
      selectors: [
        'input[name="location"]',
        'input[name="company_location"]',
        'select[name="location"]',
        'label:contains("Company Location") + input'
      ],
      method: 'value',
      validation: 'optional',
      defaultValue: 'United States',
      notes: '公司位置，默认美国'
    },
    
    // 软件标题 -> Software Title
    siteName: {
      selectors: [
        'input[id="software-name"]',
        'input[name="software_name"]',
        'input[name="software_title"]',
        'input[maxlength="52"]'
      ],
      method: 'value',
      validation: 'required',
      maxLength: 52,
      notes: '软件标题，使用website-info.js中的siteName字段，最大52字符'
    },

    // 分类 -> Category
    category: {
      selectors: [
        'select[name="categories"]',
        '.select2-selection__rendered',
        '#select2-categories-7d-container'
      ],
      method: 'select2',
      validation: 'required',
      defaultValue: 'AI Content Generators',
      notes: '分类，默认选择AI Content Generators，使用Select2组件'
    },

    // 目标受众 -> Audience
    userCases: {
      selectors: [
        'input[name="audience"]',
        'input[placeholder*="What type of users"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '目标受众，使用website-info.js中的userCases字段'
    },

    // 起始价格 -> Starting Price
    startingPrice: {
      selectors: [
        'input[id="optimize-pricing"]',
        'input[name="starting_price"]',
        'input[placeholder*="$30/month/user"]'
      ],
      method: 'value',
      validation: 'optional',
      defaultValue: 'Free',
      notes: '起始价格，默认Free'
    },

    // 免费选项 -> Free Options
    freeOptions: {
      selectors: [
        'input[name="free_options"][value="Free Version"]'
      ],
      method: 'checkbox',
      validation: 'optional',
      defaultValue: true,
      notes: '免费选项，选择Free Version'
    },

    // 支持选项 -> Support Options
    supportOptions: {
      selectors: [
        'input[name="support"][value="Not Offered"]'
      ],
      method: 'checkbox',
      validation: 'optional',
      defaultValue: true,
      notes: '支持选项，选择Not Offered'
    },

    // 培训选项 -> Training Options
    trainingOptions: {
      selectors: [
        'input[name="training"][value="Not Offered"]'
      ],
      method: 'checkbox',
      validation: 'optional',
      defaultValue: true,
      notes: '培训选项，选择Not Offered'
    },

    // 支持平台 -> Platforms Supported
    platformSaaS: {
      selectors: [
        'input[name="deployment"][value="SaaS"]'
      ],
      method: 'checkbox',
      validation: 'optional',
      defaultValue: true,
      notes: '支持平台，选择SaaS / Web'
    },

    platformWindows: {
      selectors: [
        'input[name="deployment"][value="Windows"]'
      ],
      method: 'checkbox',
      validation: 'optional',
      defaultValue: true,
      notes: '支持平台，选择Windows - Installed'
    },

    platformMac: {
      selectors: [
        'input[name="deployment"][value="Mac"]'
      ],
      method: 'checkbox',
      validation: 'optional',
      defaultValue: true,
      notes: '支持平台，选择Mac - Installed'
    },
    
    // Logo上传 -> Logo
    logoUpload: {
      selectors: [
        'input[name="logo"]',
        'input[type="file"]',
        'input[accept*="image"]',
        'label:contains("Logo") + input'
      ],
      method: 'file-upload',
      validation: 'optional',
      acceptedFormats: 'JPG, GIF, PNG',
      notes: 'Logo上传，推荐正方形高分辨率图片'
    },
    
    // 产品描述 -> Product Description
    detailedIntro: {
      selectors: [
        'textarea[name="description"]',
        'textarea[name="product_description"]',
        'textarea[placeholder*="Product Description"]',
        'label:contains("Product Description") + textarea'
      ],
      method: 'value',
      validation: 'required',
      notes: '产品描述，使用website-info.js中的detailedIntro字段'
    },
    
    // 目标受众 -> Audience
    targetAudience: {
      selectors: [
        'select[name="audience"]',
        'input[name="target_audience"]',
        'label:contains("Audience") + select',
        'label:contains("Audience") + input'
      ],
      method: 'select-or-value',
      validation: 'optional',
      defaultValue: 'Business Professionals',
      notes: '目标受众，默认商业专业人士'
    }
  },
  
  // 自定义填写函数
  customFillElement: async function(element, value, config) {
    console.log(`SourceForge自定义填写: ${element.name || element.placeholder}, 方法: ${config.method}`);
    
    switch (config.method) {
      case 'value':
        // 标准输入框和文本域处理
        element.focus();
        await new Promise(resolve => setTimeout(resolve, 100));
        
        // 清空现有内容
        element.value = '';
        await new Promise(resolve => setTimeout(resolve, 50));
        
        // 处理特殊字段
        let finalValue = value;
        if (config.defaultValue && (!value || value.trim() === '')) {
          finalValue = config.defaultValue;
        }
        
        // 设置新值
        element.value = finalValue;
        
        // 触发事件
        element.dispatchEvent(new Event('input', { bubbles: true }));
        element.dispatchEvent(new Event('change', { bubbles: true }));
        element.dispatchEvent(new Event('blur', { bubbles: true }));
        
        console.log(`✓ 填写字段: ${element.name} = "${finalValue.substring(0, 50)}..."`);
        break;
        
      case 'select2':
        // Select2组件处理
        console.log('⚠️ Select2组件需要特殊处理');
        console.log(`请手动选择分类: ${config.defaultValue}`);
        // 尝试点击Select2容器
        const select2Container = document.querySelector('.select2-selection__rendered');
        if (select2Container) {
          select2Container.click();
          await new Promise(resolve => setTimeout(resolve, 500));

          // 查找选项
          const option = document.querySelector(`[title="${config.defaultValue}"]`);
          if (option) {
            option.click();
            console.log(`✓ 选择Select2选项: ${config.defaultValue}`);
          }
        }
        break;

      case 'checkbox':
        // 复选框处理
        if (config.defaultValue) {
          element.checked = true;
          element.dispatchEvent(new Event('change', { bubbles: true }));
          console.log(`✓ 勾选复选框: ${element.value}`);
        }
        break;

      case 'select-or-value':
        // 下拉选择或输入框处理
        if (element.tagName.toLowerCase() === 'select') {
          // 下拉选择
          const targetValue = config.defaultValue || value;
          const option = Array.from(element.options).find(opt =>
            opt.text.includes(targetValue) || opt.value.includes(targetValue)
          );

          if (option) {
            element.value = option.value;
            element.dispatchEvent(new Event('change', { bubbles: true }));
            console.log(`✓ 选择选项: ${option.text}`);
          }
        } else {
          // 输入框
          element.value = config.defaultValue || value;
          element.dispatchEvent(new Event('input', { bubbles: true }));
          element.dispatchEvent(new Event('change', { bubbles: true }));
        }
        break;
        
      case 'file-upload':
        // 文件上传处理
        console.log('⚠️ 文件上传字段需要手动处理');
        console.log(`请手动上传Logo文件 (${config.acceptedFormats})`);
        console.log('推荐：正方形高分辨率图片');
        break;
        
      default:
        element.value = value;
        element.dispatchEvent(new Event('input', { bubbles: true }));
        element.dispatchEvent(new Event('change', { bubbles: true }));
    }
  },
  
  // 提交流程配置
  submitConfig: {
    submitButton: [
      'button[type="submit"]',
      'input[type="submit"]',
      'button:contains("Submit")',
      'input[value*="Submit"]'
    ],
    submitMethod: 'click',
    waitAfterFill: 3000,
    waitAfterSubmit: 5000,
    successIndicators: [
      'text:contains("submitted")',
      'text:contains("received")',
      'text:contains("thank you")',
      'text:contains("review")'
    ],
    errorIndicators: [
      'text:contains("error")',
      'text:contains("required")',
      'text:contains("invalid")'
    ]
  },
  
  // 特殊处理
  specialHandling: {
    requiresLogin: false,
    hasCaptcha: false,
    hasFileUpload: true, // 有Logo上传
    isProfessionalDirectory: true, // 专业软件目录
    requiresJavaScript: true, // 需要JavaScript
    hasTermsAgreement: true, // 有条款协议
    
    // 表单验证规则
    formValidation: {
      requiredFields: ['fullName', 'contactEmail', 'companyName', 'jobTitle', 'siteUrl', 'siteName', 'detailedIntro', 'category', 'userCases'],
      optionalFields: ['phone', 'foundedYear', 'companyLocation', 'logoUpload', 'targetAudience', 'startingPrice', 'freeOptions', 'supportOptions', 'trainingOptions', 'platformSaaS', 'platformWindows', 'platformMac'],
      emailValidation: true,
      urlValidation: true,
      fileUploadFields: ['logoUpload'],
      characterLimits: {
        siteName: 52
      },
      select2Fields: ['category'],
      checkboxFields: ['freeOptions', 'supportOptions', 'trainingOptions', 'platformSaaS', 'platformWindows', 'platformMac']
    },
    
    // 特殊注意事项
    notes: [
      '这是SourceForge商业软件目录的提交表单',
      '表单包含19个字段：9个必填，10个可选',
      '需要详细的公司和产品信息',
      '有Logo上传功能（可选）',
      '需要同意SourceForge条款和隐私政策',
      '表单需要JavaScript支持',
      '目录包含108,600+软件标题',
      '提供高级营销包升级选项',
      '专注于商业软件和服务',
      '由Slashdot Media运营',
      '软件标题最大52字符',
      '使用Select2组件选择分类',
      '默认选择AI Content Generators分类',
      '支持多种平台：SaaS/Web、Windows、Mac',
      '免费选项、支持和培训默认选择Not Offered',
      '起始价格默认为Free'
    ]
  }
};

// 自定义处理函数
export function handleSourceForgeSubmission(data, _rule) {
  console.log('Processing SourceForge form submission...');

  const processedData = { ...data };

  // 确保URL格式正确
  if (processedData.siteUrl && !processedData.siteUrl.startsWith('http')) {
    processedData.siteUrl = 'https://' + processedData.siteUrl;
  }

  // 处理字符限制
  if (processedData.siteName && processedData.siteName.length > 52) {
    processedData.siteName = processedData.siteName.substring(0, 52);
  }

  // 设置默认值
  if (!processedData.jobTitle) {
    processedData.jobTitle = 'Product Manager';
  }

  if (!processedData.phone) {
    processedData.phone = '******-578-3358';
  }

  if (!processedData.foundedYear) {
    processedData.foundedYear = '2025';
  }

  if (!processedData.companyLocation) {
    processedData.companyLocation = 'United States';
  }

  if (!processedData.targetAudience) {
    processedData.targetAudience = 'Business Professionals';
  }

  // 设置新字段默认值
  processedData.category = 'AI Content Generators';
  processedData.startingPrice = 'Free';
  processedData.freeOptions = true;
  processedData.supportOptions = true;
  processedData.trainingOptions = true;
  processedData.platformSaaS = true;
  processedData.platformWindows = true;
  processedData.platformMac = true;

  return processedData;
}

// 专业软件目录检测
export function detectProfessionalDirectory() {
  console.log('检测专业软件目录...');

  // 检查页面内容
  const pageText = document.body.textContent.toLowerCase();
  const keywords = ['business software', 'directory', 'vendors', 'commercial'];

  let foundKeywords = 0;
  keywords.forEach(keyword => {
    if (pageText.includes(keyword)) {
      foundKeywords++;
    }
  });

  if (foundKeywords >= 3) {
    console.log('✓ 确认为专业软件目录');
    return true;
  }

  return false;
}

// JavaScript要求检测
export function checkJavaScriptRequirement() {
  console.log('检查JavaScript要求...');

  const jsMessage = document.querySelector('text:contains("JavaScript is required")');
  if (jsMessage) {
    console.log('✓ 检测到JavaScript要求');
    return true;
  }

  return false;
}

// 条款协议检测
export function detectTermsAgreement() {
  console.log('检测条款协议...');

  const termsText = document.body.textContent.toLowerCase();
  if (termsText.includes('terms of use') && termsText.includes('privacy policy')) {
    console.log('✓ 检测到条款和隐私政策协议');
    return true;
  }

  return false;
}

// SourceForge信息提醒
export function showSourceForgeInfo() {
  console.log('📊 SourceForge 商业软件目录信息:');
  console.log('');
  console.log('平台特色:');
  console.log('- 108,600+ 软件标题');
  console.log('- 可按价格、功能、分类、受众比较');
  console.log('- 专业的商业软件目录');
  console.log('- 高级营销包升级选项');
  console.log('');
  console.log('提交要求:');
  console.log('- 详细的公司信息');
  console.log('- 完整的产品描述');
  console.log('- 可选的Logo上传');
  console.log('- 同意条款和隐私政策');
  console.log('');
  console.log('SourceForge - 专业软件发现平台！');
}

// 营销包信息
export function showMarketingPackages() {
  console.log('📈 SourceForge 营销包信息:');
  console.log('');
  console.log('升级选项:');
  console.log('- 高级软件档案');
  console.log('- 增强可见性');
  console.log('- 获得合格客户');
  console.log('- 专业营销支持');
  console.log('');
  console.log('联系方式:');
  console.log('- 总部: 225 Broadway Suite 1600, San Diego, CA 92101');
  console.log('- 电话: +1 (858) 422-6466');
  console.log('- 网站: sourceforge.net');
  console.log('');
  console.log('升级您的软件档案以获得更多客户！');
}

// 表单验证
export function validateSourceForgeForm() {
  console.log('验证SourceForge表单...');

  const requiredFields = [
    { name: 'name', label: '姓名' },
    { name: 'email', label: '邮箱' },
    { name: 'company', label: '公司名称' },
    { name: 'title', label: '职位' },
    { name: 'website', label: '网站' },
    { name: 'software_title', label: '软件标题' },
    { name: 'description', label: '产品描述' }
  ];

  let isValid = true;

  requiredFields.forEach(field => {
    const element = document.querySelector(`input[name="${field.name}"], textarea[name="${field.name}"]`);
    if (!element || !element.value.trim()) {
      console.log(`⚠️ 必填字段为空: ${field.label}`);
      isValid = false;
    }
  });

  // 检查条款协议
  const termsCheckbox = document.querySelector('input[type="checkbox"]');
  if (termsCheckbox && !termsCheckbox.checked) {
    console.log('⚠️ 需要同意条款和隐私政策');
    isValid = false;
  }

  if (isValid) {
    console.log('✓ 表单验证通过');
  }

  return isValid;
}

// Logo上传提醒
export function showLogoUploadReminder() {
  console.log('📁 Logo上传提醒:');
  console.log('');
  console.log('Logo要求:');
  console.log('- 推荐正方形图片');
  console.log('- 高分辨率');
  console.log('- 支持格式: JPG, GIF, PNG');
  console.log('- 文件大小: 建议小于5MB');
  console.log('');
  console.log('Logo将用于软件目录展示！');
}
