// LaunchingNext.com 网站规则配置
// 网站: https://www.launchingnext.com/submit/
// 最后更新: 2025-07-24

export const SITE_RULE = {
  domain: 'www.launchingnext.com',
  siteName: 'Launching Next',
  priority: 1,
  lastUpdated: '2025-07-24',

  fieldMappings: {
    siteName: {
      selectors: [
        'input[name="startupname"]',
        'label:contains("Startup Name") + input',
        '.form-control[name="startupname"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '创业公司名称，最多100字符'
    },

    siteUrl: {
      selectors: [
        'input[name="startupurl"]',
        'label:contains("Startup URL") + input',
        '.form-control[name="startupurl"]'
      ],
      method: 'value',
      validation: 'required|url',
      notes: '创业公司URL，预填https://'
    },

    siteDescription: {
      selectors: [
        'input[name="description"]',
        'label:contains("Your headline") + input',
        '.form-control[name="description"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '标语，5-8个词描述创业公司'
    },

    detailedIntro: {
      selectors: [
        'textarea[name="fulldescription"]',
        '#fulldescription',
        'label:contains("Full startup description") + textarea'
      ],
      method: 'value',
      validation: 'required',
      notes: '详细描述，最多2500字符'
    },

    tags: {
      selectors: [
        'textarea[name="tags"]',
        'label:contains("Tags") + textarea',
        '.form-control[name="tags"]'
      ],
      method: 'value',
      validation: 'optional',
      notes: '标签，5-10个用逗号分隔，最多500字符'
    },

    fundingType: {
      selectors: [
        'input[name="funding"][value="2"]',
        'input[type="radio"][value="2"]',
        'label:contains("bootstrapped startup") input'
      ],
      method: 'radio',
      validation: 'optional',
      defaultValue: '2',
      notes: '融资类型，默认选择bootstrapped startup'
    },

    boardMembers: {
      selectors: [
        'input[name="boardmembers"][value="0"]',
        'input[type="radio"][value="0"]',
        'label:contains("board members") + input[value="0"]'
      ],
      method: 'radio',
      validation: 'optional',
      defaultValue: '0',
      notes: '是否寻求董事会成员，默认No'
    },

    fullName: {
      selectors: [
        'input[name="user"]',
        'label:contains("Your Name") + input',
        '.form-control[name="user"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '提交者姓名'
    },

    contactEmail: {
      selectors: [
        'input[name="email"]',
        'label:contains("Your Email Address") + input',
        '.form-control[name="email"]'
      ],
      method: 'value',
      validation: 'required|email',
      notes: '联系邮箱'
    },

    mathCaptcha: {
      selectors: [
        'input[name="math"]',
        'label:contains("What\'s 2+3?") + input',
        '.form-control[name="math"]'
      ],
      method: 'value',
      validation: 'required',
      defaultValue: '5',
      notes: '数学验证码，2+3=5'
    }
  },

  submitConfig: {
    submitButton: 'input[name="formSubmit"], .btn-primary',
    submitMethod: 'click',
    successIndicators: ['.success-message'],
    errorIndicators: ['.error-message']
  },

  specialHandling: {
    requiresLogin: false,
    hasCaptcha: true,
    hasFileUpload: false,
    customScript: 'handleLaunchingNextSubmission',
    formValidation: {
      requiredFields: ['siteName', 'siteUrl', 'siteDescription', 'detailedIntro', 'fullName', 'contactEmail', 'mathCaptcha'],
      emailValidation: true,
      urlValidation: true
    },
    notes: [
      '创业项目提交平台',
      '有数学验证码2+3=5',
      '字符数限制严格',
      '支持标签和融资类型',
      '董事会成员推荐服务',
      '详细的项目描述要求'
    ]
  }
};

export function handleLaunchingNextSubmission(data, rule) {
  const processedData = { ...data };

  // 确保URL格式正确
  if (processedData.siteUrl && !processedData.siteUrl.startsWith('http')) {
    processedData.siteUrl = 'https://' + processedData.siteUrl;
  }

  // 自动填写数学验证码
  processedData.mathCaptcha = '5';

  // 设置默认融资类型为bootstrapped startup
  const fundingRadio = document.querySelector('input[name="funding"][value="2"]');
  if (fundingRadio) {
    fundingRadio.checked = true;
  }

  // 设置默认不寻求董事会成员
  const boardRadio = document.querySelector('input[name="boardmembers"][value="0"]');
  if (boardRadio) {
    boardRadio.checked = true;
  }

  return processedData;
}

export async function customFillElement(element, value, config) {
  // 处理单选按钮
  if (element.type === 'radio') {
    element.checked = true;
    element.dispatchEvent(new Event('change', { bubbles: true }));
    return true;
  }

  // 处理数学验证码
  if (element.name === 'math') {
    element.value = '5';
    element.dispatchEvent(new Event('input', { bubbles: true }));
    element.dispatchEvent(new Event('change', { bubbles: true }));
    return true;
  }

  // 处理字符计数的文本域
  if (element.name === 'fulldescription') {
    element.value = value;
    element.dispatchEvent(new Event('input', { bubbles: true }));
    element.dispatchEvent(new Event('change', { bubbles: true }));

    // 更新字符计数
    const counter = document.getElementById('countchars');
    if (counter) {
      const remaining = 2500 - value.length;
      counter.textContent = remaining;
    }
    return true;
  }

  if (element.tagName === 'INPUT' || element.tagName === 'TEXTAREA') {
    element.value = value;
    element.dispatchEvent(new Event('input', { bubbles: true }));
    element.dispatchEvent(new Event('change', { bubbles: true }));
    return true;
  }

  return false;
}