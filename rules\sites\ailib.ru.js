// ailib.ru 网站规则配置
// 网站: https://ailib.ru/en/add-ai/free/
// 表单技术: WordPress Form with Multiple Select
// 最后更新: 2025-07-08

export const SITE_RULE = {
  // 基本信息
  domain: 'ailib.ru',
  siteName: 'AILib',
  priority: 1,
  lastUpdated: '2025-07-08',
  
  // 字段映射规则
  fieldMappings: {
    // 邮箱 -> E-mail
    contactEmail: {
      selectors: [
        'input[name="email"]',
        'input[type="email"]',
        'input[placeholder*="E-mail"]',
        'form input[type="email"]:first-of-type'
      ],
      method: 'value',
      validation: 'required|email',
      notes: '邮箱地址，使用website-info.js中的contactEmail字段'
    },
    
    // AI工具名称 -> Name of AI Tool
    siteName: {
      selectors: [
        'input[name="ai_name"]',
        'input[name="tool_name"]',
        'input[placeholder*="Name of AI Tool"]',
        'form input[type="text"]:first-of-type'
      ],
      method: 'value',
      validation: 'required',
      notes: 'AI工具名称，使用website-info.js中的siteName字段'
    },
    
    // 链接 -> Link
    siteUrl: {
      selectors: [
        'input[name="ai-link"]',
        'input[id="ai-link"]',
        'input[data-field-name="ai-link"]',
        'input.text-field'
      ],
      method: 'value',
      validation: 'required|url',
      notes: '工具链接，使用website-info.js中的siteUrl字段'
    },
    
    // 描述 -> Description
    siteDescription: {
      selectors: [
        'textarea[name="description"]',
        'textarea[placeholder*="Description"]',
        'form textarea:first-of-type'
      ],
      method: 'value',
      validation: 'required',
      notes: '工具描述，使用website-info.js中的siteDescription字段'
    },
    
    // 行业 -> Industry
    industry: {
      selectors: [
        'select[name="ai-specs[]"]',
        'select[id="ai-specs"]',
        'select[data-field-name="ai-specs"]',
        'select.select-field[multiple="1"]'
      ],
      method: 'select',
      validation: 'required',
      defaultValue: '202', // Design的value值
      notes: '行业分类，默认选择Design (value=202)，支持多选（按CTRL键）'
    },
    
    // 任务 -> Task
    task: {
      selectors: [
        'select[name="ai-task[]"]',
        'select[id="ai-task"]',
        'select[data-field-name="ai-task"]',
        'select.select-field[multiple="1"]:nth-of-type(2)'
      ],
      method: 'select',
      validation: 'required',
      defaultValue: '75', // Create text的value值
      notes: '任务类型，默认选择Create text (value=75)，支持多选（按CTRL键）'
    },
    
    // 转换方式 -> How to convert
    conversion: {
      selectors: [
        'select[name="ai-tag[]"]',
        'select[id="ai-tag"]',
        'select[data-field-name="ai-tag"]',
        'select.select-field[multiple="1"]:nth-of-type(3)'
      ],
      method: 'select',
      validation: 'required',
      defaultValue: '160', // Text to Text的value值
      notes: '转换方式，默认选择Text to Text (value=160)，支持多选（按CTRL键）'
    },
    
    // 免费选项 -> Free
    pricingFree: {
      selectors: [
        'input[name="ai_free"]',
        'input[value="ai_free"]',
        'input[data-field-name="ai_free"]',
        'input.checkboxes-field:contains("Free")'
      ],
      method: 'checkbox',
      validation: 'optional',
      defaultValue: true,
      notes: '免费选项，勾选Free复选框'
    },

    // 付费选项 -> Paid (不勾选)
    pricingPaid: {
      selectors: [
        'input[name="ai_paid"]',
        'input[value="ai_paid"]',
        'input[data-field-name="ai_paid"]'
      ],
      method: 'checkbox',
      validation: 'optional',
      defaultValue: false,
      notes: '付费选项，不勾选Paid复选框'
    },

    // 试用选项 -> Trial (不勾选)
    pricingTrial: {
      selectors: [
        'input[name="ai_trial"]',
        'input[value="ai_trial"]',
        'input[data-field-name="ai_trial"]'
      ],
      method: 'checkbox',
      validation: 'optional',
      defaultValue: false,
      notes: '试用选项，不勾选Trial access复选框'
    },

    // API选项 -> API (不勾选)
    pricingAPI: {
      selectors: [
        'input[name="ai_api"]',
        'input[value="ai_api"]',
        'input[data-field-name="ai_api"]'
      ],
      method: 'checkbox',
      validation: 'optional',
      defaultValue: false,
      notes: 'API选项，不勾选API复选框'
    },
    
    // 隐私协议 -> Privacy Agreement
    privacyAgreement: {
      selectors: [
        'input[type="checkbox"]',
        'input[name="privacy"]',
        'input[name="agreement"]'
      ],
      method: 'checkbox',
      validation: 'required',
      defaultValue: true,
      notes: '隐私协议，必须勾选同意处理个人信息'
    }
  },
  
  // 自定义填写函数
  customFillElement: async function(element, value, config) {
    console.log(`AILib自定义填写: ${element.name || element.tagName}, 方法: ${config.method}`);
    
    switch (config.method) {
      case 'value':
        // 标准输入框和文本域处理
        element.focus();
        await new Promise(resolve => setTimeout(resolve, 100));
        
        // 清空现有内容
        element.value = '';
        await new Promise(resolve => setTimeout(resolve, 50));
        
        // 设置新值
        element.value = value;
        
        // 触发事件
        element.dispatchEvent(new Event('input', { bubbles: true }));
        element.dispatchEvent(new Event('change', { bubbles: true }));
        element.dispatchEvent(new Event('blur', { bubbles: true }));
        
        console.log(`✓ 填写字段: ${value.substring(0, 50)}...`);
        break;
        
      case 'select':
        // 多选下拉框处理
        element.focus();
        await new Promise(resolve => setTimeout(resolve, 100));

        const targetValue = config.defaultValue;

        // 查找匹配的选项（按value值匹配）
        const option = Array.from(element.options).find(opt =>
          opt.value === targetValue
        );

        if (option) {
          option.selected = true;
          element.dispatchEvent(new Event('change', { bubbles: true }));
          console.log(`✓ 选择选项: ${option.text} (value: ${option.value})`);
        } else {
          console.log(`⚠️ 未找到选项值: ${targetValue}`);
          // 尝试按文本匹配作为备选
          const textOption = Array.from(element.options).find(opt =>
            opt.text.includes('Design') || opt.text.includes('Create text') || opt.text.includes('Text to Text')
          );
          if (textOption) {
            textOption.selected = true;
            element.dispatchEvent(new Event('change', { bubbles: true }));
            console.log(`✓ 备选匹配: ${textOption.text} (value: ${textOption.value})`);
          }
        }
        break;
        
      case 'select-or-radio':
        // 下拉选择或单选按钮处理
        if (element.tagName.toLowerCase() === 'select') {
          // 下拉选择逻辑
          const option = Array.from(element.options).find(opt => 
            opt.text.includes(config.defaultValue) || opt.value.includes(config.defaultValue)
          );
          if (option) {
            element.value = option.value;
            element.dispatchEvent(new Event('change', { bubbles: true }));
          }
        } else if (element.type === 'radio') {
          // 单选按钮逻辑
          element.checked = true;
          element.dispatchEvent(new Event('change', { bubbles: true }));
        }
        break;
        
      case 'checkbox':
        // 复选框处理
        if (config.defaultValue) {
          element.checked = true;
          element.dispatchEvent(new Event('change', { bubbles: true }));
          console.log(`✓ 勾选复选框: 隐私协议`);
        }
        break;
        
      default:
        element.value = value;
        element.dispatchEvent(new Event('input', { bubbles: true }));
        element.dispatchEvent(new Event('change', { bubbles: true }));
    }
  },
  
  // 提交流程配置
  submitConfig: {
    submitButton: [
      'button[type="submit"]',
      'input[type="submit"]',
      'button:contains("Send")',
      'input[value*="Send"]'
    ],
    submitMethod: 'click',
    waitAfterFill: 3000,
    waitAfterSubmit: 5000,
    successIndicators: [
      'text:contains("submitted")',
      'text:contains("success")',
      'text:contains("thank you")',
      'text:contains("received")'
    ],
    errorIndicators: [
      'text:contains("error")',
      'text:contains("required")',
      'text:contains("invalid")'
    ]
  },
  
  // 特殊处理
  specialHandling: {
    requiresLogin: false,
    hasCaptcha: false,
    hasFileUpload: false,
    isRussianSite: true, // 俄语网站
    hasMultiSelect: true, // 有多选字段
    hasPrivacyAgreement: true, // 有隐私协议
    isNeuralNetworkLibrary: true, // 神经网络库
    
    // 表单验证规则
    formValidation: {
      requiredFields: ['contactEmail', 'siteName', 'siteUrl', 'siteDescription', 'industry', 'task', 'conversion', 'privacyAgreement'],
      optionalFields: ['pricingFree', 'pricingPaid', 'pricingTrial', 'pricingAPI'],
      emailValidation: true,
      urlValidation: true,
      multiSelectFields: ['industry', 'task', 'conversion'],
      checkboxFields: ['privacyAgreement', 'pricingFree', 'pricingPaid', 'pricingTrial', 'pricingAPI']
    },
    
    // 特殊注意事项
    notes: [
      '这是AILib俄语神经网络库的英文版提交表单',
      '表单包含9个字段，全部必填',
      '支持多选字段（按CTRL键选择多个选项）',
      '专注于神经网络和AI工具收录',
      '有详细的行业和任务分类',
      '支持多种转换方式',
      '必须同意隐私协议',
      '提交后可以支持项目',
      '有Telegram机器人集成',
      '提供免费ChatGPT访问'
    ]
  }
};

// 自定义处理函数
export function handleAILibSubmission(data, _rule) {
  console.log('Processing AILib form submission...');

  const processedData = { ...data };

  // 确保URL格式正确
  if (processedData.siteUrl && !processedData.siteUrl.startsWith('http')) {
    processedData.siteUrl = 'https://' + processedData.siteUrl;
  }

  // 设置默认值
  processedData.industry = '202'; // Design
  processedData.task = '75'; // Create text
  processedData.conversion = '160'; // Text to Text
  processedData.pricingFree = true; // 勾选Free
  processedData.pricingPaid = false; // 不勾选Paid
  processedData.pricingTrial = false; // 不勾选Trial
  processedData.pricingAPI = false; // 不勾选API
  processedData.privacyAgreement = true;

  return processedData;
}

// 多选字段处理
export async function handleMultiSelectField(selectElement, values) {
  console.log(`处理多选字段: ${values}`);

  if (!Array.isArray(values)) {
    values = [values];
  }

  // 清除所有选择
  Array.from(selectElement.options).forEach(option => {
    option.selected = false;
  });

  // 选择指定值
  values.forEach(value => {
    const option = Array.from(selectElement.options).find(opt =>
      opt.text.includes(value) || opt.value.includes(value)
    );

    if (option) {
      option.selected = true;
      console.log(`✓ 选择了选项: ${option.text}`);
    }
  });

  selectElement.dispatchEvent(new Event('change', { bubbles: true }));
}

// AILib信息提醒
export function showAILibInfo() {
  console.log('🤖 AILib 神经网络库信息:');
  console.log('');
  console.log('平台特色:');
  console.log('- 俄语神经网络和提示库');
  console.log('- 支持英文界面');
  console.log('- 免费AI工具收录');
  console.log('- 详细的分类系统');
  console.log('');
  console.log('分类系统:');
  console.log('- 行业分类：设计、商业、开发等');
  console.log('- 任务类型：创建文本、图像、视频等');
  console.log('- 转换方式：文本到文本、图像到图像等');
  console.log('- 付费模式：免费、付费、试用等');
  console.log('');
  console.log('特殊功能:');
  console.log('- 多选支持（按CTRL键）');
  console.log('- Telegram机器人集成');
  console.log('- 免费ChatGPT访问');
  console.log('- 隐私协议保护');
  console.log('');
  console.log('AILib - 连接俄语AI社区的桥梁！');
}

// 行业分类信息
export function showIndustryCategories() {
  console.log('🏭 AILib 行业分类:');
  console.log('');
  console.log('主要行业:');
  console.log('- Design (设计)');
  console.log('- Business (商业)');
  console.log('- Development (开发)');
  console.log('- E-commerce (电商)');
  console.log('- Advertising & SMM (广告和社交媒体)');
  console.log('- Copywriting (文案写作)');
  console.log('- Video (视频)');
  console.log('- Music (音乐)');
  console.log('- Education (教育)');
  console.log('- Health (健康)');
  console.log('');
  console.log('支持多选，按CTRL键选择多个行业！');
}

// 任务类型信息
export function showTaskTypes() {
  console.log('📋 AILib 任务类型:');
  console.log('');
  console.log('创建类任务:');
  console.log('- Create text (创建文本)');
  console.log('- Create image (创建图像)');
  console.log('- Create video (创建视频)');
  console.log('- Create audio (创建音频)');
  console.log('- Create 3D object (创建3D对象)');
  console.log('');
  console.log('编辑类任务:');
  console.log('- Edit image (编辑图像)');
  console.log('- Edit video (编辑视频)');
  console.log('- Improve text (改进文本)');
  console.log('- Analyze content (分析内容)');
  console.log('');
  console.log('支持多选，按CTRL键选择多个任务！');
}

// 转换方式信息
export function showConversionTypes() {
  console.log('🔄 AILib 转换方式:');
  console.log('');
  console.log('文本相关:');
  console.log('- Text to Text (文本到文本)');
  console.log('- Text to Image (文本到图像)');
  console.log('- Text to Video (文本到视频)');
  console.log('- Text to Audio (文本到音频)');
  console.log('');
  console.log('图像相关:');
  console.log('- Image to Image (图像到图像)');
  console.log('- Image to Text (图像到文本)');
  console.log('- Image to Video (图像到视频)');
  console.log('- Image to 3D (图像到3D)');
  console.log('');
  console.log('支持多选，按CTRL键选择多个转换方式！');
}

// 表单验证
export function validateAILibForm() {
  console.log('验证AILib表单...');

  const requiredFields = [
    { selector: 'input[type="email"]', label: '邮箱' },
    { selector: 'input[type="text"]', label: 'AI工具名称' },
    { selector: 'input[type="url"], input[name="link"]', label: '链接' },
    { selector: 'textarea', label: '描述' }
  ];

  let isValid = true;

  requiredFields.forEach(field => {
    const element = document.querySelector(field.selector);
    if (!element || !element.value.trim()) {
      console.log(`⚠️ 必填字段为空: ${field.label}`);
      isValid = false;
    }
  });

  // 检查多选字段
  const selectFields = document.querySelectorAll('select');
  selectFields.forEach(select => {
    const selectedOptions = Array.from(select.selectedOptions);
    if (selectedOptions.length === 0) {
      console.log(`⚠️ 多选字段未选择: ${select.name || '未知字段'}`);
      isValid = false;
    }
  });

  // 检查隐私协议
  const privacyCheckbox = document.querySelector('input[type="checkbox"]');
  if (privacyCheckbox && !privacyCheckbox.checked) {
    console.log('⚠️ 需要同意隐私协议');
    isValid = false;
  }

  if (isValid) {
    console.log('✓ 表单验证通过');
  }

  return isValid;
}

// 多选操作提醒
export function showMultiSelectReminder() {
  console.log('🖱️ 多选操作提醒:');
  console.log('');
  console.log('如何进行多选:');
  console.log('1. 按住CTRL键');
  console.log('2. 点击要选择的选项');
  console.log('3. 可以选择多个选项');
  console.log('4. 松开CTRL键完成选择');
  console.log('');
  console.log('适用字段:');
  console.log('- Industry (行业)');
  console.log('- Task (任务)');
  console.log('- How to convert (转换方式)');
  console.log('');
  console.log('多选让您的AI工具分类更精准！');
}
