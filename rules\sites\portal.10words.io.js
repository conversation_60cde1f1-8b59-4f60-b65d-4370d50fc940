// Portal.10words.io 网站规则配置
// 网站: https://portal.10words.io/submissions/submit
// 最后更新: 2025-07-24

export const SITE_RULE = {
  // 基本信息
  domain: 'portal.10words.io',
  siteName: '10words.io Portal',
  priority: 1,
  lastUpdated: '2025-07-24',

  // 字段映射规则
  fieldMappings: {
    // 项目名称 -> Project Name
    siteName: {
      selectors: [
        'input[placeholder="Enter the name of your project"]',
        'input[placeholder*="name of your project"]',
        'input[type="text"]:first-of-type'
      ],
      method: 'value',
      validation: 'required',
      notes: '项目名称，只输入项目名称，不要添加额外词汇'
    },

    // 项目描述 -> Description
    siteDescription: {
      selectors: [
        'textarea[placeholder="Describe your project in 10 words or less!"]',
        'textarea[placeholder*="Describe your project"]',
        'textarea[rows="3"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '项目描述，10个词以内'
    },

    // 项目URL -> Project URL
    siteUrl: {
      selectors: [
        'input[placeholder="Enter a full link to your project"]',
        'input[placeholder*="full link to your project"]',
        'input[type="text"]:nth-of-type(2)'
      ],
      method: 'value',
      validation: 'required|url',
      notes: '项目完整链接，可包含跟踪参数'
    },

    // Twitter用户名 -> Twitter Handle (可选)
    twitterUrl: {
      selectors: [
        'input[placeholder="Enter your project or personal Twitter"]',
        'input[placeholder*="Twitter"]',
        '.rounded-r-md input[type="text"]'
      ],
      method: 'value',
      validation: 'optional',
      notes: 'Twitter用户名，不需要@符号'
    },

    // 项目分类 -> What best describes your project?
    category: {
      selectors: [
        'input[name="submission-category"]',
        '#push-website',
        '#push-app',
        '#push-saas',
        '#push-newsletter',
        '#push-other'
      ],
      method: 'radio',
      validation: 'required',
      notes: '项目类型选择，默认选择Website'
    }
  },

  // 提交流程配置
  submitConfig: {
    submitButton: 'button[type="submit"], .btn-submit',
    submitMethod: 'manual', // 手动提交
    waitAfterFill: 0,
    waitAfterSubmit: 0,
    successIndicators: ['.success-message'],
    errorIndicators: ['.error-message']
  },

  // 特殊处理
  specialHandling: {
    requiresLogin: false,
    hasCaptcha: false,
    hasFileUpload: false,
    customScript: 'handle10wordsSubmission',
    formValidation: {
      requiredFields: ['siteName', 'siteDescription', 'siteUrl', 'category'],
      emailValidation: false,
      urlValidation: true
    },
    notes: [
      '10words.io项目提交平台',
      'Angular应用，使用ng-*类名',
      '项目描述限制10个词以内',
      '单选按钮组选择项目类型',
      'Twitter字段可选',
      '手动提交模式'
    ]
  }
};

// 自定义处理函数
export function handle10wordsSubmission(data) {
  console.log('Processing 10words form submission...');

  const processedData = { ...data };

  // 确保URL格式正确
  if (processedData.siteUrl && !processedData.siteUrl.startsWith('http')) {
    processedData.siteUrl = 'https://' + processedData.siteUrl;
  }

  // 处理Twitter用户名格式
  if (processedData.twitterUrl) {
    // 移除@符号，因为表单已经有@前缀
    processedData.twitterUrl = processedData.twitterUrl.replace(/^@/, '');

    // 如果是完整URL，提取用户名
    if (processedData.twitterUrl.includes('twitter.com/') || processedData.twitterUrl.includes('x.com/')) {
      const match = processedData.twitterUrl.match(/(?:twitter\.com\/|x\.com\/)([^\/\?]+)/);
      if (match) {
        processedData.twitterUrl = match[1];
      }
    }
  }

  // 确保描述在10个词以内
  if (processedData.siteDescription) {
    const words = processedData.siteDescription.trim().split(/\s+/);
    if (words.length > 10) {
      processedData.siteDescription = words.slice(0, 10).join(' ');
    }
  }

  return processedData;
}

// 自定义元素填写函数
export async function customFillElement(element, value, config) {
  console.log(`10words自定义填写: ${element.tagName}, 方法: ${config.method}`);

  switch (config.method) {
    case 'value':
      // 标准输入框和文本域处理
      element.focus();
      await new Promise(resolve => setTimeout(resolve, 200));

      element.value = value;
      element.dispatchEvent(new Event('input', { bubbles: true }));
      element.dispatchEvent(new Event('change', { bubbles: true }));
      element.dispatchEvent(new Event('blur', { bubbles: true }));

      console.log(`✓ 填写字段: ${element.placeholder || element.id} = "${value}"`);
      return true;

    case 'radio':
      // 单选按钮处理
      if (element.type === 'radio') {
        // 默认选择Website类型
        const websiteRadio = document.querySelector('#push-website') ||
                           document.querySelector('input[name="submission-category"][id*="website"]');

        if (websiteRadio) {
          websiteRadio.checked = true;
          websiteRadio.dispatchEvent(new Event('change', { bubbles: true }));
          console.log(`✓ 选择分类: Website`);
          return true;
        }
      }
      break;

    default:
      element.value = value;
      element.dispatchEvent(new Event('input', { bubbles: true }));
      return true;
  }

  return false;
}