// FutureTools.io 网站规则配置
// 网站: https://www.futuretools.io/submit-a-tool
// 最后更新: 2025-07-06

export const SITE_RULE = {
  // 基本信息
  domain: 'futuretools.io',
  siteName: 'FutureTools',
  priority: 1,
  lastUpdated: '2025-07-06',
  
  // 字段映射规则
  fieldMappings: {
    // 联系人姓名 -> Your Name
    fullName: {
      selectors: [
        'input[name="name"]',
        '#name',
        'input[placeholder*="Your Name"]',
        'input.submit-form-field[data-name="Name"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '提交者姓名'
    },
    
    // 联系邮箱 -> Your Email Address
    contactEmail: {
      selectors: [
        'input[name="email"]',
        '#email',
        'input[type="email"]',
        'input[placeholder*="Your Email"]',
        'input.submit-form-field[data-name="Email"]'
      ],
      method: 'value',
      validation: 'required|email',
      notes: '联系邮箱地址'
    },
    
    // 工具名称 -> Tool Name
    siteName: {
      selectors: [
        'input[name="Tool-Name"]',
        '#Tool-Name',
        'input[placeholder*="Name of Tool"]',
        'input.submit-form-field[data-name="Tool Name"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '工具名称'
    },
    
    // 工具URL -> Tool URL
    siteUrl: {
      selectors: [
        'input[name="Tool-URL"]',
        '#Tool-URL',
        'input[placeholder*="URL of Tool"]',
        'input.submit-form-field[data-name="Tool URL"]'
      ],
      method: 'value',
      validation: 'required|url',
      notes: '工具URL，需要包含https://'
    },
    
    // 工具标签 -> Tool Tags
    keywords: {
      selectors: [
        'input[name="Tags"]',
        '#Tags',
        'input[placeholder*="Tags"]',
        'input.submit-form-field[data-name="Tags"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '工具关键词标签'
    },
    
    // 定价模式 -> Pricing Model
    pricing: {
      selectors: [
        'select[name="Pricing-Model"]',
        '#Pricing-Model',
        'select.submit-form-field[data-name="Pricing Model"]'
      ],
      method: 'select',
      validation: 'required',
      options: ['Free', 'Freemium', 'Paid', 'Open Source', 'Google Collab', 'GitHub'],
      defaultValue: 'Free', // 默认选择Free
      notes: '定价模式选择'
    },
    
    // 工具描述 -> Tool Description
    siteDescription: {
      selectors: [
        'textarea[name="Tool-Description"]',
        '#Tool-Description',
        'textarea[placeholder*="Describe what the tool does"]',
        'textarea.submit-form-field[data-name="field"]'
      ],
      method: 'value',
      validation: 'required',
      maxLength: 5000,
      notes: '工具功能描述'
    },
    
    // 其他详情 -> Any Other Details (可选字段)
    detailedIntro: {
      selectors: [
        'textarea[name="Other-Details"]',
        '#Other-Details',
        'textarea[placeholder*="Link to explainer video"]',
        'textarea.submit-form-field[data-name="Other Details"]'
      ],
      method: 'value',
      validation: 'optional',
      maxLength: 5000,
      notes: '其他详情，如视频链接、联盟计划等'
    }
  },
  
  // 提交流程配置
  submitConfig: {
    submitButton: 'input[type="submit"].submit-button.w-button',
    submitMethod: 'click',
    waitAfterFill: 2000, // 填写后等待2秒
    waitAfterSubmit: 5000, // 提交后等待5秒
    successIndicators: [
      '.success-message.w-form-done',
      '.w-form-done',
      'div[aria-label*="success"]'
    ],
    errorIndicators: [
      '.error-message.w-form-fail',
      '.w-form-fail',
      'div[aria-label*="failure"]'
    ]
  },
  
  // 特殊处理
  specialHandling: {
    requiresLogin: false,
    hasCaptcha: false,
    hasFileUpload: false,
    
    // 表单验证规则
    formValidation: {
      requiredFields: ['fullName', 'contactEmail', 'siteName', 'siteUrl', 'keywords', 'pricing', 'siteDescription'],
      emailValidation: true,
      urlValidation: true
    },
    
    // 特殊注意事项
    notes: [
      '表单有8个字段，其中7个必填，1个可选',
      'URL需要包含https://',
      '工具描述最多5000字符',
      '定价模式有6个选项可选',
      '提交后会重定向到感谢页面'
    ]
  }
};

// 自定义处理函数
export function handleFutureToolsSubmission(data, rule) {
  console.log('Processing FutureTools.io submission...');
  
  // 特殊处理逻辑
  const processedData = { ...data };
  
  // 确保URL格式正确
  if (processedData.siteUrl && !processedData.siteUrl.startsWith('http')) {
    processedData.siteUrl = 'https://' + processedData.siteUrl;
  }
  
  // 处理关键词格式
  if (processedData.keywords) {
    // 如果是数组，转换为逗号分隔的字符串
    if (Array.isArray(processedData.keywords)) {
      processedData.keywords = processedData.keywords.join(', ');
    }
    processedData.keywords = processedData.keywords.trim();
  }
  
  // 确保描述不超过长度限制
  if (processedData.siteDescription && processedData.siteDescription.length > 5000) {
    processedData.siteDescription = processedData.siteDescription.substring(0, 4997) + '...';
  }
  
  // 设置合适的定价模式
  if (!processedData.pricing) {
    processedData.pricing = 'Free'; // 默认定价
  }
  
  return processedData;
}
