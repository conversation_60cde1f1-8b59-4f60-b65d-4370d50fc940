// AITools247.com 网站规则配置
// 网站: https://aitools247.com/submit-listing/details/
// 最后更新: 2025-07-25

export const SITE_RULE = {
  // 基本信息
  domain: 'aitools247.com',
  siteName: 'AI Tools 247',
  priority: 1,
  lastUpdated: '2025-07-25',
  
  // 字段映射规则
  fieldMappings: {
    // 分类 -> Category
    category: {
      selectors: [
        '.hp-form__field--select select[name="categories"]',
        'select[name="categories"][data-component="select"]',
        'select[name="categories"].hp-field--select',
        'select[name="categories"]'
      ],
      method: 'select',
      validation: 'required',
      options: [
        '66', '59', '60', '65', '72', '52', '67', '48', '50', '53', '57', '62',
        '54', '69', '70', '47', '61', '45', '56', '41', '55', '51', '39', '68', '58', '49', '46'
      ],
      optionTexts: [
        'Advertising', 'AI Detection', 'Audio', 'Automation', 'Business Intelligence',
        'Chat', 'Coding', 'content', 'Education', 'Generative Art', 'Graphic Design',
        'Image Editing', 'Marketing', 'Music', 'Nocode', 'Photography', 'Podcasting',
        'Productivity', 'Recruitment', 'Research', 'SEO', 'Social Media', 'Text',
        'Text-to-Video', 'Text-to-Voice', 'Tool', 'Video Editor'
      ],
      defaultValue: '49', // Tool
      notes: '工具分类，使用Select2组件'
    },

    // 标题 -> Title
    siteTitle: {
      selectors: [
        '.hp-form__field--text input[name="title"]',
        'input[name="title"][maxlength="256"]',
        'input[name="title"].hp-field.hp-field--text',
        'input[name="title"][type="text"]',
        'input[name="title"]'
      ],
      method: 'value',
      validation: 'required',
      maxLength: 256,
      notes: '工具标题'
    },

    // 描述 -> Description (包含网址)
    siteDescription: {
      selectors: [
        'textarea[name="description"]',
        '.hp-field--textarea'
      ],
      method: 'value',
      validation: 'required',
      maxLength: 10240,
      notes: '工具描述，包含网址信息',
      customValue: true // 标记需要自定义处理
    }
  },

  // 表单信息
  formInfo: {
    submitButton: {
      selectors: [
        'button[type="submit"]',
        '.hp-form__button',
        'button:contains("Submit Listing")'
      ],
      notes: '提交列表按钮'
    },
    formContainer: 'form.hp-form--listing-submit',
    totalFields: 3
  },

  // 填写策略
  fillStrategy: {
    order: ['category', 'siteTitle', 'siteDescription'],
    delay: 300,
    waitForLoad: true
  }
};

// 自定义填写函数
export async function customFillElement(element, value, config) {
  if (config.customValue && element.name === 'description') {
    // 动态加载网站信息
    try {
      const infoModule = await import(chrome.runtime.getURL('website-info.js'));
      const websiteInfo = infoModule.WEBSITE_INFO;

      const description = websiteInfo.siteDescription || value;
      const url = websiteInfo.siteUrl || '';

      // 拼接描述和网址
      const combinedValue = `${description}\n\nWebsite: ${url}`;

      element.value = combinedValue;
      element.dispatchEvent(new Event('input', { bubbles: true }));
      element.dispatchEvent(new Event('change', { bubbles: true }));

      return true;
    } catch (error) {
      console.error('加载网站信息失败:', error);
      // 降级处理
      element.value = `${value}\n\nWebsite: `;
      element.dispatchEvent(new Event('input', { bubbles: true }));
      element.dispatchEvent(new Event('change', { bubbles: true }));
      return true;
    }
  }

  return false;
}
