// Nancheng.fun 网站规则配置
// 网站: https://nancheng.fun/contribute
// 最后更新: 2025-07-24

export const SITE_RULE = {
  // 基本信息
  domain: 'nancheng.fun',
  siteName: 'Nancheng',
  priority: 1,
  lastUpdated: '2025-07-24',

  // 字段映射规则
  fieldMappings: {
    // 网站名称 -> siteName
    siteName: {
      selectors: [
        'input[name="tougao_title"]',
        '#tougao_title',
        'input[placeholder="网站名称 *"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '网站名称，最多30字符'
    },

    // 网站链接 -> siteUrl
    siteUrl: {
      selectors: [
        'input[name="tougao_sites_link"]',
        '#tougao_sites_link',
        'input[placeholder="网站链接"]'
      ],
      method: 'value',
      validation: 'required|url',
      notes: '网站链接'
    },

    // 网站描述 -> siteDescription
    siteDescription: {
      selectors: [
        'input[name="tougao_sites_sescribe"]',
        '#tougao_sites_sescribe',
        'input[placeholder="网站描叙 *"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '网站描述，最多50字符'
    },

    // 分类选择 -> category
    category: {
      selectors: [
        'select[name="tougao_cat"]',
        '#tougaocategorg',
        'select.form-control'
      ],
      method: 'select',
      validation: 'required',
      notes: '分类选择，根据网站类型选择合适分类'
    },

    // 网站介绍 -> detailedIntro
    detailedIntro: {
      selectors: [
        'textarea[name="tougao_content"]',
        '#tougao_content',
        'textarea[rows="6"]'
      ],
      method: 'value',
      validation: 'optional',
      notes: '网站详细介绍'
    },

    // 公众号二维码上传 -> 文件上传字段
    wechatQrFile: {
      selectors: [
        'input[name="tougao_wechat_qr"]',
        '#tougao_wechat_qr',
        'input[type="file"]#upload_wechat_qr'
      ],
      method: 'file',
      validation: 'optional',
      notes: '公众号二维码图片上传'
    }
  },

  // 提交流程配置
  submitConfig: {
    submitButton: '#submit, .btn-submit',
    submitMethod: 'click',
    waitAfterFill: 2000,
    waitAfterSubmit: 5000,
    successIndicators: ['.success-message'],
    errorIndicators: ['.error-message']
  },

  // 特殊处理
  specialHandling: {
    requiresLogin: false,
    hasCaptcha: false,
    hasFileUpload: false,
    customScript: 'handleNanchengSubmission',
    formValidation: {
      requiredFields: ['siteName', 'siteUrl', 'siteDescription', 'category'],
      emailValidation: false,
      urlValidation: true
    },
    notes: [
      'AI工具收录投稿平台',
      '支持多种AI工具分类',
      '有详细的分类选项',
      '中文界面',
      '支持公众号二维码上传',
      '网站描述限制50字符',
      '网站名称限制30字符'
    ]
  }
};

// 自定义处理函数
export function handleNanchengSubmission(data, rule) {
  console.log('Processing Nancheng form submission...');

  const processedData = { ...data };

  // 确保URL格式正确
  if (processedData.siteUrl && !processedData.siteUrl.startsWith('http')) {
    processedData.siteUrl = 'https://' + processedData.siteUrl;
  }

  // 处理描述长度限制（50字符）
  if (processedData.siteDescription && processedData.siteDescription.length > 50) {
    processedData.siteDescription = processedData.siteDescription.substring(0, 47) + '...';
  }

  // 处理网站名称长度限制
  if (processedData.siteName && processedData.siteName.length > 30) {
    processedData.siteName = processedData.siteName.substring(0, 30);
  }

  return processedData;
}

// 自定义元素填写函数
export async function customFillElement(element, value, config) {
  // 处理分类下拉选择框
  if (element.tagName === 'SELECT' && element.name === 'tougao_cat') {
    const options = element.querySelectorAll('option');
    let selectedOption;

    // 尝试智能匹配分类
    for (const option of options) {
      if (option.value !== '0') {
        const optionText = option.textContent.trim();
        if (optionText.includes('AI文本工具') || optionText.includes('AI对话聊天')) {
          selectedOption = option;
          break;
        }
      }
    }

    // 如果没找到合适的，选择第一个非默认选项
    if (!selectedOption) {
      selectedOption = Array.from(options).find(opt => opt.value !== '0');
    }

    if (selectedOption) {
      element.value = selectedOption.value;
      element.dispatchEvent(new Event('change', { bubbles: true }));
      return true;
    }
  }

  // 处理文件上传字段
  if (element.type === 'file') {
    console.warn('文件上传需要手动操作');
    return false;
  }

  // 处理标准输入框和文本域
  if (element.tagName === 'INPUT' || element.tagName === 'TEXTAREA') {
    element.value = value;
    element.dispatchEvent(new Event('input', { bubbles: true }));
    element.dispatchEvent(new Event('change', { bubbles: true }));
    return true;
  }

  return false;
}