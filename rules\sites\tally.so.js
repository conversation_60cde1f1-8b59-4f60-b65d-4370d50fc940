// Tally.so 通用表单规则配置
// 网站: https://tally.so/r/wvB7Xg (及其他Tally表单)
// 最后更新: 2025-07-24

export const SITE_RULE = {
  // 基本信息
  domain: 'tally.so',
  siteName: 'Tally',
  priority: 1,
  lastUpdated: '2025-07-06',
  
  // 字段映射规则 - 基于实际Tally表单结构
  fieldMappings: {
    // 提交者姓名 -> Your name (必填)
    fullName: {
      selectors: [
        '#6eeb5f15-aa1f-4e42-98aa-d6327403ac54',
        'input[aria-label="Your name"]',
        'input[aria-label*="name" i]'
      ],
      method: 'value',
      validation: 'required',
      notes: '提交者姓名'
    },

    // 联系邮箱 -> Your email (必填)
    contactEmail: {
      selectors: [
        '#5805db26-36e2-4178-b33c-3020af27f064',
        'input[aria-label="Your email"]',
        'input[aria-label*="email" i]'
      ],
      method: 'value',
      validation: 'required|email',
      notes: '联系邮箱'
    },

    // 产品名称 -> Product name (必填)
    siteName: {
      selectors: [
        '#ffed62fe-bef2-4f54-8417-f7d510f2c50a',
        'input[aria-label="Product name"]',
        'input[aria-label*="product name" i]'
      ],
      method: 'value',
      validation: 'required',
      notes: '产品名称'
    },

    // 网站URL -> Product website url (必填)
    siteUrl: {
      selectors: [
        '#b5dfc35f-2805-4877-871c-820cd17f54a0',
        'input[aria-label="Product website url"]',
        'input[type="url"]'
      ],
      method: 'value',
      validation: 'required|url',
      notes: '产品网站URL'
    },

    // 简短介绍 -> Short introduce in one sentence (必填)
    siteDescription: {
      selectors: [
        '#b6b995cc-9856-4649-9aaf-0c0c4775588a',
        'input[aria-label="Short introduce in one sentence"]',
        'input[aria-label*="short" i]'
      ],
      method: 'value',
      validation: 'required',
      notes: '产品简短介绍'
    },

    // 详细介绍 -> Long introduction (必填)
    detailedIntro: {
      selectors: [
        '#62ce5d61-a62f-4fa5-bdc0-505ecafbce68',
        'textarea[aria-label="Long introduction "]',
        'textarea[aria-label*="long" i]'
      ],
      method: 'value',
      validation: 'required',
      notes: '产品详细介绍'
    },

    // 功能特性 -> Features or benefits (可选)
    features: {
      selectors: [
        '#c6d77c53-e2d1-40ec-8604-940cf7a3cf5f',
        'textarea[aria-label="Features or benefits"]',
        'textarea[aria-label*="feature" i]'
      ],
      method: 'value',
      validation: 'optional',
      notes: '产品功能特性'
    },

    // 商业模式 -> Commercial and Open source (下拉选择)
    businessModel: {
      selectors: [
        '#e774327a-4782-42c4-aee2-7c49f242452d',
        'input[aria-label*="commercial" i]',
        'div[data-block-id="e774327a-4782-42c4-aee2-7c49f242452d"] input'
      ],
      method: 'dropdown',
      validation: 'optional',
      notes: '商业模式和开源状态'
    },

    // 价格计划 -> Pricing plan (下拉选择)
    pricing: {
      selectors: [
        '#b4edd7df-9210-41c3-8cda-fa895d210027',
        'input[aria-label*="pricing" i]',
        'div[data-block-id="b4edd7df-9210-41c3-8cda-fa895d210027"] input'
      ],
      method: 'dropdown',
      validation: 'optional',
      notes: '价格计划'
    },

    // 最低价格 -> Minimum price($) (可选)
    priceAmount: {
      selectors: [
        '#ff7b7068-ac07-40d9-85ce-804bab435b0f',
        'input[aria-label="Minimum price($)"]',
        'input[inputmode="decimal"]'
      ],
      method: 'value',
      validation: 'optional',
      notes: '最低价格（美元）'
    },

    // Logo上传 -> Logo (必填文件上传)
    logoFile: {
      selectors: [
        'input[aria-label="Logo (less than 500*500px, less than 1MB)"]',
        'div[data-block-id="7237ac5b-0655-4dac-af16-99eada30c260"] input[type="file"]'
      ],
      method: 'file',
      validation: 'required',
      notes: 'Logo图片上传，小于500x500px，小于1MB'
    },

    // 截图A -> Screen shots A (可选文件上传)
    screenshotA: {
      selectors: [
        'input[aria-label="Screen shots A (less than 2MB)"]',
        'div[data-block-id="bf2c6fd6-97a7-4962-b397-eff2565d0b14"] input[type="file"]'
      ],
      method: 'file',
      validation: 'optional',
      notes: '产品截图A，小于2MB'
    },

    // 截图B -> Screen shots B (可选文件上传)
    screenshotB: {
      selectors: [
        'input[aria-label="Screen shots B (less than 2MB)"]',
        'div[data-block-id="99e98613-9166-4aa2-bf8d-c0290086995c"] input[type="file"]'
      ],
      method: 'file',
      validation: 'optional',
      notes: '产品截图B，小于2MB'
    },

    // 了解来源 -> Where did you hear Toolsfine.com (复选框组)
    source: {
      selectors: [
        '#checkbox_0ad4796c-8b1d-45b1-b282-2b8449ec69d4', // Google Search
        'input[id*="checkbox_"][type="checkbox"]'
      ],
      method: 'checkbox',
      validation: 'optional',
      notes: '了解来源，默认选择Google Search'
    },

    // 反向链接 -> Is it possible to provide backlink (必填复选框)
    backlink: {
      selectors: [
        '#checkbox_15837936-dcc6-4152-a5ea-1c06f838f82b', // Yes, prioritize
        '#checkbox_ec32ba8a-3ad0-4ee1-ae00-a11a732251d6', // No, normal
        'input[id*="checkbox_15837936"]',
        'input[id*="checkbox_ec32ba8a"]'
      ],
      method: 'checkbox',
      validation: 'required',
      notes: '是否提供反向链接，必选其一'
    },

    // 附加信息 -> More additional informations (可选)
    additionalInfo: {
      selectors: [
        '#40f326ca-61d3-49c9-820f-16284ce018da',
        'textarea[aria-label*="additional" i]',
        'textarea[aria-label*="more" i]'
      ],
      method: 'value',
      validation: 'optional',
      notes: '附加信息或其他说明'
    }
  },

  // 提交流程配置
  submitConfig: {
    submitButton: 'button[type="submit"], .tally-submit-button button',
    submitMethod: 'manual', // 手动提交，不自动点击
    waitAfterFill: 0,
    waitAfterSubmit: 0,
    successIndicators: ['.success-message', '.thank-you'],
    errorIndicators: ['.error-message', '.tally-error']
  },

  // 特殊处理
  specialHandling: {
    requiresLogin: false,
    hasCaptcha: false,
    hasFileUpload: false,
    customScript: 'handleTallySubmission',
    formValidation: {
      requiredFields: ['siteName', 'siteUrl', 'siteDescription', 'detailedIntro'],
      emailValidation: true,
      urlValidation: true
    },
    notes: [
      'Tally.so通用表单系统',
      '支持多种字段类型',
      '动态ID生成',
      '响应式设计',
      '支持复选框和下拉选择',
      '字段映射基于aria-label和标签文本'
    ]
  }
};



// 自定义元素填写函数
export async function customFillElement(element, value, config) {
  console.log(`Tally自定义填写: ${element.id || element.tagName}, 方法: ${config.method}`);

  switch (config.method) {
    case 'value':
      // Tally文本输入框处理
      element.focus();
      await new Promise(resolve => setTimeout(resolve, 200));

      element.value = value;
      element.dispatchEvent(new Event('input', { bubbles: true }));
      element.dispatchEvent(new Event('change', { bubbles: true }));
      element.dispatchEvent(new Event('blur', { bubbles: true }));

      console.log(`✓ 填写字段: ${element.id} = "${value}"`);
      return true;

    case 'checkbox':
      // 复选框处理
      if (element.type === 'checkbox') {
        element.checked = true;
        element.dispatchEvent(new Event('change', { bubbles: true }));
        console.log(`✓ 复选框设置: ${element.checked}`);
        return true;
      }
      break;

    case 'select':
      // 下拉选择框处理
      if (element.tagName === 'SELECT') {
        const options = element.querySelectorAll('option');
        let selectedOption = Array.from(options).find(opt =>
          opt.textContent.toLowerCase().includes('business') ||
          opt.textContent.toLowerCase().includes('writing') ||
          opt.value !== ''
        );

        if (selectedOption) {
          element.value = selectedOption.value;
          element.dispatchEvent(new Event('change', { bubbles: true }));
          return true;
        }
      }
      break;

    default:
      element.value = value;
      element.dispatchEvent(new Event('input', { bubbles: true }));
      return true;
  }

  return false;
}

// 自定义处理函数
export function handleTallySubmission(data) {
  console.log('Processing Tally form submission...');

  const processedData = { ...data };

  // 确保URL格式正确
  if (processedData.siteUrl && !processedData.siteUrl.startsWith('http')) {
    processedData.siteUrl = 'https://' + processedData.siteUrl;
  }

  // 设置固定定价
  processedData.pricing = 'Free';

  // 设置固定功能特性
  processedData.features = 'API';

  // 新闻订阅默认不勾选
  if (processedData.newsletter === undefined) {
    processedData.newsletter = false;
  }

  return processedData;
}
