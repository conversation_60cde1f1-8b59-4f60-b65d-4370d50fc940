// Trustiner.com 网站规则配置
// 网站: https://trustiner.com/submit
// 最后更新: 2025-07-24

export const SITE_RULE = {
  // 基本信息
  domain: 'trustiner.com',
  siteName: 'Trustiner',
  priority: 1,
  lastUpdated: '2025-07-24',

  // 字段映射规则
  fieldMappings: {
    // 工具URL -> Tool URL
    siteUrl: {
      selectors: [
        '#website-url',
        'input[type="url"]',
        'input[placeholder="https://your-tool.com"]'
      ],
      method: 'value',
      validation: 'required|url',
      notes: '工具官方网站URL'
    },

    // 工具名称 -> Tool Name
    siteName: {
      selectors: [
        '#tool-name',
        'input[placeholder*="AI Writer Pro"]',
        'input[maxlength="100"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '工具名称，最多100字符'
    },

    // 截图上传 -> Upload a screenshot
    logoFile: {
      selectors: [
        '#screenshot-upload',
        'input[type="file"][accept="image/*"]'
      ],
      method: 'file',
      validation: 'required',
      notes: '工具界面截图，最大1500x900，10MB以内'
    },

    // 分类选择 -> Category
    category: {
      selectors: [
        '#category-select',
        'select:first-of-type'
      ],
      method: 'select',
      validation: 'required',
      notes: '工具分类选择'
    },

    // 价格模式 -> Pricing
    pricing: {
      selectors: [
        '#pricing-select',
        'select:nth-of-type(2)'
      ],
      method: 'select',
      validation: 'required',
      notes: '价格模式选择'
    },

    // 简短描述 -> Short Description
    siteDescription: {
      selectors: [
        '#summary',
        'textarea[maxlength="150"]',
        'textarea[placeholder*="short, impactful description"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '简短描述，最多150字符'
    },

    // 详细介绍 -> About Your Tool
    detailedIntro: {
      selectors: [
        '#about-tool',
        'textarea[maxlength="1000"]',
        'textarea[placeholder*="overview of your tool"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '工具详细介绍，最多1000字符'
    },

    // 反向链接选择 -> Featured Submission
    backlink: {
      selectors: [
        'input[name="backlink"]',
        '#backlink-yes',
        '#backlink-no'
      ],
      method: 'radio',
      validation: 'optional',
      notes: '是否添加反向链接获得特色展示'
    },

    // 同意条款 -> Terms and Conditions
    agreeTerms: {
      selectors: [
        '#agree-terms',
        'input[type="checkbox"]'
      ],
      method: 'checkbox',
      validation: 'required',
      notes: '同意条款和条件'
    }
  },
  // 提交流程配置
  submitConfig: {
    submitButton: 'button[type="button"]:contains("Submit Tool for Review")',
    submitMethod: 'manual', // 手动提交
    waitAfterFill: 0,
    waitAfterSubmit: 0,
    successIndicators: ['.success-message'],
    errorIndicators: ['.error-message']
  },

  // 特殊处理
  specialHandling: {
    requiresLogin: false,
    hasCaptcha: false,
    hasFileUpload: true,
    customScript: 'handleTrustinerSubmission',
    formValidation: {
      requiredFields: ['siteUrl', 'siteName', 'logoFile', 'category', 'pricing', 'siteDescription', 'detailedIntro', 'agreeTerms'],
      emailValidation: false,
      urlValidation: true
    },
    notes: [
      'Trustiner AI工具目录提交平台',
      '专业的AI工具评估和推荐平台',
      '必须上传工具截图',
      '支持标准和特色两种提交方式',
      '特色提交需要添加反向链接',
      '必须同意条款和条件',
      '表单字段较多，包含详细的工具信息',
      '手动提交模式'
    ]
  }
};

// 自定义处理函数
export function handleTrustinerSubmission(data) {
  console.log('Processing Trustiner form submission...');

  const processedData = { ...data };

  // 确保URL格式正确
  if (processedData.siteUrl && !processedData.siteUrl.startsWith('http')) {
    processedData.siteUrl = 'https://' + processedData.siteUrl;
  }

  return processedData;
}

// 自定义元素填写函数
export async function customFillElement(element, value, config) {
  console.log(`Trustiner自定义填写: ${element.tagName}, 方法: ${config.method}`);

  switch (config.method) {
    case 'value':
      // 标准输入框和文本域处理
      element.focus();
      await new Promise(resolve => setTimeout(resolve, 200));

      element.value = value;
      element.dispatchEvent(new Event('input', { bubbles: true }));
      element.dispatchEvent(new Event('change', { bubbles: true }));
      element.dispatchEvent(new Event('blur', { bubbles: true }));

      console.log(`✓ 填写字段: ${element.id} = "${value}"`);
      return true;

    case 'select':
      // 下拉选择框处理
      if (element.tagName === 'SELECT') {
        const options = element.querySelectorAll('option');
        let selectedOption;

        // 智能匹配选项
        for (const option of options) {
          if (option.value && option.value !== '') {
            const optionText = option.textContent.trim();
            if (element.id === 'category-select') {
              if (optionText.includes('AI Assistants') || optionText.includes('Content Creation')) {
                selectedOption = option;
                break;
              }
            } else if (element.id === 'pricing-select') {
              if (optionText.includes('Free') || optionText.includes('Freemium')) {
                selectedOption = option;
                break;
              }
            }
          }
        }

        // 如果没找到合适的，选择第一个非空选项
        if (!selectedOption) {
          selectedOption = Array.from(options).find(opt => opt.value !== '');
        }

        if (selectedOption) {
          element.value = selectedOption.value;
          element.dispatchEvent(new Event('change', { bubbles: true }));
          console.log(`✓ 选择选项: ${selectedOption.textContent}`);
          return true;
        }
      }
      break;

    case 'checkbox':
      // 复选框处理
      if (element.type === 'checkbox') {
        element.checked = true;
        element.dispatchEvent(new Event('change', { bubbles: true }));
        console.log(`✓ 复选框设置: ${element.checked}`);
        return true;
      }
      break;

    case 'radio':
      // 单选按钮处理 - 默认选择标准提交
      const standardRadio = document.querySelector('#backlink-no');
      if (standardRadio) {
        standardRadio.checked = true;
        standardRadio.dispatchEvent(new Event('change', { bubbles: true }));
        console.log(`✓ 选择提交方式: 标准提交`);
        return true;
      }
      break;

    case 'file':
      // 文件上传处理
      console.warn('文件上传需要手动操作');
      return false;

    default:
      element.value = value;
      element.dispatchEvent(new Event('input', { bubbles: true }));
      return true;
  }

  return false;
}