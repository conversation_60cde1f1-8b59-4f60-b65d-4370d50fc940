// 265.com 网站规则配置
// 网站: https://www.265.com/submit/
// 最后更新: 2025-07-24

export const SITE_RULE = {
  // 基本信息
  domain: 'www.265.com',
  siteName: '265导航',
  priority: 1,
  lastUpdated: '2025-07-24',

  // 字段映射规则
  fieldMappings: {
    // 推荐网址 -> 推荐网址
    siteUrl: {
      selectors: [
        'input[name="site_url"]',
        '.gb-uc-url-eb'
      ],
      method: 'value',
      validation: 'required|url',
      notes: '推荐网址'
    },

    // 网站名称 -> 网站名称
    siteName: {
      selectors: [
        'input[name="site_name"]',
        '.gb-uc-name-eb'
      ],
      method: 'value',
      validation: 'required',
      notes: '网站名称'
    },

    // 推荐分类 -> 推荐分类
    category: {
      selectors: [
        'input[name="site_category"]',
        '.gb-uc-Nb-eb'
      ],
      method: 'value',
      validation: 'required',
      notes: '推荐分类',
      defaultValue: 'AI工具'
    },

    // 网站描述 -> 网站描述
    siteDescription: {
      selectors: [
        'textarea.gb-site_description-eb',
        '.uc-gb-wc-F-zc'
      ],
      method: 'value',
      validation: 'required',
      notes: '网站描述'
    }
  },
  // 提交流程配置
  submitConfig: {
    submitButton: 'input[type="button"][value="提  交"], .uc-gb-wc-u',
    submitMethod: 'click',
    waitAfterFill: 1000,
    waitAfterSubmit: 3000,
    successIndicators: ['.success-message'],
    errorIndicators: ['.error-message']
  },

  // 特殊处理
  specialHandling: {
    requiresLogin: false,
    hasCaptcha: false,
    hasFileUpload: false,
    customScript: 'handle265Submission',
    formValidation: {
      requiredFields: ['siteUrl', 'siteName', 'category', 'siteDescription'],
      emailValidation: false,
      urlValidation: true
    },
    notes: [
      '265导航网站收录平台',
      '老牌网址导航站',
      '使用自定义提交处理函数',
      '中文界面',
      '所有字段均为必填',
      '点击提交模式'
    ]
  }
};

// 自定义处理函数
export function handle265Submission(data) {
  console.log('Processing 265导航 form submission...');

  const processedData = { ...data };

  // 确保URL格式正确
  if (processedData.siteUrl && !processedData.siteUrl.startsWith('http')) {
    processedData.siteUrl = 'https://' + processedData.siteUrl;
  }

  // 为分类提供默认值
  if (!processedData.category) {
    processedData.category = 'AI工具';
  }

  return processedData;
}

// 自定义元素填写函数
export async function customFillElement(element, value, config) {
  console.log(`265导航自定义填写: ${element.tagName}, 方法: ${config.method}`);

  switch (config.method) {
    case 'value':
      // 标准输入框和文本域处理
      element.focus();
      await new Promise(resolve => setTimeout(resolve, 200));

      element.value = value;
      element.dispatchEvent(new Event('input', { bubbles: true }));
      element.dispatchEvent(new Event('change', { bubbles: true }));
      element.dispatchEvent(new Event('blur', { bubbles: true }));

      console.log(`✓ 填写字段: ${element.name || element.className} = "${value}"`);
      return true;

    default:
      element.value = value;
      element.dispatchEvent(new Event('input', { bubbles: true }));
      return true;
  }

  return false;
}