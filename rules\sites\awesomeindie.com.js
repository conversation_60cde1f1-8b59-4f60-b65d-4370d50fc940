// AwesomeIndie.com 网站规则配置
// 网站: https://awesomeindie.com/submit
// 最后更新: 2025-07-24

export const SITE_RULE = {
  domain: 'awesomeindie.com',
  siteName: 'Awesome Indie',
  priority: 1,
  lastUpdated: '2025-07-24',

  fieldMappings: {
    siteName: {
      selectors: [
        '#title',
        'input[placeholder="Enter the name of the product"]',
        '.ant-input[aria-required="true"]:first-of-type'
      ],
      method: 'value',
      validation: 'required',
      notes: '产品名称，3-40字符'
    },

    siteUrl: {
      selectors: [
        '#website',
        'input[placeholder="URL of the product"]',
        'input[type="url"]:first-of-type'
      ],
      method: 'value',
      validation: 'required|url',
      notes: '产品主网站URL'
    },

    tagline: {
      selectors: [
        '#tagline',
        'input[placeholder="Enter a tagline"]',
        'input[type="text"]:nth-of-type(2)'
      ],
      method: 'value',
      validation: 'required',
      notes: '产品标语，5-64字符'
    },

    category: {
      selectors: [
        '.ant-select-selector',
        '#rc_select_0',
        '.ant-select-multiple'
      ],
      method: 'select',
      validation: 'required',
      notes: '产品分类，最少1个最多5个'
    },

    siteDescription: {
      selectors: [
        '#description',
        'textarea[placeholder="Write a description"]',
        '.ant-input[style*="height: 280px"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '详细产品描述'
    },

    twitterUrl: {
      selectors: [
        '#socialTwitter',
        'input[placeholder="Twitter"]'
      ],
      method: 'value',
      validation: 'optional|url',
      notes: 'Twitter社交链接'
    },

    linkedinUrl: {
      selectors: [
        '#socialLinkedIn',
        'input[placeholder="LinkedIn"]'
      ],
      method: 'value',
      validation: 'optional|url',
      notes: 'LinkedIn社交链接'
    },

    githubUrl: {
      selectors: [
        '#socialGitHub',
        'input[placeholder="GitHub"]'
      ],
      method: 'value',
      validation: 'optional|url',
      notes: 'GitHub社交链接'
    },

    facebookUrl: {
      selectors: [
        '#socialFacebook',
        'input[placeholder="Facebook"]'
      ],
      method: 'value',
      validation: 'optional|url',
      notes: 'Facebook社交链接'
    },

    instagramUrl: {
      selectors: [
        '#socialInstagram',
        'input[placeholder="Instagram"]'
      ],
      method: 'value',
      validation: 'optional|url',
      notes: 'Instagram社交链接'
    },

    youtubeUrl: {
      selectors: [
        '#youTubeVideoUrl',
        'input[placeholder="YouTube video URL"]'
      ],
      method: 'value',
      validation: 'optional|url',
      notes: 'YouTube视频URL'
    }
  },

  submitConfig: {
    submitButton: 'button[type="submit"], .ant-btn-primary',
    submitMethod: 'click',
    successIndicators: ['.success-message'],
    errorIndicators: ['.error-message']
  },

  specialHandling: {
    requiresLogin: false,
    hasCaptcha: false,
    hasFileUpload: false,
    customScript: 'handleAwesomeIndieSubmission',
    formValidation: {
      requiredFields: ['siteName', 'siteUrl', 'tagline', 'category', 'siteDescription'],
      emailValidation: false,
      urlValidation: true
    },
    notes: [
      '使用Ant Design组件库',
      '产品名称3-40字符限制',
      '标语5-64字符限制',
      '分类最少1个最多5个',
      '多个社交媒体链接字段',
      '支持YouTube视频'
    ]
  }
};

export function handleAwesomeIndieSubmission(data, rule) {
  const processedData = { ...data };

  if (processedData.siteUrl && !processedData.siteUrl.startsWith('http')) {
    processedData.siteUrl = 'https://' + processedData.siteUrl;
  }

  // 处理所有社交媒体URL
  const socialFields = ['twitterUrl', 'linkedinUrl', 'githubUrl', 'facebookUrl', 'instagramUrl', 'youtubeUrl'];
  socialFields.forEach(field => {
    if (processedData[field] && !processedData[field].startsWith('http')) {
      processedData[field] = 'https://' + processedData[field];
    }
  });

  return processedData;
}

export async function customFillElement(element, value, config) {
  // 处理Ant Design多选下拉框
  if (element.classList.contains('ant-select-selector')) {
    element.click();
    // 等待下拉选项出现，然后选择第一个选项
    setTimeout(() => {
      const firstOption = document.querySelector('.ant-select-item-option:first-child');
      if (firstOption) {
        firstOption.click();
      }
    }, 100);
    return true;
  }

  if (element.tagName === 'INPUT' || element.tagName === 'TEXTAREA') {
    element.value = value;
    element.dispatchEvent(new Event('input', { bubbles: true }));
    element.dispatchEvent(new Event('change', { bubbles: true }));
    return true;
  }

  return false;
}