// AIToolsList.top 网站规则配置
// 网站: https://www.aitoolslist.top/submit
// 最后更新: 2025-07-24

export const SITE_RULE = {
  // 基本信息
  domain: 'www.aitoolslist.top',
  siteName: 'AI Tools List',
  priority: 1,
  lastUpdated: '2025-07-24',

  // 字段映射规则
  fieldMappings: {
    // 网站名称 -> Website Name
    siteName: {
      selectors: [
        'input[placeholder="AI Tools List"]',
        'label:contains("Website Name") + input',
        'input[id*="form-item"]:first-of-type'
      ],
      method: 'value',
      validation: 'required',
      notes: '网站名称'
    },

    // 网站URL -> Website URL
    siteUrl: {
      selectors: [
        'input[placeholder="https://aitoolslist.top/"]',
        'label:contains("Website URL") + input',
        'input[id*="form-item"]:nth-of-type(2)'
      ],
      method: 'value',
      validation: 'required|url',
      notes: '网站URL地址'
    }
  },

  // 提交流程配置
  submitConfig: {
    submitButton: 'button[type="submit"], .bg-white',
    submitMethod: 'manual', // 手动提交
    waitAfterFill: 0,
    waitAfterSubmit: 0,
    successIndicators: ['.success-message'],
    errorIndicators: ['.error-message']
  },

  // 特殊处理
  specialHandling: {
    requiresLogin: false,
    hasCaptcha: false,
    hasFileUpload: false,
    customScript: 'handleAiToolsListSubmission',
    formValidation: {
      requiredFields: ['siteName', 'siteUrl'],
      emailValidation: false,
      urlValidation: true
    },
    notes: [
      'AI Tools List AI工具目录提交平台',
      '极简表单，只有网站名称和URL两个字段',
      '深色主题界面',
      '提供付费快速提交服务（Buy me a coffee）',
      '付费后一天内处理',
      '联系邮箱：<EMAIL>',
      '手动提交模式'
    ]
  }
};

// 自定义处理函数
export function handleAiToolsListSubmission(data) {
  console.log('Processing AI Tools List form submission...');

  const processedData = { ...data };

  // 确保URL格式正确
  if (processedData.siteUrl && !processedData.siteUrl.startsWith('http')) {
    processedData.siteUrl = 'https://' + processedData.siteUrl;
  }

  return processedData;
}

// 自定义元素填写函数
export async function customFillElement(element, value, config) {
  console.log(`AI Tools List自定义填写: ${element.tagName}, 方法: ${config.method}`);

  switch (config.method) {
    case 'value':
      // 标准输入框处理
      element.focus();
      await new Promise(resolve => setTimeout(resolve, 200));

      element.value = value;
      element.dispatchEvent(new Event('input', { bubbles: true }));
      element.dispatchEvent(new Event('change', { bubbles: true }));
      element.dispatchEvent(new Event('blur', { bubbles: true }));

      console.log(`✓ 填写字段: ${element.placeholder} = "${value}"`);
      return true;

    default:
      element.value = value;
      element.dispatchEvent(new Event('input', { bubbles: true }));
      return true;
  }

  return false;
}