// BetaList.com 网站规则配置
// 网站: https://betalist.com/submissions/new
// 最后更新: 2025-07-24

export const SITE_RULE = {
  // 基本信息
  domain: 'betalist.com',
  siteName: 'BetaList',
  priority: 1,
  lastUpdated: '2025-07-24',
  
  // 字段映射规则
  fieldMappings: {
    // 产品名称 -> Product name
    siteName: {
      selectors: [
        '#submission_startup_attributes_name',
        'input[name="submission[startup_attributes][name]"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '产品名称'
    },

    // 产品介绍 -> Pitch your product in one sentence
    tagline: {
      selectors: [
        '#submission_startup_attributes_pitch',
        'input[name="submission[startup_attributes][pitch]"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '一句话产品介绍，最多75字符'
    },

    // 网站URL -> Website
    siteUrl: {
      selectors: [
        '#submission_startup_attributes_website_url',
        'input[name="submission[startup_attributes][website_url]"]'
      ],
      method: 'value',
      validation: 'required|url',
      notes: '网站URL地址'
    },

    // 产品描述 -> Describe your product
    siteDescription: {
      selectors: [
        '#submission_startup_attributes_description',
        'textarea[name="submission[startup_attributes][description]"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '产品详细描述，最多700字符'
    },

    // 视频URL -> Video URL
    videoUrl: {
      selectors: [
        '#submission_startup_attributes_video_url',
        'input[name="submission[startup_attributes][video_url]"]'
      ],
      method: 'value',
      validation: 'optional|url',
      notes: '产品视频URL，可选'
    },

    // 产品图标 -> Icon
    logoFile: {
      selectors: [
        '#submission_startup_attributes_icon',
        'input[name="submission[startup_attributes][icon]"]'
      ],
      method: 'file',
      validation: 'optional',
      notes: '产品图标文件上传'
    },

    // 产品缩略图 -> Thumbnail
    thumbnailFile: {
      selectors: [
        '#submission_startup_attributes_custom_thumbnail',
        'input[name="submission[startup_attributes][custom_thumbnail]"]'
      ],
      method: 'file',
      validation: 'optional',
      notes: '产品缩略图文件上传'
    }
  },

  // 提交流程配置
  submitConfig: {
    submitButton: 'button[type="submit"], .bg-teal-500',
    submitMethod: 'manual', // 手动提交
    waitAfterFill: 0,
    waitAfterSubmit: 0,
    successIndicators: ['.success-message'],
    errorIndicators: ['.error-message']
  },

  // 特殊处理
  specialHandling: {
    requiresLogin: true,
    hasCaptcha: false,
    hasFileUpload: true,
    customScript: 'handleBetaListSubmission',
    formValidation: {
      requiredFields: ['siteName', 'tagline', 'siteUrl', 'siteDescription'],
      emailValidation: false,
      urlValidation: true
    },
    notes: [
      'BetaList 产品发布平台',
      '专注于新产品和初创公司',
      '需要登录账户',
      '支持图标和缩略图上传',
      '包含视频URL字段',
      '现代化的Tailwind CSS界面',
      '手动提交模式'
    ]
  }
};

// 自定义处理函数
export function handleBetaListSubmission(data) {
  console.log('Processing BetaList form submission...');
  
  const processedData = { ...data };

  // 确保URL格式正确
  if (processedData.siteUrl && !processedData.siteUrl.startsWith('http')) {
    processedData.siteUrl = 'https://' + processedData.siteUrl;
  }

  // 处理视频URL
  if (processedData.videoUrl && !processedData.videoUrl.startsWith('http')) {
    processedData.videoUrl = 'https://' + processedData.videoUrl;
  }

  return processedData;
}

// 自定义元素填写函数
export async function customFillElement(element, value, config) {
  console.log(`BetaList自定义填写: ${element.tagName}, 方法: ${config.method}`);

  switch (config.method) {
    case 'value':
      // 标准输入框和文本域处理
      element.focus();
      await new Promise(resolve => setTimeout(resolve, 200));

      element.value = value;
      element.dispatchEvent(new Event('input', { bubbles: true }));
      element.dispatchEvent(new Event('change', { bubbles: true }));
      element.dispatchEvent(new Event('blur', { bubbles: true }));

      console.log(`✓ 填写字段: ${element.name} = "${value}"`);
      return true;

    case 'file':
      // 文件上传处理
      console.warn('文件上传需要手动操作');
      return false;

    default:
      element.value = value;
      element.dispatchEvent(new Event('input', { bubbles: true }));
      return true;
  }

  return false;
}
