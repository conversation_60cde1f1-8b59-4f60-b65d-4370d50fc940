// seekme.ai 网站规则配置
// 网站: https://seekme.ai/submittool
// 表单技术: Simple HTML Form with Tailwind CSS
// 最后更新: 2025-07-08

export const SITE_RULE = {
  // 基本信息
  domain: 'seekme.ai',
  siteName: 'Seek Me AI',
  priority: 1,
  lastUpdated: '2025-07-08',
  
  // 字段映射规则
  fieldMappings: {
    // 标题 -> Title
    siteName: {
      selectors: [
        'input[id="title"]',
        'input[placeholder="Enter title"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '工具标题，使用website-info.js中的siteName字段'
    },
    
    // 副标题 -> Subtitle
    siteDescription: {
      selectors: [
        'input[id="subtitle"]',
        'input[placeholder="Enter subtitle"]'
      ],
      method: 'value',
      validation: 'optional',
      notes: '副标题，使用website-info.js中的siteDescription字段'
    },
    
    // 链接 -> Link
    siteUrl: {
      selectors: [
        'input[id="link"]',
        'input[placeholder="Enter tool link"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '工具链接，使用website-info.js中的siteUrl字段'
    },
    
    // 邮箱 -> Email (Founder or Team)
    contactEmail: {
      selectors: [
        'input[id="email"]',
        'input[type="email"]',
        'input[placeholder="Enter Email"]'
      ],
      method: 'value',
      validation: 'required|email',
      notes: '邮箱地址，使用website-info.js中的contactEmail字段'
    },
    
    // 描述 -> Description
    detailedIntro: {
      selectors: [
        'textarea[id="description"]',
        'textarea[placeholder="Enter description"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '详细描述，使用website-info.js中的detailedIntro字段'
    },
    
    // 标签 -> Tags (separated by commas)
    keywords: {
      selectors: [
        'input[id="tags"]',
        'input[placeholder*="separated by commas"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '标签，使用website-info.js中的keywords字段，转换为逗号分隔'
    },
    
    // 分类 -> Category
    category: {
      selectors: [
        'input[id="category"]',
        'input[placeholder="Enter category"]'
      ],
      method: 'value',
      validation: 'required',
      defaultValue: 'AI Tools',
      notes: '分类，使用默认值AI Tools'
    },
    
    // 图片上传 -> Image
    imageUpload: {
      selectors: [
        'input[id="file1"]',
        'input[type="file"]',
        'input[name="file1"]'
      ],
      method: 'file-upload',
      validation: 'optional',
      notes: '图片上传，可选字段，需要手动处理'
    },
    
    // 付费模式 -> Payment
    pricing: {
      selectors: [
        'select[id="payment"]'
      ],
      method: 'select',
      validation: 'required',
      defaultValue: 'free',
      notes: '付费模式，默认选择free'
    }
  },
  
  // 自定义填写函数
  customFillElement: async function(element, value, config) {
    console.log(`Seek Me AI自定义填写: ${element.id || element.tagName}, 方法: ${config.method}`);
    
    switch (config.method) {
      case 'value':
        // 标准输入框和文本域处理
        element.focus();
        await new Promise(resolve => setTimeout(resolve, 100));
        
        // 清空现有内容
        element.value = '';
        await new Promise(resolve => setTimeout(resolve, 50));
        
        // 处理特殊字段
        let finalValue = value;
        if (element.id === 'category') {
          // 分类使用默认值
          finalValue = config.defaultValue;
        } else if (element.id === 'tags' && Array.isArray(value)) {
          // 标签转换为逗号分隔的字符串
          finalValue = value.join(', ');
        }
        
        // 设置新值
        element.value = finalValue;
        
        // 触发事件
        element.dispatchEvent(new Event('input', { bubbles: true }));
        element.dispatchEvent(new Event('change', { bubbles: true }));
        element.dispatchEvent(new Event('blur', { bubbles: true }));
        
        console.log(`✓ 填写字段: ${element.id} = "${finalValue.substring(0, 50)}..."`);
        break;
        
      case 'select':
        // 下拉选择处理
        element.focus();
        await new Promise(resolve => setTimeout(resolve, 100));
        
        const targetValue = config.defaultValue || 'free';
        
        // 查找匹配的选项
        const option = Array.from(element.options).find(opt => 
          opt.value === targetValue || opt.text.toLowerCase() === targetValue.toLowerCase()
        );
        
        if (option) {
          element.value = option.value;
          element.dispatchEvent(new Event('change', { bubbles: true }));
          console.log(`✓ 选择选项: ${option.text}`);
        } else {
          console.log(`⚠️ 未找到选项: ${targetValue}`);
        }
        break;
        
      case 'file-upload':
        // 文件上传处理
        console.log('⚠️ 文件上传字段需要手动处理');
        console.log('请手动上传工具的图片文件');
        break;
        
      default:
        element.value = value;
        element.dispatchEvent(new Event('input', { bubbles: true }));
        element.dispatchEvent(new Event('change', { bubbles: true }));
    }
  },
  
  // 提交流程配置
  submitConfig: {
    submitButton: [
      'button[type="submit"]',
      'form#mainform button',
      'button:contains("Submit")'
    ],
    submitMethod: 'click',
    waitAfterFill: 2000,
    waitAfterSubmit: 5000,
    successIndicators: [
      '.success-message',
      '.alert-success',
      'text:contains("submitted")',
      'text:contains("success")'
    ],
    errorIndicators: [
      '.error-message',
      '.alert-error',
      'text:contains("error")'
    ]
  },
  
  // 特殊处理
  specialHandling: {
    requiresLogin: false,
    hasCaptcha: false,
    hasFileUpload: true, // 有文件上传字段
    isTailwindCSS: true, // 使用Tailwind CSS
    isSimpleForm: true, // 简单HTML表单
    
    // 表单验证规则
    formValidation: {
      requiredFields: ['siteName', 'siteUrl', 'contactEmail', 'detailedIntro', 'keywords', 'category', 'pricing'],
      emailValidation: true,
      urlValidation: true,
      fileUploadFields: ['imageUpload']
    },
    
    // 特殊注意事项
    notes: [
      '这是简单的HTML表单，使用Tailwind CSS',
      '表单包含10个字段，7个必填，3个可选',
      '有文件上传字段，需要手动处理',
      '标签字段需要逗号分隔格式',
      '分类使用默认值AI Tools',
      '付费模式默认选择free',
      '表单ID: mainform',
      '字段使用简单的id命名',
      '邮箱用于推广联系',
      '专注于AI工具提交'
    ]
  }
};

// 自定义处理函数
export function handleSeekMeAISubmission(data, _rule) {
  console.log('Processing Seek Me AI form submission...');

  const processedData = { ...data };

  // 确保URL格式正确
  if (processedData.siteUrl && !processedData.siteUrl.startsWith('http')) {
    processedData.siteUrl = 'https://' + processedData.siteUrl;
  }

  // 处理标签格式
  if (processedData.keywords) {
    if (Array.isArray(processedData.keywords)) {
      processedData.keywords = processedData.keywords.join(', ');
    }
  }

  // 设置默认值
  processedData.category = 'AI Tools';
  processedData.pricing = 'free';

  return processedData;
}

// 文件上传提醒函数
export function showFileUploadReminder() {
  console.log('📁 文件上传提醒:');
  console.log('');
  console.log('请手动上传工具的图片文件:');
  console.log('- 点击"Choose File"按钮');
  console.log('- 选择工具的截图或Logo');
  console.log('- 推荐使用清晰的工具界面截图');
  console.log('- 支持常见图片格式（JPG, PNG, GIF等）');
  console.log('');
  console.log('图片将用于在目录中展示您的工具！');
}
