// freeaitools.fyi 网站规则配置
// 网站: https://www.freeaitools.fyi/submit-tool
// 表单技术: Material-UI (MUI) React Components
// 最后更新: 2025-07-07

export const SITE_RULE = {
  // 基本信息
  domain: 'freeaitools.fyi',
  siteName: 'Free AI Tools',
  priority: 1,
  lastUpdated: '2025-07-07',
  
  // 字段映射规则
  fieldMappings: {
    // 提交者姓名 -> Your name
    fullName: {
      selectors: [
        'input[id="form2-YourName--952282311"]',
        'input[name="G-c9f16740"]',
        'input[placeholder="Enter your name"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '提交者姓名，使用website-info.js中的fullName字段'
    },
    
    // 邮箱地址 -> Email
    contactEmail: {
      selectors: [
        'input[id="form2-E-mailaddress-680205010"]',
        'input[name="G-97283abc"]',
        'input[placeholder="Enter your email address"]',
        'input[type="email"]'
      ],
      method: 'value',
      validation: 'required|email',
      notes: '邮箱地址，使用website-info.js中的contactEmail字段'
    },
    
    // AI工具名称 -> Name of tool
    siteName: {
      selectors: [
        'input[id="form2-Name--1087518422"]',
        'input[name="G-78f29c18"]',
        'input[placeholder="Write the name of the AI tool"]'
      ],
      method: 'value',
      validation: 'required',
      notes: 'AI工具名称，使用website-info.js中的siteName字段'
    },
    
    // 工具描述 -> Description of tool
    siteDescription: {
      selectors: [
        'textarea[id="form2-Description-226645117"]',
        'textarea[name="G-870075e8"]',
        'textarea[placeholder*="Describe the tool"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '工具描述，使用website-info.js中的siteDescription字段'
    },
    
    // 工具URL -> URL
    siteUrl: {
      selectors: [
        'input[id="form2-URL-1766040304"]',
        'input[name="G-e173a1c8"]',
        'input[placeholder="Enter the link to the tool"]'
      ],
      method: 'value',
      validation: 'required|url',
      notes: '工具URL，使用website-info.js中的siteUrl字段'
    },
    
    // 标签 -> Tags
    keywords: {
      selectors: [
        'div[id="form2-Tags--1087339848"]',
        'input[name="G-3aeaa685"]',
        '.MuiSelect-select[aria-labelledby="form2-Tags--1087339848"]'
      ],
      method: 'mui-select-multiple',
      validation: 'required',
      defaultValue: 'Product',
      notes: '工具标签，默认选择Product'
    },

    // 免费计划类型 -> Free plan type
    pricing: {
      selectors: [
        'div[id="form2-Pricing-935057607"]',
        'input[name="G-b92cdcfc"]',
        '.MuiSelect-select[aria-labelledby="form2-Pricing-935057607"]'
      ],
      method: 'mui-select',
      validation: 'required',
      defaultValue: 'Free',
      notes: '免费计划类型，默认选择Free'
    },
    
    // 免费计划详情 -> Free plan details
    freePlanDetails: {
      selectors: [
        'input[id="form2-FreePlandetails--1866241696"]',
        'input[name="G-20a61c8c"]',
        'input[placeholder*="free words"]'
      ],
      method: 'value',
      validation: 'required',
      defaultValue: 'Unlimited free use with full features',
      notes: '免费计划详情，使用默认描述'
    }
  },
  
  // 自定义填写函数
  customFillElement: async function(element, value, config) {
    console.log(`Free AI Tools自定义填写: ${element.id || element.name}, 方法: ${config.method}`);
    
    switch (config.method) {
      case 'value':
        // 标准输入框和文本域处理
        element.focus();
        await new Promise(resolve => setTimeout(resolve, 100));
        
        // 清空现有内容
        element.value = '';
        await new Promise(resolve => setTimeout(resolve, 50));
        
        // 处理特殊字段
        let finalValue = value;
        if (element.id === 'form2-FreePlandetails--1866241696') {
          // 免费计划详情使用默认值
          finalValue = config.defaultValue;
        }
        
        // 设置新值
        element.value = finalValue;
        
        // 触发MUI事件
        element.dispatchEvent(new Event('input', { bubbles: true }));
        element.dispatchEvent(new Event('change', { bubbles: true }));
        element.dispatchEvent(new Event('blur', { bubbles: true }));
        
        console.log(`✓ 填写字段: ${element.placeholder || element.id} = "${finalValue.substring(0, 50)}..."`);
        break;
        
      case 'mui-select':
        // MUI单选下拉组件处理 - Free plan type
        try {
          // 点击下拉框打开选项
          element.click();
          console.log('点击打开MUI Select组件');

          // 等待选项加载
          await new Promise(resolve => setTimeout(resolve, 800));

          // 查找Free选项
          const options = document.querySelectorAll('[role="option"], .MuiMenuItem-root, [data-value]');
          let targetOption = null;

          for (const option of options) {
            const text = (option.textContent || option.innerText || '').trim();
            if (text === 'Free' || text === config.defaultValue) {
              targetOption = option;
              break;
            }
          }

          if (targetOption) {
            targetOption.click();
            console.log(`✓ 选择免费计划: ${targetOption.textContent}`);
          } else {
            // 如果没找到精确匹配，查找包含Free的选项
            for (const option of options) {
              const text = (option.textContent || option.innerText || '').toLowerCase();
              if (text.includes('free')) {
                option.click();
                console.log(`✓ 选择包含Free的选项: ${option.textContent}`);
                break;
              }
            }
          }
        } catch (error) {
          console.error('MUI Select处理出错:', error);
        }
        break;
        
      case 'mui-select-multiple':
        // MUI多选下拉组件处理 - Tags
        try {
          // 点击下拉框打开选项
          element.click();
          console.log('点击打开MUI多选组件');

          // 等待选项加载
          await new Promise(resolve => setTimeout(resolve, 800));

          // 查找Product选项
          const options = document.querySelectorAll('[role="option"], .MuiMenuItem-root');
          let targetOption = null;

          // 首先尝试精确匹配Product
          for (const option of options) {
            const text = (option.textContent || option.innerText || '').trim();
            if (text === 'Product' || text === config.defaultValue) {
              targetOption = option;
              break;
            }
          }

          if (targetOption) {
            targetOption.click();
            console.log(`✓ 选择标签: ${targetOption.textContent}`);
          } else {
            // 如果没找到Product，查找相关选项
            const fallbackOptions = ['Productivity', 'Business', 'Tools', 'Software'];
            for (const fallback of fallbackOptions) {
              for (const option of options) {
                const text = (option.textContent || option.innerText || '').trim();
                if (text === fallback) {
                  option.click();
                  console.log(`✓ 选择备选标签: ${text}`);
                  targetOption = option;
                  break;
                }
              }
              if (targetOption) break;
            }

            // 如果还是没找到，选择第一个选项
            if (!targetOption && options.length > 0) {
              options[0].click();
              console.log(`✓ 选择第一个标签: ${options[0].textContent}`);
            }
          }

          // 等待选择完成
          await new Promise(resolve => setTimeout(resolve, 300));

          // 点击其他地方关闭下拉框
          document.body.click();
        } catch (error) {
          console.error('MUI多选处理出错:', error);
        }
        break;
        
      default:
        element.value = value;
        element.dispatchEvent(new Event('input', { bubbles: true }));
        element.dispatchEvent(new Event('change', { bubbles: true }));
    }
  },

  // 提交流程配置
  submitConfig: {
    submitButton: [
      'button[type="submit"]',
      'button:contains("Submit")',
      '.MuiButton-contained',
      '.MuiButton-fullWidth'
    ],
    submitMethod: 'click',
    waitAfterFill: 2000,
    waitAfterSubmit: 5000,
    successIndicators: [
      '.success-message',
      '.thank-you',
      '[class*="success"]',
      'text:contains("submitted")'
    ],
    errorIndicators: [
      '.error-message',
      '.MuiAlert-standardError',
      '[class*="error"]'
    ]
  },

  // 特殊处理
  specialHandling: {
    requiresLogin: false,
    hasCaptcha: false,
    hasFileUpload: false,
    isMaterialUI: true, // 使用Material-UI组件
    hasPayment: true, // 需要付费提交

    // 表单验证规则
    formValidation: {
      requiredFields: ['fullName', 'contactEmail', 'siteName', 'siteDescription', 'siteUrl', 'keywords', 'pricing', 'freePlanDetails'],
      emailValidation: true,
      urlValidation: true,
      tagLimit: { min: 1, max: 3 }
    },

    // 特殊注意事项
    notes: [
      '这是React网站，使用Material-UI (MUI)组件',
      '表单包含8个字段，全部必填',
      '需要付费才能提交工具',
      '提交后24-48小时内上线',
      '标签选择限制1-3个',
      '专注于免费AI工具收录',
      '使用MUI Select组件进行选择',
      '字段ID包含随机数字',
      '免费计划详情使用默认描述',
      '网站专注于免费AI工具推广'
    ]
  }
};

// 自定义处理函数
export function handleFreeAIToolsSubmission(data, _rule) {
  console.log('Processing Free AI Tools form submission...');

  const processedData = { ...data };

  // 确保URL格式正确
  if (processedData.siteUrl && !processedData.siteUrl.startsWith('http')) {
    processedData.siteUrl = 'https://' + processedData.siteUrl;
  }

  // 设置默认值
  processedData.keywords = 'Product';
  processedData.pricing = 'Free';
  processedData.freePlanDetails = 'Unlimited free use with full features';

  return processedData;
}
