// aisitelist.com 网站规则配置
// 网站: https://aisitelist.com/submit-ai-tool/
// 表单技术: MetForm + React Select
// 最后更新: 2025-07-07

export const SITE_RULE = {
  // 基本信息
  domain: 'aisitelist.com',
  siteName: 'AI Site List',
  priority: 1,
  lastUpdated: '2025-07-07',
  
  // 字段映射规则
  fieldMappings: {
    // 名字 -> First Name
    firstName: {
      selectors: [
        'input[name="mf-first-name"][id="mf-input-text-6f17bd4"]',
        'input[placeholder="First Name "]',
        '#mf-input-text-6f17bd4'
      ],
      method: 'value',
      validation: 'required',
      defaultValue: 'John',
      notes: '名字，使用固定值John'
    },

    // 姓氏 -> Last Name
    lastName: {
      selectors: [
        'input[name="mf-first-name"][id="mf-input-text-5822a73"]',
        'input[placeholder="Last Name "]',
        '#mf-input-text-5822a73'
      ],
      method: 'value',
      validation: 'required',
      defaultValue: 'Smith',
      notes: '姓氏，使用固定值Smith'
    },
    
    // 邮箱 -> Email
    contactEmail: {
      selectors: [
        'input[name="mf-email"]',
        '#mf-input-email-7b283f1',
        'input[type="email"]'
      ],
      method: 'value',
      validation: 'required|email',
      notes: '邮箱地址，使用website-info.js中的contactEmail字段'
    },
    
    // 手机号 -> Mobile No
    phone: {
      selectors: [
        'input[name="mf-telephone"]',
        '#mf-input-telephone-ad01c84',
        'input[type="tel"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '手机号码，使用website-info.js中的phone字段'
    },
    
    // 公司名称 -> Company Name
    companyName: {
      selectors: [
        'input[name="mf-first-name"][id="mf-input-text-84c39f8"]',
        'input[placeholder="AITool "]',
        '#mf-input-text-84c39f8'
      ],
      method: 'value',
      validation: 'optional',
      notes: '公司名称，使用website-info.js中的companyName字段'
    },
    
    // AI工具名称 -> AI Tool Name
    siteName: {
      selectors: [
        'input[name="mf-first-name"][id="mf-input-text-85b1a59"]',
        'input[placeholder="AI Tool "]',
        '#mf-input-text-85b1a59'
      ],
      method: 'value',
      validation: 'required',
      notes: 'AI工具名称，使用website-info.js中的siteName字段'
    },
    
    // 工具URL -> URL Of AI Tools
    siteUrl: {
      selectors: [
        'input[name="mf-url"]',
        '#mf-input-url-4dfae9d',
        'input[type="url"]'
      ],
      method: 'value',
      validation: 'required|url',
      notes: '工具URL，使用website-info.js中的siteUrl字段'
    },
    
    // 订阅类型 -> Select Tool Subscription Type
    pricing: {
      selectors: [
        '.mf_multiselect__value-container',
        '#react-select-2-input',
        'input[aria-autocomplete="list"]'
      ],
      method: 'react-select',
      validation: 'required',
      defaultValue: 'Free',
      notes: '订阅类型，使用React Select组件，默认选择Free'
    },
    
    // 工具分类 -> AI Tool category
    category: {
      selectors: [
        'input[name="mf-first-name"][id="mf-input-text-b5350fa"]',
        'input[placeholder*="Chatbot, Copywriting"]',
        '#mf-input-text-b5350fa'
      ],
      method: 'value',
      validation: 'required',
      notes: '工具分类，使用website-info.js中的category字段'
    },
    
    // 简短描述 -> Tool Short Details Overview
    siteDescription: {
      selectors: [
        'input[name="mf-text"][id="mf-input-text-c5686a0"]',
        'input[placeholder*="Write Short Descriptions"]',
        '#mf-input-text-c5686a0'
      ],
      method: 'value',
      validation: 'required',
      minLength: 150,
      notes: '简短描述，使用website-info.js中的siteDescription字段，最少150字符'
    },
    
    // 详细描述 -> Write Full Details About The Tool
    detailedIntro: {
      selectors: [
        'textarea[name="mf-textarea"]',
        '#mf-input-text-area-76677bd',
        'textarea[placeholder*="Write Full Details"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '详细描述，使用website-info.js中的detailedIntro字段'
    },
    
    // 工具优点 -> Write the Pros Of the AI Tool
    uniqueSellingPoints: {
      selectors: [
        'textarea[placeholder*="Pros Of the AI Tool"]',
        'input[placeholder*="Pros Of the AI Tool"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '工具优点，使用website-info.js中的uniqueSellingPoints字段'
    },
    
    // 工具缺点 -> Write the Cons Of the AI Tool
    cons: {
      selectors: [
        'textarea[id="mf-input-text-area-2c2443c"]',
        'textarea[placeholder*="Cons Of the AI Tool"]',
        'textarea[name="mf-textarea"]:last-of-type',
        'input[placeholder*="Cons Of the AI Tool"]'
      ],
      method: 'value',
      validation: 'required',
      defaultValue: 'Currently, the main limitation is that it is newly launched and does not have a large user base yet. However, the development team is actively working on improvements and expanding the feature set.',
      notes: '工具缺点，使用默认英文回复'
    }
  },

  // 自定义填写函数
  customFillElement: async function(element, value, config) {
    console.log(`AI Site List自定义填写: ${element.name || element.id}, 方法: ${config.method}`);

    switch (config.method) {
      case 'value':
        // 标准输入框和文本域处理
        element.focus();
        await new Promise(resolve => setTimeout(resolve, 100));

        // 清空现有内容
        element.value = '';
        await new Promise(resolve => setTimeout(resolve, 50));

        // 处理特殊字段
        let finalValue = value;
        if (element.id === 'mf-input-text-6f17bd4') {
          // First Name - 使用固定的有趣名字
          finalValue = 'Zephyr';
        } else if (element.id === 'mf-input-text-5822a73') {
          // Last Name - 使用固定的有趣姓氏
          finalValue = 'Nightingale';
        } else if (element.id === 'mf-input-telephone-ad01c84') {
          // Mobile No - 处理手机号码格式，去掉+1前缀，限制10位数字
          if (typeof value === 'string') {
            // 移除所有非数字字符
            let phoneNumber = value.replace(/\D/g, '');
            // 如果是+1开头的美国号码，移除开头的1
            if (phoneNumber.startsWith('1') && phoneNumber.length === 11) {
              phoneNumber = phoneNumber.substring(1);
            }
            // 限制为10位数字
            finalValue = phoneNumber.substring(0, 10);
          }
        } else if (element.id === 'mf-input-text-c5686a0') {
          // Tool Short Details Overview - 确保最少150字符
          if (typeof value === 'string' && value.trim()) {
            finalValue = value.trim();
            // 如果少于150字符，重复内容或添加描述
            while (finalValue.length < 150) {
              finalValue += '. This AI tool provides innovative solutions for users.';
            }
          } else {
            finalValue = 'This is an innovative AI tool that provides advanced features and capabilities for users. It offers comprehensive solutions with user-friendly interface and powerful functionality.';
          }
        } else if (element.id === 'mf-input-text-area-2c2443c' || element.placeholder && element.placeholder.includes('Cons Of the AI Tool')) {
          // 工具缺点使用默认值
          finalValue = config.defaultValue || 'Currently, the main limitation is that it is newly launched and does not have a large user base yet. However, the development team is actively working on improvements and expanding the feature set.';
        }

        // 设置新值
        element.value = finalValue;

        // 触发事件
        element.dispatchEvent(new Event('input', { bubbles: true }));
        element.dispatchEvent(new Event('change', { bubbles: true }));
        element.dispatchEvent(new Event('blur', { bubbles: true }));

        console.log(`✓ 填写字段: ${element.placeholder || element.name} = "${finalValue.substring(0, 50)}..."`);
        break;

      case 'react-select':
        // React Select组件处理
        try {
          // 点击React Select容器打开选项
          const container = element.closest('.mf_multiselect__value-container') || element;
          if (container) {
            container.click();
            console.log('点击打开React Select组件');

            // 等待选项加载
            await new Promise(resolve => setTimeout(resolve, 800));

            // 查找Free选项 - 修复CSS选择器语法
            let freeOption = document.querySelector('[data-value="Free"]');
            if (!freeOption) {
              // 查找包含Free文本的选项
              const allOptions = document.querySelectorAll('[class*="option"], [role="option"], .mf_multiselect__option');
              for (const option of allOptions) {
                if (option.textContent && option.textContent.includes('Free')) {
                  freeOption = option;
                  break;
                }
              }
            }

            if (freeOption) {
              freeOption.click();
              console.log(`✓ 选择订阅类型: Free`);
            } else {
              // 尝试在输入框中输入Free
              const input = document.querySelector('#react-select-2-input');
              if (input) {
                input.focus();
                input.value = 'Free';
                input.dispatchEvent(new Event('input', { bubbles: true }));

                // 等待选项出现并点击
                await new Promise(resolve => setTimeout(resolve, 500));
                const allOptions = document.querySelectorAll('[class*="option"], [role="option"]');
                for (const option of allOptions) {
                  if (option.textContent && option.textContent.includes('Free')) {
                    option.click();
                    console.log(`✓ 输入并选择: Free`);
                    break;
                  }
                }
              }
            }
          }
        } catch (error) {
          console.error('React Select处理出错:', error);
        }
        break;

      default:
        element.value = value;
        element.dispatchEvent(new Event('input', { bubbles: true }));
        element.dispatchEvent(new Event('change', { bubbles: true }));
    }
  },

  // 提交流程配置
  submitConfig: {
    submitButton: [
      'button[type="submit"]',
      'input[type="submit"]',
      '.mf-btn',
      'button:contains("Submit")'
    ],
    submitMethod: 'click',
    waitAfterFill: 2000,
    waitAfterSubmit: 5000,
    successIndicators: [
      '.mf-success-message',
      '.success-message',
      '.thank-you',
      '[class*="success"]'
    ],
    errorIndicators: [
      '.mf-error-message',
      '.error-message',
      '[class*="error"]'
    ]
  },

  // 特殊处理
  specialHandling: {
    requiresLogin: false,
    hasCaptcha: false,
    hasFileUpload: false,
    hasReactSelect: true, // 有React Select组件
    hasMetForm: true, // 使用MetForm插件

    // 表单验证规则
    formValidation: {
      requiredFields: ['firstName', 'lastName', 'contactEmail', 'phone', 'siteName', 'siteUrl', 'pricing', 'category', 'siteDescription', 'detailedIntro', 'uniqueSellingPoints', 'cons'],
      emailValidation: true,
      urlValidation: true,
      phoneValidation: true
    },

    // 特殊注意事项
    notes: [
      '这是WordPress网站，使用MetForm插件',
      '表单包含12个字段，大部分必填',
      '姓名需要分割为First Name和Last Name',
      '使用React Select组件选择订阅类型',
      '订阅类型默认选择Free',
      '工具缺点使用固定英文回复',
      '字段ID格式：mf-input-{type}-{hash}',
      '多个字段使用相同的name属性mf-first-name',
      '需要通过ID区分不同字段',
      '网站专注于AI工具收录和推广'
    ]
  }
};

// 自定义处理函数
export function handleAISiteListSubmission(data, _rule) {
  console.log('Processing AI Site List form submission...');

  const processedData = { ...data };

  // 确保URL格式正确
  if (processedData.siteUrl && !processedData.siteUrl.startsWith('http')) {
    processedData.siteUrl = 'https://' + processedData.siteUrl;
  }

  // 设置固定姓名
  processedData.firstName = 'Zephyr';
  processedData.lastName = 'Nightingale';

  // 处理手机号码格式
  if (processedData.phone) {
    // 移除所有非数字字符
    let phoneNumber = processedData.phone.replace(/\D/g, '');
    // 如果是+1开头的美国号码，移除开头的1
    if (phoneNumber.startsWith('1') && phoneNumber.length === 11) {
      phoneNumber = phoneNumber.substring(1);
    }
    // 限制为10位数字
    processedData.phone = phoneNumber.substring(0, 10);
  }

  // 处理简短描述最小长度要求
  if (processedData.siteDescription) {
    let description = processedData.siteDescription.trim();
    // 确保至少150字符
    while (description.length < 150) {
      description += '. This AI tool provides innovative solutions for users.';
    }
    processedData.siteDescription = description;
  }

  // 设置默认值
  processedData.pricing = 'Free';
  processedData.cons = 'Currently, the main limitation is that it is newly launched and does not have a large user base yet. However, the development team is actively working on improvements and expanding the feature set.';

  return processedData;
}
