// ainewsbase.com 网站规则配置
// 网站: https://ainewsbase.com/submit-your-ai-tool/
// 表单技术: Elementor Forms
// 最后更新: 2025-07-08

export const SITE_RULE = {
  // 基本信息
  domain: 'ainewsbase.com',
  siteName: 'AI News Base',
  priority: 1,
  lastUpdated: '2025-07-08',
  
  // 字段映射规则
  fieldMappings: {
    // 工具名称 -> Tool Name
    siteName: {
      selectors: [
        'input[id="form-field-name"]',
        'input[name="form_fields[name]"]',
        'input[placeholder="Name"]'
      ],
      method: 'value',
      validation: 'optional',
      notes: '工具名称，使用website-info.js中的siteName字段'
    },
    
    // 联系邮箱 -> Contact Email
    contactEmail: {
      selectors: [
        'input[id="form-field-email"]',
        'input[name="form_fields[email]"]',
        'input[type="email"]',
        'input[placeholder="Email"]'
      ],
      method: 'value',
      validation: 'required|email',
      notes: '联系邮箱，使用website-info.js中的contactEmail字段'
    },
    
    // 工具URL -> URL
    siteUrl: {
      selectors: [
        'input[id="form-field-field_21ffdb5"]',
        'input[name="form_fields[field_21ffdb5]"]',
        'input[type="url"]',
        'input[placeholder*="https://"]'
      ],
      method: 'value',
      validation: 'required|url',
      notes: '工具URL，使用website-info.js中的siteUrl字段，必须包含https://'
    },
    
    // 简要描述 -> Brief description
    siteDescription: {
      selectors: [
        'textarea[id="form-field-message"]',
        'textarea[name="form_fields[message]"]',
        'textarea[placeholder="Keep it short"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '简要描述，使用website-info.js中的siteDescription字段'
    },
    
    // 定价信息 -> Pricing
    pricingInfo: {
      selectors: [
        'textarea[id="form-field-field_3693895"]',
        'textarea[name="form_fields[field_3693895]"]',
        'textarea[placeholder*="How much does your AI tool cost"]'
      ],
      method: 'value',
      validation: 'required',
      defaultValue: 'Free to use permanently. No subscription required.',
      notes: '定价信息，使用默认值表示免费使用'
    }
  },
  
  // 自定义填写函数
  customFillElement: async function(element, value, config) {
    console.log(`AI News Base自定义填写: ${element.id || element.name}, 方法: ${config.method}`);
    
    switch (config.method) {
      case 'value':
        // 标准输入框和文本域处理
        element.focus();
        await new Promise(resolve => setTimeout(resolve, 100));
        
        // 清空现有内容
        element.value = '';
        await new Promise(resolve => setTimeout(resolve, 50));
        
        // 处理特殊字段
        let finalValue = value;
        if (element.id === 'form-field-field_3693895') {
          // 定价信息使用默认值
          finalValue = config.defaultValue;
        }
        
        // 设置新值
        element.value = finalValue;
        
        // 触发事件
        element.dispatchEvent(new Event('input', { bubbles: true }));
        element.dispatchEvent(new Event('change', { bubbles: true }));
        element.dispatchEvent(new Event('blur', { bubbles: true }));
        
        console.log(`✓ 填写字段: ${element.id} = "${finalValue.substring(0, 50)}..."`);
        break;
        
      default:
        element.value = value;
        element.dispatchEvent(new Event('input', { bubbles: true }));
        element.dispatchEvent(new Event('change', { bubbles: true }));
    }
  },
  
  // 提交流程配置
  submitConfig: {
    submitButton: [
      'button[type="submit"]',
      '.elementor-button',
      'button:contains("Submit")'
    ],
    submitMethod: 'click',
    waitAfterFill: 2000,
    waitAfterSubmit: 5000,
    successIndicators: [
      '.elementor-message-success',
      '.success-message',
      '.thank-you',
      '[class*="success"]'
    ],
    errorIndicators: [
      '.elementor-message-danger',
      '.error-message',
      '[class*="error"]'
    ]
  },
  
  // 特殊处理
  specialHandling: {
    requiresLogin: false,
    hasCaptcha: false,
    hasFileUpload: false,
    isElementorForm: true, // 使用Elementor表单
    isFreeSubmission: true, // 免费提交
    hasNewsletter: true, // 有邮件订阅服务
    
    // 表单验证规则
    formValidation: {
      requiredFields: ['contactEmail', 'siteUrl', 'siteDescription', 'pricingInfo'],
      emailValidation: true,
      urlValidation: true,
      urlRequirement: 'https://' // URL必须包含https://
    },
    
    // 特殊注意事项
    notes: [
      '这是WordPress网站，使用Elementor表单插件',
      '表单包含5个字段，4个必填，1个可选',
      '免费在AI工具页面列出',
      '有5000+邮件订阅者的周刊推广服务',
      'URL字段必须包含https://',
      '简要描述要求保持简短',
      '定价信息需要详细说明费用和试用期',
      '表单字段名格式：form_fields[field_name]',
      '字段ID格式：form-field-{field_name}',
      '支持AI工具免费列表和付费推广'
    ]
  }
};

// 自定义处理函数
export function handleAINewsBaseSubmission(data, _rule) {
  console.log('Processing AI News Base form submission...');

  const processedData = { ...data };

  // 确保URL格式正确
  if (processedData.siteUrl && !processedData.siteUrl.startsWith('http')) {
    processedData.siteUrl = 'https://' + processedData.siteUrl;
  }

  // 设置默认定价信息
  processedData.pricingInfo = 'Free to use permanently. No subscription required.';

  return processedData;
}
