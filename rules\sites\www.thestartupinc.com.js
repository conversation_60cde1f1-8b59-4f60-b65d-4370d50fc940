// TheStartupInc.com 网站规则配置
// 网站: https://www.thestartupinc.com/submit-startup/
// 最后更新: 2025-07-24

export const SITE_RULE = {
  domain: 'www.thestartupinc.com',
  siteName: 'The Startup INC',
  priority: 1,
  lastUpdated: '2025-07-24',

  fieldMappings: {
    siteName: {
      selectors: [
        'input[name="startup-name"]',
        '.wpcf7-form-control[name="startup-name"]',
        'label:contains("Startup Name") input'
      ],
      method: 'value',
      validation: 'required',
      notes: '创业公司名称'
    },

    siteDescription: {
      selectors: [
        'input[name="define-startup"]',
        '.wpcf7-form-control[name="define-startup"]',
        'label:contains("Define Your Startup") input'
      ],
      method: 'value',
      validation: 'required',
      notes: '一句话定义创业公司'
    },

    foundedYear: {
      selectors: [
        'input[name="dfounding-date"]',
        'input[type="date"]',
        'label:contains("Founding Date") input'
      ],
      method: 'value',
      validation: 'required',
      notes: '成立日期'
    },

    category: {
      selectors: [
        'input[name="industry"]',
        '.wpcf7-form-control[name="industry"]',
        'label:contains("Industry") input'
      ],
      method: 'value',
      validation: 'required',
      notes: '行业分类'
    },

    employeeCount: {
      selectors: [
        'select[name="number-employees"]',
        '.wpcf7-select[name="number-employees"]',
        'label:contains("Number Of Employees") select'
      ],
      method: 'select',
      validation: 'required',
      defaultValue: '1-10',
      notes: '员工数量'
    },

    headquarters: {
      selectors: [
        'input[name="headquarters"]',
        '.wpcf7-form-control[name="headquarters"]',
        'label:contains("Headquarters") input'
      ],
      method: 'value',
      validation: 'optional',
      notes: '总部地址'
    },

    linkedinUrl: {
      selectors: [
        'input[name="startup-linkedin"]',
        'input[type="url"][name="startup-linkedin"]',
        'label:contains("Linkedin Profile") input'
      ],
      method: 'value',
      validation: 'optional|url',
      notes: 'LinkedIn档案'
    },

    twitterUrl: {
      selectors: [
        'input[name="startup-twitter"]',
        'input[type="url"][name="startup-twitter"]',
        'label:contains("Twitter Profile") input'
      ],
      method: 'value',
      validation: 'optional|url',
      notes: 'Twitter档案'
    },

    facebookUrl: {
      selectors: [
        'input[name="startup-facebook"]',
        'input[type="url"][name="startup-facebook"]',
        'label:contains("Facebook Profile") input'
      ],
      method: 'value',
      validation: 'optional|url',
      notes: 'Facebook档案'
    },

    contactEmail: {
      selectors: [
        'input[name="email-id"]',
        'input[type="email"]',
        'label:contains("Email") input'
      ],
      method: 'value',
      validation: 'required|email',
      notes: '联系邮箱'
    },

    phone: {
      selectors: [
        'input[name="contact-number"]',
        'input[type="number"]',
        'label:contains("Contact Number") input'
      ],
      method: 'value',
      validation: 'optional',
      notes: '联系电话'
    },

    videoUrl: {
      selectors: [
        'input[name="promo-video"]',
        'input[type="url"][name="promo-video"]',
        'label:contains("Promo Video URL") input'
      ],
      method: 'value',
      validation: 'optional|url',
      notes: '宣传视频URL'
    },

    androidAppUrl: {
      selectors: [
        'input[name="android-app"]',
        'input[type="url"][name="android-app"]',
        'label:contains("Android App URL") input'
      ],
      method: 'value',
      validation: 'optional|url',
      notes: 'Android应用URL'
    },

    iosAppUrl: {
      selectors: [
        'input[name="ios-app"]',
        'input[type="url"][name="ios-app"]',
        'label:contains("iOS Appp URL") input'
      ],
      method: 'value',
      validation: 'optional|url',
      notes: 'iOS应用URL'
    },

    siteUrl: {
      selectors: [
        'input[name="startup-website"]',
        'input[type="url"][name="startup-website"]',
        'label:contains("Website") input'
      ],
      method: 'value',
      validation: 'required|url',
      notes: '网站URL'
    },

    achievements: {
      selectors: [
        'textarea[name="milestones-startup"]',
        '.wpcf7-textarea[name="milestones-startup"]',
        'label:contains("Achievements or Milestones") textarea'
      ],
      method: 'value',
      validation: 'optional',
      notes: '成就或里程碑'
    },

    detailedIntro: {
      selectors: [
        'textarea[name="about-startup"]',
        '.wpcf7-textarea[name="about-startup"]',
        'label:contains("About Your Startup") textarea'
      ],
      method: 'value',
      validation: 'required',
      notes: '关于创业公司，最少500字符'
    },

    problemSolved: {
      selectors: [
        'textarea[name="startup-problems-solve"]',
        '.wpcf7-textarea[name="startup-problems-solve"]',
        'label:contains("problems does your startup solve") textarea'
      ],
      method: 'value',
      validation: 'required',
      notes: '解决的问题'
    },

    targetAudience: {
      selectors: [
        'textarea[name="service-for"]',
        '.wpcf7-textarea[name="service-for"]',
        'label:contains("Product/ Service is for") textarea'
      ],
      method: 'value',
      validation: 'required',
      notes: '目标用户'
    },

    uniqueValue: {
      selectors: [
        'textarea[name="standout-crowd"]',
        '.wpcf7-textarea[name="standout-crowd"]',
        'label:contains("stand out from the crowd") textarea'
      ],
      method: 'value',
      validation: 'required',
      notes: '独特价值'
    },

    futurePlans: {
      selectors: [
        'textarea[name="future-plan"]',
        '.wpcf7-textarea[name="future-plan"]',
        'label:contains("future plans") textarea'
      ],
      method: 'value',
      validation: 'required',
      notes: '未来计划'
    },

    foundersInfo: {
      selectors: [
        'textarea[name="about-founders"]',
        '.wpcf7-textarea[name="about-founders"]',
        'label:contains("About The Founders") textarea'
      ],
      method: 'value',
      validation: 'required',
      notes: '创始人信息，最少250字符'
    }
  },

  submitConfig: {
    submitButton: 'input[type="submit"], .wpcf7-submit',
    submitMethod: 'click',
    successIndicators: ['.wpcf7-mail-sent-ok'],
    errorIndicators: ['.wpcf7-validation-errors']
  },

  specialHandling: {
    requiresLogin: false,
    hasCaptcha: false,
    hasFileUpload: false,
    customScript: 'handleTheStartupIncSubmission',
    formValidation: {
      requiredFields: ['siteName', 'siteDescription', 'foundedYear', 'category', 'employeeCount', 'contactEmail', 'siteUrl', 'detailedIntro', 'problemSolved', 'targetAudience', 'uniqueValue', 'futurePlans', 'foundersInfo'],
      emailValidation: true,
      urlValidation: true
    },
    notes: [
      '使用Contact Form 7构建',
      '付费发布服务$20',
      '详细的创业公司信息收集',
      '多个文本域有字符限制',
      '包含社交媒体和应用链接',
      '要求独特内容不可复制粘贴'
    ]
  }
};

export function handleTheStartupIncSubmission(data, rule) {
  const processedData = { ...data };

  // URL格式化
  const urlFields = ['siteUrl', 'linkedinUrl', 'twitterUrl', 'facebookUrl', 'videoUrl', 'androidAppUrl', 'iosAppUrl'];
  urlFields.forEach(field => {
    if (processedData[field] && !processedData[field].startsWith('http')) {
      processedData[field] = 'https://' + processedData[field];
    }
  });

  // 设置默认员工数量
  const employeeSelect = document.querySelector('select[name="number-employees"]');
  if (employeeSelect) {
    employeeSelect.value = '1-10';
  }

  return processedData;
}

export async function customFillElement(element, value, config) {
  // 处理下拉选择框
  if (element.tagName === 'SELECT') {
    const options = element.querySelectorAll('option');
    const option = Array.from(options).find(opt => opt.value === '1-10');
    if (option) {
      element.value = option.value;
      element.dispatchEvent(new Event('change', { bubbles: true }));
      return true;
    }
  }

  // 处理日期字段
  if (element.type === 'date') {
    const currentDate = new Date();
    const dateString = currentDate.toISOString().split('T')[0];
    element.value = dateString;
    element.dispatchEvent(new Event('change', { bubbles: true }));
    return true;
  }

  if (element.tagName === 'INPUT' || element.tagName === 'TEXTAREA') {
    element.value = value;
    element.dispatchEvent(new Event('input', { bubbles: true }));
    element.dispatchEvent(new Event('change', { bubbles: true }));
    return true;
  }

  return false;
}