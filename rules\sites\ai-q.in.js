// ai-q.in 网站规则配置
// 网站: https://ai-q.in/submit-tool/
// 表单技术: Contact Form 7
// 最后更新: 2025-07-07

export const SITE_RULE = {
  // 基本信息
  domain: 'ai-q.in',
  siteName: 'AI-Q',
  priority: 1,
  lastUpdated: '2025-07-07',
  
  // 字段映射规则
  fieldMappings: {
    // 提交者姓名 -> Your Name
    fullName: {
      selectors: [
        'input[name="your-name"]',
        'input[placeholder="Your Name"]',
        '.wpcf7-text[name="your-name"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '提交者姓名，使用website-info.js中的fullName字段'
    },
    
    // 工具名称 -> Tool Name
    siteName: {
      selectors: [
        'input[name="tool-name"]',
        'input[placeholder="Tool Name"]',
        '.wpcf7-text[name="tool-name"]'
      ],
      method: 'value',
      validation: 'required',
      notes: 'AI工具名称，使用website-info.js中的siteName字段'
    },
    
    // 工具描述 -> Tool Description
    siteDescription: {
      selectors: [
        'textarea[name="tool-description"]',
        'textarea[placeholder="Tool Description"]',
        '.wpcf7-textarea[name="tool-description"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '工具描述，使用website-info.js中的siteDescription字段，最大2000字符'
    },
    
    // 工具分类 -> Category
    category: {
      selectors: [
        'input[name="category"]',
        'input[placeholder="Category"]',
        '.wpcf7-text[name="category"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '工具分类，使用website-info.js中的category字段'
    },
    
    // 工具链接 -> Tool Link
    siteUrl: {
      selectors: [
        'input[name="tool-link"]',
        'input[placeholder="http://example.com"]',
        '.wpcf7-url[name="tool-link"]'
      ],
      method: 'value',
      validation: 'required|url',
      notes: '工具链接，使用website-info.js中的siteUrl字段'
    },
    
    // 工具所有者邮箱 -> Tool Owner Email
    contactEmail: {
      selectors: [
        'input[name="tool-owner-email"]',
        'input[placeholder="<EMAIL>"]',
        '.wpcf7-email[name="tool-owner-email"]'
      ],
      method: 'value',
      validation: 'required|email',
      notes: '工具所有者邮箱，使用website-info.js中的contactEmail字段'
    },
    
    // 联盟注册URL -> Affiliate Registration URL
    affiliateUrl: {
      selectors: [
        'input[name="affiliate-url"]',
        'input[placeholder*="affiliate program"]',
        '.wpcf7-url[name="affiliate-url"]'
      ],
      method: 'value',
      validation: 'optional|url',
      notes: '联盟注册URL，可选字段'
    },
    
    // 工具社交媒体 -> Tool Social Handle
    twitterUrl: {
      selectors: [
        'input[name="tool-social"]',
        'input[placeholder="Social Media Handle"]',
        '.wpcf7-text[name="tool-social"]',
        'input[aria-describedby*="tool-social"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '工具社交媒体账号，使用website-info.js中的twitterUrl字段'
    },
    
    // 价格 -> Price
    pricing: {
      selectors: [
        'select[name="price"]',
        '.wpcf7-select[name="price"]'
      ],
      method: 'select',
      validation: 'required',
      defaultValue: 'Free',
      notes: '价格模式，下拉选择：Free, Paid, Freemium'
    }
  },
  
  // 自定义填写函数
  customFillElement: async function(element, value, config) {
    console.log(`AI-Q自定义填写: ${element.name || element.placeholder}, 方法: ${config.method}`);
    
    switch (config.method) {
      case 'value':
        // 标准输入框和文本域处理
        element.focus();
        await new Promise(resolve => setTimeout(resolve, 100));
        
        // 清空现有内容
        element.value = '';
        await new Promise(resolve => setTimeout(resolve, 50));
        
        // 处理特殊字段
        let finalValue = value;
        if (element.name === 'tool-description' && element.maxLength === 2000) {
          // 工具描述限制2000字符
          finalValue = value.substring(0, 2000);
        } else if (element.name === 'tool-social') {
          // 社交媒体处理 - 使用twitterUrl
          if (typeof value === 'string' && value.trim()) {
            if (value.startsWith('http')) {
              // 如果是完整URL，提取用户名
              const urlParts = value.split('/');
              const username = urlParts[urlParts.length - 1] || 'aitool';
              finalValue = '@' + username;
            } else if (value.startsWith('@')) {
              // 如果已经有@前缀，直接使用
              finalValue = value;
            } else {
              // 如果只是用户名，添加@前缀
              finalValue = '@' + value;
            }
          } else {
            // 如果没有值，使用默认值
            finalValue = '@aitool';
          }
        } else if (element.name === 'affiliate-url' && !value) {
          // 联盟URL为空时跳过
          return;
        }
        
        // 设置新值
        element.value = finalValue;
        
        // 触发事件
        element.dispatchEvent(new Event('input', { bubbles: true }));
        element.dispatchEvent(new Event('change', { bubbles: true }));
        element.dispatchEvent(new Event('blur', { bubbles: true }));
        
        console.log(`✓ 填写字段: ${element.name} = "${finalValue.substring(0, 50)}..."`);
        break;
        
      case 'select':
        // 下拉选择框处理
        if (element.tagName.toLowerCase() === 'select') {
          // 价格映射
          const pricingMapping = {
            'Free': 'Free',
            'Freemium': 'Freemium',
            'Paid': 'Paid'
          };
          
          let targetValue = pricingMapping[value] || config.defaultValue;
          
          // 查找匹配的选项
          const option = Array.from(element.options).find(opt => 
            opt.value === targetValue || 
            opt.text === targetValue ||
            opt.text.toLowerCase().includes(targetValue.toLowerCase())
          );
          
          if (option) {
            element.value = option.value;
            element.dispatchEvent(new Event('change', { bubbles: true }));
            console.log(`✓ 选择价格: ${option.text}`);
          } else {
            // 使用默认值
            const defaultOption = Array.from(element.options).find(opt => 
              opt.text === config.defaultValue || opt.value === config.defaultValue
            );
            if (defaultOption) {
              element.value = defaultOption.value;
              element.dispatchEvent(new Event('change', { bubbles: true }));
              console.log(`✓ 使用默认价格: ${defaultOption.text}`);
            }
          }
        }
        break;
        
      default:
        element.value = value;
        element.dispatchEvent(new Event('input', { bubbles: true }));
        element.dispatchEvent(new Event('change', { bubbles: true }));
    }
  },
  
  // 提交流程配置
  submitConfig: {
    submitButton: [
      'input[type="submit"]',
      '.wpcf7-submit',
      'input[value="Submit"]'
    ],
    submitMethod: 'click',
    waitAfterFill: 2000,
    waitAfterSubmit: 5000,
    successIndicators: [
      '.wpcf7-mail-sent-ok',
      '.wpcf7-response-output',
      '.success-message',
      'text:contains("sent successfully")'
    ],
    errorIndicators: [
      '.wpcf7-validation-errors',
      '.wpcf7-mail-sent-ng',
      '.error-message'
    ]
  },
  
  // 特殊处理
  specialHandling: {
    requiresLogin: false,
    hasCaptcha: false,
    hasFileUpload: false,
    isContactForm7: true, // 使用Contact Form 7
    hasAffiliateProgram: true, // 支持联盟计划
    
    // 表单验证规则
    formValidation: {
      requiredFields: ['fullName', 'siteName', 'siteDescription', 'category', 'siteUrl', 'contactEmail', 'twitterUrl', 'pricing'],
      emailValidation: true,
      urlValidation: true,
      characterLimits: {
        siteDescription: 2000
      }
    },
    
    // 特殊注意事项
    notes: [
      '这是WordPress网站，使用Contact Form 7插件',
      '表单包含9个字段，8个必填，1个可选',
      '工具描述限制2000字符',
      '价格选项：Free, Paid, Freemium',
      '联盟注册URL是可选字段',
      '社交媒体账号需要@前缀',
      '网站专注于AI工具评测和推荐',
      '提交不保证发布，需要人工审核',
      '要求提供SEO优化的文章内容'
    ]
  }
};

// 自定义处理函数
export function handleAIQSubmission(data, _rule) {
  console.log('Processing AI-Q form submission...');

  const processedData = { ...data };

  // 确保URL格式正确
  if (processedData.siteUrl && !processedData.siteUrl.startsWith('http')) {
    processedData.siteUrl = 'https://' + processedData.siteUrl;
  }

  // 处理社交媒体账号
  if (processedData.twitterUrl) {
    const socialUrl = processedData.twitterUrl;
    if (socialUrl.startsWith('http')) {
      const urlParts = socialUrl.split('/');
      processedData.twitterUrl = '@' + (urlParts[urlParts.length - 1] || 'aitool');
    } else if (!socialUrl.startsWith('@')) {
      processedData.twitterUrl = '@' + socialUrl;
    }
    // 如果已经有@前缀，保持不变
  } else {
    // 如果没有twitterUrl，使用默认值
    processedData.twitterUrl = '@aitool';
  }

  // 处理工具描述长度
  if (processedData.siteDescription && processedData.siteDescription.length > 2000) {
    processedData.siteDescription = processedData.siteDescription.substring(0, 2000);
  }

  // 设置默认价格
  processedData.pricing = 'Free';

  // 联盟URL处理（可选）
  if (!processedData.affiliateUrl) {
    delete processedData.affiliateUrl;
  }

  return processedData;
}
