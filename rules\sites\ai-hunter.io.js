// AI-Hunter.io 网站规则配置
// 网站: https://ai-hunter.io/submit-a-tool/
// 最后更新: 2025-07-06

export const SITE_RULE = {
  // 基本信息
  domain: 'ai-hunter.io',
  siteName: 'AI Hunter',
  priority: 1,
  lastUpdated: '2025-07-06',
  
  // 字段映射规则
  fieldMappings: {
    // 联系人姓名 -> First Name
    fullName: {
      selectors: [
        'input[name="name-1"]',
        'input[placeholder*="First Name"]',
        '#forminator-field-name-1_676260573a582'
      ],
      method: 'value',
      validation: 'required',
      notes: '只需要名字，不需要姓氏'
    },

    // 联系邮箱 -> Email
    contactEmail: {
      selectors: [
        'input[name="email-1"]',
        'input[type="email"]',
        '#forminator-field-email-1_676260573a582'
      ],
      method: 'value',
      validation: 'required|email'
    },

    // 网站名称 -> AI Tool Name
    siteName: {
      selectors: [
        'input[name="name-3"]',
        'input[placeholder*="ChatGPT"]',
        '#forminator-field-name-3_676260573a582'
      ],
      method: 'value',
      validation: 'required',
      maxLength: 100
    },

    // 网站URL -> AI Tool Website
    siteUrl: {
      selectors: [
        'input[name="url-1"]',
        'input[type="url"]',
        '#forminator-field-url-1_676260573a582'
      ],
      method: 'value',
      validation: 'required|url'
    },
    
    // 网站分类 -> AI Tool Category (下拉选择)
    category: {
      selectors: [
        'select[name="select-1"]',
        '#forminator-form-20773__field--select-1_676260573a582'
      ],
      method: 'select',
      validation: 'required',
      options: [
        'Fun-Tools', '3D', 'Aggregators', 'AI-Detection', 'Audio-Editing', 'Avatars',
        'Chat', 'Code-Assistant', 'Copywriting', 'Customer-Support',
        'Design-Assistant', 'Developer-Tools', 'E-commerce', 'Education-Assistant',
        'Email-Assistant', 'Experiments', 'Fashion', 'Finance',
        'Gaming', 'General-Writing', 'Generative-Art', 'Generative-Code',
        'Generative-Ideas', 'Gift-Ideas', 'Healthcare', 'Human-Ressources',
        'Image-Editing', 'Image-Generator', 'Image-Improvement', 'Image-Scanning',
        'Inspiration', 'Inspiration-Research', 'Legal-Assistant', 'Life-Assistant',
        'Low-code-no-code', 'Marketing', 'Memory', 'Motion-Capture', 'Music',
        'Non-AI-Tools', 'Paraphraser', 'Personalized-Videos', 'Podcasting',
        'Presentations', 'Productivity', 'Prompt-Guides', 'Prompt',
        'Real-Estate', 'Research', 'Resources', 'Sales', 'Search-Engine',
        'Self-Improvement', 'SEO', 'Social-Media', 'Speech-To-Text',
        'Spreadsheets', 'SQL', 'Startup-Tools', 'Story-Teller', 'Summarizer',
        'Text-To-Speach', 'Transcriber', 'Translation', 'Video-Editing',
        'Video-Generator', 'Voice-Modulation'
      ],
      defaultValue: 'Fun-Tools' // 默认选择
    },

    // 定价模式 -> Pricing
    pricing: {
      selectors: [
        'select[name="select-2"]',
        '#forminator-form-20773__field--select-2_676260573a582'
      ],
      method: 'select',
      validation: 'required',
      options: ['43', '45', '53', '44', '49'], // Free, Freemium, Open Source, Paid, Contact for Pricing
      optionTexts: ['Free', 'Freemium', 'Open Source', 'Paid', 'Contact for Pricing'],
      defaultValue: '43' // Free
    },
    
    // 网站描述 -> AI Tool Description
    siteDescription: {
      selectors: [
        'textarea[name="textarea-1"]',
        'textarea[placeholder*="Full AI Tool Description"]',
        '#forminator-field-textarea-1_676260573a582'
      ],
      method: 'value',
      validation: 'required',
      minLength: 50,
      maxLength: 1000,
      notes: '必须是独特内容，不能复制粘贴'
    }
  },
  
  // 提交流程配置
  submitConfig: {
    submitButton: 'button.forminator-button.forminator-button-submit',
    submitMethod: 'click',
    waitAfterFill: 2000, // 填写后等待2秒
    waitAfterSubmit: 5000, // 提交后等待5秒
    successIndicators: [
      '.forminator-response-message.forminator-success',
      '.success-message',
      '.thank-you'
    ],
    errorIndicators: [
      '.forminator-response-message.forminator-error',
      '.error-message',
      '.validation-error'
    ]
  },
  
  // 特殊处理
  specialHandling: {
    requiresLogin: false,
    hasCaptcha: false,
    hasFileUpload: false,
    
    // 自定义处理脚本
    customScript: 'handleAIHunterSubmission',
    
    // 表单验证规则
    formValidation: {
      requiredFields: ['fullName', 'contactEmail', 'siteName', 'siteUrl', 'category', 'pricing', 'siteDescription'],
      emailValidation: true,
      urlValidation: true,
      uniqueContentCheck: true
    },
    
    // 特殊注意事项
    notes: [
      '描述必须是独特内容，不能复制其他地方的描述',
      '审核时间通常为1-2周',
      '免费提交，无需付费',
      '分类选择要准确，影响展示效果'
    ]
  }
};

// 自定义处理函数
export function handleAIHunterSubmission(data, rule) {
  console.log('Processing AI-Hunter.io submission...');
  
  // 特殊处理逻辑
  const processedData = { ...data };
  
  // 确保描述内容独特性
  if (processedData.siteDescription) {
    // 可以添加一些独特性检查或处理
    processedData.siteDescription = processedData.siteDescription.trim();
  }
  
  // 确保分类选择正确
  if (!rule.fieldMappings.category.options.includes(processedData.category)) {
    processedData.category = 'Productivity'; // 默认分类
  }
  
  return processedData;
}
