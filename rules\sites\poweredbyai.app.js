// poweredbyai.app 网站规则配置
// 网站: https://poweredbyai.app/submit-tool
// 表单技术: React Form with Tailwind CSS
// 最后更新: 2025-07-08

export const SITE_RULE = {
  // 基本信息
  domain: 'poweredbyai.app',
  siteName: 'Powered by AI',
  priority: 1,
  lastUpdated: '2025-07-08',
  
  // 字段映射规则
  fieldMappings: {
    // 工具名称 -> Name
    siteName: {
      selectors: [
        'input[name="name"]',
        'input[placeholder="Open AI"]',
        'form input[type="text"]:first-of-type',
        'input.bg-primary:first-of-type'
      ],
      method: 'value',
      validation: 'required',
      notes: '工具名称，使用website-info.js中的siteName字段'
    },
    
    // 分类 -> Category
    category: {
      selectors: [
        'select[name="category"]',
        'select.bg-primary',
        'select:has(option[value="1"])',
        'form select:first-of-type'
      ],
      method: 'select',
      validation: 'required',
      defaultValue: '1', // Machine Learning
      availableOptions: [
        { value: '1', text: 'Machine Learning' },
        { value: '2', text: 'NLP' },
        { value: '3', text: 'Expert system' },
        { value: '4', text: 'Vision' },
        { value: '6', text: 'Speech' },
        { value: '7', text: 'Planning' },
        { value: '8', text: 'Robotics' },
        { value: '9', text: 'Other' }
      ],
      notes: '分类，默认选择Machine Learning (value=1)'
    },
    
    // 描述 -> Description
    siteDescription: {
      selectors: [
        'input[name="description"]',
        'input.bg-transparent.h-24',
        'form input[type="text"]:nth-of-type(2)',
        'input.border-gray-600:nth-of-type(2)'
      ],
      method: 'value',
      validation: 'required',
      notes: '工具描述，使用website-info.js中的siteDescription字段'
    },
    
    // 图标 -> Icon
    faviconUrl: {
      selectors: [
        'input[name="icon"]',
        'input.bg-transparent:nth-of-type(3)',
        'form input[type="text"]:nth-of-type(3)'
      ],
      method: 'value',
      validation: 'optional',
      notes: '工具图标链接，使用website-info.js中的faviconUrl字段，可选'
    },
    
    // 链接 -> Links
    siteUrl: {
      selectors: [
        'input[name="links.0.link"]',
        'input[placeholder="Enter Link"]',
        'input.bg-transparent:nth-of-type(4)'
      ],
      method: 'value',
      validation: 'required',
      notes: '工具链接，使用website-info.js中的siteUrl字段，不包含https或www'
    },
    
    // 平台 -> Platform
    platform: {
      selectors: [
        'select[name="links.0.platform"]',
        'select:has(option[value="website"])',
        'form select:nth-of-type(2)'
      ],
      method: 'select',
      validation: 'required',
      defaultValue: 'website',
      availableOptions: [
        'website', 'youtube', 'twitter', 'facebook', 
        'instagram', 'tiktok', 'product hunt'
      ],
      notes: '平台类型，默认选择website'
    }
  },
  
  // 自定义填写函数
  customFillElement: async function(element, value, config) {
    console.log(`Powered by AI自定义填写: ${element.name || element.tagName}, 方法: ${config.method}`);
    
    switch (config.method) {
      case 'value':
        // 标准输入框处理
        element.focus();
        await new Promise(resolve => setTimeout(resolve, 100));
        
        // 清空现有内容
        element.value = '';
        await new Promise(resolve => setTimeout(resolve, 50));
        
        // 处理URL格式（移除协议和www）
        let finalValue = value;
        if (element.name === 'links.0.link' && value) {
          finalValue = value.replace(/^https?:\/\//, '').replace(/^www\./, '');
        }
        
        // 设置新值
        element.value = finalValue;
        
        // 触发React事件
        const inputEvent = new Event('input', { bubbles: true });
        const changeEvent = new Event('change', { bubbles: true });
        
        // React特殊处理
        Object.defineProperty(inputEvent, 'target', {
          writable: false,
          value: element
        });
        Object.defineProperty(changeEvent, 'target', {
          writable: false,
          value: element
        });
        
        element.dispatchEvent(inputEvent);
        element.dispatchEvent(changeEvent);
        element.dispatchEvent(new Event('blur', { bubbles: true }));
        
        console.log(`✓ 填写字段: ${element.name} = "${finalValue}"`);
        break;
        
      case 'select':
        // 下拉选择处理
        element.focus();
        await new Promise(resolve => setTimeout(resolve, 100));
        
        const targetValue = config.defaultValue;
        
        // 查找匹配的选项
        const option = Array.from(element.options).find(opt => 
          opt.value === targetValue
        );
        
        if (option) {
          element.value = option.value;
          element.dispatchEvent(new Event('change', { bubbles: true }));
          console.log(`✓ 选择选项: ${option.text || option.value}`);
        } else {
          console.log(`⚠️ 未找到选项: ${targetValue}`);
        }
        break;
        
      default:
        element.value = value;
        element.dispatchEvent(new Event('input', { bubbles: true }));
        element.dispatchEvent(new Event('change', { bubbles: true }));
    }
  },
  
  // 提交流程配置
  submitConfig: {
    submitButton: [
      'button[type="submit"]',
      'button:contains("Next Step")',
      '.border-\\[\\#9536f4\\]',
      'button.w-full'
    ],
    submitMethod: 'click',
    waitAfterFill: 2000,
    waitAfterSubmit: 4000,
    successIndicators: [
      'text:contains("success")',
      'text:contains("submitted")',
      'text:contains("next step")'
    ],
    errorIndicators: [
      'text:contains("error")',
      'text:contains("required")',
      'text:contains("invalid")'
    ]
  },
  
  // 特殊处理
  specialHandling: {
    requiresLogin: false,
    hasCaptcha: false,
    hasFileUpload: false,
    isReactForm: true, // React表单
    hasTailwindCSS: true, // 使用Tailwind CSS
    hasMultiStep: true, // 多步骤表单
    hasKeywordSelection: true, // 有关键词选择
    hasLinkManagement: true, // 有链接管理
    
    // 表单验证规则
    formValidation: {
      requiredFields: ['siteName', 'category', 'siteDescription', 'siteUrl', 'platform'],
      optionalFields: ['faviconUrl'],
      urlFormatting: true, // 需要URL格式化
      multiStepForm: true
    },
    
    // 特殊注意事项
    notes: [
      '这是Powered by AI的工具提交表单',
      '表单包含6个字段：5个必填，1个可选',
      '使用React和Tailwind CSS构建',
      '多步骤表单，这是第一步',
      '专注于AI工具分类和收录',
      '有8个AI技术分类可选',
      '支持关键词选择（最多3个）',
      '支持多种平台链接',
      '链接不需要包含https或www',
      '有图标上传功能（可选）',
      '团队会编辑描述以优化展示',
      '下一步是平台和定价设置'
    ]
  }
};

// 自定义处理函数
export function handlePoweredByAISubmission(data, _rule) {
  console.log('Processing Powered by AI form submission...');

  const processedData = { ...data };

  // 处理URL格式（移除协议和www）
  if (processedData.siteUrl) {
    processedData.siteUrl = processedData.siteUrl
      .replace(/^https?:\/\//, '')
      .replace(/^www\./, '');
  }

  // 设置默认值
  processedData.category = '1'; // Machine Learning
  processedData.platform = 'website';

  return processedData;
}

// React表单检测
export function detectReactForm() {
  console.log('检测React表单...');

  // 检查React特征
  const reactElements = document.querySelectorAll('[data-reactroot], [data-react-helmet]');
  const tailwindClasses = document.querySelectorAll('.bg-primary, .border-gray-600');

  if (reactElements.length > 0 || tailwindClasses.length > 0) {
    console.log('✓ 检测到React表单和Tailwind CSS');
    return true;
  }

  return false;
}

// Powered by AI信息提醒
export function showPoweredByAIInfo() {
  console.log('🚀 Powered by AI 信息:');
  console.log('');
  console.log('平台特色:');
  console.log('- 专注于AI工具发现和分享');
  console.log('- 现代化的React界面');
  console.log('- 多步骤提交流程');
  console.log('- 专业的AI技术分类');
  console.log('');
  console.log('AI技术分类 (8个):');
  console.log('- Machine Learning (机器学习) - 推荐');
  console.log('- NLP (自然语言处理)');
  console.log('- Expert system (专家系统)');
  console.log('- Vision (计算机视觉)');
  console.log('- Speech (语音技术)');
  console.log('- Planning (规划算法)');
  console.log('- Robotics (机器人技术)');
  console.log('- Other (其他)');
  console.log('');
  console.log('支持平台 (7种):');
  console.log('- Website (网站) - 推荐');
  console.log('- YouTube, Twitter, Facebook');
  console.log('- Instagram, TikTok, Product Hunt');
  console.log('');
  console.log('提交流程:');
  console.log('1. 基本信息（当前步骤）');
  console.log('2. 平台和定价设置');
  console.log('3. 审核和发布');
  console.log('');
  console.log('Powered by AI - 发现最新的AI工具！');
}

// AI分类选择帮助
export function showAICategoryHelp() {
  console.log('🤖 AI分类选择帮助:');
  console.log('');
  console.log('Machine Learning (机器学习):');
  console.log('- 深度学习、神经网络');
  console.log('- 数据挖掘、预测分析');
  console.log('- 推荐系统、分类算法');
  console.log('');
  console.log('NLP (自然语言处理):');
  console.log('- 文本生成、翻译工具');
  console.log('- 聊天机器人、语言模型');
  console.log('- 情感分析、文本摘要');
  console.log('');
  console.log('Vision (计算机视觉):');
  console.log('- 图像识别、物体检测');
  console.log('- 人脸识别、图像生成');
  console.log('- OCR、图像编辑');
  console.log('');
  console.log('Speech (语音技术):');
  console.log('- 语音识别、语音合成');
  console.log('- 语音助手、音频处理');
  console.log('- 语音翻译、音乐生成');
  console.log('');
  console.log('选择最符合您工具核心技术的分类！');
}

// 关键词选择提醒
export function showKeywordSelectionReminder() {
  console.log('🏷️ 关键词选择提醒:');
  console.log('');
  console.log('选择要求:');
  console.log('- 最多选择3个关键词');
  console.log('- 选择与工具功能最相关的词汇');
  console.log('- 有助于用户搜索和发现');
  console.log('');
  console.log('推荐关键词类型:');
  console.log('- 技术类型：AI, ML, NLP, CV');
  console.log('- 功能类型：Generate, Analyze, Predict');
  console.log('- 应用领域：Business, Creative, Developer');
  console.log('- 用户群体：Beginner, Professional, Enterprise');
  console.log('');
  console.log('点击"Add keyword"按钮添加关键词！');
}

// URL格式处理
export function processUrlForSubmission(url) {
  console.log(`处理URL格式: ${url}`);

  // 移除协议前缀
  let processedUrl = url.replace(/^https?:\/\//, '');

  // 移除www前缀
  processedUrl = processedUrl.replace(/^www\./, '');

  // 移除尾部斜杠
  processedUrl = processedUrl.replace(/\/$/, '');

  console.log(`处理后的URL: ${processedUrl}`);
  return processedUrl;
}

// 表单验证
export function validatePoweredByAIForm() {
  console.log('验证Powered by AI表单...');

  const requiredFields = [
    { selector: 'input[name="name"]', label: '工具名称' },
    { selector: 'select[name="category"]', label: '分类' },
    { selector: 'input[name="description"]', label: '描述' },
    { selector: 'input[name="links.0.link"]', label: '链接' },
    { selector: 'select[name="links.0.platform"]', label: '平台' }
  ];

  let isValid = true;

  requiredFields.forEach(field => {
    const element = document.querySelector(field.selector);
    if (!element || !element.value.trim()) {
      console.log(`⚠️ 必填字段为空: ${field.label}`);
      isValid = false;
    }
  });

  if (isValid) {
    console.log('✓ 表单验证通过');
  }

  return isValid;
}

// 多步骤表单说明
export function showMultiStepFormInfo() {
  console.log('📋 多步骤表单说明:');
  console.log('');
  console.log('第一步 - 基本信息（当前）:');
  console.log('- 工具名称和分类');
  console.log('- 描述和图标');
  console.log('- 关键词选择');
  console.log('- 链接和平台');
  console.log('');
  console.log('第二步 - 平台和定价:');
  console.log('- 定价模式设置');
  console.log('- 平台兼容性');
  console.log('- 功能特性');
  console.log('');
  console.log('优势:');
  console.log('- 降低用户认知负担');
  console.log('- 提高表单完成率');
  console.log('- 更好的用户体验');
  console.log('- 数据收集更完整');
}

// React表单特点
export function showReactFormFeatures() {
  console.log('⚛️ React表单特点:');
  console.log('');
  console.log('技术优势:');
  console.log('- 组件化开发');
  console.log('- 状态管理');
  console.log('- 虚拟DOM优化');
  console.log('- 实时验证');
  console.log('');
  console.log('用户体验:');
  console.log('- 快速响应');
  console.log('- 流畅交互');
  console.log('- 动态更新');
  console.log('- 现代化界面');
  console.log('');
  console.log('Tailwind CSS样式:');
  console.log('- 响应式设计');
  console.log('- 一致的视觉风格');
  console.log('- 快速开发');
  console.log('- 易于维护');
}
