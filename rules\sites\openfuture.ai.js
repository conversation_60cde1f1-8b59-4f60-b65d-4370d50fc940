// OpenFuture.ai 网站规则配置
// 网站: https://openfuture.ai/zh/submit-tool
// 最后更新: 2025-07-24

export const SITE_RULE = {
  domain: 'openfuture.ai',
  siteName: 'Open Future AI',
  priority: 1,
  lastUpdated: '2025-07-24',

  fieldMappings: {
    siteName: {
      selectors: [
        '#tool_name',
        'input[name="tool_name"]',
        'label:contains("工具名称") + input'
      ],
      method: 'value',
      validation: 'required',
      notes: '工具名称'
    },

    siteUrl: {
      selectors: [
        '#tool_url',
        'input[name="tool_url"]',
        'input[type="url"]'
      ],
      method: 'value',
      validation: 'required|url',
      notes: '工具网址'
    },

    contactEmail: {
      selectors: [
        '#email',
        'input[name="email"]',
        'input[type="email"]'
      ],
      method: 'value',
      validation: 'required|email',
      notes: '联系邮箱'
    },

    pricingModel: {
      selectors: [
        '#pricing_model',
        'select[name="pricing_model"]',
        'label:contains("定价模式") + select'
      ],
      method: 'select',
      validation: 'required',
      defaultValue: 'freemium',
      notes: '定价模式，默认Freemium'
    },

    siteDescription: {
      selectors: [
        '#tool_description',
        'textarea[name="tool_description"]',
        'label:contains("工具描述") + textarea'
      ],
      method: 'value',
      validation: 'optional',
      notes: '工具描述，可选'
    }
  },

  submitConfig: {
    submitButton: 'input[type="submit"], .col-4.mx-auto',
    submitMethod: 'click',
    successIndicators: ['.success-message'],
    errorIndicators: ['.error-message']
  },

  specialHandling: {
    requiresLogin: false,
    hasCaptcha: false,
    hasFileUpload: false,
    customScript: 'handleOpenFutureSubmission',
    formValidation: {
      requiredFields: ['siteName', 'siteUrl', 'contactEmail', 'pricingModel'],
      emailValidation: true,
      urlValidation: true
    },
    notes: [
      'AI工具目录提交平台',
      '中文界面',
      '定价模式选择',
      'Bootstrap样式',
      '简洁的5字段表单',
      'POST方法提交'
    ]
  }
};

export function handleOpenFutureSubmission(data, rule) {
  const processedData = { ...data };

  if (processedData.siteUrl && !processedData.siteUrl.startsWith('http')) {
    processedData.siteUrl = 'https://' + processedData.siteUrl;
  }

  // 自动选择默认定价模式
  const pricingSelect = document.querySelector('#pricing_model');
  if (pricingSelect) {
    pricingSelect.value = 'freemium';
  }

  return processedData;
}

export async function customFillElement(element, value, config) {
  // 处理下拉选择框
  if (element.tagName === 'SELECT') {
    const options = element.querySelectorAll('option');
    const option = Array.from(options).find(opt => opt.value === 'freemium');
    if (option) {
      element.value = option.value;
      element.dispatchEvent(new Event('change', { bubbles: true }));
      return true;
    }
  }

  if (element.tagName === 'INPUT' || element.tagName === 'TEXTAREA') {
    element.value = value;
    element.dispatchEvent(new Event('input', { bubbles: true }));
    element.dispatchEvent(new Event('change', { bubbles: true }));
    return true;
  }

  return false;
}