// 1WebsDirectory.com 网站规则配置
// 网站: https://www.1websdirectory.com/submission/0/free-listing/
// 最后更新: 2025-07-26

export const SITE_RULE = {
  // 基本信息
  domain: 'www.1websdirectory.com',
  siteName: '1 Webs Directory',
  priority: 1,
  lastUpdated: '2025-07-26',

  // 字段映射规则
  fieldMappings: {
    // 网站URL -> Website URL
    siteUrl: {
      selectors: [
        'input[name="company_url"]',
        'input[placeholder="Website URL"]'
      ],
      method: 'value',
      validation: 'required|url',
      notes: '网站URL地址'
    },

    // 公司名称 -> Company Name
    siteName: {
      selectors: [
        'input[name="company_name"]',
        'input[placeholder="Website title"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '公司/网站名称'
    },

    // 简短描述 -> Short Description
    siteDescription: {
      selectors: [
        'textarea[name="short_description"]',
        'textarea[placeholder="Short Description"]',
        'textarea[rows="8"]'
      ],
      method: 'value',
      validation: 'optional',
      maxLength: 350,
      notes: '简短描述，限制350字符'
    },

    // 联系人姓名 -> Your Name
    fullName: {
      selectors: [
        'input[name="contact_name"]',
        'input[placeholder="Enter Name"]'
      ],
      method: 'value',
      validation: 'required',
      maxLength: 60,
      notes: '联系人姓名，最多60字符'
    },

    // 联系邮箱 -> Your Email
    contactEmail: {
      selectors: [
        'input[name="contact_email"]',
        'input[type="email"]',
        'input[placeholder="Enter Email"]'
      ],
      method: 'value',
      validation: 'required|email',
      notes: '联系邮箱地址'
    },

    // 主分类 -> Business and Ecology
    category: {
      selectors: [
        'select[name="category"]',
        '#categoryid'
      ],
      method: 'select',
      validation: 'required',
      options: ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12', '13', '14', '15', '16'],
      defaultValue: '2',
      notes: '主分类选择，默认选择Business'
    },

    // 子分类 -> Subcategory
    subCategory: {
      selectors: [
        'select[name="sub_category"]',
        '.sub_categoryid'
      ],
      method: 'select',
      validation: 'optional',
      notes: '子分类选择，可选'
    },

    // 地区 -> Region
    region: {
      selectors: [
        'select[name="region"]'
      ],
      method: 'select',
      validation: 'optional',
      notes: '地区选择，可选'
    },

    // Facebook链接
    facebookUrl: {
      selectors: [
        'input[name="facebook"]'
      ],
      method: 'value',
      validation: 'optional|url',
      notes: 'Facebook页面链接'
    },

    // Twitter链接
    twitterUrl: {
      selectors: [
        'input[name="twitter"]'
      ],
      method: 'value',
      validation: 'optional|url',
      notes: 'Twitter页面链接'
    },

    // LinkedIn链接
    linkedinUrl: {
      selectors: [
        'input[name="Linkedin"]'
      ],
      method: 'value',
      validation: 'optional|url',
      notes: 'LinkedIn页面链接'
    },

    // 同意条款
    agreeTerms: {
      selectors: [
        'input[name="terms_and_condition"]'
      ],
      method: 'checkbox',
      validation: 'required',
      defaultValue: true,
      notes: '同意使用条款和提交指南'
    },

    // 订阅更新
    subscribeUpdates: {
      selectors: [
        'input[name="subscription_for_updates"]'
      ],
      method: 'checkbox',
      validation: 'optional',
      defaultValue: true,
      notes: '订阅更新、免费资源和电子书'
    }
  },

  // 提交流程配置
  submitConfig: {
    submitButton: 'input[name="submit"], input[value="Submit Website"]',
    submitMethod: 'manual',
    waitAfterFill: 0,
    waitAfterSubmit: 0,
    successIndicators: ['.success-message'],
    errorIndicators: ['.error-message']
  },

  // 特殊处理
  specialHandling: {
    requiresLogin: false,
    hasCaptcha: false,
    hasFileUpload: true,
    customScript: 'handle1WebsDirectorySubmission',
    formValidation: {
      requiredFields: ['siteUrl', 'siteName', 'fullName', 'contactEmail', 'category', 'agreeTerms'],
      optionalFields: ['siteDescription', 'subCategory', 'region', 'facebookUrl', 'twitterUrl', 'linkedinUrl', 'subscribeUpdates'],
      emailValidation: true,
      urlValidation: true
    },
    notes: [
      '1 Webs Directory 网站目录',
      '免费网站提交服务',
      '三步提交流程：基本信息 → 商业详情 → 社交媒体',
      '支持多级分类选择（主分类 → 子分类 → 子子分类）',
      '支持地区选择',
      '支持社交媒体链接（Facebook、Twitter、LinkedIn等）',
      '支持文件上传（Logo和图片）',
      '描述限制350字符',
      '联系人姓名限制60字符',
      '必须同意使用条款',
      '可选订阅更新服务',
      '现代化Bootstrap界面',
      '使用Django框架',
      '手动提交模式'
    ]
  }
};

// 自定义处理函数
export function handle1WebsDirectorySubmission(data, rule) {
  console.log('Processing 1 Webs Directory form submission...');
  
  const processedData = { ...data };
  
  // 确保URL格式正确
  if (processedData.siteUrl && !processedData.siteUrl.startsWith('http')) {
    processedData.siteUrl = 'https://' + processedData.siteUrl;
  }
  
  // 设置默认分类为Business
  if (!processedData.category) {
    processedData.category = '2';
  }
  
  // 确保同意条款被勾选
  processedData.agreeTerms = true;
  
  // 处理描述长度限制
  if (processedData.siteDescription && processedData.siteDescription.length > 350) {
    processedData.siteDescription = processedData.siteDescription.substring(0, 347) + '...';
  }
  
  // 处理姓名长度限制
  if (processedData.fullName && processedData.fullName.length > 60) {
    processedData.fullName = processedData.fullName.substring(0, 60);
  }
  
  return processedData;
}
