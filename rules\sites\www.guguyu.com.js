// Guguyu.com 网站规则配置
// 网站: https://www.guguyu.com/info/6
// 最后更新: 2025-07-24

export const SITE_RULE = {
  // 基本信息
  domain: 'www.guguyu.com',
  siteName: 'Guguyu',
  priority: 1,
  lastUpdated: '2025-07-24',

  // 字段映射规则
  fieldMappings: {
    // 网站名称 -> siteName
    siteName: {
      selectors: [
        'input[name="webname"]',
        '#webname',
        'input[placeholder*="网站名称"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '网站名称，需要小于16个字节'
    },

    // 网站URL -> siteUrl
    siteUrl: {
      selectors: [
        'input[name="weburl"]',
        '#weburl',
        'input[placeholder*="https://"]'
      ],
      method: 'value',
      validation: 'required|url',
      notes: '网站URL地址'
    },

    // 网站说明 -> siteDescription
    siteDescription: {
      selectors: [
        'input[name="brief"]',
        '#brief',
        'input[placeholder*="网站简短说明"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '网站简短说明，20到50个字节'
    },

    // 联系方式 -> contactEmail
    contactEmail: {
      selectors: [
        'input[name="contact"]',
        '#contact',
        'input[placeholder*="联系方式"]'
      ],
      method: 'value',
      validation: 'optional',
      notes: '联系方式，选填'
    },

    // 附言描述 -> detailedIntro
    detailedIntro: {
      selectors: [
        'textarea[name="brief2"]',
        '#brief2',
        'textarea[placeholder*="描述网站特点优势"]'
      ],
      method: 'value',
      validation: 'optional',
      notes: '网站特点优势描述，100字符以内'
    }
  },

  // 提交流程配置
  submitConfig: {
    submitButton: '#suggest-btn, button[type="submit"]',
    submitMethod: 'click',
    waitAfterFill: 2000,
    waitAfterSubmit: 5000,
    successIndicators: ['.success-message'],
    errorIndicators: ['.error-message']
  },

  // 特殊处理
  specialHandling: {
    requiresLogin: false,
    hasCaptcha: false,
    hasFileUpload: false,
    customScript: 'handleGuguyuSubmission',
    formValidation: {
      requiredFields: ['siteName', 'siteUrl', 'siteDescription'],
      emailValidation: false,
      urlValidation: true
    },
    notes: [
      '古古鱼网站收录平台',
      '简洁的表单设计',
      '字节长度限制',
      '中文界面',
      '无分类选择'
    ]
  }
};

// 自定义处理函数
export function handleGuguyuSubmission(data, rule) {
  console.log('Processing Guguyu form submission...');

  const processedData = { ...data };

  // 确保URL格式正确
  if (processedData.siteUrl && !processedData.siteUrl.startsWith('http')) {
    processedData.siteUrl = 'https://' + processedData.siteUrl;
  }

  // 处理网站名称长度限制（16字节，约8个汉字）
  if (processedData.siteName && processedData.siteName.length > 8) {
    processedData.siteName = processedData.siteName.substring(0, 8);
  }

  // 处理描述长度限制（20-50字节，约10-25个汉字）
  if (processedData.siteDescription) {
    if (processedData.siteDescription.length > 25) {
      processedData.siteDescription = processedData.siteDescription.substring(0, 22) + '...';
    }
  }

  // 处理附言长度限制（100字符）
  if (processedData.detailedIntro && processedData.detailedIntro.length > 100) {
    processedData.detailedIntro = processedData.detailedIntro.substring(0, 97) + '...';
  }

  return processedData;
}

// 自定义元素填写函数
export async function customFillElement(element, value, config) {
  // 处理标准输入框和文本域
  if (element.tagName === 'INPUT' || element.tagName === 'TEXTAREA') {
    element.value = value;
    element.dispatchEvent(new Event('input', { bubbles: true }));
    element.dispatchEvent(new Event('change', { bubbles: true }));
    return true;
  }

  return false;
}