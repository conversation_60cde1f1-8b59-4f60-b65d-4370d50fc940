// Coglist.com 网站规则配置
// 网站: https://coglist.com/admin/submitSite
// 最后更新: 2025-07-09

export const SITE_RULE = {
  // 基本信息
  domain: 'coglist.com',
  siteName: 'Coglist',
  priority: 1,
  lastUpdated: '2025-07-09',
  
  // 字段映射规则
  fieldMappings: {
    // 产品名称 -> Product Name
    siteName: {
      selectors: [
        'input[name="name"]',
        '#form-title',
        'input[placeholder*="name of the product"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '产品名称'
    },
    
    // 品牌 -> Brand
    companyName: {
      selectors: [
        'input[name="brand"]',
        '#form-brand',
        'input[placeholder*="brand of the site"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '产品品牌'
    },
    
    // 阶段分类 -> Stage Category (选择development stage)
    stageCategory: {
      selectors: [
        'select[name="stageCategory[]"]',
        '#form-stageCategory-1',
        'select.form__input:first-of-type'
      ],
      method: 'select',
      validation: 'required',
      targetValue: 'development stage',
      defaultValue: 'development stage',
      notes: '产品阶段分类，选择development stage'
    },

    // 产品分类 -> Category (选择productivity)
    category: {
      selectors: [
        'select[name="category[]"]',
        '#form-category-1',
        'select.form__input[onchange*="loadSubcategories"]'
      ],
      method: 'select',
      validation: 'required',
      targetValue: 'productivity',
      defaultValue: 'productivity',
      notes: '产品分类，选择productivity'
    },

    // 独特功能 -> Unique Features
    features: {
      selectors: [
        'input[name="features[]"]',
        '#form-features',
        'input[placeholder*="Key Feature"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '产品独特功能'
    },

    // 优点 -> Pros
    pros: {
      selectors: [
        'input[name="pros[]"]',
        '#form-pros',
        'input[placeholder*="Enter Pro"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '产品优点'
    },

    // 缺点 -> Cons
    cons: {
      selectors: [
        'input[name="cons[]"]',
        '#form-cons',
        'input[placeholder*="Enter Cons"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '产品缺点'
    },

    // 官方网站 -> Official Website
    siteUrl: {
      selectors: [
        'input[name="websiteUrl"]',
        '#form-websiteUrl',
        'input[placeholder*="Official Website URL"]'
      ],
      method: 'value',
      validation: 'required|url',
      notes: '官方网站URL'
    },

    // 标语 -> Tagline
    tagline: {
      selectors: [
        'textarea[name="intro"]',
        '#form-intro',
        'textarea[placeholder*="Product Brief Introduction"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '产品标语（少于100字符）'
    },

    // 详细内容 -> Content
    detailedIntro: {
      selectors: [
        'textarea[name="brief"]',
        '#editor',
        'iframe#editor_ifr'
      ],
      method: 'tinymce',
      validation: 'required',
      notes: '产品详细内容（TinyMCE编辑器）'
    },

    // SEO标题 -> Title
    seoTitle: {
      selectors: [
        'input[name="seoTitle"]',
        '#seoTitle',
        'input[maxlength="60"]'
      ],
      method: 'value',
      validation: 'optional',
      notes: 'SEO标题（最多60字符）'
    },

    // SEO关键词 -> Keywords
    seoKeywords: {
      selectors: [
        'input[name="seoKeyword"]',
        '#seoKeyword',
        'input[placeholder*="video editor,video generator"]'
      ],
      method: 'value',
      validation: 'optional',
      notes: 'SEO关键词'
    },

    // SEO描述 -> Description
    seoDescription: {
      selectors: [
        'textarea[name="seoDescription"]',
        '#seoDescription',
        'textarea[maxlength="160"]'
      ],
      method: 'value',
      validation: 'required',
      notes: 'SEO描述（最多160字符）'
    }
  },
  
  // 提交流程配置
  submitConfig: {
    submitButton: 'button[type="submit"], .submit-button, input[type="submit"]',
    submitMethod: 'click',
    waitAfterFill: 2000, // 填写后等待2秒
    waitAfterSubmit: 3000, // 提交后等待3秒
    successIndicators: [
      '.success-message',
      '.alert-success',
      '.notification-success',
      '[class*="success"]'
    ],
    errorIndicators: [
      '.error-message',
      '.alert-error',
      '.alert-danger',
      '[class*="error"]'
    ]
  },
  
  // 特殊处理
  specialHandling: {
    requiresLogin: true, // 可能需要登录到admin
    hasCaptcha: false,
    hasFileUpload: false,
    
    // 表单验证规则
    formValidation: {
      requiredFields: ['siteName', 'companyName', 'stageCategory', 'category', 'features', 'pros', 'cons', 'siteUrl', 'tagline', 'detailedIntro', 'seoDescription'],
      emailValidation: false,
      urlValidation: true
    },
    
    // 特殊注意事项
    notes: [
      '需要登录到admin才能访问',
      '表单包含10个字段：产品名称、品牌、阶段分类、产品分类、功能、优点、缺点、网站、标语、详细内容',
      '所有字段都是必填的',
      '阶段分类和产品分类都有子分类功能',
      '支持添加多个分类、功能、优点、缺点',
      '使用TinyMCE富文本编辑器',
      '使用自定义CSS类和样式'
    ]
  }
};

// 自定义处理函数
export function handleCoglistSubmission(data, rule) {
  console.log('Processing Coglist.com submission...');
  
  // 特殊处理逻辑
  const processedData = { ...data };
  
  // 设置默认阶段分类为development stage
  processedData.stageCategory = 'development stage';

  // 设置默认产品分类为productivity
  processedData.category = 'productivity';

  // 如果没有品牌信息，使用网站名称作为品牌
  if (!processedData.companyName && processedData.siteName) {
    processedData.companyName = processedData.siteName;
  }

  // 设置默认功能、优点、缺点
  if (!processedData.features) {
    processedData.features = 'Easy to use interface';
  }

  if (!processedData.pros) {
    processedData.pros = 'User-friendly design';
  }

  if (!processedData.cons) {
    processedData.cons = 'Limited free features';
  }

  // 设置默认SEO信息
  if (!processedData.seoTitle && processedData.siteName) {
    processedData.seoTitle = processedData.siteName + ' - AI Tool';
  }

  if (!processedData.seoKeywords) {
    processedData.seoKeywords = 'ai tool, productivity, automation';
  }

  if (!processedData.seoDescription && processedData.tagline) {
    processedData.seoDescription = processedData.tagline;
  }
  
  return processedData;
}

// 自定义元素填写函数，专门处理表单组件
export async function customFillElement(element, value, config) {
  console.log('🔧 Coglist自定义填写函数被调用:', element, value);
  
  // 处理带有form__input类的输入框
  if (element.classList.contains('form__input')) {
    try {
      element.value = value;
      element.dispatchEvent(new Event('input', { bubbles: true }));
      element.dispatchEvent(new Event('change', { bubbles: true }));
      element.dispatchEvent(new Event('blur', { bubbles: true }));
      console.log('✅ 使用form__input类填写:', value);
      return true;
    } catch (error) {
      console.warn('form__input类填写失败:', error);
    }
  }
  
  // 处理阶段分类下拉选择
  if (element.tagName === 'SELECT' && element.name === 'stageCategory[]') {
    try {
      element.value = config.targetValue;
      element.dispatchEvent(new Event('change', { bubbles: true }));
      
      // 触发onchange事件以加载子分类
      if (element.onchange) {
        element.onchange();
      }
      
      console.log('✅ 选择阶段分类:', config.defaultValue);
      return true;
    } catch (error) {
      console.warn('阶段分类选择失败:', error);
    }
  }
  
  // 处理子分类下拉选择（如果出现）
  if (element.tagName === 'SELECT' && element.name === 'stageSubcategory[]') {
    try {
      // 等待子分类加载
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // 选择第一个可用选项
      const options = element.querySelectorAll('option[value!=""]');
      if (options.length > 0) {
        element.value = options[0].value;
        element.dispatchEvent(new Event('change', { bubbles: true }));
        console.log('✅ 选择子分类:', options[0].textContent);
        return true;
      }
    } catch (error) {
      console.warn('子分类选择失败:', error);
    }
  }
  
  // 处理TinyMCE编辑器
  if (element.id === 'editor' || element.tagName === 'IFRAME' && element.id.includes('editor')) {
    try {
      // 等待TinyMCE加载
      await new Promise(resolve => setTimeout(resolve, 1000));

      // 使用TinyMCE API设置内容
      if (window.tinymce && tinymce.get('editor')) {
        tinymce.get('editor').setContent(value);
        console.log('✅ 使用TinyMCE API设置内容:', value);
        return true;
      }

      // 备用方法：直接设置textarea值
      const textarea = document.querySelector('textarea[name="brief"]');
      if (textarea) {
        textarea.value = value;
        textarea.dispatchEvent(new Event('input', { bubbles: true }));
        console.log('✅ 使用textarea设置TinyMCE内容:', value);
        return true;
      }
    } catch (error) {
      console.warn('TinyMCE编辑器处理失败:', error);
    }
  }

  // 处理字符计数字段
  if (element.classList.contains('char-count') || element.id.includes('Count')) {
    // 跳过字符计数元素
    return false;
  }

  // 默认处理
  return false;
}
