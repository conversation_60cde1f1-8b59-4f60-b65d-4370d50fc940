// Submit.biz 网站规则配置
// 网站: https://www.submit.biz/index.php?go=addpage&catid=23
// 最后更新: 2025-07-24

export const SITE_RULE = {
  // 基本信息
  domain: 'www.submit.biz',
  siteName: 'Submit.biz',
  priority: 1,
  lastUpdated: '2025-07-24',

  // 字段映射规则
  fieldMappings: {
    // 网站URL -> Url
    siteUrl: {
      selectors: [
        'input[name="url"]',
        'input[type="text"][name="url"]'
      ],
      method: 'value',
      validation: 'required|url',
      notes: '网站URL地址，最多255字符'
    },

    // 邮箱 -> Email
    contactEmail: {
      selectors: [
        '#email',
        'input[name="email"]'
      ],
      method: 'value',
      validation: 'required|email',
      notes: '联系邮箱'
    },

    // 网站标题 -> Title
    siteName: {
      selectors: [
        'input[name="title"]',
        'input[type="text"][name="title"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '网站标题，最多100字符'
    },

    // 网站描述 -> Description
    siteDescription: {
      selectors: [
        'textarea[name="description"]',
        'textarea[cols="60"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '网站描述，最多320字符'
    },

    // 链接类型 -> Link Type
    linkType: {
      selectors: [
        'input[name="linkType"]',
        'input[value="free"]',
        'input[value="paid"]',
        'input[value="sponsor"]'
      ],
      method: 'radio',
      validation: 'required',
      notes: '链接类型选择：免费、付费、特色'
    },

    // 优惠券 -> Coupon
    coupon: {
      selectors: [
        'input[name="coupon"]',
        'input[size="16"]'
      ],
      method: 'value',
      validation: 'optional',
      notes: '优惠券代码，最多16字符'
    },

    // 关键词 -> Keywords
    keywords: {
      selectors: [
        'input[name="keywords"]',
        'input[size="50"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '关键词，最多100字符'
    },

    // 验证码 -> Security Code
    captcha: {
      selectors: [
        '#code',
        'input[name="code"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '验证码，最多10字符'
    }
  },
  // 提交流程配置
  submitConfig: {
    submitButton: 'input[type="submit"], input[value="Submit"]',
    submitMethod: 'manual', // 手动提交
    waitAfterFill: 0,
    waitAfterSubmit: 0,
    successIndicators: ['.success-message'],
    errorIndicators: ['.error-message']
  },

  // 特殊处理
  specialHandling: {
    requiresLogin: false,
    hasCaptcha: true,
    hasFileUpload: false,
    customScript: 'handleSubmitBizSubmission',
    formValidation: {
      requiredFields: ['siteUrl', 'contactEmail', 'siteName', 'siteDescription', 'linkType', 'keywords', 'captcha'],
      emailValidation: true,
      urlValidation: true
    },
    notes: [
      'Submit.biz 网站目录提交平台',
      '传统的网站目录服务',
      '提供三种提交选项：',
      '- 免费列表：标准审核',
      '- 付费列表：$10.90，24-48小时审核，永不过期',
      '- 特色列表：$19.90，24-48小时审核，分类页面顶部位置',
      '包含验证码验证',
      '描述限制320字符，有实时计数器',
      '支持优惠券代码',
      '传统表格样式界面',
      '手动提交模式'
    ]
  }
};

// 自定义处理函数
export function handleSubmitBizSubmission(data) {
  console.log('Processing Submit.biz form submission...');

  const processedData = { ...data };

  // 确保URL格式正确
  if (processedData.siteUrl && !processedData.siteUrl.startsWith('http')) {
    processedData.siteUrl = 'https://' + processedData.siteUrl;
  }

  return processedData;
}

// 自定义元素填写函数
export async function customFillElement(element, value, config) {
  console.log(`Submit.biz自定义填写: ${element.tagName}, 方法: ${config.method}`);

  switch (config.method) {
    case 'value':
      // 标准输入框和文本域处理
      element.focus();
      await new Promise(resolve => setTimeout(resolve, 200));

      // 清除默认值（如果是URL字段的http://）
      if (element.name === 'url' && element.value === 'http://') {
        element.value = '';
      }

      element.value = value;
      element.dispatchEvent(new Event('input', { bubbles: true }));
      element.dispatchEvent(new Event('change', { bubbles: true }));
      element.dispatchEvent(new Event('blur', { bubbles: true }));

      // 触发字符计数器（如果是描述字段）
      if (element.name === 'description') {
        const event = new Event('keyup', { bubbles: true });
        element.dispatchEvent(event);
      }

      console.log(`✓ 填写字段: ${element.name} = "${value}"`);
      return true;

    case 'radio':
      // 单选按钮处理 - 默认选择免费选项
      const freeRadio = document.querySelector('input[name="linkType"][value="free"]');
      if (freeRadio) {
        freeRadio.checked = true;
        freeRadio.dispatchEvent(new Event('change', { bubbles: true }));
        console.log(`✓ 选择链接类型: 免费列表`);
        return true;
      }
      break;

    default:
      element.value = value;
      element.dispatchEvent(new Event('input', { bubbles: true }));
      return true;
  }

  return false;
}