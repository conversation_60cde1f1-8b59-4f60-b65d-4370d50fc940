<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Site Submitter - 批量网址管理</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            font-weight: 700;
        }

        .header p {
            font-size: 1.1em;
            opacity: 0.9;
        }

        .stats {
            display: flex;
            justify-content: center;
            gap: 30px;
            margin-top: 20px;
        }

        .stat-item {
            text-align: center;
        }

        .stat-number {
            font-size: 2em;
            font-weight: bold;
            display: block;
        }

        .stat-label {
            font-size: 0.9em;
            opacity: 0.8;
        }

        .controls {
            padding: 20px 30px;
            background: #f8f9fa;
            border-bottom: 1px solid #e9ecef;
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 15px;
        }

        .batch-controls {
            display: flex;
            gap: 10px;
            align-items: center;
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }

        .btn-success {
            background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
            color: white;
        }

        .btn-success:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(86, 171, 47, 0.4);
        }

        .btn-warning {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
        }

        .btn-warning:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(240, 147, 251, 0.4);
        }

        .search-box {
            padding: 10px 15px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 14px;
            width: 250px;
            transition: border-color 0.3s ease;
        }

        .search-box:focus {
            outline: none;
            border-color: #667eea;
        }

        .content {
            padding: 30px;
        }

        .group {
            margin-bottom: 30px;
            border: 2px solid #e9ecef;
            border-radius: 12px;
            overflow: hidden;
            transition: all 0.3s ease;
        }

        .group:hover {
            border-color: #667eea;
            box-shadow: 0 5px 20px rgba(102, 126, 234, 0.1);
        }

        .group-header {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            padding: 15px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 1px solid #e9ecef;
        }

        .group-title {
            font-size: 1.2em;
            font-weight: 600;
            color: #495057;
        }

        .group-actions {
            display: flex;
            gap: 10px;
        }

        .btn-sm {
            padding: 6px 12px;
            font-size: 0.85em;
        }

        .group-content {
            padding: 20px;
        }

        .url-item {
            display: flex;
            align-items: center;
            padding: 12px 0;
            border-bottom: 1px solid #f1f3f4;
            transition: background-color 0.2s ease;
        }

        .url-item:last-child {
            border-bottom: none;
        }

        .url-item:hover {
            background-color: #f8f9fa;
        }

        .url-checkbox {
            margin-right: 15px;
            transform: scale(1.2);
        }

        .url-link {
            flex: 1;
            color: #0066cc;
            text-decoration: none;
            font-size: 0.95em;
            padding: 8px 12px;
            border-radius: 6px;
            transition: all 0.2s ease;
        }

        .url-link:hover {
            background-color: #e3f2fd;
            color: #0052a3;
        }

        .url-actions {
            display: flex;
            gap: 8px;
        }

        .btn-xs {
            padding: 4px 8px;
            font-size: 0.75em;
            border-radius: 4px;
        }

        .hidden {
            display: none;
        }

        .progress-bar {
            width: 100%;
            height: 4px;
            background-color: #e9ecef;
            border-radius: 2px;
            overflow: hidden;
            margin-top: 10px;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #667eea, #764ba2);
            width: 0%;
            transition: width 0.3s ease;
        }

        @media (max-width: 768px) {
            .controls {
                flex-direction: column;
                align-items: stretch;
            }

            .batch-controls {
                justify-content: center;
            }

            .search-box {
                width: 100%;
            }

            .stats {
                flex-direction: column;
                gap: 15px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 AI Site Submitter</h1>
            <p>批量网址管理工具 - 高效提交AI工具到各大平台</p>
            <div class="stats">
                <div class="stat-item">
                    <span class="stat-number" id="totalUrls">0</span>
                    <span class="stat-label">总网址数</span>
                </div>
                <div class="stat-item">
                    <span class="stat-number" id="totalGroups">0</span>
                    <span class="stat-label">分组数</span>
                </div>
                <div class="stat-item">
                    <span class="stat-number" id="selectedCount">0</span>
                    <span class="stat-label">已选择</span>
                </div>
            </div>
        </div>

        <div class="controls">
            <div class="batch-controls">
                <button class="btn btn-primary" onclick="openAllGroups()">
                    🌐 打开所有分组
                </button>
                <button class="btn btn-success" onclick="openSelectedUrls()">
                    ✅ 打开选中网址
                </button>
                <button class="btn btn-warning" onclick="selectAll()">
                    📋 全选/取消
                </button>
            </div>
            <input type="text" class="search-box" id="searchBox" placeholder="🔍 搜索网址..." onkeyup="filterUrls()">
        </div>

        <div class="content" id="groupsContainer">
            <!-- 分组内容将通过JavaScript动态生成 -->
        </div>
    </div>

    <script>
        // URL数据
        const urls = [
            "https://ai-hunter.io/submit-a-tool/",
            "https://www.insidr.ai/submit-tools/",
            "https://www.futuretools.io/submit-a-tool",
            "https://featuredaitool.com/submit/",
            "https://www.dropyourai.com/submit-tool",
            "https://aivalley.ai/submit-tool/",
            "https://aireviewbattle.com/submit-your-ai-tool/",
            "https://seminal.ai/submit-tool/",
            "https://www.superaitools.io/submit-a-tool",
            "https://www.futureagitools.com/submit-a-site",
            "https://www.ainewshub.org/submit-ai-tool",
            "https://tally.so/r/nPpyBV",
            "https://bai.tools/submit-ai-tools",
            "https://aitoptools.com/account/submit-tool/",
            "https://airespo.com/submit-tool/",
            "https://simplifyaitools.com/submit-tool/",
            "https://itirupati.com/submit-your-app-tool/",
            "https://www.aitoolzdir.com/submit",
            "https://fastpedia.io/submit-tool/",
            "https://the-ai-mentor.com/submit-ai-tools/",
            "https://www.tools-ai.online/submit-tool",
            "https://aitools.neilpatel.com/submit/",
            "https://www.allaitools.tech/submit-tool",
            "https://aisitelist.com/submit-ai-tool/",
            "https://www.startupaitools.com/submit-ai-tools/",
            "https://www.freeaitools.fyi/submit-tool",
            "https://aitoolforbusiness.com/submit-ai-tool/",
            "https://ai-q.in/submit-tool/",
            "https://allaitool.ai/submit-ai-tool",
            "https://aicyclopedia.com/submit-your-ai-tool/",
            "https://www.best-ai-tools.org/submit-tool",
            "https://toolscout.ai/submit",
            "https://www.theainavigator.com/submit-an-ai-tool",
            "https://findaiforthat.com/submit-ai-tool/",
            "https://appliedai.tools/submit-ai-tool/",
            "https://www.pickai.tools/submit",
            "https://www.tooljunction.io/submit-tool",
            "https://www.guideofaitool.com/submit-ai-tool",
            "https://ainewsbase.com/submit-your-ai-tool/",
            "https://www.comparebiztech.com/submit-ai-tool/",
            "https://aitoolbee.com/submit/",
            "https://seekme.ai/submittool",
            "https://www.listedai.co/submit",
            "https://www.promoteproject.com/submit-startup",
            "https://www.activesearchresults.com/addwebsite.php",
            "https://osalt.com/suggest",
            "https://twelve.tools/submit-your-tool",
            "https://sourceforge.net/software/vendors/new",
            "https://airtable.com/appcN6nvv5n1GpABK/pagzZyGl6fEI2RWDq/form",
            "https://library.phygital.plus/tool-submission",
            "https://aiworthy.org/submit-tool/",
            "https://ailib.ru/en/add-ai/free/",
            "https://designtools.ai/submit/",
            "https://landing.mycloudmedia.co.uk/apps-and-websites-submit-ai-or-saas-tool/new-submission.html",
            "https://www.humanornot.co/submit-tool",
            "https://free-ai-tools-directory.com/submit-request/",
            "https://inside.thewarehouse.ai/submissions",
            "https://www.australiawebdirectory.net/submit.php",
            "https://www.allstatesusadirectory.com/submit.php",
            "https://www.freeprwebdirectory.com/submit.php",
            "https://www.freeinternetwebdirectory.com/submit.php",
            "https://info-listings.com/submit.php",
            "https://www.qualityinternetdirectory.com/submit.php",
            "https://www.ukinternetdirectory.net/submit.php",
            "https://www.usawebsitesdirectory.com/submit.php",
            "https://www.worldweb-directory.com/add.php",
            "https://www.offpagesavvy.com/submit-your-site/",
            "https://www.ontoplist.com/join/",
            "https://once.tools/submit",
            "https://www.webwiki.com/info/add-website.html",
            "https://unmatchedstyle.com/submit",
            "https://aiagentslive.com/agents/products/new",
            "https://www.ainave.com/submit/tools",
            "https://www.aisupersmart.com/submit-tool/",
            "https://aitoolscorner.com/dashboard/add-product",
            "https://www.aipulse.fyi/submit",
            "https://aiagentsbase.com/submit",
            "https://aiex.me/submit/form?plan=free",
            "https://alltheaitools.com/submit-tool",
            "https://aitooltrek.com/submit",
            "https://aitoolsmagazine.com/submit-listing/details/",
            "https://aitoolsmarketer.com/submit/",
            "https://www.expify.ai/dashboard/manage-products",
            "https://www.dynamite-ai.com/submit",
            "https://coglist.com/admin/submitSite",
            "https://aiagentsdirectory.com/submit-agent",
            "https://aiagentslist.com/submit",
            "https://heyaiworld.com/submit",
            "https://findyouragent.ai/agents/new",
            "https://www.listyourtool.com/submit-tool",
            "https://toolwave.io/submit-ai",
            "https://trustedby.ai/submit",
            "https://sidebar.io/submit",
            "https://www.advanced-innovation.io/ki-tool-einreichen",
            "https://makerthrive.com/profeed",
            "https://ismailblogger.com/submit-tools/",
            "https://whattheai.tech/submit",
            "https://www.smart-tools.ai/en/submit",
            "https://www.gptdemo.net/gpt/add-tool",
            "https://aitoolsguide.com/contact/",
            "https://lookaitools.com/submit-listing/details/",
            "https://aitogrow.com/#send-your-tool",
            "https://anyfp.com/contact/",
            "https://infrabase.ai/submit",
            "https://www.indieai.co/add-tool",
            "https://aixcollection.com/submit",
            "https://orbic.ai/submit/tools",
            "https://caida.eu/submit.php",
            "https://www.prolinkdirectory.com/submit.php",
            "https://morefunz.com/submit-url",
            "https://www.postfreedirectory.com/submit",
            "https://autoais.com/?U=Add",
            "https://www.saashub.com/services",
            "https://www.snkcreation.com/snk-free-site-submission",
            "https://submitx.com/?page=submit-url.html",
            "https://www.freewebsubmission.com/",
            "https://bestfreeaiwebsites.com/submit-tool/",
            "https://www.developerupdates.com/directory/submit",
            "https://www.w3.org/WAI/test-evaluate/tools/submit-a-tool/",
            "https://aitoolboard.com/list",
            "https://kk.org/cooltools/submit-a-tool/",
            "https://tools4mirs.org/software/submit/",
            "https://ai.ctlt.ubc.ca/submit-an-event-resource-or-tool/",
            "https://lazarshishmanov.com/en/best-free-tools/submit-tool/",
            "https://gptbot.io/submit-ai-tool",
            "https://www.findseotools.com/submit",
            "https://10015.io/product-finder/submit",
            "https://awesomeindie.com/submit",
            "https://www.brouseai.com/submission/ai",
            "https://www.business-software.com/add-your-product/",
            "https://www.eu-startups.com/directory/?wpbdp_view=submit_listing",
            "https://www.freeonline.org/segnala-un-sito/",
            "https://www.geekwire.com/submit-startup/",
            "https://launched.io/SubmitStartup",
            "https://indiehackerstacks.com/me/products/",
            "https://www.launchingnext.com/submit/",
            "https://payonceapps.com/add-your-app/",
            "https://saaspo.com/submit",
            "https://www.submissionwebdirectory.com/submit.php",
            "https://www.thestartupinc.com/submit-startup/",
            "https://www.britainbusinessdirectory.com/submit.php",
            "https://www.marketinginternetdirectory.com/submit.php",
            "http://www.localsites.ca/article/free-listing-submit-361.asp",
            "https://www.siteswebdirectory.com/submit.php",
            "https://www.sitepromotiondirectory.com/submit.php",
            "https://www.sonicrun.com/freelisting.html",
            "https://www.highrankdirectory.com/submit.php",
            "https://www.jayde.com/submit.html",
            "https://www.gmawebdirectory.com/submit.php",
            "https://www.promotebusinessdirectory.com/submit.php?c=698&LINK_TYPE=2",
            "https://www.9sites.net/addurl.php",
            "https://www.cipinet.com/suggest.php?action=addlink&TID=sf",
            "https://openfuture.ai/zh/submit-tool",
            "https://www.toolhunter.ai/submit-a-tool",
            "https://www.ainavpro.com/contribute",
            "https://www.ainav.cn/%e6%8f%90%e4%ba%a4%e7%bd%91%e7%ab%99",
            "https://wenjuan.feishu.cn/m/cfm?t=s3T13jinwMYi-2h33",
            "https://2agi.net/zh/submit",
            "https://iforai.com/submit_website/",
            "https://zhexieai.com/%e6%8f%90%e4%ba%a4%e7%bd%91%e5%9d%80",
            "https://nancheng.fun/contribute",
            "https://ceifi.com/panel/create/",
            "https://www.yjpoo.com/submit-ai-tool/",
            "https://www.hhlink.com/%E6%8F%90%E4%BA%A4%E6%96%B0%E7%BD%91%E7%AB%99",
            "https://www.guguyu.com/info/6",
            "https://www.ailookme.com/%e7%bd%91%e5%9d%80%e6%8f%90%e4%ba%a4",
            "https://lbbai.com/contribute",
            "https://saasaitools.com/submit/",
            "https://docs.google.com/",
            "https://www.saasworthy.com/offerings",
            "https://tally.so/r/wvB7Xg",
            "https://news.bensbites.com/submit",
            "https://whatisaitools.com/submit",
            "https://portal.10words.io/submissions/submit",
            "https://tools.robingood.com/",
            "https://aiai.tools/submit-ai-tool",
            "https://toolsapp.cc/submit",
            "https://www.aibesttop.com/submit",
            "https://aitoolly.com/zh/submit",
            "https://right-ai.com/submit",
            "https://ai-toolhubs.com/submit",
            "https://www.seewhatnewai.com/zh/submit",
            "https://www.canopydirectory.com/",
            "https://ai-findr.com/zh/submit",
            "https://detectortools.ai/submit-tool/",
            "https://trustiner.com/submit",
            "https://tools.so/submit",
            "https://tools-ai.xyz/submit",
            "https://www.aitoolslist.top/submit",
            "https://ainavhub.com/submit",
            "https://projecthunt.me",
            "https://www.aitoolgo.com/submit",
            "https://viesearch.com/submit",
            "https://www.aitoolxplorer.com/tool-vorschlagen",
            "https://gainweb.org/submit.php?LINK_TYPE=2",
            "https://www.dizila.com/submit?c=4&LINK_TYPE=1",
            "https://www.skoobe.biz/index.php?go=addpage&catid=23",
            "https://www.submit.biz/index.php?go=addpage&catid=23",
            "https://www.textlinkdirectory.com/index.php?go=addpage&catid=43",
            "https://www.canadawebdir.com/submit.php?c=279",
            "https://www.gtawebdirectory.com/submit.php?id=392",
            "https://www.fire-directory.com/submit.php",
            "https://www.marketingwebdirectory.com/submit?c=433&LINK_TYPE=1",
            "https://www.fashionlistings.org/listing_website.asp?free=true",
            "https://phalternatives.com/",
            "https://www.aitoolhouse.com/submit-gpt",
            "https://directory.aibusinesstool.com/submit-your-ai-tool-directory",
            "https://huntscreens.com/en/submit",
            "https://aiwikiweb.com/submit-your-ai-tool/",
            "http://m.bokequ.com/e/DoInfo/AddInfo.php?mid=10&enews=MAddInfo&classid=139&Submit=%E7%A1%AE%E5%AE%9A%E9%80%89%E6%8B%A9",
            "https://www.navs.cc/1383.html",
            "https://www.265.com/submit/",
            "https://www.ai138.com/submit",
            "https://www.aig123.com/site-submit",
            "https://jvyan.cn/accept",
            "https://www.zhijian100.cn/submit-website",
            "https://www.haobgl.com/article-toadd.html",
            "https://ai-nav.net/submit",
            "https://betalist.com/submissions/new",
            "https://aitoolguru.com/submit-ai-tool",
            "https://loxr142exnq.typeform.com/to/RB6ZnEf2",
            "https://aitools.xyz/submit-tool",
            "https://www.aitooli.com/submit",
            "https://aitools247.com/submit-listing/details/",
            "https://www.247webdirectory.com/submit.aspx",
            "https://www.1websdirectory.com/submission/0/free-listing/"
        ];

        // 全局变量
        let allGroups = [];
        let isAllSelected = false;

        // 初始化页面
        document.addEventListener('DOMContentLoaded', function() {
            initializePage();
        });

        function initializePage() {
            // 过滤空URL
            const validUrls = urls.filter(url => url && url.trim() !== '');

            // 按5个为一组分组
            allGroups = [];
            for (let i = 0; i < validUrls.length; i += 5) {
                allGroups.push(validUrls.slice(i, i + 5));
            }

            // 更新统计信息
            updateStats();

            // 渲染分组
            renderGroups();
        }

        function updateStats() {
            document.getElementById('totalUrls').textContent = urls.filter(url => url && url.trim() !== '').length;
            document.getElementById('totalGroups').textContent = allGroups.length;
            updateSelectedCount();
        }

        function updateSelectedCount() {
            const selectedCheckboxes = document.querySelectorAll('.url-checkbox:checked');
            document.getElementById('selectedCount').textContent = selectedCheckboxes.length;
        }

        function renderGroups() {
            const container = document.getElementById('groupsContainer');
            container.innerHTML = '';

            allGroups.forEach((group, groupIndex) => {
                const groupDiv = document.createElement('div');
                groupDiv.className = 'group';
                groupDiv.innerHTML = `
                    <div class="group-header">
                        <div class="group-title">
                            📁 分组 ${groupIndex + 1} (${group.length} 个网址)
                        </div>
                        <div class="group-actions">
                            <button class="btn btn-primary btn-sm" onclick="openGroup(${groupIndex})">
                                🚀 打开本组
                            </button>
                            <button class="btn btn-success btn-sm" onclick="selectGroup(${groupIndex})">
                                ✅ 选择本组
                            </button>
                        </div>
                    </div>
                    <div class="group-content">
                        ${group.map((url, urlIndex) => `
                            <div class="url-item">
                                <input type="checkbox" class="url-checkbox"
                                       id="url-${groupIndex}-${urlIndex}"
                                       data-url="${url}"
                                       onchange="updateSelectedCount()">
                                <a href="${url}" target="_blank" class="url-link" title="${url}">
                                    ${getUrlDisplayName(url)}
                                </a>
                                <div class="url-actions">
                                    <button class="btn btn-primary btn-xs" onclick="openSingleUrl('${url}')">
                                        🔗 打开
                                    </button>
                                    <button class="btn btn-warning btn-xs" onclick="copyUrl('${url}')">
                                        📋 复制
                                    </button>
                                </div>
                            </div>
                        `).join('')}
                    </div>
                `;
                container.appendChild(groupDiv);
            });
        }

        function getUrlDisplayName(url) {
            try {
                const urlObj = new URL(url);
                const domain = urlObj.hostname.replace('www.', '');
                const path = urlObj.pathname;

                // 提取有意义的路径部分
                if (path && path !== '/') {
                    const pathParts = path.split('/').filter(part => part);
                    const lastPart = pathParts[pathParts.length - 1];
                    if (lastPart && lastPart !== 'submit' && lastPart !== 'add') {
                        return `${domain}${path.length > 30 ? '/...' + lastPart : path}`;
                    }
                }

                return domain;
            } catch (e) {
                return url.length > 50 ? url.substring(0, 50) + '...' : url;
            }
        }

        // 打开所有分组
        function openAllGroups() {
            if (!confirm(`确定要打开所有 ${allGroups.length} 个分组（共 ${urls.filter(url => url && url.trim() !== '').length} 个网址）吗？\n\n这可能会打开很多标签页，建议分批操作。`)) {
                return;
            }

            let delay = 0;
            allGroups.forEach((group, index) => {
                setTimeout(() => {
                    openGroup(index);
                    updateProgress((index + 1) / allGroups.length * 100);
                }, delay);
                delay += 1000; // 每组间隔1秒
            });
        }

        // 打开指定分组
        function openGroup(groupIndex) {
            const group = allGroups[groupIndex];
            if (!group) return;

            group.forEach((url, index) => {
                setTimeout(() => {
                    window.open(url, '_blank');
                }, index * 200); // 每个URL间隔200ms
            });

            // 显示成功消息
            showMessage(`✅ 已打开分组 ${groupIndex + 1} 的 ${group.length} 个网址`, 'success');
        }

        // 选择分组
        function selectGroup(groupIndex) {
            const checkboxes = document.querySelectorAll(`input[id^="url-${groupIndex}-"]`);
            const isGroupSelected = Array.from(checkboxes).every(cb => cb.checked);

            checkboxes.forEach(checkbox => {
                checkbox.checked = !isGroupSelected;
            });

            updateSelectedCount();
            showMessage(`${isGroupSelected ? '取消选择' : '已选择'} 分组 ${groupIndex + 1}`, 'info');
        }

        // 打开选中的网址
        function openSelectedUrls() {
            const selectedCheckboxes = document.querySelectorAll('.url-checkbox:checked');
            if (selectedCheckboxes.length === 0) {
                showMessage('❌ 请先选择要打开的网址', 'error');
                return;
            }

            if (!confirm(`确定要打开选中的 ${selectedCheckboxes.length} 个网址吗？`)) {
                return;
            }

            selectedCheckboxes.forEach((checkbox, index) => {
                setTimeout(() => {
                    window.open(checkbox.dataset.url, '_blank');
                }, index * 200);
            });

            showMessage(`✅ 已打开 ${selectedCheckboxes.length} 个选中的网址`, 'success');
        }

        // 全选/取消全选
        function selectAll() {
            const allCheckboxes = document.querySelectorAll('.url-checkbox');
            isAllSelected = !isAllSelected;

            allCheckboxes.forEach(checkbox => {
                checkbox.checked = isAllSelected;
            });

            updateSelectedCount();
            showMessage(`${isAllSelected ? '✅ 已全选' : '❌ 已取消全选'} ${allCheckboxes.length} 个网址`, 'info');
        }

        // 搜索过滤
        function filterUrls() {
            const searchTerm = document.getElementById('searchBox').value.toLowerCase();
            const groups = document.querySelectorAll('.group');

            groups.forEach(group => {
                const urlItems = group.querySelectorAll('.url-item');
                let hasVisibleItems = false;

                urlItems.forEach(item => {
                    const url = item.querySelector('.url-link').textContent.toLowerCase();
                    const fullUrl = item.querySelector('.url-checkbox').dataset.url.toLowerCase();

                    if (url.includes(searchTerm) || fullUrl.includes(searchTerm)) {
                        item.style.display = 'flex';
                        hasVisibleItems = true;
                    } else {
                        item.style.display = 'none';
                    }
                });

                group.style.display = hasVisibleItems ? 'block' : 'none';
            });
        }

        // 打开单个网址
        function openSingleUrl(url) {
            window.open(url, '_blank');
            showMessage(`🔗 已打开: ${getUrlDisplayName(url)}`, 'success');
        }

        // 复制网址
        function copyUrl(url) {
            navigator.clipboard.writeText(url).then(() => {
                showMessage(`📋 已复制: ${getUrlDisplayName(url)}`, 'success');
            }).catch(() => {
                // 降级方案
                const textArea = document.createElement('textarea');
                textArea.value = url;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);
                showMessage(`📋 已复制: ${getUrlDisplayName(url)}`, 'success');
            });
        }

        // 显示消息
        function showMessage(message, type = 'info') {
            // 创建消息元素
            const messageDiv = document.createElement('div');
            messageDiv.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                padding: 15px 20px;
                border-radius: 8px;
                color: white;
                font-weight: 600;
                z-index: 10000;
                max-width: 400px;
                box-shadow: 0 5px 15px rgba(0,0,0,0.3);
                transform: translateX(100%);
                transition: transform 0.3s ease;
            `;

            // 根据类型设置颜色
            switch (type) {
                case 'success':
                    messageDiv.style.background = 'linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%)';
                    break;
                case 'error':
                    messageDiv.style.background = 'linear-gradient(135deg, #ff416c 0%, #ff4b2b 100%)';
                    break;
                case 'info':
                default:
                    messageDiv.style.background = 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)';
                    break;
            }

            messageDiv.textContent = message;
            document.body.appendChild(messageDiv);

            // 显示动画
            setTimeout(() => {
                messageDiv.style.transform = 'translateX(0)';
            }, 100);

            // 自动隐藏
            setTimeout(() => {
                messageDiv.style.transform = 'translateX(100%)';
                setTimeout(() => {
                    document.body.removeChild(messageDiv);
                }, 300);
            }, 3000);
        }

        // 更新进度条
        function updateProgress(percentage) {
            const progressBar = document.querySelector('.progress-fill');
            if (progressBar) {
                progressBar.style.width = percentage + '%';
            }
        }

        // 键盘快捷键
        document.addEventListener('keydown', function(e) {
            // Ctrl+A 全选
            if (e.ctrlKey && e.key === 'a') {
                e.preventDefault();
                selectAll();
            }

            // Ctrl+Enter 打开选中
            if (e.ctrlKey && e.key === 'Enter') {
                e.preventDefault();
                openSelectedUrls();
            }

            // Escape 清空搜索
            if (e.key === 'Escape') {
                document.getElementById('searchBox').value = '';
                filterUrls();
            }
        });

        // 添加进度条到控制区域
        function addProgressBar() {
            const controls = document.querySelector('.controls');
            const progressContainer = document.createElement('div');
            progressContainer.innerHTML = `
                <div class="progress-bar" style="display: none;">
                    <div class="progress-fill"></div>
                </div>
            `;
            controls.appendChild(progressContainer);
        }

        // 页面加载完成后添加进度条
        document.addEventListener('DOMContentLoaded', function() {
            addProgressBar();
        });
    </script>
</body>
</html>"
