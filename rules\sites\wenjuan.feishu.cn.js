// AI Site Submitter - FeishuWenjuan 规则配置
// 更新于: 2025/7/24
// 域名: wenjuan.feishu.cn

export const SITE_RULE = {
  "domain": "wenjuan.feishu.cn",
  "siteName": "FeishuWenjuan",
  "lastUpdated": "2025-07-24",
  "fieldMappings": {
    "siteName": {
      "selectors": [
        "textarea[placeholder='请输入完整的产品名称']",
        ".uni-textarea-textarea:nth-of-type(1)"
      ],
      "method": "value",
      "validation": "required",
      "notes": "产品名称字段"
    },
    "siteUrl": {
      "selectors": [
        "textarea[placeholder='例如：https://ai-bot.cn/']",
        ".uni-textarea-textarea:nth-of-type(2)"
      ],
      "method": "value",
      "validation": "required|url",
      "notes": "产品官网URL"
    },
    "category": {
      "selectors": [
        ".selector__text",
        ".group__selector"
      ],
      "method": "click",
      "validation": "required",
      "notes": "产品分类，需要点击选择"
    },
    "siteDescription": {
      "selectors": [
        "textarea[placeholder='一句话简介，不超过20个字']",
        "textarea[maxlength='30']",
        ".uni-textarea-textarea:nth-of-type(3)"
      ],
      "method": "value",
      "validation": "required",
      "maxLength": 30,
      "notes": "产品一句话简介，限制30字符"
    },
    "detailedIntro": {
      "selectors": [
        "textarea[placeholder='包含：产品介绍、产品功能、如何使用、产品价格、应用场景等']",
        ".uni-textarea-textarea:nth-of-type(4)"
      ],
      "method": "value",
      "validation": "required",
      "notes": "产品详细介绍"
    },
    "fullName": {
      "selectors": [
        "textarea[placeholder='请输入名字']",
        "textarea[maxlength='5']",
        ".uni-textarea-textarea:nth-of-type(5)"
      ],
      "method": "value",
      "validation": "required",
      "maxLength": 5,
      "notes": "提交人名字，限制5个字符"
    },
    "contactEmail": {
      "selectors": [
        "textarea[placeholder='微信/电话/邮件等']",
        ".uni-textarea-textarea:nth-of-type(6)"
      ],
      "method": "value",
      "validation": "required",
      "notes": "提交人联系方式"
    }
  },
  "submitConfig": {
    "submitButton": "button[type='submit'], .submit-button, button:contains('提交')",
    "submitMethod": "click",
    "successIndicators": [".success-message", ".submit-success"],
    "errorIndicators": [".error-message", ".submit-error"]
  },
  "specialHandling": {
    "requiresLogin": false,
    "hasCaptcha": false,
    "hasFileUpload": false,
    "customScript": "handleFeishuWenjuanSubmission",
    "formValidation": {
      "requiredFields": ["siteName", "siteUrl", "category", "siteDescription", "detailedIntro", "fullName", "contactEmail"],
      "emailValidation": false,
      "urlValidation": true
    },
    "notes": [
      "飞书问卷表单",
      "所有字段均为textarea",
      "产品分类需要点击选择",
      "提交人名字限制5个字符",
      "产品简介限制30个字符"
    ]
  }
};

// 自定义处理函数
export function handleFeishuWenjuanSubmission(data, rule) {
  console.log('处理飞书问卷表单提交...');
  
  const processedData = { ...data };
  
  // URL格式化
  if (processedData.siteUrl && !processedData.siteUrl.startsWith('http')) {
    processedData.siteUrl = 'https://' + processedData.siteUrl;
  }
  
  // 字符限制处理
  if (processedData.siteDescription && processedData.siteDescription.length > 30) {
    processedData.siteDescription = processedData.siteDescription.substring(0, 30);
  }
  
  if (processedData.fullName && processedData.fullName.length > 5) {
    processedData.fullName = processedData.fullName.substring(0, 5);
  }
  
  return processedData;
}

// 自定义元素填写函数
export async function customFillElement(element, value, config) {
  console.log('飞书问卷自定义填写函数被调用:', element, value);
  
  // 处理分类选择器
  if (element.classList.contains('group__selector') || element.classList.contains('selector__text')) {
    element.click();
    
    // 等待下拉菜单出现
    setTimeout(() => {
      const options = document.querySelectorAll('.selector__scroll .items__container div');
      const targetOption = Array.from(options).find(opt => opt.textContent.includes('AI工具') || opt.textContent.includes('人工智能'));
      
      if (targetOption) {
        targetOption.click();
      } else if (options.length > 0) {
        // 如果没找到AI相关选项，选择第一个
        options[0].click();
      }
    }, 300);
    
    return true;
  }
  
  // 处理文本输入
  if (element.tagName === 'TEXTAREA') {
    element.value = value;
    element.dispatchEvent(new Event('input', { bubbles: true }));
    element.dispatchEvent(new Event('change', { bubbles: true }));
    return true;
  }
  
  return false;
}