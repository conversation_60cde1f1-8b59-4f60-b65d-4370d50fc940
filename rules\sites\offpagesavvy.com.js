// offpagesavvy.com 网站规则配置
// 网站: https://www.offpagesavvy.com/submit-your-site/
// 表单技术: Contact Form 7 (WordPress)
// 最后更新: 2025-07-08

export const SITE_RULE = {
  // 基本信息
  domain: 'offpagesavvy.com',
  siteName: 'Off Page Savvy',
  priority: 1,
  lastUpdated: '2025-07-08',
  
  // 字段映射规则
  fieldMappings: {
    // 您的姓名 -> Your Name
    fullName: {
      selectors: [
        'input[name="your-name"]',
        'input.wpcf7-text:first-of-type',
        'input[size="40"]:first-of-type',
        'input.wpcf7-validates-as-required:first-of-type'
      ],
      method: 'value',
      validation: 'required',
      notes: '您的姓名，使用website-info.js中的fullName字段'
    },
    
    // 网站URL -> URL
    siteUrl: {
      selectors: [
        'input[name="URL"]',
        'input[type="url"]',
        'input.wpcf7-url',
        'input.wpcf7-validates-as-url'
      ],
      method: 'value',
      validation: 'required|url',
      notes: '网站URL，使用website-info.js中的siteUrl字段'
    },
    
    // 您的邮箱 -> Your Email
    contactEmail: {
      selectors: [
        'input[name="your-email"]',
        'input[type="email"]',
        'input.wpcf7-email',
        'input.wpcf7-validates-as-email'
      ],
      method: 'value',
      validation: 'required|email',
      notes: '您的邮箱，使用website-info.js中的contactEmail字段'
    },
    
    // 电话号码 -> Phone Number
    phone: {
      selectors: [
        'input[name="Phoneno"]',
        'input[type="number"]',
        'input.wpcf7-number',
        'input.wpcf7-validates-as-number',
        'input.wpcf7-not-valid[name="Phoneno"]',
        'input[aria-describedby*="Phoneno"]'
      ],
      method: 'value',
      validation: 'required',
      defaultValue: '5554323524',
      notes: '电话号码，使用默认美国电话号码（10位数字，无+1前缀）'
    },
    
    // 消息内容 -> Your Message
    message: {
      selectors: [
        'textarea[name="your-message"]',
        'textarea.wpcf7-textarea',
        'textarea[cols="40"]',
        'textarea[rows="10"]'
      ],
      method: 'value',
      validation: 'optional',
      notes: '消息内容，使用website-info.js中的message字段'
    }
  },
  
  // 自定义填写函数
  customFillElement: async function(element, value, config) {
    console.log(`Off Page Savvy自定义填写: ${element.name || element.type}, 方法: ${config.method}`);
    
    switch (config.method) {
      case 'value':
        // 标准输入框和文本域处理
        element.focus();
        await new Promise(resolve => setTimeout(resolve, 100));
        
        // 清空现有内容
        element.value = '';
        await new Promise(resolve => setTimeout(resolve, 50));
        
        // 设置新值
        element.value = value;
        
        // 移除验证错误类
        element.classList.remove('wpcf7-not-valid');
        element.setAttribute('aria-invalid', 'false');

        // 触发Contact Form 7事件
        element.dispatchEvent(new Event('input', { bubbles: true }));
        element.dispatchEvent(new Event('change', { bubbles: true }));
        element.dispatchEvent(new Event('blur', { bubbles: true }));

        // Contact Form 7特殊处理
        const cf7Event = new CustomEvent('wpcf7:change', {
          detail: { value: value },
          bubbles: true
        });
        element.dispatchEvent(cf7Event);

        // 触发验证事件
        const validateEvent = new CustomEvent('wpcf7:validate', {
          bubbles: true
        });
        element.dispatchEvent(validateEvent);
        
        console.log(`✓ 填写字段: ${element.name} = "${value.substring(0, 50)}..."`);
        break;
        
      default:
        element.value = value;
        element.dispatchEvent(new Event('input', { bubbles: true }));
        element.dispatchEvent(new Event('change', { bubbles: true }));
    }
  },
  
  // 提交流程配置
  submitConfig: {
    submitButton: [
      'input[type="submit"]',
      'button[type="submit"]',
      '.wpcf7-submit',
      'input.wpcf7-form-control[type="submit"]'
    ],
    submitMethod: 'click',
    waitAfterFill: 2000,
    waitAfterSubmit: 5000,
    successIndicators: [
      'text:contains("submitted")',
      'text:contains("thank you")',
      'text:contains("success")',
      '.wpcf7-mail-sent-ok'
    ],
    errorIndicators: [
      'text:contains("error")',
      'text:contains("required")',
      'text:contains("invalid")',
      '.wpcf7-validation-errors'
    ]
  },
  
  // 特殊处理
  specialHandling: {
    requiresLogin: false,
    hasCaptcha: false, // 无验证码
    hasFileUpload: false,
    isWordPressForm: true, // WordPress表单
    isContactForm7: true, // Contact Form 7
    isSEODirectory: true, // SEO目录
    hasPhoneField: true, // 有电话字段
    hasMessageField: true, // 有消息字段
    isSimplifiedForm: true, // 简化表单
    
    // 表单验证规则
    formValidation: {
      requiredFields: ['fullName', 'siteUrl', 'contactEmail', 'phone'],
      optionalFields: ['message'],
      emailValidation: true,
      urlValidation: true,
      phoneValidation: true
    },
    
    // 特殊注意事项
    notes: [
      '这是Off Page Savvy的网站提交表单',
      '表单包含5个字段：4个必填，1个可选',
      'SEO和外链相关服务目录',
      '使用Contact Form 7插件',
      '无验证码保护，提交便捷',
      '包含电话号码字段',
      '消息字段使用website-info.js中的message',
      '使用实际字段名：your-name, URL, your-email, Phoneno, your-message',
      '专注于SEO和外链服务',
      '简化的联系表单',
      'WordPress + Contact Form 7架构',
      '现代化的表单验证'
    ]
  }
};

// 自定义处理函数
export function handleOffPageSavvySubmission(data, _rule) {
  console.log('Processing Off Page Savvy form submission...');

  const processedData = { ...data };

  // 确保URL格式正确
  if (processedData.siteUrl && !processedData.siteUrl.startsWith('http')) {
    processedData.siteUrl = 'https://' + processedData.siteUrl;
  }

  // 设置默认电话号码（美国格式，10位数字）
  processedData.phone = '5551234567';

  return processedData;
}

// Off Page Savvy信息提醒
export function showOffPageSavvyInfo() {
  console.log('🔗 Off Page Savvy 信息:');
  console.log('');
  console.log('平台特色:');
  console.log('- SEO和外链服务目录');
  console.log('- 专注于Off-Page SEO');
  console.log('- WordPress + Contact Form 7');
  console.log('- 简化的联系表单');
  console.log('- 无验证码，提交便捷');
  console.log('');
  console.log('服务范围:');
  console.log('- 外链建设服务');
  console.log('- SEO优化咨询');
  console.log('- 网站推广服务');
  console.log('- 数字营销解决方案');
  console.log('');
  console.log('表单特点:');
  console.log('- 只有5个字段：4个必填，1个可选');
  console.log('- 包含电话号码字段');
  console.log('- 消息字段使用message数据');
  console.log('- Contact Form 7验证');
  console.log('- 现代化表单设计');
  console.log('');
  console.log('Off Page Savvy - 专业的SEO外链服务平台！');
}

// Contact Form 7特点
export function showContactForm7Features() {
  console.log('📝 Contact Form 7特点:');
  console.log('');
  console.log('技术优势:');
  console.log('- WordPress最流行的表单插件');
  console.log('- 强大的表单验证功能');
  console.log('- 灵活的字段配置');
  console.log('- 多语言支持');
  console.log('');
  console.log('验证特性:');
  console.log('- 实时字段验证');
  console.log('- 内置邮箱验证');
  console.log('- URL格式验证');
  console.log('- 数字格式验证');
  console.log('');
  console.log('CSS类命名:');
  console.log('- wpcf7-form-control：表单控件');
  console.log('- wpcf7-text：文本输入框');
  console.log('- wpcf7-email：邮箱输入框');
  console.log('- wpcf7-url：URL输入框');
  console.log('- wpcf7-number：数字输入框');
  console.log('- wpcf7-textarea：文本域');
  console.log('');
  console.log('验证类:');
  console.log('- wpcf7-validates-as-required：必填验证');
  console.log('- wpcf7-validates-as-email：邮箱验证');
  console.log('- wpcf7-validates-as-url：URL验证');
  console.log('- wpcf7-validates-as-number：数字验证');
}

// SEO外链服务说明
export function showSEOOffPageServices() {
  console.log('🔍 SEO外链服务说明:');
  console.log('');
  console.log('Off-Page SEO服务:');
  console.log('- 高质量外链建设');
  console.log('- 权威网站链接');
  console.log('- 社交媒体推广');
  console.log('- 品牌提及建设');
  console.log('');
  console.log('外链类型:');
  console.log('- 目录提交');
  console.log('- 客座博客');
  console.log('- 资源页面链接');
  console.log('- 社交书签');
  console.log('');
  console.log('服务优势:');
  console.log('- 提高搜索排名');
  console.log('- 增加网站权重');
  console.log('- 扩大品牌影响力');
  console.log('- 获得目标流量');
  console.log('');
  console.log('适合客户:');
  console.log('- 企业网站');
  console.log('- 电商平台');
  console.log('- 个人博客');
  console.log('- 服务提供商');
}

// 电话字段处理说明
export function showPhoneFieldHandling() {
  console.log('📞 电话字段处理说明:');
  console.log('');
  console.log('字段特点:');
  console.log('- 类型：number');
  console.log('- 验证：wpcf7-validates-as-number');
  console.log('- 必填：aria-required="true"');
  console.log('- 只接受数字输入');
  console.log('');
  console.log('数据处理:');
  console.log('- 自动移除非数字字符');
  console.log('- 保持纯数字格式');
  console.log('- 使用website-info.js中的phone字段');
  console.log('');
  console.log('格式建议:');
  console.log('- 国际格式：+1234567890');
  console.log('- 本地格式：1234567890');
  console.log('- 避免使用分隔符');
  console.log('- 确保号码有效性');
  console.log('');
  console.log('验证规则:');
  console.log('- 只允许数字');
  console.log('- 长度合理性检查');
  console.log('- 实时验证反馈');
}

// 消息字段说明
export function showMessageFieldUsage() {
  console.log('💬 消息字段使用说明:');
  console.log('');
  console.log('字段配置:');
  console.log('- 名称：your-message');
  console.log('- 类型：textarea');
  console.log('- 尺寸：40列 x 10行');
  console.log('- 验证：可选字段');
  console.log('');
  console.log('数据来源:');
  console.log('- 使用website-info.js中的message字段');
  console.log('- 可以包含详细的网站描述');
  console.log('- 服务需求说明');
  console.log('- 特殊要求备注');
  console.log('');
  console.log('内容建议:');
  console.log('- 简要介绍网站');
  console.log('- 说明SEO需求');
  console.log('- 目标关键词');
  console.log('- 预期效果');
  console.log('');
  console.log('示例内容:');
  console.log('"我们是一家专业的AI工具网站，希望通过高质量的外链建设提高搜索排名，主要关键词包括AI工具、人工智能等。"');
}

// 表单验证
export function validateOffPageSavvyForm() {
  console.log('验证Off Page Savvy表单...');

  const requiredFields = [
    { selector: 'input[name="your-name"]', label: '您的姓名' },
    { selector: 'input[name="URL"]', label: '网站URL' },
    { selector: 'input[name="your-email"]', label: '您的邮箱' },
    { selector: 'input[name="Phoneno"]', label: '电话号码' }
  ];

  let isValid = true;

  requiredFields.forEach(field => {
    const element = document.querySelector(field.selector);
    if (!element || !element.value.trim()) {
      console.log(`⚠️ 必填字段为空: ${field.label}`);
      isValid = false;
    }
  });

  // 检查URL格式
  const urlField = document.querySelector('input[name="URL"]');
  if (urlField && urlField.value && !urlField.value.match(/^https?:\/\//)) {
    console.log('⚠️ URL格式可能不正确，建议包含http://或https://');
  }

  // 检查邮箱格式
  const emailField = document.querySelector('input[name="your-email"]');
  if (emailField && emailField.value && !emailField.value.includes('@')) {
    console.log('⚠️ 邮箱格式可能不正确');
  }

  // 检查电话号码格式
  const phoneField = document.querySelector('input[name="Phoneno"]');
  if (phoneField && phoneField.value && !/^\d+$/.test(phoneField.value)) {
    console.log('⚠️ 电话号码只能包含数字');
  }

  if (isValid) {
    console.log('✓ 表单验证通过');
  }

  return isValid;
}

// SEO服务网站特点
export function showSEOServiceWebsiteFeatures() {
  console.log('🎯 SEO服务网站特点:');
  console.log('');
  console.log('Off Page Savvy定位:');
  console.log('- 专业SEO服务提供商');
  console.log('- 外链建设专家');
  console.log('- 数字营销顾问');
  console.log('- 网站优化服务');
  console.log('');
  console.log('服务优势:');
  console.log('- 专业团队');
  console.log('- 丰富经验');
  console.log('- 效果保证');
  console.log('- 合理价格');
  console.log('');
  console.log('客户价值:');
  console.log('- 提升搜索排名');
  console.log('- 增加网站流量');
  console.log('- 提高转化率');
  console.log('- 建立品牌权威');
  console.log('');
  console.log('联系方式:');
  console.log('- 在线表单提交');
  console.log('- 电话直接联系');
  console.log('- 邮箱沟通');
  console.log('- 详细需求说明');
}

// Contact Form 7与其他表单对比
export function showContactForm7Comparison() {
  console.log('📊 Contact Form 7与其他表单对比:');
  console.log('');
  console.log('已配置的表单技术:');
  console.log('1. PHP Form: 8个网站 (目录网站)');
  console.log('2. Vue.js Form: 1个 (inside.thewarehouse.ai)');
  console.log('3. Contact Form 7: 1个 (offpagesavvy.com) ✅ 新增');
  console.log('');
  console.log('技术特点对比:');
  console.log('- PHP Form: 传统、稳定、功能丰富');
  console.log('- Vue.js Form: 现代、响应式、交互性强');
  console.log('- Contact Form 7: WordPress生态、易用、验证完善');
  console.log('');
  console.log('使用场景:');
  console.log('- PHP Form: 目录提交、批量处理');
  console.log('- Vue.js Form: 产品展示、用户体验');
  console.log('- Contact Form 7: 联系咨询、服务询价');
  console.log('');
  console.log('验证能力:');
  console.log('- PHP Form: 服务器端验证');
  console.log('- Vue.js Form: 客户端实时验证');
  console.log('- Contact Form 7: 双重验证机制');
}
