// ontoplist.com 网站规则配置
// 网站: https://www.ontoplist.com/join/
// 表单技术: Custom Registration Form
// 最后更新: 2025-07-08

export const SITE_RULE = {
  // 基本信息
  domain: 'ontoplist.com',
  siteName: 'OnTopList',
  priority: 1,
  lastUpdated: '2025-07-08',
  
  // 字段映射规则
  fieldMappings: {
    // 邮箱 -> Email
    contactEmail: {
      selectors: [
        'input[name="email"]',
        'input[type="email"]',
        'input#email',
        'input.form-input[type="email"]'
      ],
      method: 'value',
      validation: 'required|email',
      notes: '邮箱地址，使用website-info.js中的contactEmail字段'
    },
    
    // 密码 -> Password
    password: {
      selectors: [
        'input[name="password"]',
        'input[type="password"]',
        'input#password',
        'input.form-input[type="password"]'
      ],
      method: 'value',
      validation: 'required',
      defaultValue: 'SecurePass123!',
      notes: '密码，使用固定的6位以上复杂密码'
    },
    
    // 网站地址 -> Site Address (URL)
    siteUrl: {
      selectors: [
        'input[name="url"]',
        'input[type="url"]',
        'input#url',
        'input.form-input[type="url"]'
      ],
      method: 'value',
      validation: 'required|url',
      notes: '网站URL，使用website-info.js中的siteUrl字段'
    },
    
    // 网站标题 -> Website Title
    siteName: {
      selectors: [
        'input[name="sitename"]',
        'input#sitename',
        'input.form-input[name="sitename"]',
        'input[type="text"][name="sitename"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '网站标题，使用website-info.js中的siteName字段'
    },
    
    // 网站描述 -> Site Description
    siteDescription: {
      selectors: [
        'textarea[name="description"]',
        'textarea#description',
        'textarea.form-textarea',
        'textarea[rows="5"]'
      ],
      method: 'value',
      validation: 'optional',
      notes: '网站描述，使用website-info.js中的siteDescription字段'
    },
    
    // 列表类型 -> Listing Type
    listingType: {
      selectors: [
        'input[name="type"][value="2"]',
        'input#t2',
        'input[type="radio"][value="2"]'
      ],
      method: 'radio',
      validation: 'required',
      defaultValue: '2',
      notes: '列表类型，选择Website (value="2")'
    },
    
    // 分类 -> Category
    category: {
      selectors: [
        'select[name="category"]',
        'select#category',
        'select.form-select[name="category"]'
      ],
      method: 'select',
      validation: 'required',
      defaultValue: '90',
      notes: '分类，选择Web Design (value="90")'
    },
    
    // 语言 -> Language
    language: {
      selectors: [
        'select[name="language_id"]',
        'select#language_id',
        'select.form-select[name="language_id"]'
      ],
      method: 'select',
      validation: 'required',
      defaultValue: '1',
      notes: '语言，选择English (value="1")'
    }
  },
  
  // 自定义填写函数
  customFillElement: async function(element, value, config) {
    console.log(`OnTopList自定义填写: ${element.name || element.type}, 方法: ${config.method}`);
    
    switch (config.method) {
      case 'value':
        // 标准输入框和文本域处理
        element.focus();
        await new Promise(resolve => setTimeout(resolve, 100));
        
        // 清空现有内容
        element.value = '';
        await new Promise(resolve => setTimeout(resolve, 50));
        
        // 设置新值
        element.value = value;
        
        // 触发事件
        element.dispatchEvent(new Event('input', { bubbles: true }));
        element.dispatchEvent(new Event('change', { bubbles: true }));
        element.dispatchEvent(new Event('blur', { bubbles: true }));
        
        console.log(`✓ 填写字段: ${element.name} = "${value.substring(0, 50)}..."`);
        break;
        
      case 'radio':
        // 单选按钮处理
        console.log(`处理列表类型，目标值: ${config.defaultValue}`);
        
        // 查找所有同名单选按钮
        const radioButtons = document.querySelectorAll('input[name="type"]');
        
        // 先取消所有选择
        radioButtons.forEach(rb => {
          rb.checked = false;
        });
        
        // 选择目标选项
        const targetRadio = Array.from(radioButtons).find(rb => 
          rb.value === config.defaultValue
        );
        
        if (targetRadio) {
          targetRadio.checked = true;
          targetRadio.dispatchEvent(new Event('change', { bubbles: true }));
          console.log(`✓ 选择列表类型: Website (${config.defaultValue})`);
        } else {
          console.log(`⚠️ 未找到列表类型: ${config.defaultValue}`);
        }
        break;
        
      case 'select':
        // 下拉选择框处理
        console.log(`处理下拉选择，字段: ${element.name}, 目标值: ${config.defaultValue}`);
        
        // 设置选中值
        element.value = config.defaultValue;
        
        // 触发change事件
        element.dispatchEvent(new Event('change', { bubbles: true }));
        
        // 特殊处理分类选择（可能需要触发子分类加载）
        if (element.name === 'category' && typeof getSubCats === 'function') {
          getSubCats(config.defaultValue);
        }
        
        console.log(`✓ 选择${element.name}: ${config.defaultValue}`);
        break;
        
      default:
        element.value = value;
        element.dispatchEvent(new Event('input', { bubbles: true }));
        element.dispatchEvent(new Event('change', { bubbles: true }));
    }
  },
  
  // 提交流程配置
  submitConfig: {
    submitButton: [
      'button[type="submit"]',
      'input[type="submit"]',
      'button:contains("Join")',
      'button:contains("Register")',
      '.submit-button'
    ],
    submitMethod: 'click',
    waitAfterFill: 3000,
    waitAfterSubmit: 5000,
    successIndicators: [
      'text:contains("registered")',
      'text:contains("welcome")',
      'text:contains("success")',
      'text:contains("account created")'
    ],
    errorIndicators: [
      'text:contains("error")',
      'text:contains("required")',
      'text:contains("invalid")',
      'text:contains("exists")'
    ]
  },
  
  // 特殊处理
  specialHandling: {
    requiresLogin: false,
    hasCaptcha: false, // 无验证码
    hasFileUpload: false,
    isRegistrationForm: true, // 注册表单
    isTopListDirectory: true, // 顶级列表目录
    hasPasswordField: true, // 有密码字段
    hasRadioButtons: true, // 有单选按钮
    hasSelectDropdowns: true, // 有下拉选择
    requiresAccount: true, // 需要创建账户
    
    // 表单验证规则
    formValidation: {
      requiredFields: ['contactEmail', 'password', 'siteUrl', 'siteName', 'listingType', 'category', 'language'],
      optionalFields: ['siteDescription'],
      emailValidation: true,
      urlValidation: true,
      passwordValidation: true,
      radioGroups: ['listingType'],
      selectFields: ['category', 'language']
    },
    
    // 特殊注意事项
    notes: [
      '这是OnTopList的网站注册表单',
      '表单包含8个字段：7个必填，1个可选',
      '顶级列表目录，需要注册账户',
      '无验证码保护，注册便捷',
      '密码使用固定复杂密码：SecurePass123!',
      '列表类型选择Website (value="2")',
      '分类选择Web Design (value="90")',
      '语言选择English (value="1")',
      '使用实际字段名：email, password, url, sitename, description, type, category, language_id',
      '注册后可能需要邮箱验证',
      '专注于网站和博客列表',
      '现代化的表单设计'
    ]
  }
};

// 自定义处理函数
export function handleOnTopListSubmission(data, _rule) {
  console.log('Processing OnTopList registration form submission...');

  const processedData = { ...data };

  // 确保URL格式正确
  if (processedData.siteUrl && !processedData.siteUrl.startsWith('http')) {
    processedData.siteUrl = 'https://' + processedData.siteUrl;
  }

  // 设置固定值
  processedData.password = 'SecurePass123!'; // 固定复杂密码
  processedData.listingType = '2'; // Website
  processedData.category = '90'; // Web Design
  processedData.language = '1'; // English

  return processedData;
}

// OnTopList信息提醒
export function showOnTopListInfo() {
  console.log('🔝 OnTopList 信息:');
  console.log('');
  console.log('平台特色:');
  console.log('- 顶级列表目录');
  console.log('- 网站和博客收录');
  console.log('- 需要注册账户');
  console.log('- 现代化界面设计');
  console.log('- 分类详细，选择丰富');
  console.log('');
  console.log('注册要求:');
  console.log('- 邮箱地址（必填）');
  console.log('- 密码（6位以上复杂密码）');
  console.log('- 网站URL（必填）');
  console.log('- 网站标题（必填）');
  console.log('- 网站描述（可选）');
  console.log('');
  console.log('默认选择:');
  console.log('- 列表类型：Website ✅');
  console.log('- 分类：Web Design ✅');
  console.log('- 语言：English ✅');
  console.log('');
  console.log('表单特点:');
  console.log('- 8个字段：7个必填，1个可选');
  console.log('- 包含单选按钮和下拉选择');
  console.log('- 现代化表单验证');
  console.log('- 无验证码干扰');
  console.log('');
  console.log('OnTopList - 专业的网站列表目录！');
}

// 注册表单特点
export function showRegistrationFormFeatures() {
  console.log('📝 注册表单特点:');
  console.log('');
  console.log('与目录提交表单的区别:');
  console.log('- 需要创建用户账户');
  console.log('- 包含密码字段');
  console.log('- 邮箱验证可能需要');
  console.log('- 登录后可管理列表');
  console.log('');
  console.log('账户管理优势:');
  console.log('- 可以编辑网站信息');
  console.log('- 查看统计数据');
  console.log('- 管理多个网站');
  console.log('- 接收通知消息');
  console.log('');
  console.log('密码要求:');
  console.log('- 至少6个字符');
  console.log('- 建议包含大小写字母');
  console.log('- 建议包含数字和特殊字符');
  console.log('- 固定使用：SecurePass123!');
  console.log('');
  console.log('注册流程:');
  console.log('1. 填写注册表单');
  console.log('2. 提交注册信息');
  console.log('3. 可能需要邮箱验证');
  console.log('4. 激活账户');
  console.log('5. 登录管理网站');
}

// 列表类型说明
export function showListingTypeOptions() {
  console.log('📋 列表类型说明:');
  console.log('');
  console.log('可选类型:');
  console.log('1. Blog (value="1")');
  console.log('   - 个人博客');
  console.log('   - 内容博客');
  console.log('   - 新闻博客');
  console.log('');
  console.log('2. Website (value="2") ✅ 默认选择');
  console.log('   - 企业网站');
  console.log('   - 产品网站');
  console.log('   - 服务网站');
  console.log('   - 工具网站');
  console.log('');
  console.log('选择建议:');
  console.log('- AI工具网站 → Website');
  console.log('- 技术博客 → Blog');
  console.log('- 企业官网 → Website');
  console.log('- 产品展示 → Website');
  console.log('');
  console.log('默认选择Website的原因:');
  console.log('- 适用范围更广');
  console.log('- 包含工具类网站');
  console.log('- 商业价值更高');
  console.log('- 用户期望更明确');
}

// 分类选择说明
export function showCategorySelection() {
  console.log('🗂️ 分类选择说明:');
  console.log('');
  console.log('主要分类包括:');
  console.log('- Technology (value="3")');
  console.log('- Web Design (value="90") ✅ 默认选择');
  console.log('- 其他分类...');
  console.log('');
  console.log('Web Design分类特点:');
  console.log('- 包含网站设计相关');
  console.log('- 涵盖前端开发');
  console.log('- 包括设计工具');
  console.log('- 适合AI设计工具');
  console.log('');
  console.log('分类选择建议:');
  console.log('- AI设计工具 → Web Design');
  console.log('- 技术工具 → Technology');
  console.log('- 根据网站主要功能选择');
  console.log('- 考虑目标用户群体');
  console.log('');
  console.log('子分类功能:');
  console.log('- 选择主分类后可能加载子分类');
  console.log('- getSubCats()函数处理');
  console.log('- 提供更精确的分类');
}

// 语言选择说明
export function showLanguageSelection() {
  console.log('🌐 语言选择说明:');
  console.log('');
  console.log('可选语言:');
  console.log('- English (value="1") ✅ 默认选择');
  console.log('- Arabic (value="2")');
  console.log('- 其他语言...');
  console.log('');
  console.log('English选择原因:');
  console.log('- 国际通用语言');
  console.log('- 用户群体最大');
  console.log('- 搜索量最高');
  console.log('- 商业价值最高');
  console.log('');
  console.log('语言影响:');
  console.log('- 影响搜索结果');
  console.log('- 影响用户群体');
  console.log('- 影响网站曝光');
  console.log('- 影响流量质量');
}

// 表单验证
export function validateOnTopListForm() {
  console.log('验证OnTopList注册表单...');

  const requiredFields = [
    { selector: 'input[name="email"]', label: '邮箱地址' },
    { selector: 'input[name="password"]', label: '密码' },
    { selector: 'input[name="url"]', label: '网站URL' },
    { selector: 'input[name="sitename"]', label: '网站标题' }
  ];

  let isValid = true;

  requiredFields.forEach(field => {
    const element = document.querySelector(field.selector);
    if (!element || !element.value.trim()) {
      console.log(`⚠️ 必填字段为空: ${field.label}`);
      isValid = false;
    }
  });

  // 检查列表类型选择
  const listingType = document.querySelector('input[name="type"]:checked');
  if (!listingType) {
    console.log('⚠️ 请选择列表类型');
    isValid = false;
  }

  // 检查分类选择
  const category = document.querySelector('select[name="category"]');
  if (!category || !category.value) {
    console.log('⚠️ 请选择分类');
    isValid = false;
  }

  // 检查语言选择
  const language = document.querySelector('select[name="language_id"]');
  if (!language || !language.value) {
    console.log('⚠️ 请选择语言');
    isValid = false;
  }

  // 检查密码长度
  const password = document.querySelector('input[name="password"]');
  if (password && password.value.length < 6) {
    console.log('⚠️ 密码至少需要6个字符');
    isValid = false;
  }

  if (isValid) {
    console.log('✓ 表单验证通过');
  }

  return isValid;
}

// 注册后续步骤
export function showPostRegistrationSteps() {
  console.log('📧 注册后续步骤:');
  console.log('');
  console.log('可能的流程:');
  console.log('1. 邮箱验证');
  console.log('   - 检查邮箱收件箱');
  console.log('   - 点击验证链接');
  console.log('   - 激活账户');
  console.log('');
  console.log('2. 账户设置');
  console.log('   - 完善个人信息');
  console.log('   - 设置通知偏好');
  console.log('   - 配置隐私设置');
  console.log('');
  console.log('3. 网站管理');
  console.log('   - 编辑网站信息');
  console.log('   - 查看统计数据');
  console.log('   - 管理多个网站');
  console.log('');
  console.log('4. 平台功能');
  console.log('   - 浏览其他网站');
  console.log('   - 参与社区互动');
  console.log('   - 获取推广机会');
}
