// SuperAITools.io 网站规则配置
// 网站: https://www.superaitools.io/submit-a-tool
// 最后更新: 2025-07-06

export const SITE_RULE = {
  // 基本信息
  domain: 'superaitools.io',
  siteName: 'Super AI Tools',
  priority: 1,
  lastUpdated: '2025-07-06',
  
  // 字段映射规则
  fieldMappings: {
    // 提交者姓名 -> Your name
    fullName: {
      selectors: [
        '#form1-Yourname-845622360',
        'input[name="G-0ebb6bd6"]',
        'input[placeholder*="Your Name"]',
        'input.MuiInputBase-input[placeholder*="Name"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '提交者姓名'
    },
    
    // 商务邮箱 -> Business Email
    contactEmail: {
      selectors: [
        '#form1-Youremailaddress-1055599683',
        'input[name="G-53481ce5"]',
        'input[placeholder*="Your Business Email"]',
        'input[type="email"].MuiInputBase-input'
      ],
      method: 'value',
      validation: 'required|email',
      notes: '商务邮箱地址'
    },
    
    // 工具名称 -> Tool Name
    siteName: {
      selectors: [
        '#form1-ToolName-90177075',
        'input[name="G-302ff1ac"]',
        'input[placeholder*="Tool Name"]',
        'input.MuiInputBase-input[placeholder*="Tool Name"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '工具名称'
    },
    
    // 工具URL -> Tool URL
    siteUrl: {
      selectors: [
        '#form1-ToolURL--689821497',
        'input[name="G-939332e2"]',
        'input[placeholder*="Tool link"]',
        'input.MuiInputBase-input[placeholder*="link"]'
      ],
      method: 'value',
      validation: 'required|url',
      notes: '工具网站URL'
    },
    
    // 定价模式 -> Pricing Model
    pricing: {
      selectors: [
        '#form1-Pricingmodel--2090496945',
        'input[name="G-b8e9ba16"]',
        '.MuiSelect-select[aria-labelledby*="Pricingmodel"]',
        'div[role="button"][aria-labelledby*="Pricingmodel"]'
      ],
      method: 'mui-select',
      validation: 'required',
      options: ['Free', 'Freemium', 'Paid'],
      defaultValue: 'Free',
      notes: 'MUI下拉选择，默认选择Free'
    },
    
    // 工具标签 -> Tool Tags
    keywords: {
      selectors: [
        '#form1-ToolTags-90355649',
        'input[name="G-ddc4d4cd"]',
        'input[placeholder*="Tool Tags"]',
        'input.MuiInputBase-input[placeholder*="Tags"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '工具关键词标签'
    },
    
    // 工具描述 -> Tool Description
    siteDescription: {
      selectors: [
        '#form1-ToolDescription-272962388',
        'textarea[name="G-cd991011"]',
        'textarea[placeholder*="Describe your product"]',
        'textarea.MuiInputBase-input[placeholder*="product"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '工具产品描述'
    },
    
    // 其他备注 -> Message (可选)
    message: {
      selectors: [
        '#form1-Otherremarks-1130168803',
        'textarea[name="G-505495f3"]',
        'textarea[placeholder*="Other remarks"]',
        'textarea.MuiInputBase-input[placeholder*="remarks"]'
      ],
      method: 'value',
      validation: 'optional',
      notes: '其他备注信息'
    }
  },
  
  // 自定义填写函数
  customFillElement: async function(element, value, config) {
    console.log(`SuperAITools自定义填写: ${element.name || element.id}, 方法: ${config.method}`);
    
    switch (config.method) {
      case 'value':
        // Material-UI输入框处理
        element.focus();
        await new Promise(resolve => setTimeout(resolve, 100));
        
        element.value = value;
        element.dispatchEvent(new Event('input', { bubbles: true }));
        element.dispatchEvent(new Event('change', { bubbles: true }));
        element.dispatchEvent(new Event('blur', { bubbles: true }));
        
        // MUI可能需要额外的事件
        element.dispatchEvent(new Event('keydown', { bubbles: true }));
        element.dispatchEvent(new Event('keyup', { bubbles: true }));
        break;
        
      case 'mui-select':
        // Material-UI下拉选择特殊处理
        const selectButton = element.closest('.MuiSelect-select') || element;
        
        // 点击打开下拉菜单
        selectButton.click();
        await new Promise(resolve => setTimeout(resolve, 300));
        
        // 查找选项
        const targetValue = config.defaultValue || value;
        const optionSelector = `li[data-value="${targetValue}"], .MuiMenuItem-root[data-value="${targetValue}"], .MuiListItem-root:contains("${targetValue}")`;
        
        // 等待选项出现
        let option = null;
        for (let i = 0; i < 10; i++) {
          option = document.querySelector(optionSelector);
          if (option) break;
          await new Promise(resolve => setTimeout(resolve, 100));
        }
        
        if (option) {
          option.click();
          console.log(`✓ 选择MUI选项: ${targetValue}`);
        } else {
          // 如果找不到精确匹配，尝试文本匹配
          const allOptions = document.querySelectorAll('.MuiMenuItem-root, .MuiListItem-root');
          for (const opt of allOptions) {
            if (opt.textContent && opt.textContent.trim() === targetValue) {
              opt.click();
              console.log(`✓ 选择MUI选项(文本匹配): ${targetValue}`);
              break;
            }
          }
        }
        break;
        
      default:
        element.value = value;
        element.dispatchEvent(new Event('input', { bubbles: true }));
        element.dispatchEvent(new Event('change', { bubbles: true }));
    }
  },
  
  // 提交流程配置
  submitConfig: {
    submitButton: 'button[type="submit"].MuiButton-root, .MuiButton-contained[type="submit"]',
    submitMethod: 'click',
    waitAfterFill: 2000,
    waitAfterSubmit: 5000,
    successIndicators: [
      '.success-message',
      '.MuiAlert-standardSuccess',
      '[class*="success"]'
    ],
    errorIndicators: [
      '.error-message',
      '.MuiAlert-standardError',
      '[class*="error"]'
    ]
  },
  
  // 特殊处理
  specialHandling: {
    requiresLogin: false,
    hasCaptcha: false,
    hasFileUpload: false,
    
    // 表单验证规则
    formValidation: {
      requiredFields: ['fullName', 'contactEmail', 'siteName', 'siteUrl', 'pricing', 'keywords', 'siteDescription'],
      emailValidation: true,
      urlValidation: true
    },
    
    // 特殊注意事项
    notes: [
      '使用Material-UI (MUI)组件库',
      '有8个字段，其中7个必填，1个可选',
      'MUI下拉选择需要特殊处理',
      '定价模式默认选择Free',
      '需要商务邮箱而非个人邮箱'
    ]
  }
};

// 自定义处理函数
export function handleSuperAIToolsSubmission(data, rule) {
  console.log('Processing Super AI Tools submission...');
  
  const processedData = { ...data };
  
  // 确保URL格式正确
  if (processedData.siteUrl && !processedData.siteUrl.startsWith('http')) {
    processedData.siteUrl = 'https://' + processedData.siteUrl;
  }
  
  // 设置默认定价
  if (!processedData.pricing) {
    processedData.pricing = 'Free';
  }
  
  // 处理关键词格式
  if (processedData.keywords) {
    // 如果是数组，转换为逗号分隔的字符串
    if (Array.isArray(processedData.keywords)) {
      processedData.keywords = processedData.keywords.join(', ');
    }
    processedData.keywords = processedData.keywords.trim();
  }
  
  return processedData;
}
