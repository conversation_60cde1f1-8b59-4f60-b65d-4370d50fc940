// Smart-Tools.ai 网站规则配置
// 网站: https://www.smart-tools.ai/en/submit
// 最后更新: 2025-07-24

export const SITE_RULE = {
  domain: 'www.smart-tools.ai',
  siteName: 'Smart Tools AI',
  priority: 1,
  lastUpdated: '2025-07-24',

  fieldMappings: {
    authorName: {
      selectors: [
        'input[name="o0Zk0oGupz4FikYb02wNT"]',
        'input[id="mui-33"]',
        'input[type="text"]:first-of-type'
      ],
      method: 'value',
      validation: 'required',
      defaultValue: 'AI Tool Developer',
      notes: '提交者姓名，使用authorName字段'
    },

    contactEmail: {
      selectors: [
        'input[name="3ZXMNUpw6gUGw8sXnJhUyW"]',
        'input[id="mui-34"]',
        'input[type="email"]'
      ],
      method: 'value',
      validation: 'required|email',
      notes: '提交者邮箱，使用contactEmail字段'
    },

    siteDescription: {
      selectors: [
        'textarea[name="5kNOExmxTPdXnagFDMqrW6"]',
        'textarea[id="mui-35"]',
        'textarea'
      ],
      method: 'value',
      validation: 'required',
      notes: 'AI需求描述，使用siteDescription字段'
    }
  },

  submitConfig: {
    submitButton: 'button:contains("Submit")',
    submitMethod: 'click',
    successIndicators: ['.success-message'],
    errorIndicators: ['.error-message']
  },

  specialHandling: {
    requiresLogin: false,
    hasCaptcha: false,
    hasFileUpload: false,
    customScript: 'handleSmartToolsSubmission',
    formValidation: {
      requiredFields: ['authorName', 'contactEmail', 'siteDescription'],
      emailValidation: true,
      urlValidation: false
    },
    notes: [
      '包含两个可选复选框',
      '使用MUI组件',
      '价格模式有5个选项'
    ]
  }
};

export function handleSmartToolsSubmission(data, rule) {
  const processedData = { ...data };

  if (processedData.siteUrl && !processedData.siteUrl.startsWith('http')) {
    processedData.siteUrl = 'https://' + processedData.siteUrl;
  }

  if (processedData.pricing) {
    const pricingMap = {
      'Free': 'free',
      'Freemium': 'freemium',
      'Paid': 'paid',
      'Open Source': 'open source',
      'GitHub': 'github'
    };
    processedData.pricing = pricingMap[processedData.pricing] || 'free';
  }

  return processedData;
}

export async function customFillElement(element, value, config) {
  if (element.tagName === 'SELECT' || element.getAttribute('role') === 'combobox') {
    const options = element.querySelectorAll('option');
    const option = Array.from(options).find(opt =>
      opt.value === value || opt.textContent.trim() === value
    );
    if (option) {
      element.value = option.value;
      element.dispatchEvent(new Event('change', { bubbles: true }));
      return true;
    }
  }

  if (element.tagName === 'INPUT' || element.tagName === 'TEXTAREA') {
    element.value = value;
    element.dispatchEvent(new Event('input', { bubbles: true }));
    return true;
  }

  return false;
}