// aicyclopedia.com 网站规则配置
// 网站: https://aicyclopedia.com/submit-your-ai-tool/
// 表单技术: Elementor Multi-Step Forms
// 最后更新: 2025-07-07

export const SITE_RULE = {
  // 基本信息
  domain: 'aicyclopedia.com',
  siteName: 'AI Cyclopedia',
  priority: 1,
  lastUpdated: '2025-07-07',
  
  // 字段映射规则
  fieldMappings: {
    // 第一步：基本信息
    // 工具名称 -> Tool Name
    siteName: {
      selectors: [
        'input[id="form-field-name"]',
        'input[name="form_fields[name]"]',
        'input[placeholder="Tool Name"]'
      ],
      method: 'value',
      validation: 'required',
      step: 1,
      notes: '工具名称，使用website-info.js中的siteName字段'
    },
    
    // 标语 -> Tag Line
    tagLine: {
      selectors: [
        'input[id="form-field-field_18f75f2"]',
        'input[name="form_fields[field_18f75f2]"]',
        'input[placeholder="Tag Line"]'
      ],
      method: 'value',
      validation: 'required',
      step: 1,
      defaultValue: 'Innovative AI Tool for Enhanced Productivity',
      notes: '工具标语，使用默认值或website-info.js中的siteDescription简化版'
    },
    
    // 定价模式 -> Pricing Model
    pricing: {
      selectors: [
        'select[id="form-field-field_65f5ab7"]',
        'select[name="form_fields[field_65f5ab7]"]'
      ],
      method: 'select',
      validation: 'required',
      step: 1,
      defaultValue: 'FREE',
      notes: '定价模式，默认选择FREE'
    },
    
    // 官方网站链接 -> Official Website Link
    siteUrl: {
      selectors: [
        'input[id="form-field-field_34b2203"]',
        'input[name="form_fields[field_34b2203]"]',
        'input[placeholder="Official Website Link"]',
        'input[type="url"]:first-of-type'
      ],
      method: 'value',
      validation: 'required|url',
      step: 1,
      notes: '官方网站链接，使用website-info.js中的siteUrl字段'
    },
    
    // 第二步：链接信息
    // Facebook链接 -> Facebook Link
    facebookUrl: {
      selectors: [
        'input[id="form-field-field_9fbc29c"]',
        'input[name="form_fields[field_9fbc29c]"]',
        'input[placeholder="Facebook Link"]'
      ],
      method: 'value',
      validation: 'optional|url',
      step: 2,
      notes: 'Facebook链接，可选字段'
    },
    
    // 邮箱 -> Email
    contactEmail: {
      selectors: [
        'input[id="form-field-field_2dac996"]',
        'input[name="form_fields[field_2dac996]"]',
        'input[placeholder="Email"]',
        'input[type="email"]'
      ],
      method: 'value',
      validation: 'required|email',
      step: 2,
      notes: '联系邮箱，使用website-info.js中的contactEmail字段'
    },
    
    // X链接 -> X Link
    twitterUrl: {
      selectors: [
        'input[id="form-field-field_5abd4da"]',
        'input[name="form_fields[field_5abd4da]"]',
        'input[placeholder="X Link"]'
      ],
      method: 'value',
      validation: 'optional|url',
      step: 2,
      notes: 'X(Twitter)链接，使用website-info.js中的twitterUrl字段'
    },
    
    // Instagram链接 -> Instagram Link
    instagramUrl: {
      selectors: [
        'input[id="form-field-field_0ab4e6f"]',
        'input[name="form_fields[field_0ab4e6f]"]',
        'input[placeholder="Instagram Link"]'
      ],
      method: 'value',
      validation: 'optional|url',
      step: 2,
      notes: 'Instagram链接，可选字段'
    },
    
    // Discord链接 -> Discord Link
    discordUrl: {
      selectors: [
        'input[id="form-field-field_2d0b3af"]',
        'input[name="form_fields[field_2d0b3af]"]',
        'input[placeholder="Discord Link"]'
      ],
      method: 'value',
      validation: 'optional|url',
      step: 2,
      notes: 'Discord链接，可选字段'
    },
    
    // YouTube视频链接 -> Introduction Youtube Video Link
    youtubeUrl: {
      selectors: [
        'input[id="form-field-field_cf21197"]',
        'input[name="form_fields[field_cf21197]"]',
        'input[placeholder="Introduction Youtube Video LInk"]'
      ],
      method: 'value',
      validation: 'optional|url',
      step: 2,
      notes: 'YouTube介绍视频链接，可选字段'
    },
    
    // 第三步：最后信息
    // 工具描述 -> Tool Description
    siteDescription: {
      selectors: [
        'textarea[id="form-field-field_0ae884c"]',
        'textarea[name="form_fields[field_0ae884c]"]',
        'textarea[placeholder="Tool Description"]'
      ],
      method: 'value',
      validation: 'optional',
      step: 3,
      notes: '工具描述，使用website-info.js中的siteDescription字段'
    }
    
    // 注意：Tool Logo 和 Featured Image 是文件上传字段，需要特殊处理
  },
  
  // 自定义填写函数
  customFillElement: async function(element, value, config) {
    console.log(`AI Cyclopedia自定义填写: ${element.id || element.name}, 方法: ${config.method}, 步骤: ${config.step}`);
    
    switch (config.method) {
      case 'value':
        // 标准输入框和文本域处理
        element.focus();
        await new Promise(resolve => setTimeout(resolve, 100));
        
        // 清空现有内容
        element.value = '';
        await new Promise(resolve => setTimeout(resolve, 50));
        
        // 处理特殊字段
        let finalValue = value;
        if (element.id === 'form-field-field_18f75f2') {
          // Tag Line使用默认值或简化的描述
          finalValue = config.defaultValue;
        }
        
        // 设置新值
        element.value = finalValue;
        
        // 触发事件
        element.dispatchEvent(new Event('input', { bubbles: true }));
        element.dispatchEvent(new Event('change', { bubbles: true }));
        element.dispatchEvent(new Event('blur', { bubbles: true }));
        
        console.log(`✓ 填写字段: ${element.id} = "${finalValue.substring(0, 50)}..."`);
        break;
        
      case 'select':
        // 下拉选择框处理
        if (element.tagName.toLowerCase() === 'select') {
          // 定价模式选择
          const pricingMapping = {
            'Free': 'FREE',
            'Freemium': 'FREEMIUM',
            'Paid': 'PAID'
          };
          
          let targetValue = pricingMapping[value] || config.defaultValue;
          
          // 查找匹配的选项
          const option = Array.from(element.options).find(opt => 
            opt.value === targetValue || 
            opt.text === targetValue
          );
          
          if (option) {
            element.value = option.value;
            element.dispatchEvent(new Event('change', { bubbles: true }));
            console.log(`✓ 选择定价模式: ${option.text}`);
          }
        }
        break;
        
      default:
        element.value = value;
        element.dispatchEvent(new Event('input', { bubbles: true }));
        element.dispatchEvent(new Event('change', { bubbles: true }));
    }
  },
  
  // 多步骤表单处理
  multiStepConfig: {
    isMultiStep: true,
    totalSteps: 3,
    stepIndicators: '.e-form__indicators__indicator',
    nextButton: '.e-form__buttons__wrapper__button-next',
    previousButton: '.e-form__buttons__wrapper__button-previous',
    submitButton: 'button[type="submit"]',
    stepDelay: 1000,
    
    steps: [
      {
        stepNumber: 1,
        stepName: 'Basic Information',
        fields: ['siteName', 'tagLine', 'pricing', 'siteUrl']
      },
      {
        stepNumber: 2,
        stepName: 'Links',
        fields: ['facebookUrl', 'contactEmail', 'twitterUrl', 'instagramUrl', 'discordUrl', 'youtubeUrl']
      },
      {
        stepNumber: 3,
        stepName: 'Last Few Information',
        fields: ['siteDescription']
      }
    ]
  },

  // 提交流程配置
  submitConfig: {
    submitButton: [
      'button[type="submit"]',
      '.e-form__buttons__wrapper__button:not(.e-form__buttons__wrapper__button-next):not(.e-form__buttons__wrapper__button-previous)',
      'button:contains("Send")'
    ],
    submitMethod: 'click',
    waitAfterFill: 2000,
    waitAfterSubmit: 5000,
    successIndicators: [
      '.elementor-message-success',
      '.success-message',
      '.thank-you',
      '[class*="success"]'
    ],
    errorIndicators: [
      '.elementor-message-danger',
      '.error-message',
      '[class*="error"]'
    ]
  },

  // 特殊处理
  specialHandling: {
    requiresLogin: false,
    hasCaptcha: false,
    hasFileUpload: true, // 有文件上传字段
    isElementorForm: true, // 使用Elementor表单
    isMultiStepForm: true, // 多步骤表单

    // 表单验证规则
    formValidation: {
      requiredFields: ['siteName', 'tagLine', 'pricing', 'siteUrl', 'contactEmail'],
      emailValidation: true,
      urlValidation: true,
      fileUploadFields: ['toolLogo', 'featuredImage']
    },

    // 特殊注意事项
    notes: [
      '这是WordPress网站，使用Elementor多步骤表单',
      '表单分为3个步骤：基本信息、链接、最后信息',
      '包含文件上传字段：Tool Logo和Featured Image',
      '第一步：4个字段（工具名称、标语、定价、网站链接）',
      '第二步：6个字段（社交媒体链接和邮箱）',
      '第三步：3个字段（描述和2个文件上传）',
      '定价选项：FREE, FREEMIUM, PAID',
      '大部分社交媒体链接是可选的',
      '需要逐步点击Next按钮进行下一步',
      '最后一步包含文件上传，可能需要特殊处理'
    ]
  }
};

// 自定义处理函数
export function handleAICyclopediaSubmission(data, _rule) {
  console.log('Processing AI Cyclopedia form submission...');

  const processedData = { ...data };

  // 确保URL格式正确
  if (processedData.siteUrl && !processedData.siteUrl.startsWith('http')) {
    processedData.siteUrl = 'https://' + processedData.siteUrl;
  }

  // 处理社交媒体URL
  const socialFields = ['facebookUrl', 'twitterUrl', 'instagramUrl', 'discordUrl', 'youtubeUrl'];
  socialFields.forEach(field => {
    if (processedData[field] && !processedData[field].startsWith('http')) {
      processedData[field] = 'https://' + processedData[field];
    }
  });

  // 设置默认值
  processedData.tagLine = 'Innovative AI Tool for Enhanced Productivity';
  processedData.pricing = 'FREE';

  // 移除空的可选字段
  const optionalFields = ['facebookUrl', 'instagramUrl', 'discordUrl', 'youtubeUrl'];
  optionalFields.forEach(field => {
    if (!processedData[field]) {
      delete processedData[field];
    }
  });

  return processedData;
}

// 多步骤表单处理函数
export async function handleMultiStepForm(rule, data) {
  console.log('处理多步骤表单提交...');

  const { multiStepConfig } = rule;

  for (let i = 0; i < multiStepConfig.totalSteps; i++) {
    const step = multiStepConfig.steps[i];
    console.log(`处理第${step.stepNumber}步: ${step.stepName}`);

    // 填写当前步骤的字段
    for (const fieldName of step.fields) {
      const fieldConfig = rule.fieldMappings[fieldName];
      if (fieldConfig && data[fieldName]) {
        const element = document.querySelector(fieldConfig.selectors[0]);
        if (element) {
          await rule.customFillElement(element, data[fieldName], fieldConfig);
          await new Promise(resolve => setTimeout(resolve, 200));
        }
      }
    }

    // 如果不是最后一步，点击Next按钮
    if (i < multiStepConfig.totalSteps - 1) {
      const nextButton = document.querySelector(multiStepConfig.nextButton);
      if (nextButton) {
        nextButton.click();
        console.log(`点击Next按钮，进入第${i + 2}步`);
        await new Promise(resolve => setTimeout(resolve, multiStepConfig.stepDelay));
      }
    }
  }

  console.log('多步骤表单填写完成');
}
