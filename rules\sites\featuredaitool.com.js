// FeaturedAITool.com 网站规则配置
// 网站: https://featuredaitool.com/submit/
// 最后更新: 2025-07-06

export const SITE_RULE = {
  // 基本信息
  domain: 'featuredaitool.com',
  siteName: 'FeaturedAITool',
  priority: 1,
  lastUpdated: '2025-07-06',
  
  // 字段映射规则
  fieldMappings: {
    // 联系人姓名 -> Name
    fullName: {
      selectors: [
        'input[name="fname"]',
        '#nf-field-5',
        'input.ninja-forms-field[autocomplete="given-name"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '提交者姓名'
    },
    
    // 联系邮箱 -> Email
    contactEmail: {
      selectors: [
        'input[name="email"]',
        '#nf-field-7',
        'input[type="email"].ninja-forms-field'
      ],
      method: 'value',
      validation: 'required|email',
      notes: '联系邮箱地址'
    },
    
    // 国家 -> Country
    country: {
      selectors: [
        'select[name="nf-field-8"]',
        '#nf-field-8',
        'select.ninja-forms-field'
      ],
      method: 'select',
      validation: 'required',
      defaultValue: 'US', // United States
      notes: '国家选择，默认选择United States'
    },
    
    // 工具名称 -> Tool Name
    siteName: {
      selectors: [
        '#nf-field-9',
        'input.ninja-forms-field[id="nf-field-9"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '工具名称'
    },
    
    // 网站URL -> Website URL
    siteUrl: {
      selectors: [
        'input[name="address"]',
        '#nf-field-10',
        'input.ninja-forms-field[autocomplete="street-address"][id="nf-field-10"]'
      ],
      method: 'value',
      validation: 'required|url',
      notes: '工具网站URL'
    },
    
    // 工具分类 -> Category
    category: {
      selectors: [
        '#nf-field-11',
        'input.ninja-forms-field[id="nf-field-11"]'
      ],
      method: 'value',
      validation: 'required',
      defaultValue: 'AI Tools', // 默认分类
      notes: '工具分类'
    },
    
    // 定价模式 -> Pricing Model (复选框)
    pricing: {
      selectors: [
        '#nf-field-12-0',
        'input[name="nf-field-12"][value="free"]',
        'input[type="checkbox"][value="free"]'
      ],
      method: 'checkbox',
      validation: 'required',
      defaultValue: 'free', // 默认选择Free
      targetValue: 'free', // 明确指定要选择的值
      options: ['free', 'paid', 'freemium', 'free-trial', 'open-source'],
      notes: '定价模式，默认选择Free复选框'
    },
    
    // 工具描述 -> Tool Description
    siteDescription: {
      selectors: [
        'textarea[name="nf-field-13"]',
        '#nf-field-13',
        'textarea.ninja-forms-field'
      ],
      method: 'value',
      validation: 'required',
      notes: '工具功能描述'
    }
  },

  // 自定义填写函数
  customFillElement: async function(element, value, config) {
    console.log(`FeaturedAITool自定义填写: ${element.id}, 方法: ${config.method}`);

    switch (config.method) {
      case 'value':
        // 特殊处理邮箱字段的默认值
        if (element.type === 'email' && element.value.includes('please provide')) {
          console.log('清除邮箱默认值');
          element.focus();
          element.select();
          element.value = '';
          element.dispatchEvent(new Event('input', { bubbles: true }));
          await new Promise(resolve => setTimeout(resolve, 100));
        }

        element.value = value;
        element.dispatchEvent(new Event('input', { bubbles: true }));
        element.dispatchEvent(new Event('change', { bubbles: true }));
        break;

      case 'checkbox':
        // Ninja Forms复选框特殊处理
        const targetValue = config.targetValue || config.defaultValue || value;
        if (element.value === targetValue) {
          // 先取消选中其他同名复选框
          const sameNameBoxes = document.querySelectorAll(`input[name="${element.name}"]`);
          sameNameBoxes.forEach(box => {
            if (box !== element) {
              box.checked = false;
              box.classList.remove('nf-checked');
              const label = document.querySelector(`label[for="${box.id}"]`);
              if (label) label.classList.remove('nf-checked-label');
            }
          });

          // 选中目标复选框
          element.checked = true;
          element.classList.add('nf-checked');

          // 添加标签样式
          const targetLabel = document.querySelector(`label[for="${element.id}"]`);
          if (targetLabel) {
            targetLabel.classList.add('nf-checked-label');
          }

          // 为外层容器添加通过验证的样式
          const wrapper = element.closest('[data-field-id]');
          if (wrapper) {
            wrapper.classList.add('nf-pass');
          }

          element.dispatchEvent(new Event('change', { bubbles: true }));
          console.log(`✓ 选中Ninja Forms复选框: ${element.value}`);
        }
        break;

      default:
        // 默认处理
        element.value = value;
        element.dispatchEvent(new Event('input', { bubbles: true }));
        element.dispatchEvent(new Event('change', { bubbles: true }));
    }
  },

  // 提交流程配置
  submitConfig: {
    submitButton: 'input[type="submit"].ninja-forms-field, button[type="submit"].ninja-forms-field',
    submitMethod: 'click',
    waitAfterFill: 2000, // 填写后等待2秒
    waitAfterSubmit: 5000, // 提交后等待5秒
    successIndicators: [
      '.ninja-forms-success-msg',
      '.nf-success',
      '.success-message'
    ],
    errorIndicators: [
      '.ninja-forms-error-msg',
      '.nf-error',
      '.error-message'
    ]
  },
  
  // 特殊处理
  specialHandling: {
    requiresLogin: false,
    hasCaptcha: false,
    hasFileUpload: false,
    
    // 表单验证规则
    formValidation: {
      requiredFields: ['fullName', 'contactEmail', 'country', 'siteName', 'siteUrl', 'category', 'pricing', 'siteDescription'],
      emailValidation: true,
      urlValidation: true
    },
    
    // 特殊注意事项
    notes: [
      '表单使用Ninja Forms插件',
      '有7个必填字段',
      '邮箱字段有默认占位文本，需要先清空再填写',
      '定价模式使用复选框，默认选择Free',
      '国家默认选择United States',
      '分类默认使用AI Tools'
    ]
  }
};

// 自定义处理函数
export function handleFeaturedAIToolSubmission(data, rule) {
  console.log('Processing FeaturedAITool.com submission...');
  
  // 特殊处理逻辑
  const processedData = { ...data };
  
  // 确保URL格式正确
  if (processedData.siteUrl && !processedData.siteUrl.startsWith('http')) {
    processedData.siteUrl = 'https://' + processedData.siteUrl;
  }
  
  // 设置默认国家
  if (!processedData.country) {
    processedData.country = 'US'; // United States
  }
  
  // 设置默认分类
  if (!processedData.category) {
    processedData.category = 'AI Tools';
  }
  
  // 设置默认定价模式
  if (!processedData.pricing) {
    processedData.pricing = 'free';
  }
  
  return processedData;
}
