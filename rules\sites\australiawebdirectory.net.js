// australiawebdirectory.net 网站规则配置
// 网站: https://www.australiawebdirectory.net/submit.php
// 表单技术: PHP Form with CAPTCHA
// 最后更新: 2025-07-08

export const SITE_RULE = {
  // 基本信息
  domain: 'australiawebdirectory.net',
  siteName: 'Australia Web Directory',
  priority: 1,
  lastUpdated: '2025-07-08',
  
  // 字段映射规则
  fieldMappings: {
    // 定价选项 -> Pricing
    linkType: {
      selectors: [
        'input[name="LINK_TYPE"][value="normal"]',
        'input[type="radio"][value="normal"]',
        'input[value="normal"]'
      ],
      method: 'radio',
      validation: 'required',
      defaultValue: 'normal',
      availableOptions: ['featured', 'normal', 'reciprocal'],
      notes: '定价选项，默认选择normal (Regular links)'
    },

    // 标题 -> Title
    siteName: {
      selectors: [
        'input[name="TITLE"]',
        'input.text:first-of-type',
        'input[maxlength="100"]',
        'input[size="40"]:first-of-type'
      ],
      method: 'value',
      validation: 'required',
      notes: '网站标题，使用website-info.js中的siteName字段'
    },
    
    // URL -> URL
    siteUrl: {
      selectors: [
        'input[name="URL"]',
        'input[maxlength="255"]',
        'input.text:nth-of-type(2)',
        'input[size="40"]:nth-of-type(2)'
      ],
      method: 'value',
      validation: 'optional|url',
      notes: '网站URL，使用website-info.js中的siteUrl字段'
    },
    
    // 描述 -> Description
    siteDescription: {
      selectors: [
        'textarea[name="DESCRIPTION"]',
        'textarea.text',
        'textarea[rows="3"]',
        'textarea[cols="37"]'
      ],
      method: 'value',
      validation: 'optional',
      maxLength: 1000,
      notes: '网站描述，使用website-info.js中的siteDescription字段，限制1000字符'
    },
    
    // 您的姓名 -> Your Name
    fullName: {
      selectors: [
        'input[name="OWNER_NAME"]',
        'input[maxlength="50"]',
        'input.text:nth-of-type(3)',
        'input[size="40"]:nth-of-type(3)'
      ],
      method: 'value',
      validation: 'required',
      notes: '您的姓名，使用website-info.js中的fullName字段'
    },
    
    // 您的邮箱 -> Your Email
    contactEmail: {
      selectors: [
        'input[name="OWNER_EMAIL"]',
        'input.text:nth-of-type(4)',
        'input[size="40"]:nth-of-type(4)',
        'input[maxlength="255"]:nth-of-type(2)'
      ],
      method: 'value',
      validation: 'required|email',
      notes: '您的邮箱，使用website-info.js中的contactEmail字段'
    },

    // 分类 -> Category
    category: {
      selectors: [
        'select[name="CATEGORY_ID"]',
        'select:has(option[value="453"])',
        'form select:first-of-type'
      ],
      method: 'select',
      validation: 'required',
      defaultValue: '453', // Work
      notes: '分类，默认选择Work (value=453)'
    },
    
    // 分类 -> Category
    category: {
      selectors: [
        'select[name="category"]',
        'select:contains("Business & Economy")',
        'form select:first-of-type'
      ],
      method: 'select',
      validation: 'required',
      defaultValue: 'Business & Economy',
      notes: '分类，默认选择Business & Economy'
    },
    
    // 互惠链接URL -> Reciprocal Link URL
    reciprocalUrl: {
      selectors: [
        'input[name="reciprocal_url"]',
        'input[placeholder*="Reciprocal"]',
        'form input[type="text"]:nth-of-type(4)'
      ],
      method: 'value',
      validation: 'optional',
      notes: '互惠链接URL，可选，用于互惠链接提交'
    },
    
    // 验证码 -> Enter the code shown
    captcha: {
      selectors: [
        'input[name="captcha"]',
        'input[placeholder*="code"]',
        'form input[type="text"]:last-of-type'
      ],
      method: 'captcha',
      validation: 'required',
      notes: '验证码，需要手动输入图片中显示的代码'
    },
    
    // 提交规则协议 -> Submission Rules Agreement
    rulesAgreement: {
      selectors: [
        'input[name="AGREERULES"]',
        'input[id="AGREERULES"]',
        'input[type="checkbox"]'
      ],
      method: 'checkbox',
      validation: 'required',
      defaultValue: true,
      notes: '提交规则协议，必须勾选同意'
    }
  },
  
  // 自定义填写函数
  customFillElement: async function(element, value, config) {
    console.log(`Australia Web Directory自定义填写: ${element.name || element.type}, 方法: ${config.method}`);
    
    switch (config.method) {
      case 'value':
        // 标准输入框和文本域处理
        element.focus();
        await new Promise(resolve => setTimeout(resolve, 100));
        
        // 清空现有内容
        element.value = '';
        await new Promise(resolve => setTimeout(resolve, 50));
        
        // 设置新值
        element.value = value;
        
        // 触发事件
        element.dispatchEvent(new Event('input', { bubbles: true }));
        element.dispatchEvent(new Event('change', { bubbles: true }));
        element.dispatchEvent(new Event('blur', { bubbles: true }));
        
        console.log(`✓ 填写字段: ${element.name} = "${value.substring(0, 50)}..."`);
        break;
        
      case 'radio':
        // 单选按钮处理
        console.log(`处理定价选项，目标值: ${config.defaultValue}`);

        // 查找所有同名单选按钮
        const radioButtons = document.querySelectorAll('input[name="LINK_TYPE"]');

        // 先取消所有选择
        radioButtons.forEach(rb => {
          rb.checked = false;
        });

        // 选择目标选项
        const targetRadio = Array.from(radioButtons).find(rb =>
          rb.value === config.defaultValue
        );

        if (targetRadio) {
          targetRadio.checked = true;
          targetRadio.dispatchEvent(new Event('change', { bubbles: true }));
          console.log(`✓ 选择定价选项: Regular links (${config.defaultValue})`);
        } else {
          console.log(`⚠️ 未找到定价选项: ${config.defaultValue}`);
        }
        break;

      case 'select':
        // 下拉选择处理
        element.focus();
        await new Promise(resolve => setTimeout(resolve, 100));

        const targetValue = config.defaultValue;

        // 查找匹配的选项（按value值匹配）
        const option = Array.from(element.options).find(opt =>
          opt.value === targetValue
        );

        if (option) {
          element.value = option.value;
          element.dispatchEvent(new Event('change', { bubbles: true }));
          console.log(`✓ 选择分类: Work (value: ${option.value})`);
        } else {
          console.log(`⚠️ 未找到分类值: ${targetValue}`);
          // 尝试按文本匹配作为备选
          const textOption = Array.from(element.options).find(opt =>
            opt.text.includes('Work')
          );
          if (textOption) {
            textOption.selected = true;
            element.dispatchEvent(new Event('change', { bubbles: true }));
            console.log(`✓ 备选匹配: ${textOption.text} (value: ${textOption.value})`);
          }
        }
        break;
        
      case 'checkbox':
        // 复选框处理
        element.checked = config.defaultValue;
        element.dispatchEvent(new Event('change', { bubbles: true }));
        console.log(`✓ 勾选协议: ${config.defaultValue}`);
        break;
        
      case 'captcha':
        // 验证码处理
        console.log('⚠️ 验证码字段需要手动处理');
        console.log('请查看页面上的验证码图片并手动输入');
        break;
        
      default:
        element.value = value;
        element.dispatchEvent(new Event('input', { bubbles: true }));
        element.dispatchEvent(new Event('change', { bubbles: true }));
    }
  },
  
  // 提交流程配置
  submitConfig: {
    submitButton: [
      'button[type="submit"]',
      'input[type="submit"]',
      'button:contains("Submit")',
      'input[value*="Submit"]'
    ],
    submitMethod: 'click',
    waitAfterFill: 2000,
    waitAfterSubmit: 5000,
    successIndicators: [
      'text:contains("submitted")',
      'text:contains("thank you")',
      'text:contains("success")',
      'text:contains("approved")'
    ],
    errorIndicators: [
      'text:contains("error")',
      'text:contains("required")',
      'text:contains("invalid")',
      'text:contains("captcha")'
    ]
  },
  
  // 特殊处理
  specialHandling: {
    requiresLogin: false,
    hasCaptcha: true, // 有验证码
    hasFileUpload: false,
    isPHPForm: true, // PHP表单
    isAustralianDirectory: true, // 澳大利亚目录
    hasReciprocalLink: true, // 有互惠链接选项
    hasPaidOptions: true, // 有付费选项
    hasRulesAgreement: true, // 有规则协议
    
    // 表单验证规则
    formValidation: {
      requiredFields: ['linkType', 'siteName', 'fullName', 'contactEmail', 'category', 'rulesAgreement'],
      optionalFields: ['siteUrl', 'siteDescription', 'reciprocalUrl', 'captcha'],
      emailValidation: true,
      urlValidation: true,
      captchaRequired: false, // 验证码不需要自动填写
      agreementRequired: true,
      characterLimits: {
        siteDescription: 1000
      },
      radioGroups: ['linkType'],
      selectFields: ['category']
    },
    
    // 特殊注意事项
    notes: [
      '这是Australia Web Directory的网站提交表单',
      '表单包含7个字段：6个必填，1个可选',
      '澳大利亚网站目录，接受全球网站',
      '验证码不需要自动填写，手动处理',
      '必须同意提交规则协议',
      '默认选择Regular links（免费，3-4个月审核）',
      '默认选择Work分类（value=453）',
      '有付费选项：Featured links $12.95/年',
      '免费选项：Regular links（3-4个月审核）',
      '互惠链接：Regular links with reciprocal（2-3个月审核）',
      '描述限制1000字符',
      '13个主要分类，687个子分类',
      '活跃链接3631个，待审核20234个',
      '使用实际字段名：LINK_TYPE, TITLE, URL, DESCRIPTION, OWNER_NAME, OWNER_EMAIL, CATEGORY_ID, AGREERULES'
    ]
  }
};

// 自定义处理函数
export function handleAustraliaWebDirectorySubmission(data, _rule) {
  console.log('Processing Australia Web Directory form submission...');

  const processedData = { ...data };

  // 确保URL格式正确
  if (processedData.siteUrl && !processedData.siteUrl.startsWith('http')) {
    processedData.siteUrl = 'https://' + processedData.siteUrl;
  }

  // 处理描述字符限制
  if (processedData.siteDescription && processedData.siteDescription.length > 1000) {
    processedData.siteDescription = processedData.siteDescription.substring(0, 1000);
  }

  // 设置默认值
  processedData.linkType = 'normal'; // Regular links
  processedData.category = '453'; // Work
  processedData.rulesAgreement = true;

  return processedData;
}

// 澳大利亚目录信息提醒
export function showAustraliaWebDirectoryInfo() {
  console.log('🇦🇺 Australia Web Directory 信息:');
  console.log('');
  console.log('平台特色:');
  console.log('- 澳大利亚专业网站目录');
  console.log('- 接受全球网站提交');
  console.log('- 13个主要分类，687个子分类');
  console.log('- 活跃链接3631个');
  console.log('');
  console.log('提交选项 (3种):');
  console.log('1. Featured links - $12.95/年');
  console.log('   - 几小时内批准');
  console.log('   - 显示在其他链接之上');
  console.log('');
  console.log('2. Regular links - 免费');
  console.log('   - 3-4个月审核');
  console.log('   - 不保证审核通过');
  console.log('');
  console.log('3. Regular links with reciprocal - 免费');
  console.log('   - 2-3个月审核');
  console.log('   - 需要在首页放置互惠链接');
  console.log('   - 不保证审核通过');
  console.log('');
  console.log('Australia Web Directory - 澳大利亚权威网站目录！');
}

// 分类选择帮助
export function showCategoryHelp() {
  console.log('📂 分类选择帮助:');
  console.log('');
  console.log('主要分类 (13个):');
  console.log('- Arts & Humanities (艺术人文)');
  console.log('- Blogs (博客)');
  console.log('- Business & Economy (商业经济) - 推荐');
  console.log('- Computers & Internet (计算机互联网)');
  console.log('- Education (教育)');
  console.log('- Entertainment (娱乐)');
  console.log('- Health (健康)');
  console.log('- News & Media (新闻媒体)');
  console.log('- Recreation & Sports (娱乐体育)');
  console.log('- Reference (参考)');
  console.log('- Science and Technology (科学技术)');
  console.log('- Shopping (购物)');
  console.log('- Society (社会)');
  console.log('');
  console.log('每个主要分类下有多个子分类可选！');
}

// 互惠链接说明
export function showReciprocalLinkInfo() {
  console.log('🔗 互惠链接说明:');
  console.log('');
  console.log('互惠链接要求:');
  console.log('- 必须放置在网站首页（index页面）');
  console.log('- 使用指定的HTML代码');
  console.log('- 链接必须可见且可点击');
  console.log('');
  console.log('互惠链接代码:');
  console.log('<a href="https://www.australiawebdirectory.net/business_and_economy/">Australia</a>');
  console.log('');
  console.log('优势:');
  console.log('- 审核时间缩短（2-3个月 vs 3-4个月）');
  console.log('- 提高审核通过率');
  console.log('- 建立双向链接关系');
  console.log('- 增加网站权重');
  console.log('');
  console.log('注意：互惠链接是可选的！');
}

// 验证码处理提醒
export function showCaptchaReminder() {
  console.log('🔒 验证码处理提醒:');
  console.log('');
  console.log('验证码要求:');
  console.log('- 查看页面上的验证码图片');
  console.log('- 输入图片中显示的代码');
  console.log('- 区分大小写');
  console.log('- 防止自动注册');
  console.log('');
  console.log('操作步骤:');
  console.log('1. 填写完所有其他字段');
  console.log('2. 查看验证码图片');
  console.log('3. 在验证码输入框中输入代码');
  console.log('4. 勾选同意提交规则');
  console.log('5. 点击提交按钮');
  console.log('');
  console.log('请手动完成验证码输入！');
}

// 提交规则说明
export function showSubmissionRules() {
  console.log('📋 提交规则说明:');
  console.log('');
  console.log('基本要求:');
  console.log('- 网站内容必须合法');
  console.log('- 不接受成人内容');
  console.log('- 不接受垃圾网站');
  console.log('- 网站必须可正常访问');
  console.log('');
  console.log('互惠链接要求:');
  console.log('- 如选择互惠，链接必须在首页');
  console.log('- 使用指定的HTML代码');
  console.log('- 链接必须持续有效');
  console.log('');
  console.log('审核说明:');
  console.log('- 人工审核，不保证通过');
  console.log('- 审核时间较长（2-4个月）');
  console.log('- 重复提交可能被拒绝');
  console.log('');
  console.log('必须勾选"I AGREE with the submission rules"！');
}

// 表单验证
export function validateAustraliaWebDirectoryForm() {
  console.log('验证Australia Web Directory表单...');

  const requiredFields = [
    { selector: 'input[name="title"]', label: '网站标题' },
    { selector: 'input[name="your_name"]', label: '您的姓名' },
    { selector: 'input[name="your_email"]', label: '您的邮箱' },
    { selector: 'select[name="category"]', label: '分类' },
    { selector: 'input[name="captcha"]', label: '验证码' }
  ];

  let isValid = true;

  requiredFields.forEach(field => {
    const element = document.querySelector(field.selector);
    if (!element || !element.value.trim()) {
      console.log(`⚠️ 必填字段为空: ${field.label}`);
      isValid = false;
    }
  });

  // 检查协议勾选
  const agreement = document.querySelector('input[type="checkbox"]');
  if (agreement && !agreement.checked) {
    console.log('⚠️ 请勾选同意提交规则');
    isValid = false;
  }

  // 检查验证码
  const captcha = document.querySelector('input[name="captcha"]');
  if (captcha && !captcha.value.trim()) {
    console.log('⚠️ 请输入验证码');
    isValid = false;
  }

  if (isValid) {
    console.log('✓ 表单验证通过');
  }

  return isValid;
}

// 澳大利亚目录统计
export function showDirectoryStats() {
  console.log('📊 目录统计信息:');
  console.log('');
  console.log('当前统计:');
  console.log('- 活跃链接: 3,631个');
  console.log('- 待审核链接: 20,234个');
  console.log('- 今日新增: 60个');
  console.log('- 待审核文章: 370篇');
  console.log('- 总文章数: 1篇');
  console.log('- 总分类数: 13个');
  console.log('- 子分类数: 687个');
  console.log('');
  console.log('审核队列:');
  console.log('- 待审核链接较多，请耐心等待');
  console.log('- 付费选项可快速审核');
  console.log('- 互惠链接可优先处理');
}
