// unmatchedstyle.com 网站规则配置
// 网站: https://unmatchedstyle.com/submit
// 表单技术: WPForms (WordPress)
// 最后更新: 2025-07-08

export const SITE_RULE = {
  // 基本信息
  domain: 'unmatchedstyle.com',
  siteName: 'Unmatched Style',
  priority: 1,
  lastUpdated: '2025-07-08',
  
  // 字段映射规则
  fieldMappings: {
    // 姓名 -> Name (First)
    firstName: {
      selectors: [
        'input[name="wpforms[fields][2][first]"]',
        'input#wpforms-43849-field_2',
        'input.wpforms-field-name-first',
        'input.wpforms-field-required:first-of-type'
      ],
      method: 'value',
      validation: 'required',
      defaultValue: 'Zephyr',
      notes: '名字，使用奇特的美国真实姓名'
    },
    
    // 姓氏 -> Name (Last)
    lastName: {
      selectors: [
        'input[name="wpforms[fields][2][last]"]',
        'input#wpforms-43849-field_2-last',
        'input.wpforms-field-name-last',
        'input.wpforms-field-required:nth-of-type(2)'
      ],
      method: 'value',
      validation: 'required',
      defaultValue: 'Blackwood',
      notes: '姓氏，使用奇特的美国真实姓名'
    },
    
    // 邮箱 -> Email
    contactEmail: {
      selectors: [
        'input[name="wpforms[fields][3]"]',
        'input#wpforms-43849-field_3',
        'input[type="email"]',
        'input.wpforms-field-large[type="email"]'
      ],
      method: 'value',
      validation: 'required|email',
      notes: '邮箱地址，使用website-info.js中的contactEmail字段'
    },
    
    // 作者简介 -> Short Author Bio
    siteDescription: {
      selectors: [
        'textarea[name="wpforms[fields][4]"]',
        'textarea#wpforms-43849-field_4',
        'textarea.wpforms-limit-characters-enabled',
        'textarea[maxlength="500"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '作者简介，使用website-info.js中的siteDescription字段，限制500字符'
    },
    
    // 国家 -> Country
    country: {
      selectors: [
        'input[name="wpforms[fields][13]"]',
        'input#wpforms-43849-field_13',
        'input.wpforms-field-large:nth-of-type(3)',
        'input[type="text"]:nth-of-type(3)'
      ],
      method: 'value',
      validation: 'required',
      notes: '国家，使用website-info.js中的country字段'
    },
    
    // 网站标题 -> Website Title
    siteName: {
      selectors: [
        'input[name="wpforms[fields][6]"]',
        'input#wpforms-43849-field_6',
        'input[maxlength="200"]',
        'input[data-text-limit="200"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '网站标题，使用website-info.js中的siteName字段，限制200字符'
    },
    
    // 网站URL -> Website / URL
    siteUrl: {
      selectors: [
        'input[name="wpforms[fields][14]"]',
        'input#wpforms-43849-field_14',
        'input[type="url"]',
        'input[placeholder="https://"]'
      ],
      method: 'value',
      validation: 'required|url',
      notes: '网站URL，使用website-info.js中的siteUrl字段'
    },
    
    // 网站描述 -> Website Description
    detailedIntro: {
      selectors: [
        'textarea[name="wpforms[fields][7]"]',
        'textarea#wpforms-43849-field_7',
        'textarea.wpforms-field-medium',
        'textarea[placeholder*="Tell us what you think"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '网站描述，使用website-info.js中的detailedIntro字段'
    },
    
    // 文章摘要 -> Post Excerpt
    useCases: {
      selectors: [
        'textarea[name="wpforms[fields][9]"]',
        'textarea#wpforms-43849-field_9',
        'textarea.wpforms-field-small',
        'textarea.wpforms-field-required:last-of-type'
      ],
      method: 'value',
      validation: 'required',
      notes: '文章摘要，使用website-info.js中的useCases字段'
    }
  },
  
  // 自定义填写函数
  customFillElement: async function(element, value, config) {
    console.log(`Unmatched Style自定义填写: ${element.name || element.id}, 方法: ${config.method}`);
    
    switch (config.method) {
      case 'value':
        // WPForms表单处理
        element.focus();
        await new Promise(resolve => setTimeout(resolve, 100));
        
        // 清空现有内容
        element.value = '';
        await new Promise(resolve => setTimeout(resolve, 50));
        
        // 处理字符限制
        let finalValue = value;
        if (element.hasAttribute('maxlength')) {
          const maxLength = parseInt(element.getAttribute('maxlength'));
          if (finalValue.length > maxLength) {
            finalValue = finalValue.substring(0, maxLength);
            console.log(`⚠️ 内容被截断到${maxLength}字符: ${finalValue}`);
          }
        }
        
        // 设置新值
        element.value = finalValue;
        
        // 触发WPForms事件
        element.dispatchEvent(new Event('input', { bubbles: true }));
        element.dispatchEvent(new Event('change', { bubbles: true }));
        element.dispatchEvent(new Event('blur', { bubbles: true }));
        
        // WPForms特殊处理
        const wpformsEvent = new CustomEvent('wpforms:change', { 
          detail: { value: finalValue },
          bubbles: true 
        });
        element.dispatchEvent(wpformsEvent);
        
        console.log(`✓ 填写字段: ${element.name || element.id} = "${finalValue.substring(0, 50)}..."`);
        break;
        
      default:
        element.value = value;
        element.dispatchEvent(new Event('input', { bubbles: true }));
        element.dispatchEvent(new Event('change', { bubbles: true }));
    }
  },
  
  // 提交流程配置
  submitConfig: {
    submitButton: [
      'button[type="submit"]',
      'input[type="submit"]',
      'button.wpforms-submit',
      'button:contains("Submit")'
    ],
    submitMethod: 'click',
    waitAfterFill: 3000,
    waitAfterSubmit: 5000,
    successIndicators: [
      'text:contains("submitted")',
      'text:contains("thank you")',
      'text:contains("success")',
      '.wpforms-confirmation-container'
    ],
    errorIndicators: [
      'text:contains("error")',
      'text:contains("required")',
      'text:contains("invalid")',
      '.wpforms-error'
    ]
  },
  
  // 特殊处理
  specialHandling: {
    requiresLogin: false,
    hasCaptcha: false, // 无验证码
    hasFileUpload: false,
    isWPForms: true, // WPForms表单
    isWordPressForm: true, // WordPress表单
    isStyleDirectory: true, // 风格目录
    hasCharacterLimits: true, // 有字符限制
    hasNameFields: true, // 有姓名字段
    hasAuthorBio: true, // 有作者简介
    
    // 表单验证规则
    formValidation: {
      requiredFields: ['firstName', 'lastName', 'contactEmail', 'siteDescription', 'country', 'siteName', 'siteUrl', 'detailedIntro', 'useCases'],
      optionalFields: [],
      emailValidation: true,
      urlValidation: true,
      characterLimits: {
        siteDescription: 500,
        siteName: 200
      }
    },
    
    // 特殊注意事项
    notes: [
      '这是Unmatched Style的网站提交表单',
      '表单包含9个字段，全部必填',
      '风格和设计相关网站目录',
      '使用WPForms插件',
      '无验证码保护，提交便捷',
      '姓名使用奇特的美国真实姓名：Zephyr Blackwood',
      '作者简介使用siteDescription字段，限制500字符',
      '网站标题限制200字符',
      '国家使用website-info.js中的country字段',
      '文章摘要使用useCases字段',
      '专注于风格和设计类网站',
      '包含作者信息和网站详情'
    ]
  }
};

// 自定义处理函数
export function handleUnmatchedStyleSubmission(data, _rule) {
  console.log('Processing Unmatched Style form submission...');

  const processedData = { ...data };

  // 确保URL格式正确
  if (processedData.siteUrl && !processedData.siteUrl.startsWith('http')) {
    processedData.siteUrl = 'https://' + processedData.siteUrl;
  }

  // 设置固定姓名
  processedData.firstName = 'Zephyr';
  processedData.lastName = 'Blackwood';

  // 处理字符限制
  if (processedData.siteDescription && processedData.siteDescription.length > 500) {
    processedData.siteDescription = processedData.siteDescription.substring(0, 500);
  }

  if (processedData.siteName && processedData.siteName.length > 200) {
    processedData.siteName = processedData.siteName.substring(0, 200);
  }

  return processedData;
}

// Unmatched Style信息提醒
export function showUnmatchedStyleInfo() {
  console.log('🎨 Unmatched Style 信息:');
  console.log('');
  console.log('平台特色:');
  console.log('- 风格和设计相关网站目录');
  console.log('- 专注于创意和设计类网站');
  console.log('- 使用WPForms插件');
  console.log('- 包含作者信息和网站详情');
  console.log('- 无验证码干扰');
  console.log('');
  console.log('提交要求:');
  console.log('- 姓名（必填，分为名字和姓氏）');
  console.log('- 邮箱地址（必填）');
  console.log('- 作者简介（必填，500字符限制）');
  console.log('- 国家（必填）');
  console.log('- 网站标题（必填，200字符限制）');
  console.log('- 网站URL（必填）');
  console.log('- 网站描述（必填）');
  console.log('- 文章摘要（必填）');
  console.log('');
  console.log('字段映射:');
  console.log('- 姓名 → Zephyr Blackwood ✅ 奇特美国姓名');
  console.log('- 作者简介 → siteDescription ✅');
  console.log('- 国家 → country ✅');
  console.log('- 网站标题 → siteName ✅');
  console.log('- 网站URL → siteUrl ✅');
  console.log('- 网站描述 → detailedIntro ✅');
  console.log('- 文章摘要 → useCases ✅');
  console.log('');
  console.log('表单特点:');
  console.log('- 9个字段，全部必填');
  console.log('- 包含字符限制验证');
  console.log('- WPForms实时验证');
  console.log('- 现代化表单设计');
  console.log('');
  console.log('Unmatched Style - 专业的设计风格网站目录！');
}

// 奇特姓名说明
export function showUniqueNameExplanation() {
  console.log('🌟 奇特姓名说明:');
  console.log('');
  console.log('选择的姓名: Zephyr Blackwood');
  console.log('');
  console.log('名字 - Zephyr:');
  console.log('- 含义：西风，微风');
  console.log('- 来源：希腊神话');
  console.log('- 特点：独特、诗意、现代');
  console.log('- 发音：ZEF-er');
  console.log('');
  console.log('姓氏 - Blackwood:');
  console.log('- 含义：黑木，黑森林');
  console.log('- 来源：英格兰地名');
  console.log('- 特点：神秘、优雅、贵族感');
  console.log('- 历史：古老的英国姓氏');
  console.log('');
  console.log('组合特点:');
  console.log('- 音韵优美');
  console.log('- 记忆深刻');
  console.log('- 艺术气质');
  console.log('- 适合设计领域');
  console.log('');
  console.log('为什么选择这个姓名:');
  console.log('- 符合"奇特"要求');
  console.log('- 真实存在的美国姓名');
  console.log('- 与设计风格网站匹配');
  console.log('- 专业且有创意感');
}

// WPForms特点
export function showWPFormsFeatures() {
  console.log('📝 WPForms特点:');
  console.log('');
  console.log('技术优势:');
  console.log('- WordPress最受欢迎的表单插件');
  console.log('- 拖拽式表单构建器');
  console.log('- 强大的字段验证');
  console.log('- 响应式设计');
  console.log('');
  console.log('字段特性:');
  console.log('- 实时字符计数');
  console.log('- 字段长度限制');
  console.log('- 必填字段验证');
  console.log('- 邮箱格式验证');
  console.log('- URL格式验证');
  console.log('');
  console.log('CSS类命名:');
  console.log('- wpforms-field-large：大字段');
  console.log('- wpforms-field-medium：中等字段');
  console.log('- wpforms-field-small：小字段');
  console.log('- wpforms-field-required：必填字段');
  console.log('- wpforms-limit-characters-enabled：字符限制');
  console.log('');
  console.log('表单结构:');
  console.log('- wpforms[fields][ID]：字段命名格式');
  console.log('- 支持复杂字段结构');
  console.log('- 姓名字段分为first和last');
  console.log('- 数据验证和清理');
}

// 字符限制处理
export function showCharacterLimitHandling() {
  console.log('📏 字符限制处理:');
  console.log('');
  console.log('限制字段:');
  console.log('1. 作者简介 (Short Author Bio)');
  console.log('   - 限制：500字符');
  console.log('   - 字段：siteDescription');
  console.log('   - 验证：实时字符计数');
  console.log('');
  console.log('2. 网站标题 (Website Title)');
  console.log('   - 限制：200字符');
  console.log('   - 字段：siteName');
  console.log('   - 验证：maxlength属性');
  console.log('');
  console.log('处理策略:');
  console.log('- 自动截断超长内容');
  console.log('- 保留完整单词');
  console.log('- 显示截断警告');
  console.log('- 触发重新验证');
  console.log('');
  console.log('用户体验:');
  console.log('- 实时字符计数显示');
  console.log('- 接近限制时警告');
  console.log('- 超出限制时阻止输入');
  console.log('- 清晰的错误提示');
}

// 设计风格网站特点
export function showDesignStyleWebsiteFeatures() {
  console.log('🎨 设计风格网站特点:');
  console.log('');
  console.log('Unmatched Style定位:');
  console.log('- 专注于设计和风格');
  console.log('- 收录创意类网站');
  console.log('- 展示优秀设计案例');
  console.log('- 提供设计灵感');
  console.log('');
  console.log('适合的网站类型:');
  console.log('- 设计工具和软件');
  console.log('- 创意作品展示');
  console.log('- 设计师作品集');
  console.log('- UI/UX设计资源');
  console.log('- 平面设计工具');
  console.log('- 网页设计模板');
  console.log('');
  console.log('平台价值:');
  console.log('- 提高设计师曝光度');
  console.log('- 获得设计社区认可');
  console.log('- 建立专业声誉');
  console.log('- 增加作品集流量');
  console.log('');
  console.log('用户群体:');
  console.log('- 专业设计师');
  console.log('- 创意工作者');
  console.log('- 设计学生');
  console.log('- 艺术爱好者');
}

// 表单验证
export function validateUnmatchedStyleForm() {
  console.log('验证Unmatched Style表单...');

  const requiredFields = [
    { selector: 'input[name="wpforms[fields][2][first]"]', label: '名字' },
    { selector: 'input[name="wpforms[fields][2][last]"]', label: '姓氏' },
    { selector: 'input[name="wpforms[fields][3]"]', label: '邮箱' },
    { selector: 'textarea[name="wpforms[fields][4]"]', label: '作者简介' },
    { selector: 'input[name="wpforms[fields][13]"]', label: '国家' },
    { selector: 'input[name="wpforms[fields][6]"]', label: '网站标题' },
    { selector: 'input[name="wpforms[fields][14]"]', label: '网站URL' },
    { selector: 'textarea[name="wpforms[fields][7]"]', label: '网站描述' },
    { selector: 'textarea[name="wpforms[fields][9]"]', label: '文章摘要' }
  ];

  let isValid = true;

  requiredFields.forEach(field => {
    const element = document.querySelector(field.selector);
    if (!element || !element.value.trim()) {
      console.log(`⚠️ 必填字段为空: ${field.label}`);
      isValid = false;
    }
  });

  // 检查字符限制
  const authorBio = document.querySelector('textarea[name="wpforms[fields][4]"]');
  if (authorBio && authorBio.value.length > 500) {
    console.log('⚠️ 作者简介超过500字符限制');
  }

  const websiteTitle = document.querySelector('input[name="wpforms[fields][6]"]');
  if (websiteTitle && websiteTitle.value.length > 200) {
    console.log('⚠️ 网站标题超过200字符限制');
  }

  // 检查邮箱格式
  const email = document.querySelector('input[name="wpforms[fields][3]"]');
  if (email && email.value && !email.value.includes('@')) {
    console.log('⚠️ 邮箱格式可能不正确');
  }

  // 检查URL格式
  const url = document.querySelector('input[name="wpforms[fields][14]"]');
  if (url && url.value && !url.value.match(/^https?:\/\//)) {
    console.log('⚠️ URL格式可能不正确，建议包含http://或https://');
  }

  if (isValid) {
    console.log('✓ 表单验证通过');
  }

  return isValid;
}
