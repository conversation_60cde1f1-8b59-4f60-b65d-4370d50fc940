// FindSEOTools.com 网站规则配置
// 网站: https://www.findseotools.com/submit
// 最后更新: 2025-07-24

export const SITE_RULE = {
  domain: 'www.findseotools.com',
  siteName: 'Find SEO Tools',
  priority: 1,
  lastUpdated: '2025-07-24',

  fieldMappings: {
    newOrOldTool: {
      selectors: [
        '#new-or-old-tool',
        'select[name="New-or-Old-Tool"]'
      ],
      method: 'select',
      validation: 'required',
      defaultValue: 'New Tool',
      notes: '新工具或已发布工具'
    },

    siteName: {
      selectors: [
        '#tool-name-2',
        'input[name="Tool-Name"]',
        'input[placeholder="e.g. Semrush"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '工具名称'
    },

    siteDescription: {
      selectors: [
        '#short-description',
        'input[name="Short-Description"]',
        'input[placeholder*="competitors marketing"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '9个词以内的简短描述'
    },

    category: {
      selectors: [
        '#Category-Options',
        'select[name="Category-Options"]'
      ],
      method: 'select',
      validation: 'required',
      defaultValue: 'tool-suite/Tool Suite',
      notes: '工具分类'
    },

    pricing: {
      selectors: [
        '#Pricing-Options',
        'select[name="Pricing-Options"]'
      ],
      method: 'select',
      validation: 'required',
      defaultValue: 'Free Options',
      notes: '定价模式'
    },

    siteUrl: {
      selectors: [
        '#tool-website-2',
        'input[name="Tool-Website"]',
        'input[placeholder="e.g. https://www.semrush.com/"]'
      ],
      method: 'value',
      validation: 'required|url',
      notes: '工具网站'
    },

    detailedIntro: {
      selectors: [
        '#long-description',
        'input[name="Long-Description"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '详细描述，1-2段'
    },

    twitterUrl: {
      selectors: [
        '#tool-twitter',
        'input[name="Tool-Twitter"]',
        'input[placeholder="e.g. https://twitter.com/semrush"]'
      ],
      method: 'value',
      validation: 'required|url',
      notes: 'Twitter账号'
    },

    launchYear: {
      selectors: [
        '#Tool-Launched-Year',
        'input[name="Tool-Launched-Year"]',
        'input[placeholder="e.g. 2008"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '发布年份'
    },

    logoUrl: {
      selectors: [
        '#tool-image-2',
        'input[name="Tool-Image"]'
      ],
      method: 'value',
      validation: 'optional|url',
      notes: '工具图片URL，1280x800px'
    },

    fullName: {
      selectors: [
        '#Submitter-Name-2',
        'input[name="Submitter-Name"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '提交者姓名'
    },

    contactEmail: {
      selectors: [
        '#Submitter-Email-2',
        'input[name="Submitter-Email"]',
        'input[type="email"]'
      ],
      method: 'value',
      validation: 'required|email',
      notes: '提交者邮箱'
    },

    founderInfo: {
      selectors: [
        '#Submitter-Optin-2',
        'input[name="Submitter-Optin"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '是否为创始人'
    }
  },

  submitConfig: {
    submitButton: 'input[type="submit"], .submit-button',
    submitMethod: 'click',
    successIndicators: ['.success-message-2'],
    errorIndicators: ['.error-message-2']
  },

  specialHandling: {
    requiresLogin: false,
    hasCaptcha: false,
    hasFileUpload: false,
    customScript: 'handleFindSEOToolsSubmission',
    formValidation: {
      requiredFields: ['newOrOldTool', 'siteName', 'siteDescription', 'category', 'pricing', 'siteUrl', 'detailedIntro', 'twitterUrl', 'launchYear', 'fullName', 'contactEmail', 'founderInfo'],
      emailValidation: true,
      urlValidation: true
    },
    notes: [
      '专业的SEO工具目录',
      '描述限制9个词',
      '需要Twitter账号',
      '需要发布年份',
      '图片要求1280x800px',
      '使用Webflow构建'
    ]
  }
};

export function handleFindSEOToolsSubmission(data, rule) {
  const processedData = { ...data };

  if (processedData.siteUrl && !processedData.siteUrl.startsWith('http')) {
    processedData.siteUrl = 'https://' + processedData.siteUrl;
  }

  if (processedData.twitterUrl && !processedData.twitterUrl.startsWith('http')) {
    processedData.twitterUrl = 'https://' + processedData.twitterUrl;
  }

  return processedData;
}

export async function customFillElement(element, value, config) {
  if (element.tagName === 'SELECT') {
    const options = element.querySelectorAll('option');
    let option;

    if (element.name === 'New-or-Old-Tool') {
      option = Array.from(options).find(opt => opt.value === 'New Tool');
    } else if (element.name === 'Category-Options') {
      option = Array.from(options).find(opt => opt.value === 'tool-suite/Tool Suite');
    } else if (element.name === 'Pricing-Options') {
      option = Array.from(options).find(opt => opt.value === 'Free Options');
    } else {
      option = Array.from(options).find(opt => opt.value === value);
    }

    if (option) {
      element.value = option.value;
      element.dispatchEvent(new Event('change', { bubbles: true }));
      return true;
    }
  }

  if (element.tagName === 'INPUT') {
    element.value = value;
    element.dispatchEvent(new Event('input', { bubbles: true }));
    return true;
  }

  return false;
}