// 2AGI.net 网站规则配置
// 网站: https://2agi.net/zh/submit
// 最后更新: 2025-07-24

export const SITE_RULE = {
  domain: '2agi.net',
  siteName: '2AGI',
  priority: 1,
  lastUpdated: '2025-07-24',

  fieldMappings: {
    // 产品名称 -> siteName
    siteName: {
      selectors: [
        'input[placeholder="请输入产品名称"]',
        'input[maxlength="100"]',
        '#el-id-1024-20'
      ],
      method: 'value',
      validation: 'required',
      notes: '产品名称，最多100字符'
    },

    // 英文名称 -> siteName (英文版)
    siteTitle: {
      selectors: [
        'input[placeholder="请输入英文名称"]',
        '#el-id-1024-21'
      ],
      method: 'value',
      validation: 'required',
      notes: '英文名称，最多100字符'
    },

    // 产品简介 -> siteDescription
    siteDescription: {
      selectors: [
        'input[placeholder="请输入产品简介，不超过30字"]',
        'input[maxlength="30"]',
        '#el-id-1024-22'
      ],
      method: 'value',
      validation: 'required',
      notes: '产品简介，最多30字符'
    },

    // 产品网站 -> siteUrl
    siteUrl: {
      selectors: [
        'input[placeholder="请输入产品网站"]',
        '#el-id-1024-23'
      ],
      method: 'value',
      validation: 'required|url',
      notes: '产品网站URL'
    },

    // 站点名称 -> siteName (重复使用)
    companyName: {
      selectors: [
        'input[placeholder="请输入站点名称"]',
        '#el-id-1024-24'
      ],
      method: 'value',
      validation: 'required',
      notes: '站点名称'
    },

    // 产品图片上传
    logoFile: {
      selectors: [
        '.el-upload__input[accept="image/*"]',
        'input[type="file"][accept="image/*"]'
      ],
      method: 'file',
      validation: 'optional',
      notes: '产品图片上传，支持jpg/png，不超过500kb'
    },

    // 产品分类 -> category
    category: {
      selectors: [
        '.el-select input[placeholder="请选择产品分类"]',
        '#el-id-1024-25'
      ],
      method: 'select',
      validation: 'optional',
      notes: '产品分类选择'
    },

    // 产品信息标题 -> 使用siteName
    productTitle: {
      selectors: [
        'input[placeholder="请输入标题"]',
        '#el-id-1024-26'
      ],
      method: 'value',
      validation: 'required',
      notes: '产品信息标题'
    },

    // 产品信息内容 -> detailedIntro
    detailedIntro: {
      selectors: [
        'textarea[placeholder="请输入内容"]',
        '#el-id-1024-27'
      ],
      method: 'value',
      validation: 'required',
      notes: '产品详细信息'
    },

    // 联系人姓名 -> fullName
    fullName: {
      selectors: [
        'input[placeholder="请输入您的姓名或称呼"]',
        '#el-id-1024-28'
      ],
      method: 'value',
      validation: 'required',
      notes: '联系人姓名'
    },

    // 微信 -> 可选字段
    wechatId: {
      selectors: [
        'input[placeholder="请输入您的微信"]',
        '#el-id-1024-29'
      ],
      method: 'value',
      validation: 'optional',
      notes: '微信号'
    },

    // Twitter -> twitterUrl
    twitterUrl: {
      selectors: [
        'input[placeholder="请输入您的Twitter"]',
        '#el-id-1024-30'
      ],
      method: 'value',
      validation: 'optional',
      notes: 'Twitter账号'
    },

    // 电话 -> phone
    phone: {
      selectors: [
        'input[placeholder="请输入您的电话"]',
        '#el-id-1024-31'
      ],
      method: 'value',
      validation: 'optional',
      notes: '联系电话'
    },

    // 邮箱 -> contactEmail
    contactEmail: {
      selectors: [
        'input[placeholder="请输入您的邮箱"]',
        '#el-id-1024-32'
      ],
      method: 'value',
      validation: 'optional|email',
      notes: '联系邮箱'
    },

    // 来源 -> 硬编码为"百度"
    source: {
      selectors: [
        'input[placeholder="您是从哪里了解到我们的产品"]',
        '#el-id-1024-33'
      ],
      method: 'value',
      validation: 'required',
      defaultValue: '百度',
      notes: '了解来源，硬编码为百度'
    }
  },

  submitConfig: {
    submitButton: '.el-button--primary:contains("提交"), button[type="button"]:contains("提交")',
    submitMethod: 'click',
    successIndicators: ['.success-message'],
    errorIndicators: ['.error-message']
  },

  specialHandling: {
    requiresLogin: false,
    hasCaptcha: false,
    hasFileUpload: true,
    customScript: 'handle2AGISubmission',
    formValidation: {
      requiredFields: ['siteName', 'siteTitle', 'siteDescription', 'siteUrl', 'companyName', 'productTitle', 'detailedIntro', 'fullName', 'source'],
      emailValidation: true,
      urlValidation: true
    },
    notes: [
      'AI产品提交平台',
      'Element Plus UI框架',
      '多个必填字段',
      '图片上传功能',
      '动态产品信息添加',
      '中文界面'
    ]
  }
};

export function handle2AGISubmission(data) {
  const processedData = { ...data };

  // 确保URL格式正确
  if (processedData.siteUrl && !processedData.siteUrl.startsWith('http')) {
    processedData.siteUrl = 'https://' + processedData.siteUrl;
  }

  // 处理Twitter URL格式
  if (processedData.twitterUrl && !processedData.twitterUrl.startsWith('http')) {
    processedData.twitterUrl = 'https://twitter.com/' + processedData.twitterUrl;
  }

  // 处理描述长度限制
  if (processedData.siteDescription && processedData.siteDescription.length > 30) {
    processedData.siteDescription = processedData.siteDescription.substring(0, 27) + '...';
  }

  // 确保来源字段为百度
  processedData.source = '百度';

  return processedData;
}

export async function customFillElement(element, value, config) {
  // 处理Element Plus下拉选择框
  if (element.classList.contains('el-select')) {
    // Element Plus下拉框需要特殊处理
    const selectWrapper = element.querySelector('.el-select__wrapper');
    if (selectWrapper) {
      selectWrapper.click();
      // 等待选项加载后选择
      setTimeout(() => {
        const options = document.querySelectorAll('.el-select-dropdown__item');
        const targetOption = Array.from(options).find(opt => opt.textContent.includes(value));
        if (targetOption) {
          targetOption.click();
        }
      }, 100);
      return true;
    }
  }

  // 处理文件上传
  if (element.type === 'file') {
    console.warn('文件上传需要手动操作');
    return false;
  }

  // 处理Element Plus输入框
  if (element.classList.contains('el-input__inner') || element.classList.contains('el-textarea__inner')) {
    element.value = value;
    element.dispatchEvent(new Event('input', { bubbles: true }));
    element.dispatchEvent(new Event('change', { bubbles: true }));
    // 触发Element Plus的更新
    element.dispatchEvent(new Event('blur', { bubbles: true }));
    return true;
  }

  if (element.tagName === 'INPUT' || element.tagName === 'TEXTAREA') {
    element.value = value;
    element.dispatchEvent(new Event('input', { bubbles: true }));
    element.dispatchEvent(new Event('change', { bubbles: true }));
    return true;
  }

  return false;
}