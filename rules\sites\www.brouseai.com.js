// BrouseAI.com 网站规则配置
// 网站: https://www.brouseai.com/submission/ai
// 最后更新: 2025-07-24

export const SITE_RULE = {
  domain: 'www.brouseai.com',
  siteName: 'Brouse AI',
  priority: 1,
  lastUpdated: '2025-07-24',

  fieldMappings: {
    contactEmail: {
      selectors: [
        'input[name="email"]',
        'input[placeholder="e.g. <EMAIL>"]',
        'label:contains("Email") + input'
      ],
      method: 'value',
      validation: 'required|email',
      notes: '联系邮箱'
    },

    siteName: {
      selectors: [
        'input[name="name"]',
        'input[placeholder="e.g. ChatGPT"]',
        'label:contains("AI Name") + input'
      ],
      method: 'value',
      validation: 'required',
      notes: 'AI工具名称'
    },

    siteUrl: {
      selectors: [
        'input[name="url"]',
        'input[placeholder="e.g. https://chatgpt.com/"]',
        'label:contains("AI Url") + input'
      ],
      method: 'value',
      validation: 'required|url',
      notes: 'AI工具URL'
    },

    tagline: {
      selectors: [
        'input[name="tagline"]',
        'input[placeholder="e.g. A free-to-use AI system"]',
        'label:contains("AI Tagline") + input'
      ],
      method: 'value',
      validation: 'required',
      notes: 'AI标语'
    },

    pricing: {
      selectors: [
        'select[name="pricing"]',
        'button[role="combobox"]',
        'label:contains("Pricing") + button'
      ],
      method: 'select',
      validation: 'required',
      defaultValue: 'free',
      notes: '定价模式'
    },

    siteDescription: {
      selectors: [
        '.tiptap.ProseMirror:first-of-type',
        '[data-placeholder*="detailed description"]',
        'div[contenteditable="true"]:first-of-type'
      ],
      method: 'innerHTML',
      validation: 'optional',
      notes: 'AI概述描述，富文本编辑器'
    },

    features: {
      selectors: [
        'p[data-placeholder*="core features"]',
        'label:contains("AI Core Features") ~ div .tiptap.ProseMirror',
        '.tiptap.ProseMirror p[data-placeholder*="core features"]'
      ],
      method: 'innerHTML',
      validation: 'optional',
      notes: '核心功能，富文本编辑器'
    },

    useCases: {
      selectors: [
        'p[data-placeholder*="use cases"]',
        'label:contains("AI Use Cases") ~ div .tiptap.ProseMirror',
        '.tiptap.ProseMirror p[data-placeholder*="use cases"]'
      ],
      method: 'innerHTML',
      validation: 'optional',
      notes: '使用案例，富文本编辑器'
    },

    faqQuestion: {
      selectors: [
        'input[placeholder="Question 1"]',
        '.bg-secondary\\/50 input:first-of-type',
        '.grid input[placeholder*="Question"]'
      ],
      method: 'value',
      validation: 'optional',
      notes: 'FAQ问题'
    },

    faqAnswer: {
      selectors: [
        'input[placeholder="Answer 1"]',
        '.bg-secondary\\/50 input:nth-of-type(2)',
        '.grid input[placeholder*="Answer"]'
      ],
      method: 'value',
      validation: 'optional',
      notes: 'FAQ答案'
    }
  },

  submitConfig: {
    submitButton: 'button[type="submit"], .submit-button',
    submitMethod: 'click',
    successIndicators: ['.success-message'],
    errorIndicators: ['.error-message']
  },

  specialHandling: {
    requiresLogin: false,
    hasCaptcha: false,
    hasFileUpload: false,
    customScript: 'handleBrouseAISubmission',
    formValidation: {
      requiredFields: ['contactEmail', 'siteName', 'siteUrl', 'tagline', 'pricing'],
      emailValidation: true,
      urlValidation: true
    },
    notes: [
      '使用现代化React表单',
      '有多个富文本编辑器',
      '支持AI自动生成内容',
      '有FAQ动态添加功能',
      '使用TipTap编辑器',
      '有定价模式选择'
    ]
  }
};

export function handleBrouseAISubmission(data, rule) {
  const processedData = { ...data };

  if (processedData.siteUrl && !processedData.siteUrl.startsWith('http')) {
    processedData.siteUrl = 'https://' + processedData.siteUrl;
  }

  // 自动选择Free定价选项
  const pricingButton = document.querySelector('button[role="combobox"]');
  if (pricingButton) {
    pricingButton.click();
    setTimeout(() => {
      const freeOption = document.querySelector('[data-value="free"]');
      if (freeOption) {
        freeOption.click();
      }
    }, 100);
  }

  return processedData;
}

export async function customFillElement(element, value, config) {
  // 处理TipTap富文本编辑器 - 通过placeholder属性定位
  if (element.hasAttribute('data-placeholder')) {
    const parentEditor = element.closest('.tiptap.ProseMirror');
    if (parentEditor) {
      parentEditor.innerHTML = `<p>${value}</p>`;
      parentEditor.dispatchEvent(new Event('input', { bubbles: true }));
      return true;
    }
  }

  // 处理TipTap富文本编辑器 - 直接定位编辑器
  if (element.classList.contains('tiptap') && element.classList.contains('ProseMirror')) {
    element.innerHTML = `<p>${value}</p>`;
    element.dispatchEvent(new Event('input', { bubbles: true }));
    return true;
  }

  // 处理FAQ输入框
  if (element.placeholder && (element.placeholder.includes('Question') || element.placeholder.includes('Answer'))) {
    element.value = value;
    element.dispatchEvent(new Event('input', { bubbles: true }));
    element.dispatchEvent(new Event('change', { bubbles: true }));
    return true;
  }

  // 处理下拉选择框
  if (element.getAttribute('role') === 'combobox') {
    element.click();
    return true;
  }

  if (element.tagName === 'INPUT') {
    element.value = value;
    element.dispatchEvent(new Event('input', { bubbles: true }));
    element.dispatchEvent(new Event('change', { bubbles: true }));
    return true;
  }

  return false;
}