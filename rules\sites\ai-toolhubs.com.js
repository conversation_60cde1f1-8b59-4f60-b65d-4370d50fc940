// AI-ToolHubs.com 网站规则配置
// 网站: https://ai-toolhubs.com/submit
// 最后更新: 2025-07-24

export const SITE_RULE = {
  // 基本信息
  domain: 'ai-toolhubs.com',
  siteName: 'AI ToolHubs',
  priority: 1,
  lastUpdated: '2025-07-24',

  // 字段映射规则
  fieldMappings: {
    // 网站名称 -> Website Name
    siteName: {
      selectors: [
        'input[name="website_name"]',
        '#website_name',
        'input[id="website_name"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '网站名称'
    },

    // 网站URL -> Website URL
    siteUrl: {
      selectors: [
        'input[name="website_url"]',
        '#website_url',
        'input[id="website_url"]'
      ],
      method: 'value',
      validation: 'required|url',
      notes: '网站URL地址'
    },

    // 网站描述 -> Website Description
    siteDescription: {
      selectors: [
        'textarea[name="website_description"]',
        '#website_description',
        'textarea[id="website_description"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '网站描述'
    }
  },

  // 提交流程配置
  submitConfig: {
    submitButton: 'input[type="submit"], input[value="提交网站"]',
    submitMethod: 'manual', // 手动提交
    waitAfterFill: 0,
    waitAfterSubmit: 0,
    successIndicators: ['.success-message'],
    errorIndicators: ['.error-message']
  },

  // 特殊处理
  specialHandling: {
    requiresLogin: false,
    hasCaptcha: false,
    hasFileUpload: false,
    customScript: 'handleAiToolHubsSubmission',
    formValidation: {
      requiredFields: ['siteName', 'siteUrl', 'siteDescription'],
      emailValidation: false,
      urlValidation: true
    },
    notes: [
      'AI ToolHubs AI工具目录提交平台',
      '简洁的三字段表单',
      '使用Flask框架，包含CSRF保护',
      'Tailwind CSS样式',
      '中英文混合界面',
      '手动提交模式'
    ]
  }
};

// 自定义处理函数
export function handleAiToolHubsSubmission(data) {
  console.log('Processing AI ToolHubs form submission...');

  const processedData = { ...data };

  // 确保URL格式正确
  if (processedData.siteUrl && !processedData.siteUrl.startsWith('http')) {
    processedData.siteUrl = 'https://' + processedData.siteUrl;
  }

  return processedData;
}

// 自定义元素填写函数
export async function customFillElement(element, value, config) {
  console.log(`AI ToolHubs自定义填写: ${element.tagName}, 方法: ${config.method}`);

  switch (config.method) {
    case 'value':
      // 标准输入框和文本域处理
      element.focus();
      await new Promise(resolve => setTimeout(resolve, 200));

      element.value = value;
      element.dispatchEvent(new Event('input', { bubbles: true }));
      element.dispatchEvent(new Event('change', { bubbles: true }));
      element.dispatchEvent(new Event('blur', { bubbles: true }));

      console.log(`✓ 填写字段: ${element.name} = "${value}"`);
      return true;

    default:
      element.value = value;
      element.dispatchEvent(new Event('input', { bubbles: true }));
      return true;
  }

  return false;
}