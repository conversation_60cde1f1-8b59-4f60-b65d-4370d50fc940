// AIWikiWeb.com 网站规则配置
// 网站: https://aiwikiweb.com/submit-your-ai-tool/
// 最后更新: 2025-07-24

export const SITE_RULE = {
  // 基本信息
  domain: 'aiwikiweb.com',
  siteName: 'AI Wiki Web',
  priority: 1,
  lastUpdated: '2025-07-24',

  // 字段映射规则
  fieldMappings: {
    // 工具名称 -> Tool Name
    siteName: {
      selectors: [
        'input[name="tool-name"]',
        '.wpcf7-form-control[name="tool-name"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '工具名称，最多400字符'
    },

    // 工具描述 -> Tool Description
    siteDescription: {
      selectors: [
        'textarea[name="tool-description"]',
        '.wpcf7-form-control[name="tool-description"]'
      ],
      method: 'value',
      validation: 'optional',
      notes: '工具描述，最多2000字符'
    },

    // 联系邮箱 -> Contact Email ID
    contactEmail: {
      selectors: [
        'input[name="contact-email"]',
        'input[type="email"]'
      ],
      method: 'value',
      validation: 'required|email',
      notes: '联系邮箱，最多400字符'
    },

    // 价格 -> Price (Free, Paid or Both)
    pricing: {
      selectors: [
        'input[name="price"]',
        '.wpcf7-form-control[name="price"]'
      ],
      method: 'value',
      validation: 'optional',
      notes: '价格模式：免费、付费或两者皆有'
    },

    // 分类 -> Category
    category: {
      selectors: [
        'input[name="category-name"]',
        '.wpcf7-form-control[name="category-name"]'
      ],
      method: 'value',
      validation: 'optional',
      notes: '工具分类，最多400字符'
    },

    // 工具网站链接 -> Tool Website Link
    siteUrl: {
      selectors: [
        'input[name="tool-website-link"]',
        '.wpcf7-form-control[name="tool-website-link"]'
      ],
      method: 'value',
      validation: 'required|url',
      notes: '工具官方网站链接，最多400字符'
    },

    // 联盟注册链接 -> Affiliate Registration link
    affiliateUrl: {
      selectors: [
        'input[name="tool-affiliate-link"]',
        '.wpcf7-form-control[name="tool-affiliate-link"]'
      ],
      method: 'value',
      validation: 'optional|url',
      notes: '联盟注册链接，最多400字符'
    },

    // 工具截图 -> Tool Screenshot
    logoFile: {
      selectors: [
        'input[name="tool-screenshot-765"]',
        'input[type="file"]'
      ],
      method: 'file',
      validation: 'optional',
      notes: '工具截图文件上传'
    }
  },
  // 提交流程配置
  submitConfig: {
    submitButton: 'input[type="submit"], .wpcf7-submit',
    submitMethod: 'manual', // 手动提交
    waitAfterFill: 0,
    waitAfterSubmit: 0,
    successIndicators: ['.wpcf7-mail-sent-ok'],
    errorIndicators: ['.wpcf7-validation-errors']
  },

  // 特殊处理
  specialHandling: {
    requiresLogin: false,
    hasCaptcha: false,
    hasFileUpload: true,
    customScript: 'handleAiWikiWebSubmission',
    formValidation: {
      requiredFields: ['siteName', 'contactEmail', 'siteUrl'],
      emailValidation: true,
      urlValidation: true
    },
    notes: [
      'AI Wiki Web AI工具目录提交平台',
      '基于WordPress Contact Form 7构建',
      '英文界面',
      '支持工具截图文件上传',
      '支持联盟链接提交',
      '包含详细的工具描述字段',
      '现代化的Elementor页面构建器界面',
      '手动提交模式'
    ]
  }
};

// 自定义处理函数
export function handleAiWikiWebSubmission(data) {
  console.log('Processing AI Wiki Web form submission...');

  const processedData = { ...data };

  // 确保URL格式正确
  if (processedData.siteUrl && !processedData.siteUrl.startsWith('http')) {
    processedData.siteUrl = 'https://' + processedData.siteUrl;
  }

  // 处理联盟链接URL
  if (processedData.affiliateUrl && !processedData.affiliateUrl.startsWith('http')) {
    processedData.affiliateUrl = 'https://' + processedData.affiliateUrl;
  }

  return processedData;
}

// 自定义元素填写函数
export async function customFillElement(element, value, config) {
  console.log(`AI Wiki Web自定义填写: ${element.tagName}, 方法: ${config.method}`);

  switch (config.method) {
    case 'value':
      // 标准输入框和文本域处理
      element.focus();
      await new Promise(resolve => setTimeout(resolve, 200));

      element.value = value;
      element.dispatchEvent(new Event('input', { bubbles: true }));
      element.dispatchEvent(new Event('change', { bubbles: true }));
      element.dispatchEvent(new Event('blur', { bubbles: true }));

      console.log(`✓ 填写字段: ${element.name} = "${value}"`);
      return true;

    case 'file':
      // 文件上传处理
      console.warn('文件上传需要手动操作');
      return false;

    default:
      element.value = value;
      element.dispatchEvent(new Event('input', { bubbles: true }));
      return true;
  }

  return false;
}