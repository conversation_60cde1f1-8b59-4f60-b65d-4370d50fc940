// VieSearch.com 网站规则配置
// 网站: https://viesearch.com/submit
// 最后更新: 2025-07-24

export const SITE_RULE = {
  // 基本信息
  domain: 'viesearch.com',
  siteName: 'VieSearch',
  priority: 1,
  lastUpdated: '2025-07-24',

  // 字段映射规则
  fieldMappings: {
    // 姓名 -> Your Name
    fullName: {
      selectors: [
        '#OWNER_NAME',
        'input[name="OWNER_NAME"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '提交者全名'
    },

    // 邮箱 -> Email Address
    contactEmail: {
      selectors: [
        '#OWNER_EMAIL',
        'input[name="OWNER_EMAIL"]'
      ],
      method: 'value',
      validation: 'required|email',
      notes: '联系邮箱，用于确认和验证列表'
    },

    // 网站标题 -> Website Title
    siteName: {
      selectors: [
        '#TITLE',
        'input[name="TITLE"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '网站标题'
    },

    // 网站描述 -> Website Description
    siteDescription: {
      selectors: [
        '#DESCRIPTION',
        'textarea[name="DESCRIPTION"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '网站描述，最多500字符'
    },

    // 网站URL -> Website URL
    siteUrl: {
      selectors: [
        '#URL',
        'input[name="URL"]'
      ],
      method: 'value',
      validation: 'required|url',
      notes: '网站URL地址'
    },

    // 网站分类 -> Website Category
    category: {
      selectors: [
        '#CATEGORY',
        'select[name="CATEGORY"]'
      ],
      method: 'select',
      validation: 'required',
      notes: '网站分类选择'
    },

    // 列表类型 -> Listing Type
    listingType: {
      selectors: [
        'input[name="LINK_TYPE"]',
        'input[value="featured"]', // Featured - $24
        'input[value="express"]',  // Express - $12
        'input[value="reciprocal"]', // Free with reciprocal
        'input[value="normal"]'    // Free text only
      ],
      method: 'radio',
      validation: 'required',
      notes: '列表类型选择：特色、快速、互惠、普通'
    },

    // 优惠券 -> Coupon (仅付费选项)
    coupon: {
      selectors: [
        '#LINK_COUPON',
        'input[name="LINK_COUPON"]'
      ],
      method: 'value',
      validation: 'optional',
      notes: '优惠券代码（可选）'
    },

    // 互惠链接URL -> Reciprocal Link URL (仅互惠选项)
    reciprocalUrl: {
      selectors: [
        '#RECPR_URL',
        'input[name="RECPR_URL"]'
      ],
      method: 'value',
      validation: 'optional|url',
      notes: '互惠链接URL（互惠选项必需）'
    },

    // 互惠链接代码 -> Reciprocal Link Code (仅互惠选项)
    reciprocalCode: {
      selectors: [
        '#RECPR_TEXT',
        'textarea[name="RECPR_TEXT"]'
      ],
      method: 'value',
      validation: 'optional',
      notes: '互惠链接HTML代码'
    }
  },
  // 提交流程配置
  submitConfig: {
    submitButton: 'button[type="submit"], .vie_button_theme',
    submitMethod: 'manual', // 手动提交
    waitAfterFill: 0,
    waitAfterSubmit: 0,
    successIndicators: ['.success-message'],
    errorIndicators: ['.error-message']
  },

  // 特殊处理
  specialHandling: {
    requiresLogin: false,
    hasCaptcha: false,
    hasFileUpload: false,
    customScript: 'handleVieSearchSubmission',
    formValidation: {
      requiredFields: ['fullName', 'contactEmail', 'siteName', 'siteDescription', 'siteUrl', 'category', 'listingType'],
      emailValidation: true,
      urlValidation: true
    },
    notes: [
      'VieSearch 人工策划的搜索引擎目录',
      '成立于2004年，传统的网站目录服务',
      '提供多种提交选项：',
      '- 特色列表：$24，缩略图，即时审核',
      '- 快速列表：$12，纯文本，即时审核',
      '- 互惠列表：免费，需要反向链接',
      '- 普通列表：免费，纯文本，排队审核',
      '目前有3,076个网站等待审核',
      '普通提交拒绝率超过74%',
      '手动提交模式'
    ]
  }
};

// 自定义处理函数
export function handleVieSearchSubmission(data) {
  console.log('Processing VieSearch form submission...');

  const processedData = { ...data };

  // 确保URL格式正确
  if (processedData.siteUrl && !processedData.siteUrl.startsWith('http')) {
    processedData.siteUrl = 'https://' + processedData.siteUrl;
  }

  // 处理互惠链接URL
  if (processedData.reciprocalUrl && !processedData.reciprocalUrl.startsWith('http')) {
    processedData.reciprocalUrl = 'https://' + processedData.reciprocalUrl;
  }

  return processedData;
}

// 自定义元素填写函数
export async function customFillElement(element, value, config) {
  console.log(`VieSearch自定义填写: ${element.tagName}, 方法: ${config.method}`);

  switch (config.method) {
    case 'value':
      // 标准输入框和文本域处理
      element.focus();
      await new Promise(resolve => setTimeout(resolve, 200));

      element.value = value;
      element.dispatchEvent(new Event('input', { bubbles: true }));
      element.dispatchEvent(new Event('change', { bubbles: true }));
      element.dispatchEvent(new Event('blur', { bubbles: true }));

      console.log(`✓ 填写字段: ${element.name || element.id} = "${value}"`);
      return true;

    case 'select':
      // 下拉选择框处理
      if (element.tagName === 'SELECT') {
        const options = element.querySelectorAll('option');
        let selectedOption;

        // 智能匹配分类
        for (const option of options) {
          if (option.value && option.value !== '0') {
            const optionText = option.textContent.trim();
            if (optionText.includes('Arts') || optionText.includes('Business') || optionText.includes('Technology')) {
              selectedOption = option;
              break;
            }
          }
        }

        // 如果没找到合适的，选择第一个非默认选项
        if (!selectedOption) {
          selectedOption = Array.from(options).find(opt => opt.value !== '0');
        }

        if (selectedOption) {
          element.value = selectedOption.value;
          element.dispatchEvent(new Event('change', { bubbles: true }));
          console.log(`✓ 选择分类: ${selectedOption.textContent}`);
          return true;
        }
      }
      break;

    case 'radio':
      // 单选按钮处理 - 默认选择免费普通列表
      const normalRadio = document.querySelector('input[name="LINK_TYPE"][value="normal"]');
      if (normalRadio) {
        normalRadio.checked = true;
        normalRadio.dispatchEvent(new Event('change', { bubbles: true }));

        // 触发相关的显示/隐藏逻辑
        const hideEvent = new Event('click', { bubbles: true });
        normalRadio.dispatchEvent(hideEvent);

        console.log(`✓ 选择列表类型: 免费普通列表`);
        return true;
      }
      break;

    default:
      element.value = value;
      element.dispatchEvent(new Event('input', { bubbles: true }));
      return true;
  }

  return false;
}