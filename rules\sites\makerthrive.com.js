// MakerThrive.com 网站规则配置
// 网站: https://makerthrive.com/profeed
// 最后更新: 2025-07-24

export const SITE_RULE = {
  // 基本信息
  domain: 'makerthrive.com',
  siteName: 'MakerThrive ProFeed',
  priority: 1,
  lastUpdated: '2025-07-24',

  // 字段映射规则
  fieldMappings: {
    // 项目名称 -> Project Name (对应website-info.js的siteName)
    siteName: {
      selectors: [
        'input[name="name"]',
        '#name',
        'input[placeholder*="Enter project name"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '项目名称，对应website-info.js的siteName字段'
    },

    // 项目链接 -> Project Link (对应website-info.js的siteUrl)
    siteUrl: {
      selectors: [
        'input[name="link"]',
        '#link',
        'input[type="url"]',
        'input[placeholder*="https://yourproject.com"]'
      ],
      method: 'value',
      validation: 'optional|url',
      notes: '项目链接，对应website-info.js的siteUrl字段'
    },

    // 项目描述 -> Description
    siteDescription: {
      selectors: [
        'textarea[name="description"]',
        '#description',
        'textarea[placeholder*="Describe your project"]'
      ],
      method: 'value',
      validation: 'optional',
      notes: '项目描述'
    },

    // 项目状态 -> Status
    projectStatus: {
      selectors: [
        'button[role="combobox"]',
        'select[aria-hidden="true"]',
        'button[aria-controls*="radix"]'
      ],
      method: 'select',
      validation: 'optional',
      options: ['idea', 'in development', 'beta', 'launched'],
      optionTexts: ['Idea', 'In Development', 'Beta', 'Launched'],
      defaultValue: 'launched',
      notes: '项目状态选择'
    },

    // 工具和技术 -> Tools & Technologies
    techStack: {
      selectors: [
        'input[placeholder*="Search for tools"]',
        '.divide-y li',
        'ul.divide-y'
      ],
      method: 'tools',
      validation: 'optional',
      notes: '工具和技术选择'
    }
  },

  // 提交流程配置
  submitConfig: {
    submitButton: 'button[type="submit"], form button:last-child',
    submitMethod: 'click',
    successIndicators: [
      '.success-message',
      '.alert-success',
      '.notification-success'
    ],
    errorIndicators: [
      '.error-message',
      '.alert-error',
      '.text-destructive'
    ]
  },

  // 特殊处理
  specialHandling: {
    requiresLogin: false, // 不需要登录
    hasCaptcha: false,
    hasFileUpload: true, // 有项目图片上传

    // 自定义处理脚本
    customScript: 'handleMakerThriveSubmission',

    // 表单验证规则
    formValidation: {
      requiredFields: ['siteName'],
      emailValidation: false,
      urlValidation: true
    },

    // 特殊注意事项
    notes: [
      '不需要登录即可提交',
      '项目图片上传是必填项',
      '工具和技术选择有搜索功能',
      '项目状态有下拉选择',
      '支持多种项目状态',
      '工具列表有图标显示',
      '可以搜索和选择多个工具'
    ]
  }
};

// 自定义处理函数
export function handleMakerThriveSubmission(data, rule) {
  console.log('Processing MakerThrive ProFeed submission...');

  const processedData = { ...data };

  // 确保URL格式正确
  if (processedData.siteUrl && !processedData.siteUrl.startsWith('http')) {
    processedData.siteUrl = 'https://' + processedData.siteUrl;
  }

  // 项目状态映射
  if (processedData.projectStatus) {
    const statusMap = {
      'Idea': 'idea',
      'In Development': 'in development',
      'Beta': 'beta',
      'Launched': 'launched'
    };

    processedData.projectStatus = statusMap[processedData.projectStatus] || 'launched';
  }

  // 处理技术栈
  if (processedData.techStack && typeof processedData.techStack === 'string') {
    // 将字符串转换为数组
    processedData.techStack = processedData.techStack.split(',').map(tech => tech.trim());
  }

  console.log('MakerThrive ProFeed 数据处理完成:', processedData);
  return processedData;
}

// 自定义元素填写函数
export async function customFillElement(element, value, config) {
  console.log('🔧 MakerThrive自定义填写函数被调用:', element, value);

  // 处理项目状态下拉选择
  if (element.getAttribute('role') === 'combobox' || element.hasAttribute('aria-controls')) {
    try {
      console.log('🎯 处理项目状态选择');

      // 点击打开下拉菜单
      element.click();

      // 等待下拉菜单出现
      await new Promise(resolve => setTimeout(resolve, 500));

      // 查找对应的选项
      const statusMap = {
        'idea': 'Idea',
        'in development': 'In Development',
        'beta': 'Beta',
        'launched': 'Launched'
      };

      const targetText = statusMap[value] || 'Launched';
      const option = document.querySelector(`[role="option"]:contains("${targetText}"), option[value="${value}"]`);

      if (option) {
        option.click();
        console.log('✅ 选择了状态:', targetText);
        return true;
      }

    } catch (error) {
      console.warn('项目状态选择失败:', error);
    }
  }

  // 处理工具和技术选择
  if (element.placeholder && element.placeholder.includes('Search for tools')) {
    try {
      console.log('🎯 处理工具搜索');

      // 如果value是数组，处理第一个工具
      const tools = Array.isArray(value) ? value : [value];
      const firstTool = tools[0];

      if (firstTool) {
        // 在搜索框中输入工具名称
        element.value = firstTool;
        element.dispatchEvent(new Event('input', { bubbles: true }));

        // 等待搜索结果
        await new Promise(resolve => setTimeout(resolve, 800));

        // 查找并点击第一个匹配的工具
        const toolItem = document.querySelector('.divide-y li:first-child');
        if (toolItem) {
          const addButton = toolItem.querySelector('.lucide-circle-plus');
          if (addButton) {
            addButton.click();
            console.log('✅ 添加了工具:', firstTool);
          }
        }

        // 清空搜索框
        element.value = '';
        element.dispatchEvent(new Event('input', { bubbles: true }));

        return true;
      }

    } catch (error) {
      console.warn('工具选择失败:', error);
    }
  }

  // 处理文件上传提示
  if (element.type === 'file') {
    console.log('⚠️ 检测到文件上传字段，需要用户手动上传项目图片');
    if (element.id === 'project-photo') {
      console.log('📷 项目图片上传：请手动选择项目的代表性图片');
    }
    return false; // 让用户手动处理文件上传
  }

  // 处理普通输入框和文本域
  if (element.tagName === 'INPUT' || element.tagName === 'TEXTAREA') {
    try {
      element.value = value;
      element.dispatchEvent(new Event('input', { bubbles: true }));
      element.dispatchEvent(new Event('change', { bubbles: true }));

      console.log('✅ 填写字段:', value);
      return true;
    } catch (error) {
      console.warn('字段填写失败:', error);
    }
  }

  // 默认处理
  return false;
};