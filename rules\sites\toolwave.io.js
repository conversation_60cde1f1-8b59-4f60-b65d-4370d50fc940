// ToolWave.io 网站规则配置
// 网站: https://toolwave.io/submit-ai
// 最后更新: 2025-07-09

export const SITE_RULE = {
  // 基本信息
  domain: 'toolwave.io',
  siteName: 'ToolWave',
  priority: 1,
  lastUpdated: '2025-07-09',
  
  // 字段映射规则
  fieldMappings: {
    // 工具名称 -> Tool Name
    siteName: {
      selectors: [
        'input[name="name"]',
        '#name',
        'input[placeholder*="Enter the name of your tool"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '工具名称'
    },

    // 网站URL -> Website URL
    siteUrl: {
      selectors: [
        'input[name="website"]',
        '#website',
        'input[type="url"]',
        'input[placeholder="https://example.com"]'
      ],
      method: 'value',
      validation: 'required|url',
      notes: '网站URL地址'
    },
    
    // 简短描述 -> Short Description
    siteDescription: {
      selectors: [
        'textarea[name="description"]',
        '#description',
        'textarea[placeholder*="Briefly explain your tool"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '简短描述（3-12个词，最多84字符）'
    },
    
    // 详细描述 -> Long Description
    detailedIntro: {
      selectors: [
        'textarea[name="longDescription"]',
        '#longDescription',
        'textarea[placeholder*="Detailed description"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '详细描述（50-600个词，最多4200字符）'
    },
    
    // 主分类 -> Main Category (选择Productivity)
    category: {
      selectors: [
        'button[role="combobox"]',
        '#main_category',
        'select[aria-hidden="true"]'
      ],
      method: 'select',
      validation: 'required',
      targetValue: 'Productivity',
      defaultValue: 'Productivity',
      notes: '主分类，选择Productivity'
    },
    
    // 定价模式 -> Pricing Model (选择free)
    pricing: {
      selectors: [
        'input[name="pricing"][value="free"]',
        '#free',
        'input[type="radio"][value="free"]'
      ],
      method: 'radio',
      validation: 'required',
      targetValue: 'free',
      defaultValue: 'Free',
      notes: '定价模式，选择Free'
    }
  },
  
  // 提交流程配置
  submitConfig: {
    submitButton: 'button[type="submit"], button:contains("Submit Tool")',
    submitMethod: 'click',
    successIndicators: [
      '.success-message',
      '.alert-success',
      '.notification-success',
      '[class*="success"]'
    ],
    errorIndicators: [
      '.error-message',
      '.alert-error',
      '.text-red-500',
      '[class*="error"]'
    ]
  },
  
  // 特殊处理
  specialHandling: {
    requiresLogin: false,
    hasCaptcha: false,
    hasFileUpload: true, // 有主图和图标上传
    
    // 表单验证规则
    formValidation: {
      requiredFields: ['siteName', 'siteUrl', 'siteDescription', 'detailedIntro', 'category', 'pricing'],
      emailValidation: false,
      urlValidation: true
    },
    
    // 特殊注意事项
    notes: [
      '详细的AI工具提交表单',
      '表单包含6个主要字段：工具名称、网站URL、简短描述、详细描述、分类、定价',
      '所有字段都是必填的',
      '有文件上传功能（主图和图标都是必填）',
      '简短描述限制3-12个词，详细描述限制50-600个词',
      '有实时字数统计',
      '使用现代UI组件和橙色主题',
      '支持多种图片格式'
    ]
  }
};

// 自定义处理函数
export function handleToolWaveSubmission(data, rule) {
  console.log('Processing ToolWave.io submission...');
  
  // 特殊处理逻辑
  const processedData = { ...data };
  
  // 确保URL格式正确
  if (processedData.siteUrl && !processedData.siteUrl.startsWith('http')) {
    processedData.siteUrl = 'https://' + processedData.siteUrl;
  }
  
  // 设置默认值
  processedData.category = 'Productivity';
  processedData.pricing = 'free';
  
  // 确保简短描述符合要求（3-12个词）
  if (!processedData.siteDescription || processedData.siteDescription.split(' ').length < 3) {
    processedData.siteDescription = 'AI-powered username generator tool for productivity';
  }
  
  // 确保详细描述符合要求（50-600个词）
  if (!processedData.detailedIntro || processedData.detailedIntro.split(' ').length < 50) {
    processedData.detailedIntro = 'Hard Usernames is an innovative AI-powered tool designed to help users generate unique and creative usernames for various platforms and services. This comprehensive solution leverages advanced artificial intelligence algorithms to create memorable, brandable, and available usernames that stand out in the digital landscape. Whether you are creating accounts for social media platforms, gaming services, professional networks, or any other online service, Hard Usernames provides intelligent suggestions that match your preferences and requirements. The tool offers multiple generation modes, including random generation, keyword-based creation, and style-specific options to ensure you find the perfect username for your needs.';
  }
  
  return processedData;
}

// 自定义元素填写函数，专门处理ToolWave组件
export async function customFillElement(element, value, config) {
  console.log('🔧 ToolWave自定义填写函数被调用:', element, value);

  // 处理下拉选择组件
  if (element.getAttribute('role') === 'combobox') {
    try {
      element.click();

      // 等待下拉菜单出现
      await new Promise(resolve => setTimeout(resolve, 500));

      // 查找包含目标值的选项
      const option = Array.from(document.querySelectorAll('[role="option"]')).find(
        opt => opt.textContent.includes(config.defaultValue) || opt.textContent.includes('Productivity')
      );

      if (option) {
        option.click();
        console.log('✅ 选择下拉选项:', config.defaultValue);
        return true;
      }

      // 备用方法：直接设置隐藏select的值
      const hiddenSelect = document.querySelector('select[aria-hidden="true"]');
      if (hiddenSelect) {
        hiddenSelect.value = config.targetValue;
        hiddenSelect.dispatchEvent(new Event('change', { bubbles: true }));
        console.log('✅ 设置隐藏select值:', config.targetValue);
        return true;
      }
    } catch (error) {
      console.warn('下拉选择失败:', error);
    }
  }

  // 处理单选按钮
  if (element.type === 'radio') {
    try {
      element.checked = true;
      element.dispatchEvent(new Event('change', { bubbles: true }));

      // 更新父容器的样式
      const parentDiv = element.closest('div.border');
      if (parentDiv) {
        parentDiv.classList.add('border-[#fc905b]', 'bg-[#fff7ea]');
        parentDiv.classList.remove('border-gray-200', 'bg-gray-50');
      }

      console.log('✅ 选择单选按钮:', config.defaultValue);
      return true;
    } catch (error) {
      console.warn('单选按钮选择失败:', error);
    }
  }

  // 处理文件上传提示
  if (element.type === 'file') {
    console.log('⚠️ 检测到文件上传字段，需要用户手动上传文件');
    if (element.id === 'imageUpload') {
      console.log('📷 主图上传：推荐横向图片，最大5MB，支持JPG/PNG/WEBP');
    } else if (element.id === 'iconUpload') {
      console.log('🎯 图标上传：推荐方形图片，最大2MB，支持PNG/JPG/WEBP/SVG');
    }
    return false; // 让用户手动处理文件上传
  }

  // 处理带有字数统计的textarea
  if (element.tagName === 'TEXTAREA') {
    try {
      element.value = value;
      element.dispatchEvent(new Event('input', { bubbles: true }));
      element.dispatchEvent(new Event('change', { bubbles: true }));

      // 更新字数统计
      const wordCount = value.split(' ').filter(word => word.length > 0).length;
      const counterElement = element.parentElement.querySelector('.text-xs.text-gray-500');
      if (counterElement && counterElement.textContent.includes('Words:')) {
        counterElement.textContent = `Words: ${wordCount}`;
      }

      console.log('✅ 填写textarea并更新字数统计:', wordCount, '个词');
      return true;
    } catch (error) {
      console.warn('textarea填写失败:', error);
    }
  }

  // 处理带有maxlength的输入框
  if (element.hasAttribute('maxlength')) {
    try {
      element.value = value;
      element.dispatchEvent(new Event('input', { bubbles: true }));
      element.dispatchEvent(new Event('change', { bubbles: true }));
      console.log('✅ 填写带字符限制的输入框:', value);
      return true;
    } catch (error) {
      console.warn('字符限制输入框填写失败:', error);
    }
  }

  // 默认处理
  return false;
}
