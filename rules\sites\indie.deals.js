// Indie.deals 网站规则配置
// 网站: https://indie.deals/submit
// 最后更新: 2025-08-01

export const SITE_RULE = {
  // 基本信息
  domain: 'indie.deals',
  siteName: 'Indie Deals',
  priority: 1,
  lastUpdated: '2025-08-01',
  
  // 字段映射规则
  fieldMappings: {
    // 产品链接 -> Link
    siteUrl: {
      selectors: [
        'input[name="link"]',
        'input[placeholder*="Enter the link to your product"]'
      ],
      method: 'value',
      validation: 'required|url',
      notes: '产品链接，必填字段'
    },

    // 产品名称 -> Name
    siteName: {
      selectors: [
        'input[name="name"]',
        'input[placeholder*="Enter the name of your product"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '产品名称，必填字段'
    },

    // 分类 -> Categories
    category: {
      selectors: [
        'button[aria-haspopup="dialog"][aria-expanded]',
        'button:contains("Select categories")'
      ],
      method: 'select',
      validation: 'required',
      notes: '产品分类，下拉选择'
    },

    // 标签 -> Tags
    tags: {
      selectors: [
        'button:contains("Select tags")',
        'button[aria-controls*="radix"]'
      ],
      method: 'select',
      validation: 'optional',
      notes: '产品标签，可选字段'
    },

    // 简短描述 -> Description
    siteDescription: {
      selectors: [
        'textarea[name="description"]',
        'textarea[placeholder*="Enter a brief description"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '产品简短描述，必填字段'
    },

    // 详细介绍 -> Introduction (EasyMDE Markdown编辑器)
    detailedIntro: {
      selectors: [
        '.CodeMirror-scroll',
        '.CodeMirror textarea[tabindex="0"]',
        '.EasyMDEContainer .CodeMirror textarea',
        '#simplemde-editor-1'
      ],
      method: 'easymde',
      validation: 'optional',
      notes: '详细介绍，使用EasyMDE编辑器，支持Markdown格式'
    },

    // 购买理由1 -> Reason to Buy #1
    features: {
      selectors: [
        'input[name="reasonToBuy1"]',
        'input[placeholder*="Enter the #1 reason to buy your product"]'
      ],
      method: 'value',
      validation: 'required',
      maxLength: 120,
      notes: '第一个购买理由，最大120字符，必填'
    },

    // 购买理由2 -> Reason to Buy #2
    advantages: {
      selectors: [
        'input[name="reasonToBuy2"]',
        'input[placeholder*="Enter the #2 reason to buy your product"]'
      ],
      method: 'value',
      validation: 'optional',
      maxLength: 120,
      notes: '第二个购买理由，最大120字符，可选'
    },

    // 购买理由3 -> Reason to Buy #3
    benefits: {
      selectors: [
        'input[name="reasonToBuy3"]',
        'input[placeholder*="Enter the #3 reason to buy your product"]'
      ],
      method: 'value',
      validation: 'optional',
      maxLength: 120,
      notes: '第三个购买理由，最大120字符，可选'
    }
  },
  
  // 提交流程配置
  submitConfig: {
    submitButton: 'button[type="submit"], .submit-button, button:contains("Submit")',
    submitMethod: 'click',
    waitAfterFill: 2000,
    waitAfterSubmit: 5000,
    successIndicators: [
      '.success-message',
      '.thank-you',
      '.submission-success'
    ],
    errorIndicators: [
      '.error-message',
      '.validation-error',
      '.form-error'
    ]
  },
  
  // 特殊处理
  specialHandling: {
    requiresLogin: false,
    hasCaptcha: false,
    hasFileUpload: false,
    hasMarkdownEditor: true,
    hasMultiSelect: true,
    
    // 表单验证规则
    formValidation: {
      requiredFields: ['siteUrl', 'siteName', 'category', 'siteDescription', 'features'],
      optionalFields: ['tags', 'detailedIntro', 'advantages', 'benefits'],
      urlValidation: true,
      maxLengthValidation: true,
      markdownSupport: true
    },
    
    // 特殊注意事项
    notes: [
      '复杂表单，包含多种字段类型',
      '支持Markdown格式的详细介绍',
      '购买理由对审核很重要',
      '分类和标签使用下拉选择',
      '有特殊的Indie Deal开关'
    ]
  }
};

// 自定义处理函数
export function handleIndieDealsSubmission(data, rule) {
  console.log('Processing Indie.deals submission...');

  const processedData = { ...data };

  // 确保URL格式正确
  if (processedData.siteUrl && !processedData.siteUrl.startsWith('http')) {
    processedData.siteUrl = 'https://' + processedData.siteUrl;
  }

  // 处理购买理由长度限制
  ['features', 'advantages', 'benefits'].forEach(field => {
    if (processedData[field] && processedData[field].length > 120) {
      processedData[field] = processedData[field].substring(0, 117) + '...';
    }
  });

  return processedData;
}

// 自定义元素填写函数
export async function customFillElement(element, value, config) {
  console.log(`Indie.deals自定义填写: ${element.tagName}, 方法: ${config.method}`);

  switch (config.method) {
    case 'easymde':
      // EasyMDE Markdown编辑器处理
      const easyMDE = element.closest('.EasyMDEContainer');
      if (easyMDE) {
        const textarea = easyMDE.querySelector('textarea[tabindex="0"]');
        if (textarea) {
          textarea.focus();
          await new Promise(resolve => setTimeout(resolve, 300));

          // 设置值
          textarea.value = value;
          textarea.dispatchEvent(new Event('input', { bubbles: true }));
          textarea.dispatchEvent(new Event('change', { bubbles: true }));

          console.log(`✓ 填写EasyMDE字段: "${value}"`);
          return true;
        }
      }
      return false;

    case 'value':
      // 标准输入框处理
      element.focus();
      await new Promise(resolve => setTimeout(resolve, 200));

      element.value = value;
      element.dispatchEvent(new Event('input', { bubbles: true }));
      element.dispatchEvent(new Event('change', { bubbles: true }));
      element.dispatchEvent(new Event('blur', { bubbles: true }));

      console.log(`✓ 填写字段: ${element.name || element.id} = "${value}"`);
      return true;

    case 'select':
      // 下拉选择处理
      element.click();
      await new Promise(resolve => setTimeout(resolve, 500));
      console.log(`✓ 点击选择器: ${element.textContent}`);
      return true;

    default:
      element.value = value;
      element.dispatchEvent(new Event('input', { bubbles: true }));
      return true;
  }

  return false;
}
