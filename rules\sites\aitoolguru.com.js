// AIToolGuru.com 网站规则配置
// 网站: https://aitoolguru.com/submit-ai-tool
// 最后更新: 2025-07-25

export const SITE_RULE = {
  // 基本信息
  domain: 'aitoolguru.com',
  siteName: 'AI Tool Guru',
  priority: 1,
  lastUpdated: '2025-07-25',
  
  // 字段映射规则
  fieldMappings: {
    // 工具名称 -> Tool Name
    siteName: {
      selectors: [
        'input#title',
        'input[placeholder*="AI Tool Name"]',
        'input[type="text"]'
      ],
      method: 'value',
      validation: 'required',
      maxLength: 60,
      notes: '工具名称，最大60字符'
    },

    // 网站URL -> Website
    siteUrl: {
      selectors: [
        'input#website',
        'input[placeholder*="https://website-name.ai"]',
        'input[type="url"]'
      ],
      method: 'value',
      validation: 'required|url',
      maxLength: 80,
      notes: '网站URL，最大80字符'
    },

    // 网站描述 -> Description
    siteDescription: {
      selectors: [
        'textarea#info',
        'textarea[placeholder*="A brief info about the tool"]',
        'textarea[rows="6"]'
      ],
      method: 'value',
      validation: 'required',
      maxLength: 500,
      notes: '工具描述，最大500字符'
    }
  },

  // 表单信息
  formInfo: {
    submitButton: {
      selectors: [
        'button.btn.btn-primary.btn-lg',
        'button:contains("Submit Tool")'
      ],
      notes: '提交按钮'
    },
    formContainer: '.content-section .container',
    totalFields: 3
  },

  // 填写策略
  fillStrategy: {
    order: ['siteName', 'siteUrl', 'siteDescription'],
    delay: 300,
    waitForLoad: true
  }
};
