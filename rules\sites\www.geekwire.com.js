// GeekWire.com 网站规则配置
// 网站: https://www.geekwire.com/submit-startup/
// 最后更新: 2025-07-24

export const SITE_RULE = {
  domain: 'www.geekwire.com',
  siteName: 'GeekWire',
  priority: 1,
  lastUpdated: '2025-07-24',

  fieldMappings: {
    companyName: {
      selectors: [
        '#input_12_1',
        'input[name="input_1"]',
        'label:contains("Company Name") + div input'
      ],
      method: 'value',
      validation: 'required',
      notes: '公司名称'
    },

    streetAddress: {
      selectors: [
        '#input_12_32',
        'input[name="input_32"]',
        'label:contains("Street Address") + div input'
      ],
      method: 'value',
      validation: 'required',
      notes: '街道地址'
    },

    city: {
      selectors: [
        '#input_12_33',
        'input[name="input_33"]',
        'label:contains("City") + div input'
      ],
      method: 'value',
      validation: 'required',
      notes: '城市'
    },

    state: {
      selectors: [
        '#input_12_34',
        'input[name="input_34"]',
        'label:contains("State / Province") + div input'
      ],
      method: 'value',
      validation: 'required',
      notes: '州/省/地区'
    },

    zipCode: {
      selectors: [
        '#input_12_35',
        'input[name="input_35"]',
        'label:contains("ZIP code") + div input'
      ],
      method: 'value',
      validation: 'required',
      notes: '邮政编码'
    },

    country: {
      selectors: [
        '#input_12_36',
        'input[name="input_36"]',
        'label:contains("Country") + div input'
      ],
      method: 'value',
      validation: 'required',
      notes: '国家'
    },

    category: {
      selectors: [
        '#input_12_3',
        'select[name="input_3"]',
        'label:contains("Category") + div select'
      ],
      method: 'select',
      validation: 'required',
      defaultValue: '2833',
      notes: '公司分类，默认选择Software'
    },

    logoUrl: {
      selectors: [
        '#input_12_27',
        'input[name="input_27"]',
        'input[type="file"]'
      ],
      method: 'file',
      validation: 'required',
      notes: 'Logo文件，至少300px宽，支持jpg/png/gif'
    },

    siteUrl: {
      selectors: [
        '#input_12_17',
        'input[name="input_17"]',
        'label:contains("Website") + div input'
      ],
      method: 'value',
      validation: 'required|url',
      notes: '公司网站'
    },

    facebookUrl: {
      selectors: [
        '#input_12_18',
        'input[name="input_18"]',
        'label:contains("Facebook") + div input'
      ],
      method: 'value',
      validation: 'optional|url',
      notes: 'Facebook页面URL'
    },

    twitterUrl: {
      selectors: [
        '#input_12_19',
        'input[name="input_19"]',
        'label:contains("Twitter") + div input'
      ],
      method: 'value',
      validation: 'optional',
      notes: 'Twitter用户名，不含@符号'
    },

    linkedinUrl: {
      selectors: [
        '#input_12_29',
        'input[name="input_29"]',
        'label:contains("LinkedIn") + div input'
      ],
      method: 'value',
      validation: 'optional|url',
      notes: 'LinkedIn公司页面URL'
    },

    foundedYear: {
      selectors: [
        '#input_12_20',
        'input[name="input_20"]',
        'label:contains("Founded Year") + div input'
      ],
      method: 'value',
      validation: 'required',
      notes: '成立年份'
    },

    employeeCount: {
      selectors: [
        '#input_12_21',
        'input[name="input_21"]',
        'label:contains("Number Employees") + div input'
      ],
      method: 'value',
      validation: 'optional',
      notes: '员工数量'
    },

    annualRevenue: {
      selectors: [
        '#input_12_22',
        'input[name="input_22"]',
        'label:contains("Annual Revenue") + div input'
      ],
      method: 'value',
      validation: 'optional',
      notes: '年收入'
    },

    fullName: {
      selectors: [
        '#input_12_23',
        'input[name="input_23"]',
        'label:contains("Contact Name") + div input'
      ],
      method: 'value',
      validation: 'required',
      notes: '联系人姓名'
    },

    contactEmail: {
      selectors: [
        '#input_12_24',
        'input[name="input_24"]',
        'label:contains("Contact E-mail") + div input'
      ],
      method: 'value',
      validation: 'required|email',
      notes: '联系邮箱'
    },

    submitterRole: {
      selectors: [
        '#input_12_25',
        'input[name="input_25"]',
        'label:contains("Contact Position") + div input'
      ],
      method: 'value',
      validation: 'required',
      notes: '联系人职位'
    }
  },

  submitConfig: {
    submitButton: '#gform_submit_button_12, input[type="submit"]',
    submitMethod: 'click',
    successIndicators: ['.gform_confirmation_message'],
    errorIndicators: ['.gfield_error']
  },

  specialHandling: {
    requiresLogin: false,
    hasCaptcha: false,
    hasFileUpload: true,
    customScript: 'handleGeekWireSubmission',
    formValidation: {
      requiredFields: ['companyName', 'streetAddress', 'city', 'state', 'zipCode', 'country', 'category', 'logoUrl', 'siteUrl', 'foundedYear', 'fullName', 'contactEmail', 'submitterRole'],
      emailValidation: true,
      urlValidation: true
    },
    notes: [
      '使用Gravity Forms构建',
      '需要上传Logo文件',
      '有详细的地址信息',
      '包含社交媒体链接',
      '有更新现有列表选项',
      '联系信息不公开显示'
    ]
  }
};

export function handleGeekWireSubmission(data, rule) {
  const processedData = { ...data };

  if (processedData.siteUrl && !processedData.siteUrl.startsWith('http')) {
    processedData.siteUrl = 'https://' + processedData.siteUrl;
  }

  // 处理社交媒体URL
  if (processedData.facebookUrl && !processedData.facebookUrl.startsWith('http')) {
    processedData.facebookUrl = 'https://' + processedData.facebookUrl;
  }

  if (processedData.linkedinUrl && !processedData.linkedinUrl.startsWith('http')) {
    processedData.linkedinUrl = 'https://' + processedData.linkedinUrl;
  }

  // Twitter字段只需要用户名，不需要URL
  if (processedData.twitterUrl && processedData.twitterUrl.startsWith('@')) {
    processedData.twitterUrl = processedData.twitterUrl.substring(1);
  }

  // 自动选择"No"表示这不是更新现有列表
  const updateRadioNo = document.querySelector('#choice_12_30_1');
  if (updateRadioNo) {
    updateRadioNo.checked = true;
  }

  return processedData;
}

export async function customFillElement(element, value, config) {
  // 处理下拉选择框
  if (element.tagName === 'SELECT') {
    const options = element.querySelectorAll('option');
    const option = Array.from(options).find(opt => opt.value === '2833' || opt.textContent.includes('Software'));
    if (option) {
      element.value = option.value;
      element.dispatchEvent(new Event('change', { bubbles: true }));
      return true;
    }
  }

  // 处理文件上传
  if (element.type === 'file') {
    console.warn('Logo文件上传需要手动处理');
    return false;
  }

  // 处理单选按钮
  if (element.type === 'radio') {
    element.checked = true;
    element.dispatchEvent(new Event('change', { bubbles: true }));
    return true;
  }

  if (element.tagName === 'INPUT') {
    element.value = value;
    element.dispatchEvent(new Event('input', { bubbles: true }));
    element.dispatchEvent(new Event('change', { bubbles: true }));
    return true;
  }

  return false;
}