// DetectorTools.ai 网站规则配置
// 网站: https://detectortools.ai/submit-tool/
// 最后更新: 2025-07-24

export const SITE_RULE = {
  // 基本信息
  domain: 'detectortools.ai',
  siteName: 'DetectorTools.ai',
  priority: 1,
  lastUpdated: '2025-07-24',

  // 字段映射规则
  fieldMappings: {
    // 产品名称 -> Product Name
    siteName: {
      selectors: [
        'input[name="fields[Product Name]"]',
        '#Product\\ Name',
        'input[id="Product Name"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '产品名称'
    },

    // 网站URL -> URL
    siteUrl: {
      selectors: [
        'input[name="fields[URL]"]',
        '#URL',
        'input[id="URL"]'
      ],
      method: 'value',
      validation: 'required|url',
      notes: '产品网站URL'
    },

    // 价格模式 -> Pricing
    pricing: {
      selectors: [
        'select[name="fields[message]"]',
        '#message',
        'select[id="message"]'
      ],
      method: 'select',
      validation: 'required',
      notes: '价格模式选择'
    },

    // 产品描述 -> Product Description (50-500 words)
    detailedIntro: {
      selectors: [
        'textarea[name="fields[Desc]"]',
        '#Desc',
        'textarea[id="Desc"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '产品详细描述，50-500词'
    },

    // 提交者姓名 -> Your Name
    fullName: {
      selectors: [
        'input[name="fields[Name]"]',
        '#Name',
        'input[id="Name"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '提交者姓名'
    },

    // 联系邮箱 -> Your Email
    contactEmail: {
      selectors: [
        'input[name="fields[email]"]',
        '#email',
        'input[type="email"]'
      ],
      method: 'value',
      validation: 'required|email',
      notes: '联系邮箱'
    }
  },

  // 提交流程配置
  submitConfig: {
    submitButton: 'button[type="submit"], .breakdance-form-button__submit',
    submitMethod: 'manual', // 手动提交
    waitAfterFill: 0,
    waitAfterSubmit: 0,
    successIndicators: ['.success-message'],
    errorIndicators: ['.error-message']
  },

  // 特殊处理
  specialHandling: {
    requiresLogin: false,
    hasCaptcha: false,
    hasFileUpload: false,
    customScript: 'handleDetectorToolsSubmission',
    formValidation: {
      requiredFields: ['siteName', 'siteUrl', 'pricing', 'detailedIntro', 'fullName', 'contactEmail'],
      emailValidation: true,
      urlValidation: true
    },
    notes: [
      'DetectorTools.ai AI检测工具目录',
      '专注AI检测器工具',
      '所有字段均为必填',
      '产品描述要求50-500词',
      '手动审核，5-7个工作日内发布',
      '不接受加密货币相关产品',
      '需要透明定价',
      '手动提交模式'
    ]
  }
};

// 自定义处理函数
export function handleDetectorToolsSubmission(data) {
  console.log('Processing DetectorTools form submission...');

  const processedData = { ...data };

  // 确保URL格式正确
  if (processedData.siteUrl && !processedData.siteUrl.startsWith('http')) {
    processedData.siteUrl = 'https://' + processedData.siteUrl;
  }

  // 确保描述在50-500词范围内
  if (processedData.detailedIntro) {
    const words = processedData.detailedIntro.trim().split(/\s+/);
    if (words.length < 50) {
      console.warn('产品描述少于50词，可能不符合要求');
    } else if (words.length > 500) {
      console.warn('产品描述超过500词，将被截断');
      processedData.detailedIntro = words.slice(0, 500).join(' ');
    }
  }

  return processedData;
}

// 自定义元素填写函数
export async function customFillElement(element, value, config) {
  console.log(`DetectorTools自定义填写: ${element.tagName}, 方法: ${config.method}`);

  switch (config.method) {
    case 'value':
      // 标准输入框和文本域处理
      element.focus();
      await new Promise(resolve => setTimeout(resolve, 200));

      element.value = value;
      element.dispatchEvent(new Event('input', { bubbles: true }));
      element.dispatchEvent(new Event('change', { bubbles: true }));
      element.dispatchEvent(new Event('blur', { bubbles: true }));

      console.log(`✓ 填写字段: ${element.name || element.id} = "${value}"`);
      return true;

    case 'select':
      // 下拉选择框处理
      if (element.tagName === 'SELECT') {
        const options = element.querySelectorAll('option');
        let selectedOption;

        // 智能匹配价格模式
        for (const option of options) {
          if (option.value) {
            const optionText = option.textContent.trim();
            if (optionText.includes('Free') || optionText.includes('Freemium')) {
              selectedOption = option;
              break;
            }
          }
        }

        // 如果没找到合适的，选择第一个非空选项
        if (!selectedOption) {
          selectedOption = Array.from(options).find(opt => opt.value !== '');
        }

        if (selectedOption) {
          element.value = selectedOption.value;
          element.dispatchEvent(new Event('change', { bubbles: true }));
          console.log(`✓ 选择价格模式: ${selectedOption.textContent}`);
          return true;
        }
      }
      break;

    default:
      element.value = value;
      element.dispatchEvent(new Event('input', { bubbles: true }));
      return true;
  }

  return false;
}