// 网站信息配置文件
// 最后更新: 2025-07-06
// 说明: 包含所有需要填写的网站字段信息，每个字段都有详细注释

export const WEBSITE_INFO = {
  // ==================== 基本信息字段 ====================
  
  // 网站名称 - 显示在各个提交网站上的产品名称
  siteName: 'Hard Usernames',
  
  // 网站标题 - 更详细的标题描述
  siteTitle: 'Ultimate Username Generator - Create Unique & Hard Usernames',
  
  // 网站URL - 完整的网站地址
  siteUrl: 'https://hardusernames.com/',
  
  // 网站描述 - 简短的网站介绍（通常50-160字符）
  siteDescription: 'AI-powered username generator that creates unique, memorable usernames for gaming, Instagram, TikTok and social media platforms instantly.',

  // 标语口号 - 一句话的品牌标语/口号
  tagline: 'Create unique usernames instantly with AI-powered creativity',

  // 详细介绍 - 更完整的网站功能描述（通常200-500字符）
  detailedIntro: 'Hard Usernames is an advanced AI-powered username generator that helps users create unique, memorable usernames for gaming, social media, and professional platforms. With 30+ categories, platform-specific optimization, and instant generation, it serves over 50,000 users daily. The tool supports multiple languages and provides customizable options including length, numbers, and symbols.',
  
  // 联系邮箱 - 网站联系邮箱
  contactEmail: '<EMAIL>',
  
  // 网站图标URL - Favicon地址
  faviconUrl: 'https://hardusernames.com/favicon.ico',
  
  // 缩略图URL - 网站预览图片
  thumbnailUrl: 'https://hardusernames.com/og-image.png',
  
  // ==================== 分类和标签 ====================
  
  // 网站分类 - 主要分类（固定，不要为了适配提交网站而修改）
  category: 'Ai Tools', // 网站的真实分类
  
  // 关键词 - SEO关键词，用逗号分隔
  keywords: 'username generator, AI usernames, gaming usernames, Instagram usernames, TikTok usernames, social media usernames, unique usernames, hard usernames',
  
  // 标签 - 相关标签
  tags: 'AI, Username, Generator, Gaming, Social Media, Instagram, TikTok, Creative, Tools',
  
  // SEO关键词 - 针对搜索引擎优化的关键词
  seoKeywords: 'username generator, AI username creator, gaming names, social media usernames, Instagram username ideas, TikTok username generator',
  
  // ==================== 功能特性 ====================
  
  // 功能特性 - 网站主要功能列表
  features: 'AI-powered generation, 30+ username categories, Platform-specific optimization, Multi-language support, Instant availability checking, Customizable length and characters, Gaming and social media focused, 100% free with no registration',
  
  // 产品亮点 - 独特卖点
  uniqueSellingPoints: 'Advanced AI flagship model, 50K+ daily users, 30+ categories, 100% free, Multi-platform optimization, Instant generation, No registration required',
  
  // 使用场景 - 具体的使用案例
  useCases: 'Gaming platform usernames, Instagram profile names, TikTok creative handles, Discord server names, Twitch streaming names, Professional LinkedIn profiles, Social media branding, Content creator identities',

  // 第二个使用案例 - 工具的次要用途
  secondUseCase: 'Create memorable brand names and social media handles',

  // 第三个使用案例 - 工具的其他用途
  thirdUseCase: 'Generate creative aliases for gaming and online platforms',

  // 第二个使用案例 - 工具的次要用途
  secondUseCase: 'Create memorable brand names and social media handles',

  // 第三个使用案例 - 工具的其他用途
  thirdUseCase: 'Generate creative aliases for gaming and online platforms',
  
  // ==================== 技术和商业信息 ====================
  
  // 技术栈 - 使用的主要技术
  techStack: 'Next.js, React, AI/ML, JavaScript, CSS, Responsive Design',
  
  // 定价模式 - 收费方式
  pricing: 'Free', // 可选: Free, Freemium, Paid, Subscription
  
  // 价格金额 - 具体价格（如果是付费的话）
  priceAmount: '$0',
  
  // 是否开源 - 是否为开源项目
  isOpenSource: false,

  // 源代码URL - 如果开源的话
  sourceCodeUrl: '',

  // 演示URL - 在线演示地址
  demoUrl: 'https://hardusernames.com/',

  // 是否有免费计划 - 是否提供免费版本
  hasFreePlan: true,

  // 是否有免费试用 - 是否提供免费试用期
  hasFreeTrial: false,

  // 是否有联盟计划 - 是否提供联盟营销计划
  hasAffiliateProgram: false,

  // 集成信息 - 与其他平台的集成
  integrations: 'Web-based platform, Browser extension support',

  // 是否需要信用卡 - 注册是否需要信用卡
  creditCardRequired: false,

  // 支持的平台 - 工具支持的操作系统/平台
  supportedPlatforms: 'Web Browser, Windows, macOS, iOS, Android',

  // API可用性 - 是否提供API接口
  apiAvailable: false,

  // 联盟计划URL - 联盟营销计划链接
  affiliateUrl: '',

  // 产品功能 - 独特功能列表
  features: 'Easy username generation, Multiple format options, Real-time availability check',

  // 产品优点 - 主要优势
  pros: 'User-friendly interface, Fast generation, Multiple options',

  // 产品缺点 - 主要劣势
  cons: 'Limited customization, Requires internet connection',

  // SEO标题 - 搜索引擎优化标题
  seoTitle: 'Hard Usernames - AI Username Generator Tool',

  // SEO关键词 - 搜索引擎关键词
  seoKeywords: 'username generator, ai tool, productivity, automation',

  // SEO描述 - 搜索引擎描述
  seoDescription: 'Generate unique and creative usernames instantly with our AI-powered username generator tool.',

  // 访问模式 - 工具的访问方式
  accessModel: 'Open Source',

  // 行业分类 - 适用的行业领域
  industry: 'Horizontal',

  // 文档URL - 产品文档链接
  documentationUrl: '',

  // 使用案例 - 产品使用场景
  useCases: 'Business automation, Content generation, Data analysis, Customer support, Workflow optimization',

  // Discord URL - Discord社区链接
  discordUrl: '',

  // Telegram URL - Telegram群组链接
  telegramUrl: '',

  // Favicon URL - 产品图标链接
  faviconUrl: 'https://via.placeholder.com/64x64?text=AI',

  // 定价详情 - 详细定价信息
  pricingDetails: 'Free to use, No subscription required',

  // 关键词 - 产品关键词标签
  keywords: 'AI tool, productivity, automation, username generator',

  // 价格 - 产品价格
  price: '0',
  
  // ==================== 公司和联系信息 ====================
  
  // 公司名称 - 开发公司或团队名称
  companyName: 'Hard Usernames Team',
  
  // 联系人姓名 - 提交者姓名
  fullName: 'James Noble',

  // 名字 - 独特的真实名字
  firstName: 'Marcus',

  // 姓氏 - 独特的真实姓氏
  lastName: 'Thornfield',

  // 登录URL - 网站登录页面地址
  loginUrl: 'https://hardusernames.com/login',

  // 用户名 - 随机生成的用户名
  username: 'Marcus_Dev2025',

  // 密码 - 随机生成的密码
  password: 'Secure@Pass666',

  // 提交者角色 - 在公司中的角色
  submitterRole: 'Developer',
  
  // 联系电话 - 联系电话号码
  phone: '******-837-2947',
  
  // 国家地区 - 网站服务的主要地区
  country: 'United States',

  // 街道地址 - 详细街道地址
  streetAddress: '548 Market Street, Suite 35410',

  // 城市 - 所在城市
  city: 'San Francisco',

  // 州/省/地区 - 州、省或地区
  state: 'California',

  // 邮政编码 - ZIP码或邮政编码
  zipCode: '94104',
  
  // ==================== 社交媒体链接 ====================
  
  // 社交媒体链接 - 相关社交媒体账号
  socialLinks: 'https://twitter.com/hardusernames, https://instagram.com/hardusernames',

  // YouTube视频链接 - 产品演示或介绍视频
  videoUrl: 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',

  // Twitter账号链接 - 官方Twitter账号
  twitterUrl: 'https://twitter.com/hardusernames',

  // Twitter用户名 - 不包含@符号的用户名
  twitterUsername: 'hardusernames',

  // LinkedIn账号链接 - 官方LinkedIn页面
  linkedinUrl: 'https://www.linkedin.com/company/hardusernames',

  // Instagram用户名 - Instagram账号用户名
  instagramUsername: 'hardusernames',

  // GitHub用户名 - GitHub账号用户名
  githubUsername: 'hardusernames',

  // Facebook链接 - 官方Facebook页面
  facebookUrl: 'https://www.facebook.com/hardusernames',

  // Facebook链接 - 官方Facebook页面
  facebookUrl: 'https://www.facebook.com/hardusernames',

  // ==================== 其他信息 ====================

  // 提交备注信息 - 给站长的留言
  message: 'Thank you for reviewing and featuring our tool.',

  // 上线时间 - 网站发布日期
  launchDate: '2025-07-06',

  // 发布年份 - 网站发布年份
  releaseYear: '2025',
  
  // 网站状态 - 当前运营状态
  websiteStatus: 'Active',
  
  // AI功能标识 - 是否包含AI功能
  hasAiFeatures: true,
  
  // 目标用户 - 主要用户群体
  targetAudience: 'Gamers, Content creators, Social media users, Influencers, Streamers, General internet users',
  
  // 用户案例 - 成功案例或用户反馈
  userCases: 'Gaming communities using unique usernames, Content creators establishing brand identity, Social media influencers creating memorable handles, Streamers building recognizable online presence',
  
  // 使用方式 - 如何使用网站
  howToUse: 'Visit website, Select username style and platform, Enter interests or preferences, Customize length and character options, Click generate button, Choose from AI-generated suggestions, Check availability across platforms',
  
  // 安装方式 - 如何访问或安装
  installMethod: 'Web-based tool, No installation required, Access via any web browser, Mobile-friendly responsive design',
  
  // 常见问题 - FAQ内容
  faqs: 'Is it free? Yes, completely free. Do I need to register? No registration required. How many usernames can I generate? Unlimited. Which platforms are supported? Instagram, TikTok, Gaming, Discord, Twitch, and more.',
  
  // 用户评分 - 用户满意度评分
  userRating: '5.0',
  
  // 竞争对手 - 类似的工具或网站
  alternatives: 'SpinXO, Jimpix, NameGenerator, UsernameGenerator, BestUsernameGenerator'
};

// 导出配置以供其他文件使用
export default WEBSITE_INFO;
