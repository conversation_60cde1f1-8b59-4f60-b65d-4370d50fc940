// AITools.xyz 网站规则配置
// 网站: https://aitools.xyz/submit-tool
// 最后更新: 2025-07-25

export const SITE_RULE = {
  // 基本信息
  domain: 'aitools.xyz',
  siteName: 'AI Tools XYZ',
  priority: 1,
  lastUpdated: '2025-07-25',
  
  // 字段映射规则
  fieldMappings: {
    // 工具名称 -> Tool Name
    siteName: {
      selectors: [
        'input[wire\\:model="name"]',
        'input[name="name"]',
        'input[placeholder*="Enter tool name"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '工具名称'
    },

    // 网站标题 -> Title
    siteTitle: {
      selectors: [
        'input[wire\\:model="title"]',
        'input[name="title"]',
        'input[placeholder*="Enter tool title"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '工具标题'
    },

    // 网站URL -> Web Site URL
    siteUrl: {
      selectors: [
        'input[wire\\:model="website_url"]',
        'input[name="website_url"]',
        'input[type="url"]',
        'input[placeholder*="https://example.com"]'
      ],
      method: 'value',
      validation: 'required|url',
      notes: '网站URL'
    },

    // 简短描述 -> Summary
    siteDescription: {
      selectors: [
        'textarea[wire\\:model="excerpt"]',
        'textarea[name="excerpt"]',
        'textarea[rows="4"]',
        'textarea[placeholder*="Summary"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '简短摘要'
    },

    // 详细介绍 -> Description
    detailedIntro: {
      selectors: [
        'textarea[wire\\:model="description"]',
        'textarea[name="description"]',
        'textarea[rows="6"]',
        'textarea[placeholder*="Description"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '详细描述'
    }
  },

  // 表单信息
  formInfo: {
    submitButton: {
      selectors: [
        'button[type="submit"]',
        'button:contains("Submit")'
      ],
      notes: '提交按钮'
    },
    formContainer: 'form[wire\\:submit="submitTool"]',
    totalFields: 5
  },

  // 填写策略
  fillStrategy: {
    order: ['siteName', 'siteTitle', 'siteUrl', 'siteDescription', 'detailedIntro'],
    delay: 300,
    waitForLoad: true
  }
};
