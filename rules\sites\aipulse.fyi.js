// AiPulse.fyi 网站规则配置
// 网站: https://www.aipulse.fyi/submit
// 最后更新: 2025-07-09

export const SITE_RULE = {
  // 基本信息
  domain: 'aipulse.fyi',
  siteName: 'AiPulse',
  priority: 1,
  lastUpdated: '2025-07-09',
  
  // 字段映射规则
  fieldMappings: {
    // 应用名称 -> App Name
    siteName: {
      selectors: [
        'input[name="name"]',
        'input[placeholder="Enter your app name"]',
        'input[id*="form-item"]:first-of-type'
      ],
      method: 'value',
      validation: 'required',
      notes: '应用名称'
    },
    
    // 描述 -> Description
    siteDescription: {
      selectors: [
        'textarea[name="description"]',
        'textarea[placeholder*="Describe what your app does"]',
        'textarea[id*="form-item"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '应用描述'
    },
    
    // 网站URL -> Website URL
    siteUrl: {
      selectors: [
        'input[name="websiteUrl"]',
        'input[placeholder="https://your-app.com"]',
        'input[type="url"]'
      ],
      method: 'value',
      validation: 'required|url',
      notes: '网站URL地址'
    },
    
    // 分类 -> Category (选择Content Creation)
    category: {
      selectors: [
        'button[role="combobox"]',
        'select[aria-hidden="true"]',
        'button[aria-controls*="radix"]'
      ],
      method: 'select',
      validation: 'required',
      targetValue: 'bf767937-d467-4a15-a895-423f98aa0749',
      defaultValue: 'Content Creation',
      notes: '产品分类，选择Content Creation'
    },
    
    // 免费计划 -> Has Free Plan
    hasFreePlan: {
      selectors: [
        'button[role="checkbox"]:first-of-type',
        'input[type="checkbox"]:first-of-type',
        'button[aria-checked="false"]:first-of-type'
      ],
      method: 'checkbox',
      validation: 'optional',
      targetValue: true,
      notes: '是否有免费计划'
    },
    
    // 免费试用 -> Has Free Trial
    hasFreeTrial: {
      selectors: [
        'button[role="checkbox"]:nth-of-type(2)',
        'input[type="checkbox"]:nth-of-type(2)',
        'button[aria-checked="false"]:nth-of-type(2)'
      ],
      method: 'checkbox',
      validation: 'optional',
      targetValue: false,
      notes: '是否有免费试用'
    },
    
    // 起始价格 -> Starting Price
    priceAmount: {
      selectors: [
        'input[name="pricingStartingPrice"]',
        'input[type="number"]',
        'input[placeholder="0.00"]'
      ],
      method: 'value',
      validation: 'optional',
      notes: '起始价格（美元/月）'
    },
    
    // 关键功能 -> Key Features
    features: {
      selectors: [
        'input[placeholder*="Enter a feature"]',
        'div.space-y-4 input[type="text"]'
      ],
      method: 'value',
      validation: 'optional',
      notes: '关键功能特性'
    },
    
    // 提交者邮箱 -> Your Email
    contactEmail: {
      selectors: [
        'input[name="submitterEmail"]',
        'input[type="email"]',
        'input[placeholder="<EMAIL>"]'
      ],
      method: 'value',
      validation: 'required|email',
      notes: '提交者邮箱'
    }
  },
  
  // 提交流程配置
  submitConfig: {
    submitButton: 'button[type="submit"], button:contains("Submit for Review")',
    submitMethod: 'click',
    waitAfterFill: 2000, // 填写后等待2秒
    waitAfterSubmit: 3000, // 提交后等待3秒
    successIndicators: [
      '.success-message',
      '.alert-success',
      '.notification-success',
      '[class*="success"]'
    ],
    errorIndicators: [
      '.error-message',
      '.alert-error',
      '.alert-danger',
      '[class*="error"]'
    ]
  },
  
  // 特殊处理
  specialHandling: {
    requiresLogin: false,
    hasCaptcha: false,
    hasFileUpload: false,
    
    // 表单验证规则
    formValidation: {
      requiredFields: ['siteName', 'siteDescription', 'siteUrl', 'category', 'contactEmail'],
      emailValidation: true,
      urlValidation: true
    },
    
    // 特殊注意事项
    notes: [
      '现代化React表单，使用Radix UI组件',
      '表单包含9个字段：应用名称、描述、URL、分类、免费计划、免费试用、价格、功能、邮箱',
      '必填字段：应用名称、描述、URL、分类、邮箱',
      '分类使用下拉选择组件',
      '复选框使用自定义按钮组件',
      '功能特性支持多个条目添加',
      '价格字段为数字类型，可选填'
    ]
  }
};

// 自定义处理函数
export function handleAiPulseSubmission(data, rule) {
  console.log('Processing AiPulse.fyi submission...');
  
  // 特殊处理逻辑
  const processedData = { ...data };
  
  // 确保URL格式正确
  if (processedData.siteUrl && !processedData.siteUrl.startsWith('http')) {
    processedData.siteUrl = 'https://' + processedData.siteUrl;
  }
  
  // 设置默认分类为Content Creation
  processedData.category = 'bf767937-d467-4a15-a895-423f98aa0749';
  
  // 设置免费计划为true
  processedData.hasFreePlan = true;
  
  // 设置默认价格
  if (!processedData.priceAmount) {
    processedData.priceAmount = '0';
  }
  
  // 处理功能特性
  if (!processedData.features) {
    processedData.features = 'AI-powered username generation';
  }
  
  return processedData;
}

// 自定义元素填写函数，专门处理React组件
export async function customFillElement(element, value, config) {
  console.log('🔧 AiPulse自定义填写函数被调用:', element, value);
  
  // 处理React输入组件
  if (element.tagName === 'INPUT' || element.tagName === 'TEXTAREA') {
    try {
      // React组件需要特殊处理
      const nativeInputValueSetter = Object.getOwnPropertyDescriptor(
        element.tagName === 'INPUT' ? window.HTMLInputElement.prototype : window.HTMLTextAreaElement.prototype, 
        'value'
      ).set;
      nativeInputValueSetter.call(element, value);
      
      // 触发React事件
      element.dispatchEvent(new Event('input', { bubbles: true }));
      element.dispatchEvent(new Event('change', { bubbles: true }));
      
      console.log('✅ 使用React组件填写:', value);
      return true;
    } catch (error) {
      console.warn('React组件填写失败:', error);
    }
  }
  
  // 处理复选框按钮
  if (element.getAttribute('role') === 'checkbox') {
    try {
      if (config.targetValue === true) {
        element.click();
        element.setAttribute('aria-checked', 'true');
        element.setAttribute('data-state', 'checked');
        console.log('✅ 勾选复选框');
        return true;
      }
    } catch (error) {
      console.warn('复选框处理失败:', error);
    }
  }
  
  // 处理下拉选择组件
  if (element.getAttribute('role') === 'combobox') {
    try {
      element.click();
      
      // 等待下拉菜单出现
      await new Promise(resolve => setTimeout(resolve, 500));
      
      // 查找并点击对应选项
      const option = document.querySelector(`option[value="${config.targetValue}"]`);
      if (option) {
        option.selected = true;
        element.dispatchEvent(new Event('change', { bubbles: true }));
        console.log('✅ 选择下拉选项:', config.defaultValue);
        return true;
      }
    } catch (error) {
      console.warn('下拉选择失败:', error);
    }
  }
  
  // 默认处理
  return false;
}
