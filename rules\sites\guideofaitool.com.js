// guideofaitool.com 网站规则配置
// 网站: https://www.guideofaitool.com/submit-ai-tool
// 表单技术: Laravel Livewire Components
// 最后更新: 2025-07-08

export const SITE_RULE = {
  // 基本信息
  domain: 'guideofaitool.com',
  siteName: 'Guide of AI Tool',
  priority: 1,
  lastUpdated: '2025-07-08',
  
  // 字段映射规则
  fieldMappings: {
    // 提交者姓名 -> Name
    fullName: {
      selectors: [
        'input[id="name"]',
        'input[wire:model="name"]',
        'input[placeholder="<PERSON>"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '提交者姓名，使用website-info.js中的fullName字段'
    },
    
    // 邮箱地址 -> Email
    contactEmail: {
      selectors: [
        'input[id="email"]',
        'input[wire:model="email"]',
        'input[type="email"]:first-of-type',
        'input[placeholder="Email"]'
      ],
      method: 'value',
      validation: 'required|email',
      notes: '邮箱地址，使用website-info.js中的contactEmail字段'
    },
    
    // PayPal邮箱 -> Paypal Email (For submission reference)
    contactEmailPaypal: {
      selectors: [
        'input[id="paypal-email"]',
        'input[wire:model="paypalEmail"]',
        'input[placeholder="Paypal Email"]'
      ],
      method: 'value',
      validation: 'required|email',
      notes: 'PayPal邮箱，使用website-info.js中的contactEmail字段（与Email字段相同）'
    },
    
    // 工具名称 -> Tool Name
    siteName: {
      selectors: [
        'input[id="tool-name"]',
        'input[wire:model="toolName"]',
        'input[placeholder="Epic AI"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '工具名称，使用website-info.js中的siteName字段'
    },
    
    // 工具URL -> Tool URL
    siteUrl: {
      selectors: [
        'input[id="tool-url"]',
        'input[wire:model="toolUrl"]',
        'input[type="url"]',
        'input[placeholder*="epictool.ai"]'
      ],
      method: 'value',
      validation: 'required|url',
      notes: '工具URL，使用website-info.js中的siteUrl字段'
    }
  },
  
  // 自定义填写函数
  customFillElement: async function(element, value, config) {
    console.log(`Guide of AI Tool自定义填写: ${element.id || element.getAttribute('wire:model')}, 方法: ${config.method}`);
    
    switch (config.method) {
      case 'value':
        // 标准输入框处理
        element.focus();
        await new Promise(resolve => setTimeout(resolve, 100));
        
        // 清空现有内容
        element.value = '';
        await new Promise(resolve => setTimeout(resolve, 50));
        
        // 设置新值
        element.value = value;
        
        // 触发Livewire事件
        element.dispatchEvent(new Event('input', { bubbles: true }));
        element.dispatchEvent(new Event('change', { bubbles: true }));
        element.dispatchEvent(new Event('blur', { bubbles: true }));
        
        // 特殊处理：触发Livewire的wire:model更新
        if (element.hasAttribute('wire:model')) {
          // 模拟Livewire的输入事件
          const livewireEvent = new CustomEvent('livewire:update', {
            bubbles: true,
            detail: {
              model: element.getAttribute('wire:model'),
              value: value
            }
          });
          element.dispatchEvent(livewireEvent);
        }

        console.log(`✓ 填写字段: ${element.id} = "${value}"`);
        break;

      default:
        element.value = value;
        element.dispatchEvent(new Event('input', { bubbles: true }));
        element.dispatchEvent(new Event('change', { bubbles: true }));
    }
  },
  
  // 提交流程配置
  submitConfig: {
    submitButton: [
      'button[type="submit"]',
      '.submit-button',
      'button[wire:loading.remove]',
      'button:contains("Submit")'
    ],
    submitMethod: 'click',
    waitAfterFill: 2000,
    waitAfterSubmit: 5000,
    
    // 提交前检查
    preSubmitChecks: [
      {
        type: 'livewire-loading',
        selector: '[wire:loading]',
        action: 'wait',
        description: '等待Livewire加载完成'
      }
    ],
    
    successIndicators: [
      '.success-message',
      '.alert-success',
      '[class*="success"]',
      'text:contains("submitted")',
      'text:contains("success")'
    ],
    errorIndicators: [
      '.error-message',
      '.alert-error',
      '[class*="error"]',
      'text:contains("error")'
    ]
  },
  
  // 特殊处理
  specialHandling: {
    requiresLogin: false,
    hasCaptcha: false,
    hasFileUpload: false,
    isLivewireForm: true, // 使用Laravel Livewire
    hasPayPalIntegration: true, // 有PayPal集成
    isSimpleForm: true, // 简单表单
    
    // 表单验证规则
    formValidation: {
      requiredFields: ['fullName', 'contactEmail', 'contactEmailPaypal', 'siteName', 'siteUrl'],
      emailValidation: true,
      urlValidation: true,
      livewireValidation: true // Livewire实时验证
    },
    
    // 特殊注意事项
    notes: [
      '这是Laravel网站，使用Livewire组件',
      '表单包含5个字段，全部必填',
      'Email和PayPal Email都使用contactEmail字段',
      '使用wire:model进行数据绑定',
      '有Livewire加载状态指示',
      '提交按钮在加载时会被禁用',
      '表单使用wire:submit.prevent提交',
      '有CSRF令牌保护',
      '字段ID使用kebab-case命名',
      '简洁的5字段表单设计'
    ]
  }
};

// 自定义处理函数
export function handleGuideOfAIToolSubmission(data, _rule) {
  console.log('Processing Guide of AI Tool form submission...');

  const processedData = { ...data };

  // 确保URL格式正确
  if (processedData.siteUrl && !processedData.siteUrl.startsWith('http')) {
    processedData.siteUrl = 'https://' + processedData.siteUrl;
  }

  // PayPal邮箱使用contactEmail字段
  processedData.contactEmailPaypal = processedData.contactEmail;

  return processedData;
}

// Livewire兼容处理
export async function handleLivewireForm() {
  console.log('处理Livewire表单...');
  
  // 等待Livewire初始化
  await new Promise(resolve => setTimeout(resolve, 1000));
  
  // 检查是否有Livewire
  if (window.Livewire) {
    console.log('检测到Livewire框架');
    
    // 获取Livewire组件
    const livewireComponent = document.querySelector('[wire\\:id]');
    if (livewireComponent) {
      const componentId = livewireComponent.getAttribute('wire:id');
      console.log(`找到Livewire组件: ${componentId}`);
    }
  }
}

// Livewire加载状态处理
export async function waitForLivewireLoading() {
  console.log('等待Livewire加载完成...');
  
  return new Promise((resolve) => {
    const checkLoading = () => {
      const loadingElement = document.querySelector('[wire\\:loading]');
      if (!loadingElement || loadingElement.style.display === 'none') {
        console.log('✓ Livewire加载完成');
        resolve();
      } else {
        setTimeout(checkLoading, 500);
      }
    };
    checkLoading();
  });
}

// 邮箱字段同步填写
export async function fillEmailFields(data, rule) {
  console.log('同步填写邮箱字段...');

  const emailValue = data.contactEmail;

  if (emailValue) {
    // 填写Email字段
    const emailField = document.querySelector('input[id="email"]');
    if (emailField) {
      await rule.customFillElement(emailField, emailValue, { method: 'value' });
    }

    // 填写PayPal Email字段（使用相同的邮箱）
    const paypalEmailField = document.querySelector('input[id="paypal-email"]');
    if (paypalEmailField) {
      await rule.customFillElement(paypalEmailField, emailValue, { method: 'value' });
    }

    console.log(`✓ 邮箱字段已同步: ${emailValue}`);
  }
}
