// 网站信息配置文件
// 最后更新: 2025-07-24
// 说明: 包含所有需要填写的网站字段信息，每个字段都有详细注释

export const WEBSITE_INFO = {
  // ==================== 基本信息字段 ====================

  // 网站名称 - 显示在各个提交网站上的产品名称
  siteName: 'HumanWhisper',

  // 网站标题 - 更详细的标题描述
  siteTitle: 'HumanWhisper - AI That Explains Complex Topics Simply',

  // 网站URL - 完整的网站地址
  siteUrl: 'https://humanwhisper.com/',

  // 网站描述 - 简短的网站介绍（通常50-160字符）
  siteDescription: 'AI that explains complex topics in simple, human-friendly language. Get clear explanations without jargon, using everyday analogies and examples.',

  // 标语口号 - 一句话的品牌标语/口号
  tagline: 'The AI That Whispers Wisdom in Your Language',

  // 欢迎消息 - 网站或工具的欢迎信息
  welcomeMessage: 'Hello! Welcome to our platform. We\'re here to provide you with the best tools and services. How can we assist you today?',

  // 详细介绍 - 更完整的网站功能描述（通常200-500字符）
  detailedIntro: 'HumanWhisper is an AI simplification tool that transforms complex topics into crystal clear understanding. Unlike other AI assistants that use technical jargon, HumanWhisper specializes in plain language explanations with real-world analogies. Perfect for students, professionals, and curious minds who want to understand complex concepts without the complexity.',

  // 联系邮箱 - 网站联系邮箱
  contactEmail: '<EMAIL>',

  // 网站图标URL - Favicon地址
  faviconUrl: 'https://humanwhisper.com/favicon.ico',

  // 缩略图URL - 网站预览图片
  thumbnailUrl: 'https://humanwhisper.com/og-image.png',
  
  // ==================== 分类和标签 ====================

  // 网站分类 - 主要分类（固定，不要为了适配提交网站而修改）
  category: 'Ai Tools', // 网站的真实分类

  // 关键词 - SEO关键词，用逗号分隔
  keywords: 'AI simplification, complex topics explained, plain language AI, educational AI, learning assistant, AI tutor, concept explanation, knowledge simplification',

  // 标签 - 相关标签
  tags: 'AI, Education, Learning, Simplification, Explanation, Knowledge, Tutor, Assistant, Plain Language, Complex Topics',

  // SEO关键词 - 针对搜索引擎优化的关键词
  seoKeywords: 'AI simplification tool, complex topics explained simply, plain language AI, educational AI assistant, concept explanation AI, learning helper',
  
  // ==================== 功能特性 ====================

  // 功能特性 - 网站主要功能列表
  features: 'Plain language explanations, Real-time simplification, Customizable complexity levels, Multi-language support, Everyday analogies, No jargon approach, Streaming responses, Privacy protection',

  // 产品亮点 - 独特卖点
  uniqueSellingPoints: 'Pre-optimized for simplification, No complex prompts needed, Instant clear explanations, Advanced NLP technology, Context understanding, Semantic analysis, Human-friendly AI',

  // 使用场景 - 具体的使用案例
  useCases: 'Learning new skills, Understanding everyday questions, Work and career topics, Academic research, Technical concept explanation, Business terminology, Scientific concepts, Financial topics',

  // 第二个使用案例 - 工具的次要用途
  secondUseCase: 'Professional development and workplace learning',

  // 第三个使用案例 - 工具的其他用途
  thirdUseCase: 'Academic study and research assistance',
  
  // ==================== 技术和商业信息 ====================

  // 技术栈 - 使用的主要技术
  techStack: 'Next.js, React, Advanced NLP, AI/ML, JavaScript, Streaming Technology, Responsive Design',

  // 定价模式 - 收费方式
  pricing: 'Freemium', // 可选: Free, Freemium, Paid, Subscription

  // 价格金额 - 具体价格（如果是付费的话）
  priceAmount: '$29.99/month',

  // 是否开源 - 是否为开源项目
  isOpenSource: false,

  // 源代码URL - 如果开源的话
  sourceCodeUrl: '',

  // 演示URL - 在线演示地址
  demoUrl: 'https://humanwhisper.com',

  // 是否有免费计划 - 是否提供免费版本
  hasFreePlan: true,

  // 是否有免费试用 - 是否提供免费试用期
  hasFreeTrial: true,

  // 是否有联盟计划 - 是否提供联盟营销计划
  hasAffiliateProgram: false,

  // 集成信息 - 与其他平台的集成
  integrations: 'Web-based platform, Multiple AI models, Real-time streaming',

  // 是否需要信用卡 - 注册是否需要信用卡
  creditCardRequired: false,

  // 支持的平台 - 工具支持的操作系统/平台
  supportedPlatforms: 'Web Browser, Windows, macOS, iOS, Android',

  // API可用性 - 是否提供API接口
  apiAvailable: false,

  // 联盟计划URL - 联盟营销计划链接
  affiliateUrl: '',

  // 邮件订阅 - 是否有邮件订阅功能
  newsletter: false,

  // 产品优点 - 主要优势
  pros: 'No jargon approach, Instant understanding, Human-friendly explanations',

  // 产品缺点 - 主要劣势
  cons: 'Credit-based system, Premium features require subscription',

  // SEO标题 - 搜索引擎优化标题
  seoTitle: 'HumanWhisper - AI That Explains Complex Topics Simply',

  // SEO关键词 - 搜索引擎关键词
  seoKeywords: 'AI simplification, complex topics explained, plain language AI, educational AI',

  // SEO描述 - 搜索引擎描述
  seoDescription: 'AI that explains complex topics in simple, human-friendly language using everyday analogies and clear explanations.',

  // 访问模式 - 工具的访问方式
  accessModel: 'Freemium',

  // 行业分类 - 适用的行业领域
  industry: 'Education',

  // 文档URL - 产品文档链接
  documentationUrl: '',

  // 使用案例 - 产品使用场景
  useCases: 'Learning new skills, Understanding everyday questions, Work and career topics, Academic research, Technical concept explanation',

  // Discord URL - Discord社区链接
  discordUrl: '',

  // Telegram URL - Telegram群组链接
  telegramUrl: '',

  // Favicon URL - 产品图标链接
  faviconUrl: 'https://humanwhisper.com/favicon.ico',

  // 定价详情 - 详细定价信息
  pricingDetails: 'Free: 100 credits, Premium: $29.99/month with 3,600 credits',

  // 关键词 - 产品关键词标签
  keywords: 'AI simplification, educational AI, plain language, complex topics',

  // 价格 - 产品价格
  price: '29.99',

  // 语言支持 - 支持的语言
  languages: 'Supports English',

  // ==================== 公司和联系信息 ====================

  // 公司名称 - 开发公司或团队名称
  companyName: 'HumanWhisper Technologies',

  // 联系人姓名 - 提交者姓名
  fullName: 'Sarah Chen',

  // 名字 - 独特的真实名字
  firstName: 'Sarah',

  // 姓氏 - 独特的真实姓氏
  lastName: 'Chen',

  // 登录URL - 网站登录页面地址
  loginUrl: 'https://humanwhisper.com/chat',

  // 用户名 - 随机生成的用户名
  username: 'sarahc.tech',

  // 密码 - 随机生成的密码
  password: 'Whisper2024#Tech',

  // 提交者角色 - 在公司中的角色
  submitterRole: 'Product Manager',

  // 联系电话 - 联系电话号码
  phone: '******-892-7341',

  // 国家地区 - 网站服务的主要地区
  country: 'United States',

  // 街道地址 - 详细街道地址
  streetAddress: '2847 Pine Street, Suite 201',

  // 城市 - 所在城市
  city: 'San Francisco',

  // 州/省/地区 - 州、省或地区
  state: 'California',

  // 邮政编码 - ZIP码或邮政编码
  zipCode: '94115',
  
  // ==================== 社交媒体链接 ====================

  // 社交媒体链接 - 相关社交媒体账号
  socialLinks: 'https://twitter.com/humanwhisper, https://linkedin.com/company/humanwhisper',

  // YouTube视频链接 - 产品演示或介绍视频
  videoUrl: 'https://www.youtube.com/watch?v=humanwhisper-demo',

  // Twitter账号链接 - 官方Twitter账号
  twitterUrl: 'https://twitter.com/humanwhisper',

  // Twitter用户名 - 不包含@符号的用户名
  twitterUsername: 'humanwhisper',

  // LinkedIn账号链接 - 官方LinkedIn页面
  linkedinUrl: 'https://www.linkedin.com/company/humanwhisper',

  // Instagram用户名 - Instagram账号用户名
  instagramUsername: 'humanwhisper',

  // GitHub用户名 - GitHub账号用户名
  githubUsername: 'humanwhisper',

  // Facebook链接 - 官方Facebook页面
  facebookUrl: 'https://www.facebook.com/humanwhisper',

  // ==================== 其他信息 ====================

  // 提交备注信息 - 给站长的留言
  message: 'HumanWhisper is an innovative AI tool that makes complex topics accessible to everyone. Thank you for considering our submission.',

  // 上线时间 - 网站发布日期
  launchDate: '2025-07-24',

  // 发布年份 - 网站发布年份
  releaseYear: '2025',

  // 网站状态 - 当前运营状态
  websiteStatus: 'Active',

  // AI功能标识 - 是否包含AI功能
  hasAiFeatures: true,

  // 目标用户 - 主要用户群体
  targetAudience: 'Students, Professionals, Curious minds, Researchers, Educators, Lifelong learners, Non-technical users',

  // 用户案例 - 成功案例或用户反馈
  userCases: 'Students understanding complex academic concepts, Professionals learning new industry terms, Researchers simplifying technical papers, Educators creating accessible content, Curious individuals exploring new topics',

  // 使用方式 - 如何使用网站
  howToUse: 'Visit website, Ask any complex question naturally, Receive instant plain-language explanation, Customize complexity level if needed, Get analogies and examples, Follow up with related questions',

  // 安装方式 - 如何访问或安装
  installMethod: 'Web-based platform, No installation required, Access via any web browser, Mobile-friendly responsive design, Instant access',

  // 常见问题 - FAQ内容
  faqs: 'Is it free? Yes, with 100 free credits. Do I need special prompts? No, ask naturally. What topics can it explain? Any complex topic. Is my data private? Yes, fully encrypted and secure.',

  // 用户评分 - 用户满意度评分
  userRating: '5.0',

  // 竞争对手 - 类似的工具或网站
  alternatives: 'ChatGPT, Claude, Perplexity, Explain Like I\'m 5, Simple Wikipedia'
};

// 导出配置以供其他文件使用
export default WEBSITE_INFO;
