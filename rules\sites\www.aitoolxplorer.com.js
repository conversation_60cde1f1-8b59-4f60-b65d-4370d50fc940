// AIToolXplorer.com 网站规则配置
// 网站: https://www.aitoolxplorer.com/tool-vorschlagen
// 最后更新: 2025-07-24

export const SITE_RULE = {
  // 基本信息
  domain: 'www.aitoolxplorer.com',
  siteName: 'AI Tool Xplorer',
  priority: 1,
  lastUpdated: '2025-07-24',

  // 字段映射规则
  fieldMappings: {
    // 姓名 -> Dein <PERSON>
    fullName: {
      selectors: [
        '#Vorname',
        'input[name="Vorname"]',
        'input[placeholder="Vorname"]'
      ],
      method: 'value',
      validation: 'optional',
      notes: '提交者姓名（德语：Vorname）'
    },

    // 邮箱 -> Deine E-Mail Adresse
    contactEmail: {
      selectors: [
        '#E-Mail',
        'input[name="E-Mail"]',
        'input[type="email"]'
      ],
      method: 'value',
      validation: 'required|email',
      notes: '联系邮箱地址'
    },

    // 工具名称 -> Tool Name
    siteName: {
      selectors: [
        'input[placeholder="Tool Name"]',
        'input[name="email-2"]:first-of-type'
      ],
      method: 'value',
      validation: 'required',
      notes: '工具名称'
    },

    // 工具URL -> Tool URL
    siteUrl: {
      selectors: [
        'input[placeholder="URL"]',
        'input[name="email-2"]:nth-of-type(2)'
      ],
      method: 'value',
      validation: 'required|url',
      notes: '工具网站URL'
    },

    // 工具分类 -> Tool Kategorie
    category: {
      selectors: [
        '#field-3',
        'select[name="field-3"]'
      ],
      method: 'select',
      validation: 'required',
      notes: '工具分类选择'
    },

    // 价格模式 -> Preis Modell
    pricing: {
      selectors: [
        '#field',
        'select[name="field"]'
      ],
      method: 'select',
      validation: 'optional',
      notes: '价格模式选择'
    },

    // 工具描述 -> Tool Beschreibung
    siteDescription: {
      selectors: [
        'textarea[placeholder="Tool Beschreibung"]',
        'textarea[name="field-2"]:first-of-type'
      ],
      method: 'value',
      validation: 'optional',
      notes: '工具描述，最多5000字符'
    },

    // 更多详情 -> Teile uns weitere Details mit
    detailedIntro: {
      selectors: [
        'textarea[placeholder="Mehr Details zum Tool"]',
        'textarea[name="field-2"]:nth-of-type(2)'
      ],
      method: 'value',
      validation: 'optional',
      notes: '工具详细信息，最多5000字符'
    }
  },
  // 提交流程配置
  submitConfig: {
    submitButton: 'input[type="submit"], .w-button',
    submitMethod: 'manual', // 手动提交
    waitAfterFill: 0,
    waitAfterSubmit: 0,
    successIndicators: ['.success-message'],
    errorIndicators: ['.error-message']
  },

  // 特殊处理
  specialHandling: {
    requiresLogin: false,
    hasCaptcha: false,
    hasFileUpload: false,
    customScript: 'handleAiToolXplorerSubmission',
    formValidation: {
      requiredFields: ['contactEmail', 'siteName', 'siteUrl', 'category'],
      emailValidation: true,
      urlValidation: true
    },
    notes: [
      'AI Tool Xplorer 德语AI工具目录',
      '德语界面（德国网站）',
      '使用Webflow构建',
      '表单字段名称有重复，需要通过占位符区分',
      '支持多种AI工具分类',
      '价格模式包含：付费、试用、免费增值',
      '文本域支持最多5000字符',
      '手动提交模式'
    ]
  }
};

// 自定义处理函数
export function handleAiToolXplorerSubmission(data) {
  console.log('Processing AI Tool Xplorer form submission...');

  const processedData = { ...data };

  // 确保URL格式正确
  if (processedData.siteUrl && !processedData.siteUrl.startsWith('http')) {
    processedData.siteUrl = 'https://' + processedData.siteUrl;
  }

  return processedData;
}

// 自定义元素填写函数
export async function customFillElement(element, value, config) {
  console.log(`AI Tool Xplorer自定义填写: ${element.tagName}, 方法: ${config.method}`);

  switch (config.method) {
    case 'value':
      // 标准输入框和文本域处理
      element.focus();
      await new Promise(resolve => setTimeout(resolve, 200));

      element.value = value;
      element.dispatchEvent(new Event('input', { bubbles: true }));
      element.dispatchEvent(new Event('change', { bubbles: true }));
      element.dispatchEvent(new Event('blur', { bubbles: true }));

      console.log(`✓ 填写字段: ${element.name || element.id} = "${value}"`);
      return true;

    case 'select':
      // 下拉选择框处理
      if (element.tagName === 'SELECT') {
        const options = element.querySelectorAll('option');
        let selectedOption;

        // 智能匹配选项
        for (const option of options) {
          if (option.value && option.value !== '') {
            const optionText = option.textContent.trim();
            if (element.id === 'field-3') { // 工具分类
              if (optionText.includes('Assistent') || optionText.includes('Text') || optionText.includes('Marketing')) {
                selectedOption = option;
                break;
              }
            } else if (element.id === 'field') { // 价格模式
              if (optionText.includes('fremium') || optionText.includes('testabo')) {
                selectedOption = option;
                break;
              }
            }
          }
        }

        // 如果没找到合适的，选择第一个非空选项
        if (!selectedOption) {
          selectedOption = Array.from(options).find(opt => opt.value !== '');
        }

        if (selectedOption) {
          element.value = selectedOption.value;
          element.dispatchEvent(new Event('change', { bubbles: true }));
          console.log(`✓ 选择选项: ${selectedOption.textContent}`);
          return true;
        }
      }
      break;

    default:
      element.value = value;
      element.dispatchEvent(new Event('input', { bubbles: true }));
      return true;
  }

  return false;
}