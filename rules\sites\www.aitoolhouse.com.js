// AIToolHouse.com 网站规则配置
// 网站: https://www.aitoolhouse.com/submit-gpt
// 最后更新: 2025-07-24

export const SITE_RULE = {
  // 基本信息
  domain: 'www.aitoolhouse.com',
  siteName: 'AI Tool House',
  priority: 1,
  lastUpdated: '2025-07-24',

  // 字段映射规则
  fieldMappings: {
    // GPT名称 -> Name
    siteName: {
      selectors: [
        '#mui-2',
        'input[id="mui-2"]',
        'label[for="mui-2"] + div input'
      ],
      method: 'value',
      validation: 'required',
      notes: 'GPT名称'
    },

    // GPT URL -> GPT URL
    siteUrl: {
      selectors: [
        '#mui-3',
        'input[id="mui-3"]',
        'label[for="mui-3"] + div input'
      ],
      method: 'value',
      validation: 'required|url',
      notes: 'GPT URL地址'
    },

    // 作者姓名 -> Author's Name
    fullName: {
      selectors: [
        '#mui-4',
        'input[id="mui-4"]',
        'label[for="mui-4"] + div input'
      ],
      method: 'value',
      validation: 'required',
      notes: '作者姓名'
    },

    // 欢迎消息 -> Welcome Message
    welcomeMessage: {
      selectors: [
        '#mui-5',
        'input[id="mui-5"]',
        'label[for="mui-5"] + div input'
      ],
      method: 'value',
      validation: 'required',
      notes: '欢迎消息'
    },

    // 简短描述 -> Short Description
    siteDescription: {
      selectors: [
        '#mui-6',
        'textarea[id="mui-6"]',
        'label[for="mui-6"] + div textarea'
      ],
      method: 'value',
      validation: 'required',
      notes: '简短描述'
    }
  },
  // 提交流程配置
  submitConfig: {
    submitButton: 'button[type="button"], .MuiButton-contained',
    submitMethod: 'manual', // 手动提交
    waitAfterFill: 0,
    waitAfterSubmit: 0,
    successIndicators: ['.success-message'],
    errorIndicators: ['.error-message']
  },

  // 特殊处理
  specialHandling: {
    requiresLogin: false,
    hasCaptcha: false,
    hasFileUpload: false,
    customScript: 'handleAiToolHouseSubmission',
    formValidation: {
      requiredFields: ['siteName', 'siteUrl', 'fullName', 'welcomeMessage', 'siteDescription'],
      emailValidation: false,
      urlValidation: true
    },
    notes: [
      'AI Tool House GPT工具提交平台',
      '基于Material-UI构建',
      '专门用于提交GPT工具',
      '所有字段均为必填项',
      '包含欢迎消息字段',
      '现代化的React界面',
      '手动提交模式'
    ]
  }
};

// 自定义处理函数
export function handleAiToolHouseSubmission(data) {
  console.log('Processing AI Tool House form submission...');

  const processedData = { ...data };

  // 确保URL格式正确
  if (processedData.siteUrl && !processedData.siteUrl.startsWith('http')) {
    processedData.siteUrl = 'https://' + processedData.siteUrl;
  }

  // 为欢迎消息提供默认值
  if (!processedData.welcomeMessage) {
    processedData.welcomeMessage = "Hello! Welcome to our platform. We're here to provide you with the best tools and services. How can we assist you today?";
  }

  return processedData;
}

// 自定义元素填写函数
export async function customFillElement(element, value, config) {
  console.log(`AI Tool House自定义填写: ${element.tagName}, 方法: ${config.method}`);

  // 为欢迎消息字段提供默认值
  if (!value && element.id === 'mui-5') {
    value = "Hello! Welcome to our platform. We're here to provide you with the best tools and services. How can we assist you today?";
  }

  switch (config.method) {
    case 'value':
      // Material-UI输入框处理
      element.focus();
      await new Promise(resolve => setTimeout(resolve, 300));

      // 清空现有值
      element.value = '';
      element.dispatchEvent(new Event('input', { bubbles: true }));
      await new Promise(resolve => setTimeout(resolve, 100));

      // 设置新值
      element.value = value;
      element.dispatchEvent(new Event('input', { bubbles: true }));
      element.dispatchEvent(new Event('change', { bubbles: true }));
      element.dispatchEvent(new Event('blur', { bubbles: true }));

      console.log(`✓ 填写字段: ${element.id} = "${value}"`);
      return true;

    default:
      element.value = value;
      element.dispatchEvent(new Event('input', { bubbles: true }));
      return true;
  }
}