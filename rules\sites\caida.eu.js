// Caida.eu 网站规则配置
// 网站: https://caida.eu/submit.php
// 最后更新: 2025-07-24

export const SITE_RULE = {
  domain: 'caida.eu',
  siteName: 'CAIDA Directory',
  priority: 1,
  lastUpdated: '2025-07-24',

  fieldMappings: {
    siteName: {
      selectors: [
        'input[name="TITLE"]',
        'input[maxlength="100"]',
        'input[size="40"]:first-of-type'
      ],
      method: 'value',
      validation: 'required',
      notes: '网站标题'
    },

    siteUrl: {
      selectors: [
        'input[name="URL"]',
        'input[maxlength="255"]:first-of-type'
      ],
      method: 'value',
      validation: 'required|url',
      notes: '网站URL'
    },

    siteDescription: {
      selectors: [
        'textarea[name="DESCRIPTION"]',
        'textarea[rows="3"][cols="37"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '网站描述'
    },

    fullName: {
      selectors: [
        'input[name="OWNER_NAME"]',
        'input[size="30"][maxlength="100"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '提交者姓名'
    },

    contactEmail: {
      selectors: [
        'input[name="OWNER_EMAIL"]',
        'input[size="30"]:nth-of-type(2)'
      ],
      method: 'value',
      validation: 'required|email',
      notes: '联系邮箱'
    },

    category: {
      selectors: [
        'select[name="CATEGORY_ID"]',
        'select option[value="6"]'
      ],
      method: 'select',
      validation: 'required',
      defaultValue: '6',
      notes: '分类选择，默认选择Design'
    }
  },

  submitConfig: {
    submitButton: 'input[name="submit"], input[value="Continue"]',
    submitMethod: 'click',
    successIndicators: ['.success-message'],
    errorIndicators: ['.error-message']
  },

  specialHandling: {
    requiresLogin: false,
    hasCaptcha: true,
    hasFileUpload: false,
    customScript: 'handleCaidaSubmission',
    formValidation: {
      requiredFields: ['siteName', 'siteUrl', 'siteDescription', 'fullName', 'contactEmail', 'category'],
      emailValidation: true,
      urlValidation: true
    },
    notes: [
      '有4种列表类型选择',
      '默认选择Regular Listings (free)',
      '有验证码验证',
      '分类默认选择Design',
      '有互惠链接选项',
      '使用传统表格布局'
    ]
  }
};

export function handleCaidaSubmission(data, rule) {
  const processedData = { ...data };

  if (processedData.siteUrl && !processedData.siteUrl.startsWith('http')) {
    processedData.siteUrl = 'https://' + processedData.siteUrl;
  }

  // 自动选择Regular Listings (free)选项
  const regularListingRadio = document.querySelector('input[name="LINK_TYPE"][value="free"]');
  if (regularListingRadio) {
    regularListingRadio.click();
  }

  return processedData;
}

export async function customFillElement(element, value, config) {
  if (element.tagName === 'SELECT') {
    const options = element.querySelectorAll('option');
    const option = Array.from(options).find(opt =>
      opt.value === value || opt.textContent.includes('Design')
    );
    if (option) {
      element.value = option.value;
      element.dispatchEvent(new Event('change', { bubbles: true }));
      return true;
    }
  }

  if (element.tagName === 'INPUT' || element.tagName === 'TEXTAREA') {
    element.value = value;
    element.dispatchEvent(new Event('input', { bubbles: true }));
    return true;
  }

  return false;
}