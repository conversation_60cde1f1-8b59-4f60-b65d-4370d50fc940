// KK.org 网站规则配置
// 网站: https://kk.org/cooltools/submit-a-tool/
// 最后更新: 2025-07-24

export const SITE_RULE = {
  domain: 'kk.org',
  siteName: 'Cool Tools',
  priority: 1,
  lastUpdated: '2025-07-24',

  fieldMappings: {
    fullName: {
      selectors: [
        'input[name="your-name"]',
        '.wpcf7-form-control-wrap.your-name input',
        'input[type="text"]:first-of-type'
      ],
      method: 'value',
      validation: 'required',
      notes: '提交者姓名'
    },

    contactEmail: {
      selectors: [
        'input[name="your-email"]',
        '.wpcf7-form-control-wrap.your-email input',
        'input[type="email"]'
      ],
      method: 'value',
      validation: 'required|email',
      notes: '联系邮箱'
    },

    siteName: {
      selectors: [
        'input[name="tool-name"]',
        '.wpcf7-form-control-wrap.tool-name input'
      ],
      method: 'value',
      validation: 'required',
      notes: '工具名称'
    },

    detailedIntro: {
      selectors: [
        'textarea[name="your-experience"]',
        '.wpcf7-form-control-wrap.your-experience textarea',
        'textarea[rows="30"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '个人使用体验，需详细描述'
    },

    siteUrl: {
      selectors: [
        'input[name="link"]',
        '.wpcf7-form-control-wrap.link input'
      ],
      method: 'value',
      validation: 'required|url',
      notes: '工具购买链接'
    }
  },

  submitConfig: {
    submitButton: 'input[type="submit"], .wpcf7-submit',
    submitMethod: 'click',
    successIndicators: ['.wpcf7-mail-sent-ok'],
    errorIndicators: ['.wpcf7-validation-errors']
  },

  specialHandling: {
    requiresLogin: false,
    hasCaptcha: false,
    hasFileUpload: false,
    customScript: 'handleCoolToolsSubmission',
    formValidation: {
      requiredFields: ['fullName', 'contactEmail', 'siteName', 'detailedIntro', 'siteUrl'],
      emailValidation: true,
      urlValidation: true
    },
    notes: [
      '需要详细的个人使用体验',
      '要求至少使用6个月',
      '有单选按钮选项',
      '使用Contact Form 7',
      '有Akismet垃圾邮件保护',
      '强调个人真实体验'
    ]
  }
};

export function handleCoolToolsSubmission(data, rule) {
  const processedData = { ...data };

  if (processedData.siteUrl && !processedData.siteUrl.startsWith('http')) {
    processedData.siteUrl = 'https://' + processedData.siteUrl;
  }

  // 自动选择"No"表示不参与工具创造
  const involvedNo = document.querySelector('input[name="involved"][value="No"]');
  if (involvedNo) {
    involvedNo.checked = true;
  }

  // 自动选择"No"表示不加入邮件列表
  const mailListNo = document.querySelector('input[name="mail-list"][value="No"]');
  if (mailListNo) {
    mailListNo.checked = true;
  }

  return processedData;
}

export async function customFillElement(element, value, config) {
  if (element.tagName === 'INPUT' || element.tagName === 'TEXTAREA') {
    element.value = value;
    element.dispatchEvent(new Event('input', { bubbles: true }));
    element.dispatchEvent(new Event('change', { bubbles: true }));
    return true;
  }

  return false;
}