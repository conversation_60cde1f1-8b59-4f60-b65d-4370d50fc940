// News.BensBites.com 网站规则配置
// 网站: https://news.bensbites.com/submit
// 最后更新: 2025-07-24

export const SITE_RULE = {
  // 基本信息
  domain: 'news.bensbites.com',
  siteName: 'Ben\'s Bites News',
  priority: 1,
  lastUpdated: '2025-07-24',

  // 字段映射规则
  fieldMappings: {
    // 文章标题 -> Title of the post
    siteName: {
      selectors: [
        'input[name="title"]',
        '#title',
        'input[placeholder*="product name"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '文章标题，例如"产品名称 - 标语"或"博客文章标题"'
    },

    // 链接URL -> Link URL
    siteUrl: {
      selectors: [
        'input[name="url"]',
        '#url',
        'input[placeholder="https://example.com"]'
      ],
      method: 'value',
      validation: 'required|url',
      notes: '链接URL地址'
    },

    // 标签 -> Tags (多选下拉框)
    keywords: {
      selectors: [
        'select[name="tags[]"]',
        '#tags',
        '.choices__input'
      ],
      method: 'multiselect',
      validation: 'optional',
      notes: 'AI相关标签，最多选择3个'
    }
  },

  // 提交流程配置
  submitConfig: {
    submitButton: 'button[type="submit"], .btn-submit',
    submitMethod: 'manual', // 手动提交
    waitAfterFill: 0,
    waitAfterSubmit: 0,
    successIndicators: ['.success-message'],
    errorIndicators: ['.error-message']
  },

  // 特殊处理
  specialHandling: {
    requiresLogin: false,
    hasCaptcha: false,
    hasFileUpload: false,
    customScript: 'handleBensBitesSubmission',
    formValidation: {
      requiredFields: ['siteName', 'siteUrl'],
      emailValidation: false,
      urlValidation: true
    },
    notes: [
      'Ben\'s Bites AI新闻提交平台',
      '提交AI相关新闻、产品或研究',
      '使用Choices.js多选标签组件',
      '内容需要是近期（几天内）的AI相关内容',
      '标签用于分类，最多3个',
      '每日00:01 Pacific Time刷新',
      '手动提交模式'
    ]
  }
};

// 自定义处理函数
export function handleBensBitesSubmission(data) {
  console.log('Processing Ben\'s Bites form submission...');

  const processedData = { ...data };

  // 确保URL格式正确
  if (processedData.siteUrl && !processedData.siteUrl.startsWith('http')) {
    processedData.siteUrl = 'https://' + processedData.siteUrl;
  }

  return processedData;
}

// 自定义元素填写函数
export async function customFillElement(element, value, config) {
  console.log(`BensBites自定义填写: ${element.tagName}, 方法: ${config.method}`);

  switch (config.method) {
    case 'value':
      // 标准输入框处理
      element.focus();
      await new Promise(resolve => setTimeout(resolve, 200));

      element.value = value;
      element.dispatchEvent(new Event('input', { bubbles: true }));
      element.dispatchEvent(new Event('change', { bubbles: true }));
      element.dispatchEvent(new Event('blur', { bubbles: true }));

      console.log(`✓ 填写字段: ${element.name} = "${value}"`);
      return true;

    case 'multiselect':
      // Choices.js多选组件处理
      if (element.classList.contains('choices__input')) {
        // 点击输入框激活下拉菜单
        element.click();
        await new Promise(resolve => setTimeout(resolve, 500));

        // 选择默认标签（show, news等）
        const showOption = document.querySelector('.choices__item[data-value="1"]'); // show
        const newsOption = document.querySelector('.choices__item[data-value="14"]'); // news

        if (showOption) {
          showOption.click();
          await new Promise(resolve => setTimeout(resolve, 200));
        }

        console.log(`✓ 选择标签: show`);
        return true;
      }
      break;

    default:
      element.value = value;
      element.dispatchEvent(new Event('input', { bubbles: true }));
      return true;
  }

  return false;
}