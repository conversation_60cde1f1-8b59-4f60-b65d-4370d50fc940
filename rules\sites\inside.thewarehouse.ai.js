// inside.thewarehouse.ai 网站规则配置
// 网站: https://inside.thewarehouse.ai/submissions
// 表单技术: Vue.js Form with Custom Components
// 最后更新: 2025-07-08

export const SITE_RULE = {
  // 基本信息
  domain: 'inside.thewarehouse.ai',
  siteName: 'The AI Warehouse',
  priority: 1,
  lastUpdated: '2025-07-08',
  
  // 字段映射规则
  fieldMappings: {
    // 平台名称 -> What is your platform's name?
    siteName: {
      selectors: [
        'input[name="pyuI41pTcgxcFVEJiO7n"]',
        'input[id="pyuI41pTcgxcFVEJiO7n"]',
        'input[data-q="platform_name"]',
        'input[placeholder="Ex: ChatGPT"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '平台名称，使用website-info.js中的siteName字段'
    },
    
    // 平台URL -> What is your platform's URL?
    siteUrl: {
      selectors: [
        'input[name="7Kt5AnGffWJsM2lip7pN"]',
        'input[id="7Kt5AnGffWJsM2lip7pN"]',
        'input[data-q="what_is_your_platform\'s_url?"]',
        'input[placeholder="https://"]'
      ],
      method: 'value',
      validation: 'required|url',
      notes: '平台URL，使用website-info.js中的siteUrl字段'
    },
    
    // 简短描述 -> Provide a short description (1 sentence)
    siteDescription: {
      selectors: [
        'textarea[name="oAKggQiOsRegmTTH07xE"]',
        'textarea[data-q="provide_a_short_description_(1_sentence)_of_your_platform_to_be_shown_on_the_ai_warehouse."]',
        'textarea.form-control',
        'form textarea:first-of-type'
      ],
      method: 'value',
      validation: 'required',
      notes: '简短描述（1句话），使用website-info.js中的siteDescription字段'
    },
    
    // 联盟计划 -> Does your platform have an affiliate program?
    affiliateProgram: {
      selectors: [
        'input[data-q="does_your_platform_have_an_affiliate_program?"][value="No"]',
        'input[id="No_g4FbQ70UBrnXcZPvQzma_1_vhib2p0oy5"]',
        'input[type="radio"][value="No"]'
      ],
      method: 'radio',
      validation: 'required',
      defaultValue: 'No',
      availableOptions: ['Yes', 'No'],
      notes: '联盟计划，默认选择No'
    },
    
    // Logo上传 -> Provide your logo or product shot
    logoUpload: {
      selectors: [
        'input[id="8dd3cc0e-340d-44f3-bbd7-1f53c665b300"]',
        'input[type="file"]',
        'input[accept=".png,.gif,.jpeg,.jpg"]',
        '.file-input'
      ],
      method: 'file-upload',
      validation: 'required',
      acceptedTypes: ['png', 'gif', 'jpeg', 'jpg'],
      notes: 'Logo上传，方形格式，支持png、gif、jpeg、jpg格式，必填'
    },

    // 名字 -> First Name
    fullName: {
      selectors: [
        'input[name="first_name"]',
        'input[id="first_name"]',
        'input[data-q="first_name"]',
        'input[placeholder="Jared"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '名字，使用website-info.js中的fullName字段'
    },

    // 邮箱 -> Email
    contactEmail: {
      selectors: [
        'input[name="email"]',
        'input[type="email"]',
        'input[data-q="email"]',
        'input[placeholder="<EMAIL>"]'
      ],
      method: 'value',
      validation: 'required|email',
      notes: '邮箱地址，使用website-info.js中的contactEmail字段'
    },

    // 公司 -> Company
    companyName: {
      selectors: [
        'input[name="g6y7oFTP5z2tBb0UAX52"]',
        'input[id="g6y7oFTP5z2tBb0UAX52"]',
        'input[data-q="company"]',
        'input[placeholder="The AI Warehouse"]'
      ],
      method: 'value',
      validation: 'optional',
      notes: '公司名称，使用website-info.js中的companyName字段，可选'
    }
  },
  
  // 自定义填写函数
  customFillElement: async function(element, value, config) {
    console.log(`The AI Warehouse自定义填写: ${element.name || element.id}, 方法: ${config.method}`);
    
    switch (config.method) {
      case 'value':
        // 标准输入框和文本域处理
        element.focus();
        await new Promise(resolve => setTimeout(resolve, 100));
        
        // 清空现有内容
        element.value = '';
        await new Promise(resolve => setTimeout(resolve, 50));
        
        // 设置新值
        element.value = value;
        
        // 触发Vue.js事件
        element.dispatchEvent(new Event('input', { bubbles: true }));
        element.dispatchEvent(new Event('change', { bubbles: true }));
        element.dispatchEvent(new Event('blur', { bubbles: true }));
        
        // Vue特殊处理
        const vueEvent = new CustomEvent('vue-input', { 
          detail: { value: value },
          bubbles: true 
        });
        element.dispatchEvent(vueEvent);
        
        console.log(`✓ 填写字段: ${element.name || element.id} = "${value.substring(0, 50)}..."`);
        break;
        
      case 'radio':
        // 单选按钮处理
        console.log(`处理单选按钮，目标值: ${config.defaultValue}`);
        
        // 查找目标单选按钮
        const targetRadio = document.querySelector(`input[type="radio"][value="${config.defaultValue}"]`);
        
        if (targetRadio) {
          targetRadio.checked = true;
          targetRadio.dispatchEvent(new Event('change', { bubbles: true }));
          console.log(`✓ 选择单选按钮: ${config.defaultValue}`);
        } else {
          console.log(`⚠️ 未找到单选按钮: ${config.defaultValue}`);
        }
        break;
        
      case 'file-upload':
        // 文件上传处理
        console.log('⚠️ 文件上传字段需要手动处理');
        console.log('请手动上传Logo或产品截图（方形格式）');
        console.log('支持格式：png、gif、jpeg、jpg');
        console.log('文件上传是必填的');
        break;
        
      default:
        element.value = value;
        element.dispatchEvent(new Event('input', { bubbles: true }));
        element.dispatchEvent(new Event('change', { bubbles: true }));
    }
  },
  
  // 提交流程配置
  submitConfig: {
    submitButton: [
      'button[type="submit"]',
      'input[type="submit"]',
      'button:contains("Submit")',
      '.submit-button'
    ],
    submitMethod: 'click',
    waitAfterFill: 2000,
    waitAfterSubmit: 4000,
    successIndicators: [
      'text:contains("submitted")',
      'text:contains("thank you")',
      'text:contains("success")',
      'text:contains("received")'
    ],
    errorIndicators: [
      'text:contains("error")',
      'text:contains("required")',
      'text:contains("invalid")'
    ]
  },
  
  // 特殊处理
  specialHandling: {
    requiresLogin: false,
    hasCaptcha: false,
    hasFileUpload: true, // 有文件上传
    isVueForm: true, // Vue.js表单
    hasCustomComponents: true, // 自定义组件
    isAIWarehouse: true, // AI仓库平台
    requiresSquareFormat: true, // 需要方形格式图片
    
    // 表单验证规则
    formValidation: {
      requiredFields: ['siteName', 'siteUrl', 'siteDescription', 'affiliateProgram', 'logoUpload', 'fullName', 'contactEmail'],
      optionalFields: ['companyName'],
      emailValidation: true,
      urlValidation: true,
      fileUploadFields: ['logoUpload'],
      radioGroups: ['affiliateProgram']
    },
    
    // 特殊注意事项
    notes: [
      '这是The AI Warehouse的产品提交表单',
      '表单包含8个字段：7个必填，1个可选',
      '使用Vue.js和自定义组件构建',
      '专注于AI产品和平台收录',
      '需要上传方形格式的Logo',
      '支持png、gif、jpeg、jpg格式',
      '简短描述限制为1句话',
      '联盟计划选项：Yes/No',
      '平台名称示例：ChatGPT',
      'URL格式：https://',
      '文件上传是必填的',
      '联系信息：名字、邮箱必填，公司可选',
      '专业的AI产品展示平台'
    ]
  }
};

// 自定义处理函数
export function handleAIWarehouseSubmission(data, _rule) {
  console.log('Processing The AI Warehouse form submission...');

  const processedData = { ...data };

  // 确保URL格式正确
  if (processedData.siteUrl && !processedData.siteUrl.startsWith('http')) {
    processedData.siteUrl = 'https://' + processedData.siteUrl;
  }

  // 设置默认值
  processedData.affiliateProgram = 'No';

  return processedData;
}

// Vue.js表单检测
export function detectVueForm() {
  console.log('检测Vue.js表单...');
  
  // 检查Vue特征
  const vueElements = document.querySelectorAll('[data-v-*]');
  const vueAttributes = document.querySelector('[data-v-8b229d05]');
  
  if (vueElements.length > 0 || vueAttributes) {
    console.log(`✓ 检测到Vue.js表单，${vueElements.length}个Vue元素`);
    return true;
  }
  
  return false;
}

// The AI Warehouse信息提醒
export function showAIWarehouseInfo() {
  console.log('🏭 The AI Warehouse 信息:');
  console.log('');
  console.log('平台特色:');
  console.log('- 专业的AI产品展示平台');
  console.log('- 现代化的Vue.js界面');
  console.log('- 专注于AI工具和平台收录');
  console.log('- 高质量的产品展示');
  console.log('');
  console.log('提交要求:');
  console.log('- 平台名称（例如：ChatGPT）');
  console.log('- 平台URL（https://格式）');
  console.log('- 简短描述（1句话）');
  console.log('- 联盟计划信息（Yes/No）');
  console.log('- Logo上传（方形格式，必填）');
  console.log('');
  console.log('支持的图片格式:');
  console.log('- PNG（推荐）');
  console.log('- GIF');
  console.log('- JPEG / JPG');
  console.log('');
  console.log('The AI Warehouse - 专业的AI产品仓库！');
}

// 文件上传指南
export function showFileUploadGuide() {
  console.log('📁 Logo上传指南:');
  console.log('');
  console.log('格式要求:');
  console.log('- 方形格式（1:1比例）');
  console.log('- 推荐尺寸：512x512px或更高');
  console.log('- 支持格式：PNG、GIF、JPEG、JPG');
  console.log('- 文件大小：建议小于5MB');
  console.log('');
  console.log('内容建议:');
  console.log('- 清晰的Logo或产品截图');
  console.log('- 高对比度，易于识别');
  console.log('- 避免过多文字');
  console.log('- 专业的视觉效果');
  console.log('');
  console.log('注意：文件上传是必填的！');
}

// 联盟计划说明
export function showAffiliateProgramInfo() {
  console.log('🤝 联盟计划说明:');
  console.log('');
  console.log('选项说明:');
  console.log('- Yes：您的平台有联盟计划');
  console.log('- No：您的平台没有联盟计划');
  console.log('');
  console.log('联盟计划的好处:');
  console.log('- 增加产品曝光度');
  console.log('- 扩大用户群体');
  console.log('- 提高转化率');
  console.log('- 建立合作伙伴关系');
  console.log('');
  console.log('默认选择：No（如果不确定）');
}

// 描述写作指南
export function showDescriptionGuide() {
  console.log('📝 描述写作指南:');
  console.log('');
  console.log('要求:');
  console.log('- 限制为1句话');
  console.log('- 简洁明了');
  console.log('- 突出核心功能');
  console.log('- 吸引用户注意');
  console.log('');
  console.log('优秀示例:');
  console.log('- "AI-powered chatbot that helps businesses automate customer support."');
  console.log('- "Generate stunning images from text descriptions using advanced AI."');
  console.log('- "Transform your writing with AI-powered grammar and style suggestions."');
  console.log('');
  console.log('避免:');
  console.log('- 过长的句子');
  console.log('- 技术术语过多');
  console.log('- 模糊的描述');
  console.log('- 营销夸大用词');
}

// 表单验证
export function validateAIWarehouseForm() {
  console.log('验证The AI Warehouse表单...');

  const requiredFields = [
    { selector: 'input[name="pyuI41pTcgxcFVEJiO7n"]', label: '平台名称' },
    { selector: 'input[name="7Kt5AnGffWJsM2lip7pN"]', label: '平台URL' },
    { selector: 'textarea[name="oAKggQiOsRegmTTH07xE"]', label: '简短描述' },
    { selector: 'input[type="file"]', label: 'Logo上传' }
  ];

  let isValid = true;

  requiredFields.forEach(field => {
    const element = document.querySelector(field.selector);
    if (!element || (element.type !== 'file' && !element.value.trim())) {
      console.log(`⚠️ 必填字段为空: ${field.label}`);
      isValid = false;
    }
  });

  // 检查单选按钮
  const radioButtons = document.querySelectorAll('input[type="radio"]:checked');
  if (radioButtons.length === 0) {
    console.log('⚠️ 请选择联盟计划选项');
    isValid = false;
  }

  // 检查文件上传
  const fileInput = document.querySelector('input[type="file"]');
  if (fileInput && !fileInput.files.length) {
    console.log('⚠️ 请上传Logo文件');
    isValid = false;
  }

  if (isValid) {
    console.log('✓ 表单验证通过');
  }

  return isValid;
}

// Vue.js表单特点
export function showVueFormFeatures() {
  console.log('⚡ Vue.js表单特点:');
  console.log('');
  console.log('技术优势:');
  console.log('- 响应式数据绑定');
  console.log('- 组件化开发');
  console.log('- 实时验证');
  console.log('- 流畅的用户体验');
  console.log('');
  console.log('界面特点:');
  console.log('- 现代化设计');
  console.log('- 自定义组件');
  console.log('- 动态交互');
  console.log('- 移动端适配');
  console.log('');
  console.log('数据处理:');
  console.log('- 实时数据验证');
  console.log('- 自动格式化');
  console.log('- 错误提示');
  console.log('- 状态管理');
}

// 平台名称建议
export function showPlatformNameSuggestions() {
  console.log('🏷️ 平台名称建议:');
  console.log('');
  console.log('命名原则:');
  console.log('- 简洁易记');
  console.log('- 反映功能');
  console.log('- 避免特殊字符');
  console.log('- 品牌一致性');
  console.log('');
  console.log('示例:');
  console.log('- ChatGPT（对话AI）');
  console.log('- DALL-E（图像生成）');
  console.log('- Grammarly（写作助手）');
  console.log('- Midjourney（艺术创作）');
  console.log('');
  console.log('使用您的官方产品名称！');
}

// 方形格式说明
export function showSquareFormatRequirement() {
  console.log('⬜ 方形格式要求:');
  console.log('');
  console.log('为什么需要方形格式:');
  console.log('- 统一的展示效果');
  console.log('- 适配各种显示尺寸');
  console.log('- 专业的视觉呈现');
  console.log('- 移动端友好');
  console.log('');
  console.log('制作建议:');
  console.log('- 使用设计工具调整比例');
  console.log('- 保持Logo居中');
  console.log('- 添加适当的内边距');
  console.log('- 确保清晰度');
  console.log('');
  console.log('推荐尺寸:');
  console.log('- 512x512px（标准）');
  console.log('- 1024x1024px（高清）');
  console.log('- 256x256px（最小）');
}
