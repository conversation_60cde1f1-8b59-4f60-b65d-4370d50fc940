// IndieAI.co 网站规则配置
// 网站: https://www.indieai.co/add-tool
// 最后更新: 2025-07-24

export const SITE_RULE = {
  domain: 'www.indieai.co',
  siteName: 'Indie AI',
  priority: 1,
  lastUpdated: '2025-07-24',

  fieldMappings: {
    contactEmail: {
      selectors: [
        '#sw-form-capture-Email',
        'input[name="<PERSON><PERSON>"]',
        'input[placeholder="<EMAIL>"]'
      ],
      method: 'value',
      validation: 'required|email',
      notes: '联系邮箱'
    },

    siteName: {
      selectors: [
        '#sw-form-capture-Title',
        'input[name="Title"]',
        'input[placeholder="Name of the tool"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '工具名称'
    },

    tagline: {
      selectors: [
        '#sw-form-capture-Subtitle',
        'input[name="Subtitle"]',
        'input[placeholder*="What it does and why it is awesome"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '工具标语'
    },

    siteUrl: {
      selectors: [
        '#sw-form-capture-Call\\ To\\ Action\\ URL',
        'input[name="Call To Action URL"]',
        'input[placeholder="URL of the tool"]'
      ],
      method: 'value',
      validation: 'required|url',
      notes: '工具链接'
    },

    category: {
      selectors: [
        '#sw-form-capture-Category',
        'input[name="Category"]',
        'input[placeholder="Categories you think are suitable"]'
      ],
      method: 'value',
      validation: 'optional',
      notes: '工具分类'
    }
  },

  submitConfig: {
    submitButton: '#sw-form-capture-submit-btn, button[data-action="submit"]',
    submitMethod: 'click',
    successIndicators: ['#sw-form-success-message'],
    errorIndicators: ['.error-message']
  },

  specialHandling: {
    requiresLogin: false,
    hasCaptcha: true,
    hasFileUpload: false,
    customScript: 'handleIndieAISubmission',
    formValidation: {
      requiredFields: ['contactEmail', 'siteName', 'tagline', 'siteUrl'],
      emailValidation: true,
      urlValidation: true
    },
    notes: [
      '使用自定义表单系统',
      '有reCAPTCHA验证',
      '字段使用特殊的ID格式',
      '提交按钮有加载动画',
      '成功消息会显示感谢文本'
    ]
  }
};

export function handleIndieAISubmission(data, rule) {
  const processedData = { ...data };

  if (processedData.siteUrl && !processedData.siteUrl.startsWith('http')) {
    processedData.siteUrl = 'https://' + processedData.siteUrl;
  }

  return processedData;
}

export async function customFillElement(element, value, config) {
  if (element.tagName === 'INPUT') {
    element.value = value;
    element.dispatchEvent(new Event('input', { bubbles: true }));
    element.dispatchEvent(new Event('change', { bubbles: true }));
    return true;
  }

  return false;
}