// qualityinternetdirectory.com 网站规则配置
// 网站: https://www.qualityinternetdirectory.com/submit.php
// 表单技术: PHP Form with CAPTCHA
// 最后更新: 2025-07-08

export const SITE_RULE = {
  // 基本信息
  domain: 'qualityinternetdirectory.com',
  siteName: 'Quality Internet Directory',
  priority: 1,
  lastUpdated: '2025-07-08',
  
  // 字段映射规则
  fieldMappings: {
    // 定价选项 -> Pricing
    linkType: {
      selectors: [
        'input[name="LINK_TYPE"][value="normal"]',
        'input[type="radio"][value="normal"]',
        'input[value="normal"]'
      ],
      method: 'radio',
      validation: 'required',
      defaultValue: 'normal',
      availableOptions: ['featured', 'normal', 'reciprocal'],
      notes: '定价选项，默认选择normal (Regular links，免费，2-3个月审核，不保证通过)'
    },
    
    // 标题 -> Title
    siteName: {
      selectors: [
        'input[name="TITLE"]',
        'input.text:first-of-type',
        'input[maxlength="100"]',
        'input[size="40"]:first-of-type'
      ],
      method: 'value',
      validation: 'required',
      notes: '网站标题，使用website-info.js中的siteName字段'
    },
    
    // URL -> URL
    siteUrl: {
      selectors: [
        'input[name="URL"]',
        'input[maxlength="255"]',
        'input.text:nth-of-type(2)',
        'input[size="40"]:nth-of-type(2)'
      ],
      method: 'value',
      validation: 'optional|url',
      notes: '网站URL，使用website-info.js中的siteUrl字段'
    },
    
    // 描述 -> Description
    detailedIntro: {
      selectors: [
        'textarea[name="DESCRIPTION"]',
        'textarea.text:first-of-type',
        'textarea[rows="3"]:first-of-type',
        'textarea[cols="37"]'
      ],
      method: 'value',
      validation: 'optional',
      maxLength: 500,
      notes: '网站详细描述，使用website-info.js中的detailedIntro字段，限制500字符'
    },
    
    // META关键词 -> META Keywords
    keywords: {
      selectors: [
        'input[name="META_KEYWORDS"]',
        'input[maxlength="2000"]',
        'input.text:nth-of-type(3)',
        'input[size="40"]:nth-of-type(3)'
      ],
      method: 'value',
      validation: 'optional',
      notes: 'META关键词，使用website-info.js中的keywords字段'
    },
    
    // META描述 -> META Description
    siteDescription: {
      selectors: [
        'textarea[name="META_DESCRIPTION"]',
        'textarea.text:nth-of-type(2)',
        'textarea[rows="3"]:nth-of-type(2)',
        'textarea[cols="30"]'
      ],
      method: 'value',
      validation: 'optional',
      maxLength: 250,
      notes: 'META描述，使用website-info.js中的siteDescription字段，限制250字符'
    },
    
    // 您的姓名 -> Your Name
    fullName: {
      selectors: [
        'input[name="OWNER_NAME"]',
        'td.field input',
        'input[maxlength="50"]',
        'input.text:nth-of-type(4)'
      ],
      method: 'value',
      validation: 'required',
      notes: '您的姓名，使用website-info.js中的fullName字段'
    },
    
    // 您的邮箱 -> Your Email
    contactEmail: {
      selectors: [
        'input[name="OWNER_EMAIL"]',
        'input.text:nth-of-type(5)',
        'input[size="40"]:nth-of-type(5)',
        'input[maxlength="255"]:nth-of-type(2)'
      ],
      method: 'value',
      validation: 'required|email',
      notes: '您的邮箱，使用website-info.js中的contactEmail字段'
    }
  },
  
  // 自定义填写函数
  customFillElement: async function(element, value, config) {
    console.log(`Quality Internet Directory自定义填写: ${element.name || element.type}, 方法: ${config.method}`);
    
    switch (config.method) {
      case 'value':
        // 标准输入框和文本域处理
        element.focus();
        await new Promise(resolve => setTimeout(resolve, 100));
        
        // 清空现有内容
        element.value = '';
        await new Promise(resolve => setTimeout(resolve, 50));
        
        // 处理字符限制
        let finalValue = value;
        if (config.maxLength && finalValue.length > config.maxLength) {
          finalValue = finalValue.substring(0, config.maxLength);
          console.log(`⚠️ 内容被截断到${config.maxLength}字符: ${finalValue}`);
        }
        
        // 设置新值
        element.value = finalValue;
        
        // 触发事件
        element.dispatchEvent(new Event('input', { bubbles: true }));
        element.dispatchEvent(new Event('change', { bubbles: true }));
        element.dispatchEvent(new Event('blur', { bubbles: true }));
        
        console.log(`✓ 填写字段: ${element.name} = "${finalValue.substring(0, 50)}..."`);
        break;
        
      case 'radio':
        // 单选按钮处理
        console.log(`处理定价选项，目标值: ${config.defaultValue}`);
        
        // 查找所有同名单选按钮
        const radioButtons = document.querySelectorAll('input[name="LINK_TYPE"]');
        
        // 先取消所有选择
        radioButtons.forEach(rb => {
          rb.checked = false;
        });
        
        // 选择目标选项
        const targetRadio = Array.from(radioButtons).find(rb => 
          rb.value === config.defaultValue
        );
        
        if (targetRadio) {
          targetRadio.checked = true;
          targetRadio.dispatchEvent(new Event('change', { bubbles: true }));
          console.log(`✓ 选择定价选项: Regular links (${config.defaultValue})`);
        } else {
          console.log(`⚠️ 未找到定价选项: ${config.defaultValue}`);
        }
        break;
        
      default:
        element.value = value;
        element.dispatchEvent(new Event('input', { bubbles: true }));
        element.dispatchEvent(new Event('change', { bubbles: true }));
    }
  },
  
  // 提交流程配置
  submitConfig: {
    submitButton: [
      'button[type="submit"]',
      'input[type="submit"]',
      'button:contains("Submit")',
      'input[value*="Submit"]'
    ],
    submitMethod: 'click',
    waitAfterFill: 2000,
    waitAfterSubmit: 5000,
    successIndicators: [
      'text:contains("submitted")',
      'text:contains("thank you")',
      'text:contains("success")',
      'text:contains("approved")'
    ],
    errorIndicators: [
      'text:contains("error")',
      'text:contains("required")',
      'text:contains("invalid")',
      'text:contains("captcha")'
    ]
  },
  
  // 特殊处理
  specialHandling: {
    requiresLogin: false,
    hasCaptcha: true, // 可能有验证码
    hasFileUpload: false,
    isPHPForm: true, // PHP表单
    isQualityDirectory: true, // 质量目录
    hasMetaFields: true, // 有META字段
    hasPaidOptions: true, // 有付费选项
    hasReciprocalOption: true, // 有互惠链接选项
    hasGuaranteedOption: true, // 有保证收录选项
    hasCharacterLimits: true, // 有字符限制
    
    // 表单验证规则
    formValidation: {
      requiredFields: ['linkType', 'siteName', 'fullName', 'contactEmail'],
      optionalFields: ['siteUrl', 'detailedIntro', 'keywords', 'siteDescription'],
      emailValidation: true,
      urlValidation: true,
      characterLimits: {
        detailedIntro: 500,
        siteDescription: 250
      },
      radioGroups: ['linkType']
    },
    
    // 特殊注意事项
    notes: [
      '这是Quality Internet Directory的网站提交表单',
      '表单包含8个字段：4个必填，4个可选',
      '质量互联网目录，强调网站质量',
      '可能有验证码保护，需要手动处理',
      '默认选择Regular links（免费，2-3个月审核，不保证通过）',
      '有付费选项：Featured links $5.79/年（1-2天审核，保证收录）',
      '有互惠链接选项：Regular links with reciprocal（2周审核）',
      '包含META字段：关键词和描述',
      '描述限制500字符，META描述限制250字符',
      '使用实际字段名：LINK_TYPE, TITLE, URL, DESCRIPTION, META_KEYWORDS, META_DESCRIPTION, OWNER_NAME, OWNER_EMAIL',
      '与其他目录网站类似的表单结构',
      '专注于高质量网站收录',
      '价格最便宜的付费选项（$5.79/年）'
    ]
  }
};

// 自定义处理函数
export function handleQualityInternetDirectorySubmission(data, _rule) {
  console.log('Processing Quality Internet Directory form submission...');

  const processedData = { ...data };

  // 确保URL格式正确
  if (processedData.siteUrl && !processedData.siteUrl.startsWith('http')) {
    processedData.siteUrl = 'https://' + processedData.siteUrl;
  }

  // 处理字符限制
  if (processedData.detailedIntro && processedData.detailedIntro.length > 500) {
    processedData.detailedIntro = processedData.detailedIntro.substring(0, 500);
  }

  if (processedData.siteDescription && processedData.siteDescription.length > 250) {
    processedData.siteDescription = processedData.siteDescription.substring(0, 250);
  }

  // 设置默认值
  processedData.linkType = 'normal'; // Regular links

  return processedData;
}

// Quality Internet Directory信息提醒
export function showQualityInternetDirectoryInfo() {
  console.log('⭐ Quality Internet Directory 信息:');
  console.log('');
  console.log('平台特色:');
  console.log('- 质量互联网目录');
  console.log('- 强调网站质量和内容');
  console.log('- 包含META字段优化');
  console.log('- 支持互惠链接选项');
  console.log('- 最便宜的付费选项');
  console.log('');
  console.log('提交选项 (3种):');
  console.log('1. Featured links - $5.79/年 ⭐ 最便宜');
  console.log('   - 1-2天审核');
  console.log('   - 保证收录');
  console.log('   - 价格最优');
  console.log('');
  console.log('2. Regular links - 免费 ✅ 默认选择');
  console.log('   - 2-3个月审核');
  console.log('   - 不保证通过');
  console.log('   - 标准审核流程');
  console.log('');
  console.log('3. Regular links with reciprocal - 免费');
  console.log('   - 2周审核');
  console.log('   - 需要在首页放置互惠链接');
  console.log('   - 快速免费选项');
  console.log('');
  console.log('字段特点:');
  console.log('- 包含META关键词和描述');
  console.log('- 描述限制500字符');
  console.log('- META描述限制250字符');
  console.log('- 支持SEO优化');
  console.log('');
  console.log('Quality Internet Directory - 最具性价比的质量目录！');
}

// 最便宜付费选项分析
export function showCheapestPaidOptionAnalysis() {
  console.log('💰 最便宜付费选项分析:');
  console.log('');
  console.log('付费选项价格排序:');
  console.log('1. Quality Internet: $5.79/年 ⭐ 最便宜');
  console.log('2. All States USA: $6.79');
  console.log('3. Free Internet: $6.97/年');
  console.log('4. Free PR Web: $9.99/年');
  console.log('5. Australia Web: $12.95/年');
  console.log('6. Info Listings: $29.99终身');
  console.log('');
  console.log('Quality Internet优势:');
  console.log('- 价格最低（$5.79/年）');
  console.log('- 审核时间快（1-2天）');
  console.log('- 保证收录');
  console.log('- 性价比最高');
  console.log('');
  console.log('适合场景:');
  console.log('- 预算有限的用户');
  console.log('- 需要快速收录');
  console.log('- 追求性价比');
  console.log('- 测试付费效果');
}

// 质量标准说明
export function showQualityStandards() {
  console.log('🏆 质量标准说明:');
  console.log('');
  console.log('Quality Internet Directory要求:');
  console.log('- 网站内容质量高');
  console.log('- 设计专业美观');
  console.log('- 功能完整可用');
  console.log('- 用户体验良好');
  console.log('');
  console.log('审核标准:');
  console.log('- 内容原创性');
  console.log('- 网站稳定性');
  console.log('- 加载速度');
  console.log('- 移动端适配');
  console.log('');
  console.log('提高通过率建议:');
  console.log('- 完善网站内容');
  console.log('- 优化用户体验');
  console.log('- 确保网站稳定');
  console.log('- 添加联系信息');
  console.log('');
  console.log('不接受的网站:');
  console.log('- 内容空洞的网站');
  console.log('- 纯广告网站');
  console.log('- 违法违规内容');
  console.log('- 技术问题严重');
}

// 互惠链接快速通道
export function showReciprocalFastTrack() {
  console.log('🚀 互惠链接快速通道:');
  console.log('');
  console.log('Quality Internet互惠选项:');
  console.log('- 审核时间: 2周');
  console.log('- 费用: 免费');
  console.log('- 要求: 首页放置互惠链接');
  console.log('');
  console.log('与其他目录对比:');
  console.log('- Australia Web: 2-3个月 → 2-3个月');
  console.log('- Free Internet: 2-3个月 → 1-2周');
  console.log('- Quality Internet: 2-3个月 → 2周 ✅');
  console.log('');
  console.log('互惠链接代码:');
  console.log('<a href="https://www.qualityinternetdirectory.com/">Quality Internet Directory</a>');
  console.log('');
  console.log('优势:');
  console.log('- 大幅缩短审核时间');
  console.log('- 完全免费');
  console.log('- 建立双向链接');
  console.log('- 提高SEO价值');
}

// 表单验证
export function validateQualityInternetDirectoryForm() {
  console.log('验证Quality Internet Directory表单...');

  const requiredFields = [
    { selector: 'input[name="TITLE"]', label: '网站标题' },
    { selector: 'input[name="OWNER_NAME"]', label: '您的姓名' },
    { selector: 'input[name="OWNER_EMAIL"]', label: '您的邮箱' }
  ];

  let isValid = true;

  requiredFields.forEach(field => {
    const element = document.querySelector(field.selector);
    if (!element || !element.value.trim()) {
      console.log(`⚠️ 必填字段为空: ${field.label}`);
      isValid = false;
    }
  });

  // 检查定价选项
  const radioButtons = document.querySelectorAll('input[name="LINK_TYPE"]:checked');
  if (radioButtons.length === 0) {
    console.log('⚠️ 请选择定价选项');
    isValid = false;
  }

  // 检查字符限制
  const description = document.querySelector('textarea[name="DESCRIPTION"]');
  if (description && description.value.length > 500) {
    console.log('⚠️ 描述超过500字符限制');
  }

  const metaDescription = document.querySelector('textarea[name="META_DESCRIPTION"]');
  if (metaDescription && metaDescription.value.length > 250) {
    console.log('⚠️ META描述超过250字符限制');
  }

  if (isValid) {
    console.log('✓ 表单验证通过');
  }

  return isValid;
}

// 目录网站完整价格对比
export function showCompleteDirectoryPriceComparison() {
  console.log('💸 目录网站完整价格对比:');
  console.log('');
  console.log('已配置的目录网站 (6个):');
  console.log('1. australiawebdirectory.net - $12.95/年');
  console.log('2. allstatesusadirectory.com - $6.79');
  console.log('3. freeprwebdirectory.com - $9.99/年');
  console.log('4. freeinternetwebdirectory.com - $6.97/年');
  console.log('5. info-listings.com - $29.99终身');
  console.log('6. qualityinternetdirectory.com - $5.79/年 ⭐ 最便宜');
  console.log('');
  console.log('价格优势排序:');
  console.log('🥇 Quality Internet: $5.79/年 (最佳性价比)');
  console.log('🥈 All States USA: $6.79 (一次性)');
  console.log('🥉 Free Internet: $6.97/年 (有互惠选项)');
  console.log('4️⃣ Free PR Web: $9.99/年 (PR专业)');
  console.log('5️⃣ Australia Web: $12.95/年 (澳洲权威)');
  console.log('6️⃣ Info Listings: $29.99终身 (长期投资)');
  console.log('');
  console.log('推荐策略:');
  console.log('- 预算有限: Quality Internet ($5.79)');
  console.log('- 快速测试: All States USA ($6.79)');
  console.log('- 长期投资: Info Listings ($29.99终身)');
  console.log('- 专业定位: Free PR Web ($9.99)');
}

// 最佳提交建议
export function showBestSubmissionRecommendation() {
  console.log('🎯 最佳提交建议:');
  console.log('');
  console.log('Quality Internet Directory优势:');
  console.log('- 💰 价格最低: $5.79/年');
  console.log('- ⚡ 审核最快: 1-2天');
  console.log('- ✅ 保证收录');
  console.log('- 🔗 有互惠选项: 2周免费');
  console.log('');
  console.log('推荐策略:');
  console.log('1. 首选互惠链接选项（免费，2周）');
  console.log('2. 如需快速，选择付费（$5.79，1-2天）');
  console.log('3. 预算充足，可多目录同时提交');
  console.log('');
  console.log('适合用户:');
  console.log('- 初次尝试付费目录');
  console.log('- 预算有限的个人站长');
  console.log('- 需要快速收录的网站');
  console.log('- 追求性价比的用户');
}
