// AiAgentsDirectory.com 网站规则配置
// 网站: https://aiagentsdirectory.com/submit-agent
// 最后更新: 2025-07-09

export const SITE_RULE = {
  // 基本信息
  domain: 'aiagentsdirectory.com',
  siteName: 'AiAgentsDirectory',
  priority: 1,
  lastUpdated: '2025-07-09',
  
  // 字段映射规则
  fieldMappings: {
    // AI代理名称 -> AI Agent Name
    siteName: {
      selectors: [
        'input[name="name"]',
        '#name',
        'input[placeholder="Enter AI Agent name"]'
      ],
      method: 'value',
      validation: 'required',
      notes: 'AI代理名称（最多35字符）'
    },
    
    // 创建者/公司名称 -> Founders / Company Name
    companyName: {
      selectors: [
        'input[name="createdBy"]',
        '#createdBy',
        'input[placeholder="Enter creator name"]'
      ],
      method: 'value',
      validation: 'optional',
      notes: '创建者或公司名称（最多50字符）'
    },
    
    // 网站URL -> Website URL
    siteUrl: {
      selectors: [
        'input[name="website"]',
        '#website',
        'input[placeholder="Enter website or github url"]'
      ],
      method: 'value',
      validation: 'required|url',
      notes: '网站或GitHub URL（最多100字符）'
    },
    
    // 联盟链接 -> Affiliate Link
    affiliateUrl: {
      selectors: [
        'input[name="affiliateLink"]',
        '#affiliateLink',
        'input[placeholder="Enter affiliate link (if available)"]'
      ],
      method: 'value',
      validation: 'optional|url',
      notes: '联盟链接（可选，最多300字符）'
    },
    
    // 演示URL -> Demo URL
    videoUrl: {
      selectors: [
        'input[name="video"]',
        '#video',
        'input[placeholder="Enter a YouTube or Vimeo URL"]'
      ],
      method: 'value',
      validation: 'optional|url',
      notes: '演示视频URL（YouTube或Vimeo，最多200字符）'
    },
    
    // 文档URL -> Documentation URL
    documentationUrl: {
      selectors: [
        'input[name="documentationUrl"]',
        '#documentationUrl',
        'input[placeholder="Enter documentation URL"]'
      ],
      method: 'value',
      validation: 'optional|url',
      notes: '文档URL（可选，最多200字符）'
    },
    
    // 联系邮箱 -> Contact Email
    contactEmail: {
      selectors: [
        'input[name="email"]',
        '#email',
        'input[placeholder="Enter contact email"]'
      ],
      method: 'value',
      validation: 'required|email',
      notes: '联系邮箱（最多50字符）'
    },
    
    // LinkedIn URL
    linkedinUrl: {
      selectors: [
        'input[name="linkedinUrl"]',
        '#linkedinUrl',
        'input[placeholder="Enter LinkedIn profile URL"]'
      ],
      method: 'value',
      validation: 'optional|url',
      notes: 'LinkedIn个人资料URL（最多100字符）'
    },
    
    // Twitter URL
    twitterUrl: {
      selectors: [
        'input[name="twitterUrl"]',
        '#twitterUrl',
        'input[placeholder="Enter Twitter profile URL"]'
      ],
      method: 'value',
      validation: 'optional|url',
      notes: 'Twitter个人资料URL（最多100字符）'
    },
    
    // GitHub URL
    githubUrl: {
      selectors: [
        'input[name="githubUrl"]',
        '#githubUrl',
        'input[placeholder="Enter GitHub profile URL"]'
      ],
      method: 'value',
      validation: 'optional|url',
      notes: 'GitHub个人资料URL（最多100字符）'
    },
    
    // Discord URL
    discordUrl: {
      selectors: [
        'input[name="discordUrl"]',
        '#discordUrl',
        'input[placeholder="Enter Discord server URL"]'
      ],
      method: 'value',
      validation: 'optional|url',
      notes: 'Discord服务器URL（最多100字符）'
    },
    
    // Telegram URL
    telegramUrl: {
      selectors: [
        'input[name="telegramUrl"]',
        '#telegramUrl',
        'input[placeholder="Enter Telegram URL"]'
      ],
      method: 'value',
      validation: 'optional|url',
      notes: 'Telegram URL（最多100字符）'
    },
    
    // 访问模式 -> Access Model (选择Open Source)
    accessModel: {
      selectors: [
        'input[name="access"][value="Open Source"]',
        'input[value="Open Source"]'
      ],
      method: 'radio',
      validation: 'required',
      targetValue: 'Open Source',
      defaultValue: 'Open Source',
      notes: '访问模式，选择Open Source'
    },
    
    // 定价模式 -> Pricing Model (选择Free)
    pricing: {
      selectors: [
        'input[name="pricingModel"][value="Free"]',
        'input[name="pricingModel"]'
      ],
      method: 'radio',
      validation: 'required',
      targetValue: 'Free',
      defaultValue: 'Free',
      notes: '定价模式，选择Free'
    },
    
    // 行业 -> Industry (选择Horizontal)
    industry: {
      selectors: [
        'input[name="industry"][value="Horizontal"]',
        'input[name="industry"]'
      ],
      method: 'radio',
      validation: 'required',
      targetValue: 'Horizontal',
      defaultValue: 'Horizontal',
      notes: '行业类型，选择Horizontal'
    },
    
    // 分类 -> Category (选择AI Agents Platform)
    category: {
      selectors: [
        'input[name="category"][value="AI Agents Platform"]',
        'input[name="category"]'
      ],
      method: 'radio',
      validation: 'required',
      targetValue: 'AI Agents Platform',
      defaultValue: 'AI Agents Platform',
      notes: '分类，选择AI Agents Platform'
    },
    
    // 标语 -> Tagline
    tagline: {
      selectors: [
        'textarea[name="shortDescription"]',
        '#shortDescription',
        'textarea[placeholder*="AI Agent card"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '标语（最多100字符）'
    },
    
    // 详细描述 -> Description
    detailedIntro: {
      selectors: [
        'textarea[name="longDescription"]',
        '#longDescription',
        'textarea[placeholder*="AI Agent page"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '详细描述（最多750字符）'
    },
    
    // 关键功能 -> Key Features
    features: {
      selectors: [
        'textarea[name="keyFeatures"]',
        '#keyFeatures',
        'textarea[placeholder*="AI-powered content generation"]'
      ],
      method: 'value',
      validation: 'optional',
      notes: '关键功能（每行一个功能，最多600字符）'
    },
    
    // 使用案例 -> Use Cases
    useCases: {
      selectors: [
        'textarea[name="useCases"]',
        '#useCases',
        'textarea[placeholder*="Content creation for marketing"]'
      ],
      method: 'value',
      validation: 'optional',
      notes: '使用案例（每行一个案例，最多600字符）'
    }
  },
  
  // 提交流程配置
  submitConfig: {
    submitButton: 'button[type="submit"], button:contains("Submit AI Agent")',
    submitMethod: 'click',
    waitAfterFill: 3000, // 填写后等待3秒
    waitAfterSubmit: 5000, // 提交后等待5秒
    successIndicators: [
      '.success-message',
      '.alert-success',
      '.notification-success',
      '[class*="success"]'
    ],
    errorIndicators: [
      '.error-message',
      '.alert-error',
      '.alert-danger',
      '[class*="error"]'
    ]
  },
  
  // 特殊处理
  specialHandling: {
    requiresLogin: false,
    hasCaptcha: false,
    hasFileUpload: true, // 有Logo和缩略图上传
    
    // 表单验证规则
    formValidation: {
      requiredFields: ['siteName', 'siteUrl', 'contactEmail', 'accessModel', 'pricing', 'industry', 'category', 'tagline', 'detailedIntro'],
      emailValidation: true,
      urlValidation: true
    },
    
    // 特殊注意事项
    notes: [
      '非常详细的AI代理提交表单',
      '表单包含20个字段：基本信息、社交媒体、分类、描述等',
      '必填字段：代理名称、网站URL、邮箱、访问模式、定价、行业、分类、标语、描述',
      '有文件上传功能（Logo图标必填，缩略图可选）',
      '支持多种社交媒体链接',
      '使用Tailwind CSS和现代响应式设计',
      '有字符计数显示',
      '支持多种分类选择（63个分类）'
    ]
  }
};

// 自定义处理函数
export function handleAiAgentsDirectorySubmission(data, rule) {
  console.log('Processing AiAgentsDirectory.com submission...');

  // 特殊处理逻辑
  const processedData = { ...data };

  // 确保URL格式正确
  if (processedData.siteUrl && !processedData.siteUrl.startsWith('http')) {
    processedData.siteUrl = 'https://' + processedData.siteUrl;
  }

  // 处理其他URL字段
  const urlFields = ['affiliateUrl', 'videoUrl', 'documentationUrl', 'linkedinUrl', 'twitterUrl', 'githubUrl', 'discordUrl', 'telegramUrl'];
  urlFields.forEach(field => {
    if (processedData[field] && !processedData[field].startsWith('http')) {
      processedData[field] = 'https://' + processedData[field];
    }
  });

  // 设置默认值
  processedData.accessModel = 'Open Source';
  processedData.pricing = 'Free';
  processedData.industry = 'Horizontal';
  processedData.category = 'AI Agents Platform';

  // 如果没有公司名称，使用网站名称
  if (!processedData.companyName && processedData.siteName) {
    processedData.companyName = processedData.siteName;
  }

  // 设置默认功能和使用案例
  if (!processedData.features) {
    processedData.features = 'AI-powered automation\nUser-friendly interface\nReal-time processing\nMulti-platform support\nAdvanced analytics';
  }

  if (!processedData.useCases) {
    processedData.useCases = 'Business process automation\nContent generation\nData analysis\nCustomer support\nWorkflow optimization';
  }

  return processedData;
}

// 自定义元素填写函数
export async function customFillElement(element, value, config) {
  console.log('🔧 AiAgentsDirectory自定义填写函数被调用:', element, value);

  // 处理单选按钮
  if (element.type === 'radio') {
    try {
      element.checked = true;
      element.dispatchEvent(new Event('change', { bubbles: true }));
      console.log('✅ 选择单选按钮:', config.defaultValue);
      return true;
    } catch (error) {
      console.warn('单选按钮选择失败:', error);
    }
  }

  // 处理文件上传提示
  if (element.type === 'file') {
    console.log('⚠️ 检测到文件上传字段，需要用户手动上传文件');
    return false; // 让用户手动处理文件上传
  }

  // 处理带有字符计数的输入框
  if (element.hasAttribute('maxlength')) {
    try {
      element.value = value;
      element.dispatchEvent(new Event('input', { bubbles: true }));
      element.dispatchEvent(new Event('change', { bubbles: true }));

      // 更新字符计数显示
      const maxLength = element.getAttribute('maxlength');
      const currentLength = value.length;
      const counterElement = element.parentElement.querySelector('.text-green-400');
      if (counterElement) {
        counterElement.textContent = `${currentLength}/${maxLength}`;
      }

      console.log('✅ 填写带字符计数的字段:', value);
      return true;
    } catch (error) {
      console.warn('字符计数字段填写失败:', error);
    }
  }

  // 默认处理
  return false;
}
