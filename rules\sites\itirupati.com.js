// itirupati.com 网站规则配置
// 网站: https://itirupati.com/submit-your-app-tool/
// 表单技术: 自定义表单
// 最后更新: 2025-07-07

export const SITE_RULE = {
  // 基本信息
  domain: 'itirupati.com',
  siteName: 'iTirupati',
  priority: 1,
  lastUpdated: '2025-07-07',
  
  // 字段映射规则
  fieldMappings: {
    // 工具名称 -> App/Tool Name
    siteName: {
      selectors: [
        'input[name="text-1"]',
        '#forminator-field-text-1_686c5c681cbcd',
        'input[name*="text-1"]',
        '.forminator-name--field'
      ],
      method: 'value',
      validation: 'required',
      notes: '工具名称，使用website-info.js中的siteName字段'
    },

    // 网站URL -> Website URL
    siteUrl: {
      selectors: [
        'input[name="url-1"]',
        '#forminator-field-url-1_686c5c681cbcd',
        'input[name*="url-1"]',
        '.forminator-website--field'
      ],
      method: 'value',
      validation: 'required|url',
      notes: '工具网站URL，使用website-info.js中的siteUrl字段'
    },

    // 联系邮箱 -> Email Address
    contactEmail: {
      selectors: [
        'input[name="email-1"]',
        '#forminator-field-email-1_686c5c681cbcd',
        'input[name*="email-1"]',
        '.forminator-email--field'
      ],
      method: 'value',
      validation: 'required|email',
      notes: '联系邮箱，使用website-info.js中的contactEmail字段'
    },

    // 工具概述 -> App/Tool Overview
    siteDescription: {
      selectors: [
        'textarea[name="textarea-1"]',
        '#forminator-field-textarea-1_686c5c681cbcd',
        'textarea[name*="textarea-1"]'
      ],
      method: 'value',
      validation: 'optional',
      notes: '工具概述，使用website-info.js中的siteDescription字段'
    },

    // 工作原理 -> How it works?
    howToUse: {
      selectors: [
        'textarea[name="textarea-2"]',
        '#forminator-field-textarea-2_686c5c681cbcd',
        'textarea[name*="textarea-2"]'
      ],
      method: 'value',
      validation: 'optional',
      notes: '工作原理描述，使用website-info.js中的howToUse字段'
    },

    // 关键特性和优势 -> Key features and benefits
    features: {
      selectors: [
        'textarea[name="textarea-3"]',
        '#forminator-field-textarea-3_686c5c681cbcd',
        'textarea[name*="textarea-3"]'
      ],
      method: 'value',
      validation: 'optional',
      notes: '关键特性，使用website-info.js中的features字段'
    },

    // 目标用户 -> Who can use it?
    targetAudience: {
      selectors: [
        'textarea[name="textarea-4"]',
        '#forminator-field-textarea-4_686c5c681cbcd',
        'textarea[name*="textarea-4"]'
      ],
      method: 'value',
      validation: 'optional',
      notes: '目标用户，使用website-info.js中的targetAudience字段'
    },

    // 定价计划 -> Pricing and plans
    pricing: {
      selectors: [
        'textarea[name="textarea-5"]',
        '#forminator-field-textarea-5_686c5c681cbcd',
        'textarea[name*="textarea-5"]'
      ],
      method: 'value',
      validation: 'optional',
      notes: '定价信息，使用website-info.js中的pricing字段'
    },

    // 支持信息 -> Support
    uniqueSellingPoints: {
      selectors: [
        'textarea[name="textarea-6"]',
        '#forminator-field-textarea-6_686c5c681cbcd',
        'textarea[name*="textarea-6"]'
      ],
      method: 'value',
      validation: 'optional',
      notes: '支持信息，使用website-info.js中的uniqueSellingPoints字段'
    },

    // 集成信息 -> Integrations
    techStack: {
      selectors: [
        'textarea[name="textarea-7"]',
        '#forminator-field-textarea-7_686c5c681cbcd',
        'textarea[name*="textarea-7"]'
      ],
      method: 'value',
      validation: 'optional',
      notes: '集成信息，使用website-info.js中的techStack字段'
    },

    // 评价和评分 -> Reviews and ratings
    userRating: {
      selectors: [
        'textarea[name="textarea-8"]',
        '#forminator-field-textarea-8_686c5c681cbcd',
        'textarea[name*="textarea-8"]'
      ],
      method: 'value',
      validation: 'optional',
      notes: '评价信息，使用website-info.js中的userRating字段，格式：Overall user rating: {userRating}'
    },

    // 常见问题 -> FAQs about your app
    faqs: {
      selectors: [
        'textarea[name="textarea-9"]',
        '#forminator-field-textarea-9_686c5c681cbcd',
        'textarea[name*="textarea-9"]'
      ],
      method: 'value',
      validation: 'optional',
      notes: 'FAQ信息，使用website-info.js中的faqs字段'
    },

    // 社交链接 -> Social links
    socialLinks: {
      selectors: [
        'textarea[name="textarea-10"]',
        '#forminator-field-textarea-10_686c5c681cbcd',
        'textarea[name*="textarea-10"]'
      ],
      method: 'value',
      validation: 'optional',
      notes: '社交链接，使用website-info.js中的socialLinks字段'
    },

    // 广告位询问 -> Are you interested in buying ad space?
    adSpace: {
      selectors: [
        'input[name="radio-1"][value="two"]',
        '#forminator-field-radio-1-2-686c5c681cbcd',
        'input[name*="radio-1"][value="two"]'
      ],
      method: 'radio',
      validation: 'required',
      defaultValue: 'No',
      notes: '广告位询问，默认选择No (value="two")'
    }
  },
  
  // 自定义填写函数
  customFillElement: async function(element, value, config) {
    console.log(`iTirupati自定义填写: ${element.name || element.placeholder}, 方法: ${config.method}`);
    
    switch (config.method) {
      case 'value':
        // 标准输入框和文本域处理
        element.focus();
        await new Promise(resolve => setTimeout(resolve, 100));

        // 清空现有内容
        element.value = '';
        await new Promise(resolve => setTimeout(resolve, 50));

        // 特殊处理userRating字段
        let finalValue = value;
        if (element.name === 'textarea-8' || element.id.includes('textarea-8')) {
          // 这是Reviews and ratings字段，需要特殊格式
          finalValue = `Overall user rating: ${value}`;
        }

        // 设置新值
        element.value = finalValue;

        // 触发事件
        element.dispatchEvent(new Event('input', { bubbles: true }));
        element.dispatchEvent(new Event('change', { bubbles: true }));
        element.dispatchEvent(new Event('blur', { bubbles: true }));

        console.log(`✓ 填写字段: ${element.placeholder || element.name} = "${finalValue.substring(0, 50)}..."`);
        break;
        
      case 'radio':
        // 单选按钮处理 - 广告位询问选择No
        if (element.type === 'radio') {
          // 对于广告位询问，选择value="two"对应的No选项
          if (element.name === 'radio-1' && element.value === 'two') {
            element.checked = true;
            element.dispatchEvent(new Event('change', { bubbles: true }));
            console.log(`✓ 选择广告位选项: No`);
          }
        }
        break;
        
      default:
        element.value = value;
        element.dispatchEvent(new Event('input', { bubbles: true }));
        element.dispatchEvent(new Event('change', { bubbles: true }));
    }
  },

  // 提交流程配置
  submitConfig: {
    submitButton: [
      'button[type="submit"]',
      'input[type="submit"]',
      'button:contains("Send Message")',
      '.submit-button',
      '.send-button'
    ],
    submitMethod: 'click',
    waitAfterFill: 2000,
    waitAfterSubmit: 5000,
    successIndicators: [
      '.success-message',
      '.thank-you',
      '.confirmation',
      '[class*="success"]'
    ],
    errorIndicators: [
      '.error-message',
      '.validation-error',
      '[class*="error"]'
    ]
  },

  // 特殊处理
  specialHandling: {
    requiresLogin: false,
    hasCaptcha: false,
    hasFileUpload: true, // 有Logo和截图上传功能
    hasMultipleTextareas: true, // 有多个文本域字段

    // 表单验证规则
    formValidation: {
      requiredFields: ['siteName', 'siteUrl', 'contactEmail', 'adSpace'],
      emailValidation: true,
      urlValidation: true
    },

    // 特殊注意事项
    notes: [
      '这是Forminator表单，使用WordPress插件',
      '有文件上传功能（Logo和截图），需要手动处理',
      '包含多个可选的文本域字段，都使用website-info.js对应字段',
      '广告位询问是必填单选按钮，选择No (value="two")',
      '评价字段特殊格式：Overall user rating: {userRating}',
      '所有字段都使用website-info.js中的真实数据，不使用默认值',
      '字段ID格式：forminator-field-{type}-{number}_686c5c681cbcd',
      '表单提交后需要人工审核',
      '网站专注于AI工具收录和推广'
    ]
  }
};

// 自定义处理函数
export function handleItirupatiSubmission(data, _rule) {
  console.log('Processing iTirupati form submission...');

  const processedData = { ...data };

  // 确保URL格式正确
  if (processedData.siteUrl && !processedData.siteUrl.startsWith('http')) {
    processedData.siteUrl = 'https://' + processedData.siteUrl;
  }

  // 处理评价字段的特殊格式
  if (processedData.userRating) {
    processedData.userRating = `Overall user rating: ${processedData.userRating}`;
  }

  // 确保广告位选择为No
  processedData.adSpace = 'No';

  return processedData;
}
