// aitoolforbusiness.com 网站规则配置
// 网站: https://aitoolforbusiness.com/submit-ai-tool/
// 表单技术: Elementor Forms
// 最后更新: 2025-07-07

export const SITE_RULE = {
  // 基本信息
  domain: 'aitoolforbusiness.com',
  siteName: 'AI Tool For Business',
  priority: 1,
  lastUpdated: '2025-07-07',
  
  // 字段映射规则
  fieldMappings: {
    // AI工具名称 -> AI 工具名稱
    siteName: {
      selectors: [
        'input[id="form-field-name"]',
        'input[name="form_fields[name]"]',
        'input[placeholder="AI 工具全名"]'
      ],
      method: 'value',
      validation: 'required',
      notes: 'AI工具名称，使用website-info.js中的siteName字段'
    },
    
    // 网站链接 -> 網站鏈結
    siteUrl: {
      selectors: [
        'input[id="form-field-field_a022d25"]',
        'input[name="form_fields[field_a022d25]"]',
        'input[type="url"]:first-of-type'
      ],
      method: 'value',
      validation: 'required|url',
      notes: '网站链接，使用website-info.js中的siteUrl字段'
    },
    
    // 网站Logo URL -> 網站 Logo URL
    faviconUrl: {
      selectors: [
        'input[id="form-field-field_5175a7b"]',
        'input[name="form_fields[field_5175a7b]"]',
        'input[type="url"]:last-of-type'
      ],
      method: 'value',
      validation: 'optional|url',
      notes: '网站Logo URL，使用website-info.js中的faviconUrl字段'
    },
    
    // 类别 -> 類別 (選最適合的)
    category: {
      selectors: [
        'select[id="form-field-field_86f40e6"]',
        'select[name="form_fields[field_86f40e6]"]',
        '.elementor-field-textual.elementor-size-sm'
      ],
      method: 'select',
      validation: 'required',
      defaultValue: 'AI 寫作工具',
      notes: '工具类别，从下拉列表中选择最匹配的分类'
    },
    
    // 你的姓名 -> 你的姓名
    fullName: {
      selectors: [
        'input[id="form-field-field_1d4c93d"]',
        'input[name="form_fields[field_1d4c93d]"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '提交者姓名，使用website-info.js中的fullName字段'
    },
    
    // 你在公司的职位 -> 你在公司的職位
    submitterRole: {
      selectors: [
        'input[id="form-field-field_d71c31b"]',
        'input[name="form_fields[field_d71c31b]"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '提交者职位，使用website-info.js中的submitterRole字段'
    },
    
    // 你的常用电邮 -> 你的常用電郵
    contactEmail: {
      selectors: [
        'input[id="form-field-email"]',
        'input[name="form_fields[email]"]',
        'input[type="email"]'
      ],
      method: 'value',
      validation: 'required|email',
      notes: '联系邮箱，使用website-info.js中的contactEmail字段'
    }
  },
  
  // 自定义填写函数
  customFillElement: async function(element, value, config) {
    console.log(`AI Tool For Business自定义填写: ${element.id || element.name}, 方法: ${config.method}`);
    
    switch (config.method) {
      case 'value':
        // 标准输入框处理
        element.focus();
        await new Promise(resolve => setTimeout(resolve, 100));
        
        // 清空现有内容
        element.value = '';
        await new Promise(resolve => setTimeout(resolve, 50));
        
        // 设置新值
        element.value = value;
        
        // 触发事件
        element.dispatchEvent(new Event('input', { bubbles: true }));
        element.dispatchEvent(new Event('change', { bubbles: true }));
        element.dispatchEvent(new Event('blur', { bubbles: true }));
        
        console.log(`✓ 填写字段: ${element.id} = "${value.substring(0, 50)}..."`);
        break;
        
      case 'select':
        // 下拉选择框处理
        if (element.tagName.toLowerCase() === 'select') {
          // 分类映射表（中文）
          const categoryMapping = {
            'AI Tools': 'AI 寫作工具',
            'Developer Tools': 'AI 資訊科技工具',
            'Content Creation': 'AI 寫作工具',
            'Image Generation': 'AI 圖像工具',
            'Video Generation': 'AI 影片工具',
            'Audio Tools': 'AI 聲音工具',
            'Chatbot': 'AI 聊天機器人工具',
            'Design': 'AI 圖像工具',
            'Marketing': 'AI 行銷工具',
            'Productivity': 'AI 行政工具',
            'Education': 'AI 研究工具',
            'Finance': 'AI 財務工具',
            'Healthcare': 'AI 研究工具',
            'Writing': 'AI 寫作工具',
            'Music': 'AI 音樂工具',
            'SEO': 'AI 搜尋引擎優化 SEO 工具',
            'Social Media': 'AI 社群媒體工具',
            'Translation': 'AI 翻譯工具',
            'Sales': 'AI 銷售工具',
            'Customer Service': 'AI 客戶服務工具',
            'HR': 'AI 人力資源工具'
          };
          
          // 尝试映射分类
          let targetValue = categoryMapping[value] || config.defaultValue;
          
          // 查找匹配的选项
          const option = Array.from(element.options).find(opt => 
            opt.value === targetValue || 
            opt.text === targetValue ||
            opt.text.includes(targetValue) ||
            targetValue.includes(opt.text)
          );
          
          if (option) {
            element.value = option.value;
            element.dispatchEvent(new Event('change', { bubbles: true }));
            console.log(`✓ 选择分类: ${option.text}`);
          } else {
            // 使用默认值
            const defaultOption = Array.from(element.options).find(opt => 
              opt.text === config.defaultValue || opt.value === config.defaultValue
            );
            if (defaultOption) {
              element.value = defaultOption.value;
              element.dispatchEvent(new Event('change', { bubbles: true }));
              console.log(`✓ 使用默认分类: ${defaultOption.text}`);
            }
          }
        }
        break;
        
      default:
        element.value = value;
        element.dispatchEvent(new Event('input', { bubbles: true }));
        element.dispatchEvent(new Event('change', { bubbles: true }));
    }
  },
  
  // 提交流程配置
  submitConfig: {
    submitButton: [
      'button[type="submit"]',
      '.elementor-button',
      'button:contains("遞交")'
    ],
    submitMethod: 'click',
    waitAfterFill: 2000,
    waitAfterSubmit: 5000,
    successIndicators: [
      '.elementor-message-success',
      '.success-message',
      '.thank-you',
      '[class*="success"]'
    ],
    errorIndicators: [
      '.elementor-message-danger',
      '.error-message',
      '[class*="error"]'
    ]
  },
  
  // 特殊处理
  specialHandling: {
    requiresLogin: false,
    hasCaptcha: false,
    hasFileUpload: false,
    isElementorForm: true, // 使用Elementor表单
    isChineseWebsite: true, // 中文网站
    
    // 表单验证规则
    formValidation: {
      requiredFields: ['siteName', 'siteUrl', 'category', 'fullName', 'submitterRole', 'contactEmail'],
      emailValidation: true,
      urlValidation: true
    },
    
    // 特殊注意事项
    notes: [
      '这是中文网站，使用Elementor表单插件',
      '表单包含7个字段，6个必填，1个可选',
      '分类选项全部为中文，共21个选项',
      '默认选择：AI 寫作工具',
      '网站Logo URL是可选字段',
      '需要提供提交者的姓名和职位',
      '专注于商业AI工具推广',
      '表单字段名格式：form_fields[field_id]'
    ]
  }
};

// 自定义处理函数
export function handleAIToolForBusinessSubmission(data, _rule) {
  console.log('Processing AI Tool For Business form submission...');

  const processedData = { ...data };

  // 确保URL格式正确
  if (processedData.siteUrl && !processedData.siteUrl.startsWith('http')) {
    processedData.siteUrl = 'https://' + processedData.siteUrl;
  }

  // 确保Logo URL格式正确
  if (processedData.faviconUrl && !processedData.faviconUrl.startsWith('http')) {
    processedData.faviconUrl = 'https://' + processedData.faviconUrl;
  }

  // 处理分类映射
  if (processedData.category) {
    const categoryMapping = {
      'AI Tools': 'AI 寫作工具',
      'Developer Tools': 'AI 資訊科技工具',
      'Content Creation': 'AI 寫作工具',
      'Image Generation': 'AI 圖像工具',
      'Video Generation': 'AI 影片工具',
      'Audio Tools': 'AI 聲音工具',
      'Chatbot': 'AI 聊天機器人工具',
      'Design': 'AI 圖像工具',
      'Marketing': 'AI 行銷工具',
      'Productivity': 'AI 行政工具',
      'Education': 'AI 研究工具',
      'Finance': 'AI 財務工具',
      'Healthcare': 'AI 研究工具',
      'Writing': 'AI 寫作工具'
    };
    
    processedData.category = categoryMapping[processedData.category] || 'AI 寫作工具';
  }

  return processedData;
}
