// allstatesusadirectory.com 网站规则配置
// 网站: https://www.allstatesusadirectory.com/submit.php
// 表单技术: PHP Form with CAPTCHA
// 最后更新: 2025-07-08

export const SITE_RULE = {
  // 基本信息
  domain: 'allstatesusadirectory.com',
  siteName: 'All States USA Directory',
  priority: 1,
  lastUpdated: '2025-07-08',
  
  // 字段映射规则
  fieldMappings: {
    // 定价选项 -> Pricing
    linkType: {
      selectors: [
        'input[name="LINK_TYPE"][value="normal"]',
        'input[type="radio"][value="normal"]',
        'input[value="normal"]'
      ],
      method: 'radio',
      validation: 'required',
      defaultValue: 'normal',
      availableOptions: ['featured', 'normal'],
      notes: '定价选项，默认选择normal (Regular links，免费，8-10周审核，通过率低)'
    },
    
    // 标题 -> Title
    siteName: {
      selectors: [
        'input[name="TITLE"]',
        'input.text:first-of-type',
        'input[maxlength="100"]',
        'input[size="40"]:first-of-type'
      ],
      method: 'value',
      validation: 'required',
      notes: '网站标题，使用website-info.js中的siteName字段'
    },
    
    // URL -> URL
    siteUrl: {
      selectors: [
        'input[name="URL"]',
        'input[maxlength="255"]',
        'input.text:nth-of-type(2)',
        'input[size="40"]:nth-of-type(2)'
      ],
      method: 'value',
      validation: 'optional|url',
      notes: '网站URL，使用website-info.js中的siteUrl字段'
    },
    
    // 描述 -> Description
    detailedIntro: {
      selectors: [
        'textarea[name="DESCRIPTION"]',
        'textarea.text:first-of-type',
        'textarea[rows="3"]:first-of-type',
        'textarea[style*="width: 440px"]'
      ],
      method: 'value',
      validation: 'optional',
      maxLength: 1000,
      notes: '网站详细描述，使用website-info.js中的detailedIntro字段，限制1000字符'
    },
    
    // META关键词 -> META Keywords
    keywords: {
      selectors: [
        'input[name="META_KEYWORDS"]',
        'input[maxlength="2000"]',
        'input.text:nth-of-type(3)',
        'input[size="40"]:nth-of-type(3)'
      ],
      method: 'value',
      validation: 'optional',
      notes: 'META关键词，使用website-info.js中的keywords字段'
    },
    
    // META描述 -> META Description
    siteDescription: {
      selectors: [
        'textarea[name="META_DESCRIPTION"]',
        'textarea.text:nth-of-type(2)',
        'textarea[rows="3"]:nth-of-type(2)',
        'textarea[cols="30"]'
      ],
      method: 'value',
      validation: 'optional',
      maxLength: 250,
      notes: 'META描述，使用website-info.js中的siteDescription字段，限制250字符'
    },
    
    // 您的姓名 -> Your Name
    fullName: {
      selectors: [
        'input[name="OWNER_NAME"]',
        'input[maxlength="50"]',
        'input.text:nth-of-type(4)',
        'input[size="40"]:nth-of-type(4)'
      ],
      method: 'value',
      validation: 'required',
      notes: '您的姓名，使用website-info.js中的fullName字段'
    },
    
    // 您的邮箱 -> Your Email
    contactEmail: {
      selectors: [
        'input[name="OWNER_EMAIL"]',
        'input.text:nth-of-type(5)',
        'input[size="40"]:nth-of-type(5)',
        'input[maxlength="255"]:nth-of-type(2)'
      ],
      method: 'value',
      validation: 'required|email',
      notes: '您的邮箱，使用website-info.js中的contactEmail字段'
    }
  },
  
  // 自定义填写函数
  customFillElement: async function(element, value, config) {
    console.log(`All States USA Directory自定义填写: ${element.name || element.type}, 方法: ${config.method}`);
    
    switch (config.method) {
      case 'value':
        // 标准输入框和文本域处理
        element.focus();
        await new Promise(resolve => setTimeout(resolve, 100));
        
        // 清空现有内容
        element.value = '';
        await new Promise(resolve => setTimeout(resolve, 50));
        
        // 处理字符限制
        let finalValue = value;
        if (config.maxLength && finalValue.length > config.maxLength) {
          finalValue = finalValue.substring(0, config.maxLength);
          console.log(`⚠️ 内容被截断到${config.maxLength}字符: ${finalValue}`);
        }
        
        // 设置新值
        element.value = finalValue;
        
        // 触发事件
        element.dispatchEvent(new Event('input', { bubbles: true }));
        element.dispatchEvent(new Event('change', { bubbles: true }));
        element.dispatchEvent(new Event('blur', { bubbles: true }));
        
        console.log(`✓ 填写字段: ${element.name} = "${finalValue.substring(0, 50)}..."`);
        break;
        
      case 'radio':
        // 单选按钮处理
        console.log(`处理定价选项，目标值: ${config.defaultValue}`);
        
        // 查找所有同名单选按钮
        const radioButtons = document.querySelectorAll('input[name="LINK_TYPE"]');
        
        // 先取消所有选择
        radioButtons.forEach(rb => {
          rb.checked = false;
        });
        
        // 选择目标选项
        const targetRadio = Array.from(radioButtons).find(rb => 
          rb.value === config.defaultValue
        );
        
        if (targetRadio) {
          targetRadio.checked = true;
          targetRadio.dispatchEvent(new Event('change', { bubbles: true }));
          console.log(`✓ 选择定价选项: Regular links (${config.defaultValue})`);
        } else {
          console.log(`⚠️ 未找到定价选项: ${config.defaultValue}`);
        }
        break;
        
      default:
        element.value = value;
        element.dispatchEvent(new Event('input', { bubbles: true }));
        element.dispatchEvent(new Event('change', { bubbles: true }));
    }
  },
  
  // 提交流程配置
  submitConfig: {
    submitButton: [
      'button[type="submit"]',
      'input[type="submit"]',
      'button:contains("Submit")',
      'input[value*="Submit"]'
    ],
    submitMethod: 'click',
    waitAfterFill: 2000,
    waitAfterSubmit: 5000,
    successIndicators: [
      'text:contains("submitted")',
      'text:contains("thank you")',
      'text:contains("success")',
      'text:contains("approved")'
    ],
    errorIndicators: [
      'text:contains("error")',
      'text:contains("required")',
      'text:contains("invalid")',
      'text:contains("captcha")'
    ]
  },
  
  // 特殊处理
  specialHandling: {
    requiresLogin: false,
    hasCaptcha: true, // 可能有验证码
    hasFileUpload: false,
    isPHPForm: true, // PHP表单
    isUSADirectory: true, // 美国目录
    hasMetaFields: true, // 有META字段
    hasPaidOptions: true, // 有付费选项
    hasCharacterLimits: true, // 有字符限制
    
    // 表单验证规则
    formValidation: {
      requiredFields: ['linkType', 'siteName', 'fullName', 'contactEmail'],
      optionalFields: ['siteUrl', 'detailedIntro', 'keywords', 'siteDescription'],
      emailValidation: true,
      urlValidation: true,
      characterLimits: {
        detailedIntro: 1000,
        siteDescription: 250
      },
      radioGroups: ['linkType']
    },
    
    // 特殊注意事项
    notes: [
      '这是All States USA Directory的网站提交表单',
      '表单包含8个字段：4个必填，4个可选',
      '美国各州目录网站，接受全球网站',
      '可能有验证码保护，需要手动处理',
      '默认选择Regular links（免费，8-10周审核，通过率低）',
      '有付费选项：Featured links $6.79（24小时内审核，保证通过）',
      '包含META字段：关键词和描述',
      '描述限制1000字符，META描述限制250字符',
      '使用实际字段名：LINK_TYPE, TITLE, URL, DESCRIPTION, META_KEYWORDS, META_DESCRIPTION, OWNER_NAME, OWNER_EMAIL',
      '与australiawebdirectory.net类似的表单结构',
      '专注于美国各州的网站收录'
    ]
  }
};

// 自定义处理函数
export function handleAllStatesUSADirectorySubmission(data, _rule) {
  console.log('Processing All States USA Directory form submission...');

  const processedData = { ...data };

  // 确保URL格式正确
  if (processedData.siteUrl && !processedData.siteUrl.startsWith('http')) {
    processedData.siteUrl = 'https://' + processedData.siteUrl;
  }

  // 处理字符限制
  if (processedData.detailedIntro && processedData.detailedIntro.length > 1000) {
    processedData.detailedIntro = processedData.detailedIntro.substring(0, 1000);
  }

  if (processedData.siteDescription && processedData.siteDescription.length > 250) {
    processedData.siteDescription = processedData.siteDescription.substring(0, 250);
  }

  // 设置默认值
  processedData.linkType = 'normal'; // Regular links

  return processedData;
}

// 美国目录信息提醒
export function showAllStatesUSADirectoryInfo() {
  console.log('🇺🇸 All States USA Directory 信息:');
  console.log('');
  console.log('平台特色:');
  console.log('- 美国各州专业网站目录');
  console.log('- 接受全球网站提交');
  console.log('- 与澳大利亚目录类似的结构');
  console.log('- 包含META字段优化');
  console.log('');
  console.log('提交选项 (2种):');
  console.log('1. Featured links - $6.79');
  console.log('   - 24小时内审核');
  console.log('   - 保证通过');
  console.log('');
  console.log('2. Regular links - 免费 ✅ 默认选择');
  console.log('   - 8-10周审核');
  console.log('   - 通过率低');
  console.log('');
  console.log('字段特点:');
  console.log('- 包含META关键词和描述');
  console.log('- 描述限制1000字符');
  console.log('- META描述限制250字符');
  console.log('- 支持SEO优化');
  console.log('');
  console.log('All States USA Directory - 美国权威网站目录！');
}

// META字段优化指南
export function showMetaFieldsGuide() {
  console.log('🔍 META字段优化指南:');
  console.log('');
  console.log('META Keywords (关键词):');
  console.log('- 使用逗号分隔');
  console.log('- 3-10个相关关键词');
  console.log('- 避免关键词堆砌');
  console.log('- 与网站内容相关');
  console.log('');
  console.log('META Description (描述):');
  console.log('- 限制250字符以内');
  console.log('- 简洁明了的网站描述');
  console.log('- 包含主要关键词');
  console.log('- 吸引用户点击');
  console.log('');
  console.log('SEO优势:');
  console.log('- 提高搜索引擎排名');
  console.log('- 增加网站曝光度');
  console.log('- 改善点击率');
  console.log('- 优化用户体验');
}

// 字符限制提醒
export function showCharacterLimits() {
  console.log('📏 字符限制提醒:');
  console.log('');
  console.log('字段限制:');
  console.log('- Description: 1000字符');
  console.log('- META Description: 250字符');
  console.log('- META Keywords: 2000字符');
  console.log('- Title: 100字符');
  console.log('- Your Name: 50字符');
  console.log('- Your Email: 255字符');
  console.log('- URL: 255字符');
  console.log('');
  console.log('超长内容会自动截断！');
}

// 表单验证
export function validateAllStatesUSADirectoryForm() {
  console.log('验证All States USA Directory表单...');

  const requiredFields = [
    { selector: 'input[name="TITLE"]', label: '网站标题' },
    { selector: 'input[name="OWNER_NAME"]', label: '您的姓名' },
    { selector: 'input[name="OWNER_EMAIL"]', label: '您的邮箱' }
  ];

  let isValid = true;

  requiredFields.forEach(field => {
    const element = document.querySelector(field.selector);
    if (!element || !element.value.trim()) {
      console.log(`⚠️ 必填字段为空: ${field.label}`);
      isValid = false;
    }
  });

  // 检查定价选项
  const radioButtons = document.querySelectorAll('input[name="LINK_TYPE"]:checked');
  if (radioButtons.length === 0) {
    console.log('⚠️ 请选择定价选项');
    isValid = false;
  }

  if (isValid) {
    console.log('✓ 表单验证通过');
  }

  return isValid;
}

// 美国目录与澳大利亚目录对比
export function showDirectoryComparison() {
  console.log('🆚 目录对比:');
  console.log('');
  console.log('All States USA Directory vs Australia Web Directory:');
  console.log('');
  console.log('相似点:');
  console.log('- 相同的表单结构');
  console.log('- 相同的字段名称');
  console.log('- 免费和付费选项');
  console.log('- PHP表单系统');
  console.log('');
  console.log('差异点:');
  console.log('- 美国 vs 澳大利亚');
  console.log('- $6.79 vs $12.95 (付费价格)');
  console.log('- 8-10周 vs 3-4个月 (审核时间)');
  console.log('- 包含META字段');
  console.log('- 无互惠链接选项');
  console.log('');
  console.log('选择建议:');
  console.log('- 美国用户优先选择All States USA');
  console.log('- 澳大利亚用户优先选择Australia Web');
  console.log('- 全球用户可以都提交');
}

// 定价策略分析
export function showPricingStrategy() {
  console.log('💰 定价策略分析:');
  console.log('');
  console.log('Featured Links ($6.79):');
  console.log('- 24小时快速审核');
  console.log('- 100%保证通过');
  console.log('- 优先展示位置');
  console.log('- 性价比较高');
  console.log('');
  console.log('Regular Links (免费):');
  console.log('- 8-10周长期等待');
  console.log('- 通过率很低');
  console.log('- 无优先展示');
  console.log('- 适合测试');
  console.log('');
  console.log('建议:');
  console.log('- 重要网站选择付费');
  console.log('- 测试网站选择免费');
  console.log('- 考虑时间成本');
  console.log('- 评估通过概率');
}
