// AIRespo.com 网站规则配置
// 网站: https://airespo.com/submit-tool/
// 最后更新: 2025-07-06

export const SITE_RULE = {
  // 基本信息
  domain: 'airespo.com',
  siteName: 'AI Respo',
  priority: 1,
  lastUpdated: '2025-07-06',
  
  // 字段映射规则
  fieldMappings: {
    // 提交者姓名 -> Your Name (使用fullName)
    fullName: {
      selectors: [
        '#submitter_name',
        'input[name="submitter_name"]',
        'input[placeholder*="Your Name"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '提交者姓名，使用website-info.js中的fullName字段'
    },
    
    // 提交者邮箱 -> Your Email (使用contactEmail)
    contactEmail: {
      selectors: [
        '#submitter_email',
        'input[name="submitter_email"]',
        'input[type="email"][placeholder*="Your Email"]'
      ],
      method: 'value',
      validation: 'required|email',
      notes: '提交者邮箱，使用website-info.js中的contactEmail字段'
    },
    
    // 工具名称 -> Tool Name (使用siteName)
    siteName: {
      selectors: [
        '#tool_name',
        'input[name="tool_name"]',
        'input[placeholder*="AI tool name"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '工具名称，使用website-info.js中的siteName字段'
    },
    
    // 工具URL -> Tool Url (使用siteUrl)
    siteUrl: {
      selectors: [
        '#tool_url',
        'input[name="tool_url"]',
        'input[type="url"][placeholder*="Secure website Url"]'
      ],
      method: 'value',
      validation: 'required|url',
      notes: '工具网站URL，使用website-info.js中的siteUrl字段'
    }
  },
  
  // 自定义填写函数
  customFillElement: async function(element, value, config) {
    console.log(`AIRespo自定义填写: ${element.id}, 方法: ${config.method}`);
    
    switch (config.method) {
      case 'value':
        // Elementor表单处理
        element.focus();
        await new Promise(resolve => setTimeout(resolve, 100));
        
        element.value = value;
        element.dispatchEvent(new Event('input', { bubbles: true }));
        element.dispatchEvent(new Event('change', { bubbles: true }));
        element.dispatchEvent(new Event('blur', { bubbles: true }));
        
        console.log(`✓ 填写字段: ${element.id} = "${value}"`);
        break;
        
      default:
        element.value = value;
        element.dispatchEvent(new Event('input', { bubbles: true }));
        element.dispatchEvent(new Event('change', { bubbles: true }));
    }
  },
  
  // 提交流程配置
  submitConfig: {
    submitButton: '#submit-listing, button[role="submit"], button:contains("Submit AI Tool")',
    submitMethod: 'click',
    waitAfterFill: 1000, // 简单表单，等待时间短
    waitAfterSubmit: 3000,
    successIndicators: [
      '.success-message',
      '.thank-you',
      'div:contains("successfully")',
      'div:contains("submitted")'
    ],
    errorIndicators: [
      '.error-message',
      '.validation-error',
      '[class*="error"]'
    ]
  },
  
  // 特殊处理
  specialHandling: {
    requiresLogin: false,
    hasCaptcha: false,
    hasFileUpload: false,
    
    // 表单验证规则
    formValidation: {
      requiredFields: ['fullName', 'contactEmail', 'siteName', 'siteUrl'],
      emailValidation: true,
      urlValidation: true
    },
    
    // 特殊注意事项
    notes: [
      '这是Elementor构建的简单表单',
      '只有4个必填字段',
      '无需注册或登录',
      '不接受联盟链接',
      '提交速度快，审核简单',
      '适合快速提交AI工具'
    ]
  }
};

// 自定义处理函数
export function handleAIRespoSubmission(data, rule) {
  console.log('Processing AI Respo submission...');
  
  const processedData = { ...data };
  
  // 确保URL格式正确
  if (processedData.siteUrl && !processedData.siteUrl.startsWith('http')) {
    processedData.siteUrl = 'https://' + processedData.siteUrl;
  }
  
  return processedData;
}
