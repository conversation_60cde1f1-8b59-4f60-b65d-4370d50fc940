// AI Site Submitter v3.0.0 - 表单检测模块
// 智能检测页面表单元素并提取字段信息，支持 HTML 内容分析
// 最后更新: 2025-07-15

/**
 * 表单检测器类
 */
class FormDetector {
    constructor() {
        this.maxFieldsPerBatch = 20; // 每批最大字段数
        this.supportedInputTypes = [
            'text', 'email', 'url', 'tel', 'password',
            'search', 'number', 'textarea', 'select',
            'radio', 'checkbox', 'file', 'date', 'datetime-local',
            'time', 'month', 'week', 'color', 'range'
        ];
    }

    /**
     * 检测页面中的表单
     * @returns {Promise<Object>} 表单数据
     */
    async detectForms() {
        try {
            console.log('🔍 开始检测页面表单...');

            // 首先检测主页面的表单
            const forms = document.querySelectorAll('form');
            console.log(`发现 ${forms.length} 个 <form> 标签`);

            let formData;

            if (forms.length > 0) {
                // 有 form 标签，选择最合适的表单
                const targetForm = this.selectBestForm(forms);
                formData = await this.extractFormData(targetForm);
            } else {
                // 没有 form 标签，检测页面中的所有输入字段
                console.log('⚠️ 未找到 <form> 标签，尝试检测页面中的输入字段...');
                formData = await this.extractFormDataFromPage();
            }

            // 检查是否主要是 iframe 表单
            const iframes = document.querySelectorAll('iframe');
            const isIframeBasedForm = this.isIframeBasedForm(iframes, formData.fields.length);

            if (isIframeBasedForm) {
                console.log('⚠️ 检测到主要为 iframe 表单，不支持 AI 识别');
                throw new Error('当前网站使用 iframe 表单（如 Typeform、Google Forms 等），暂不支持 AI 识别。请使用有直接 HTML 表单的网站。');
            }

            if (formData.fields.length === 0) {
                throw new Error('当前网页 DOM 节点检测失败 - 未找到有效的表单字段');
            }

            // 如果检测到的字段很少，提取页面 HTML 内容供 AI 分析
            if (formData.fields.length <= 3) {
                console.log('🔍 检测到的字段较少，提取页面 HTML 内容供 AI 分析...');
                formData.htmlContent = this.extractPageHtml();
                console.log(`📄 提取了 ${formData.htmlContent.length} 字符的 HTML 内容`);
            }

            console.log(`✅ 表单检测完成，共 ${formData.fields.length} 个字段`);
            return formData;

        } catch (error) {
            console.error('❌ 表单检测失败:', error);
            throw error;
        }
    }

    /**
     * 选择最佳表单
     * @param {NodeList} forms - 表单列表
     * @returns {Element} 最佳表单元素
     */
    selectBestForm(forms) {
        let bestForm = forms[0];
        let maxScore = 0;

        for (const form of forms) {
            const score = this.calculateFormScore(form);
            if (score > maxScore) {
                maxScore = score;
                bestForm = form;
            }
        }

        console.log(`选择表单，评分: ${maxScore}`);
        return bestForm;
    }

    /**
     * 计算表单评分
     * @param {Element} form - 表单元素
     * @returns {number} 表单评分
     */
    calculateFormScore(form) {
        let score = 0;
        
        // 基础字段数量评分
        const inputs = form.querySelectorAll('input, textarea, select');
        score += inputs.length * 10;
        
        // 可见性评分
        if (this.isElementVisible(form)) {
            score += 50;
        }
        
        // 表单类型评分（提交表单优先）
        const submitButtons = form.querySelectorAll('button[type="submit"], input[type="submit"]');
        score += submitButtons.length * 20;
        
        // 表单大小评分
        const rect = form.getBoundingClientRect();
        if (rect.width > 300 && rect.height > 200) {
            score += 30;
        }
        
        return score;
    }

    /**
     * 提取表单数据
     * @param {Element} form - 表单元素
     * @returns {Promise<Object>} 表单数据
     */
    async extractFormData(form) {
        const fields = [];
        const formInfo = this.getFormInfo(form);
        
        // 获取所有输入字段
        const inputs = form.querySelectorAll('input, textarea, select');
        console.log(`🔍 在表单中发现 ${inputs.length} 个输入元素`);

        for (const input of inputs) {
            const fieldData = this.extractFieldData(input);
            if (fieldData) {
                if (this.isValidField(fieldData)) {
                    fields.push(fieldData);
                    console.log(`✅ 有效字段: ${fieldData.name || fieldData.id} (${fieldData.type})`);
                } else {
                    console.log(`❌ 无效字段: ${fieldData.name || fieldData.id} (${fieldData.type}) - 验证失败`);
                }
            } else {
                console.log(`⚠️ 跳过字段: ${input.type || input.tagName} - 不支持或隐藏`);
            }
        }

        // 如果字段过多，进行分批处理
        const processedFields = this.processBatchFields(fields);
        
        return {
            fields: processedFields,
            formInfo: formInfo,
            totalFields: processedFields.length,
            needsBatching: processedFields.length > this.maxFieldsPerBatch
        };
    }

    /**
     * 从整个页面提取表单数据（当没有 form 标签时）
     * @returns {Promise<Object>} 表单数据
     */
    async extractFormDataFromPage() {
        const fields = [];
        const formInfo = {
            action: window.location.href,
            method: 'POST',
            id: '',
            className: '',
            submitSelector: 'button[type="submit"], input[type="submit"], button',
            submitText: '提交'
        };

        // 获取页面中所有输入字段，包括可能的自定义元素
        const inputs = document.querySelectorAll('input, textarea, select, [contenteditable="true"]');
        console.log(`🔍 在页面中发现 ${inputs.length} 个输入元素`);

        // 额外检查：查找可能的输入容器
        const inputContainers = document.querySelectorAll('[role="textbox"], [role="combobox"], [role="listbox"]');
        console.log(`🔍 发现 ${inputContainers.length} 个可能的输入容器`);

        for (const input of inputs) {
            const fieldData = this.extractFieldData(input);
            if (fieldData) {
                if (this.isValidField(fieldData)) {
                    fields.push(fieldData);
                    console.log(`✅ 有效字段: ${fieldData.name || fieldData.id} (${fieldData.type})`);
                } else {
                    console.log(`❌ 无效字段: ${fieldData.name || fieldData.id} (${fieldData.type}) - 验证失败`);
                }
            } else {
                console.log(`⚠️ 跳过字段: ${input.type || input.tagName} - 不支持或隐藏`);
            }
        }

        // 如果字段过多，进行分批处理
        const processedFields = this.processBatchFields(fields);

        return {
            fields: processedFields,
            formInfo: formInfo,
            totalFields: processedFields.length,
            needsBatching: processedFields.length > this.maxFieldsPerBatch
        };
    }



    /**
     * 获取表单基本信息
     * @param {Element} form - 表单元素
     * @returns {Object} 表单信息
     */
    getFormInfo(form) {
        const submitButton = form.querySelector('button[type="submit"], input[type="submit"], button:not([type])');
        
        return {
            action: form.action || window.location.href,
            method: form.method || 'POST',
            id: form.id || '',
            className: form.className || '',
            submitSelector: submitButton ? this.generateSelector(submitButton) : '',
            submitText: submitButton ? (submitButton.textContent || submitButton.value || '').trim() : ''
        };
    }

    /**
     * 提取字段数据
     * @param {Element} input - 输入元素
     * @returns {Object|null} 字段数据
     */
    extractFieldData(input) {
        const type = input.type || input.tagName.toLowerCase();

        // 处理特殊元素类型
        let actualType = type;
        if (input.hasAttribute('contenteditable')) {
            actualType = 'textarea';
        } else if (input.hasAttribute('role')) {
            const role = input.getAttribute('role');
            if (role === 'textbox') actualType = 'text';
            else if (role === 'combobox') actualType = 'select';
        }

        // 跳过不支持的字段类型
        if (!this.supportedInputTypes.includes(actualType) && actualType !== 'select') {
            console.log(`🚫 不支持的字段类型: ${actualType} (原始: ${type})`);
            return null;
        }

        // 跳过隐藏字段，但放宽可见性检查
        if (type === 'hidden') {
            console.log(`🚫 跳过隐藏字段: ${input.name || input.id}`);
            return null;
        }

        // 放宽可见性检查 - 只检查基本的显示状态
        if (!this.isElementBasicallyVisible(input)) {
            console.log(`🚫 字段不可见: ${input.name || input.id}`);
            return null;
        }

        const fieldData = {
            name: input.name || '',
            id: input.id || '',
            type: actualType,
            selector: this.generateSelector(input),
            label: this.getFieldLabel(input),
            placeholder: input.placeholder || input.getAttribute('placeholder') || '',
            required: input.required || input.hasAttribute('required') || input.hasAttribute('aria-required'),
            value: input.value || input.textContent || '',
            className: input.className || ''
        };

        // 处理选择框选项
        if (type === 'select') {
            fieldData.options = Array.from(input.options).map(option => ({
                value: option.value,
                text: option.textContent.trim()
            }));
        }

        // 处理单选框和复选框
        if (type === 'radio' || type === 'checkbox') {
            fieldData.checked = input.checked;
            fieldData.value = input.value;
        }

        return fieldData;
    }

    /**
     * 获取字段标签
     * @param {Element} input - 输入元素
     * @returns {string} 字段标签
     */
    getFieldLabel(input) {
        // 通过 for 属性查找 label
        if (input.id) {
            const label = document.querySelector(`label[for="${input.id}"]`);
            if (label) {
                return label.textContent.trim();
            }
        }

        // 查找父级 label
        const parentLabel = input.closest('label');
        if (parentLabel) {
            return parentLabel.textContent.replace(input.value || '', '').trim();
        }

        // 查找相邻的文本节点或元素
        const prevSibling = input.previousElementSibling;
        if (prevSibling && (prevSibling.tagName === 'LABEL' || prevSibling.tagName === 'SPAN')) {
            return prevSibling.textContent.trim();
        }

        // 查找父级元素中的文本
        const parent = input.parentElement;
        if (parent) {
            const textContent = parent.textContent.replace(input.value || '', '').trim();
            if (textContent.length < 100) { // 避免获取过长的文本
                return textContent;
            }
        }

        return '';
    }

    /**
     * 生成 CSS 选择器
     * @param {Element} element - DOM 元素
     * @returns {string} CSS 选择器
     */
    generateSelector(element) {
        // 优先使用 ID
        if (element.id) {
            return `#${element.id}`;
        }

        // 使用 name 属性
        if (element.name) {
            return `[name="${element.name}"]`;
        }

        // 使用类名组合
        if (element.className) {
            const classes = element.className.split(' ').filter(cls => cls.trim());
            if (classes.length > 0) {
                return `.${classes.join('.')}`;
            }
        }

        // 使用标签名和属性组合
        let selector = element.tagName.toLowerCase();
        
        if (element.type) {
            selector += `[type="${element.type}"]`;
        }
        
        if (element.placeholder) {
            selector += `[placeholder*="${element.placeholder.substring(0, 20)}"]`;
        }

        return selector;
    }

    /**
     * 检查元素是否可见
     * @param {Element} element - DOM 元素
     * @returns {boolean} 是否可见
     */
    isElementVisible(element) {
        const style = window.getComputedStyle(element);
        return style.display !== 'none' &&
               style.visibility !== 'hidden' &&
               style.opacity !== '0' &&
               element.offsetWidth > 0 &&
               element.offsetHeight > 0;
    }

    /**
     * 基本可见性检查（更宽松）
     * @param {Element} element - DOM 元素
     * @returns {boolean} 是否基本可见
     */
    isElementBasicallyVisible(element) {
        const style = window.getComputedStyle(element);
        // 只检查最基本的显示状态，不检查尺寸
        return style.display !== 'none' &&
               style.visibility !== 'hidden';
    }

    /**
     * 验证字段有效性
     * @param {Object} fieldData - 字段数据
     * @returns {boolean} 是否有效
     */
    isValidField(fieldData) {
        console.log(`🔍 验证字段: ${JSON.stringify({
            name: fieldData.name,
            id: fieldData.id,
            type: fieldData.type,
            label: fieldData.label,
            placeholder: fieldData.placeholder,
            selector: fieldData.selector
        })}`);

        // 必须有选择器
        if (!fieldData.selector) {
            console.log(`❌ 字段验证失败: 缺少选择器`);
            return false;
        }

        // 进一步放宽验证条件：有任何标识信息即可
        const hasIdentifier = fieldData.name || fieldData.id || fieldData.label ||
                             fieldData.placeholder || fieldData.className;

        if (!hasIdentifier) {
            console.log(`❌ 字段验证失败: 缺少任何标识信息`);
            return false;
        }

        // 跳过一些明显无用的字段
        const skipPatterns = ['csrf', 'token', '_method', 'authenticity_token', 'captcha'];
        const fieldIdentifier = (fieldData.name + ' ' + fieldData.id + ' ' + fieldData.className + ' ' + fieldData.label).toLowerCase();

        if (skipPatterns.some(pattern => fieldIdentifier.includes(pattern))) {
            console.log(`❌ 字段验证失败: 匹配跳过模式 - ${fieldIdentifier}`);
            return false;
        }

        console.log(`✅ 字段验证通过`);
        return true;
    }

    /**
     * 处理批量字段
     * @param {Array} fields - 字段数组
     * @returns {Array} 处理后的字段数组
     */
    processBatchFields(fields) {
        if (fields.length <= this.maxFieldsPerBatch) {
            return fields;
        }

        console.log(`字段数量过多 (${fields.length})，进行分批处理`);
        
        // 优先保留重要字段
        const importantFields = fields.filter(field => 
            field.required || 
            this.isImportantField(field)
        );

        const otherFields = fields.filter(field => 
            !field.required && 
            !this.isImportantField(field)
        );

        // 组合重要字段和部分其他字段
        const result = [
            ...importantFields,
            ...otherFields.slice(0, this.maxFieldsPerBatch - importantFields.length)
        ];

        console.log(`保留 ${result.length} 个重要字段`);
        return result;
    }

    /**
     * 判断是否为重要字段
     * @param {Object} field - 字段数据
     * @returns {boolean} 是否重要
     */
    isImportantField(field) {
        const importantKeywords = [
            'name', 'email', 'url', 'title', 'description', 
            'category', 'price', 'contact', 'company', 'website'
        ];

        const fieldText = (field.name + ' ' + field.label + ' ' + field.placeholder).toLowerCase();
        
        return importantKeywords.some(keyword => fieldText.includes(keyword));
    }

    /**
     * 判断是否为 iframe 表单网站
     * @param {NodeList} iframes - iframe 元素列表
     * @param {number} directFieldsCount - 直接检测到的字段数量
     * @returns {boolean} 是否为 iframe 表单
     */
    isIframeBasedForm(iframes, directFieldsCount) {
        // 没有 iframe，肯定不是 iframe 表单
        if (iframes.length === 0) {
            return false;
        }

        // 有 iframe 但没有直接字段，很可能是 iframe 表单
        if (directFieldsCount === 0) {
            return true;
        }

        // 有 iframe 且直接字段很少，检查 iframe 的特征
        if (directFieldsCount <= 2) {
            for (const iframe of iframes) {
                const src = iframe.src || '';
                const iframeIndicators = [
                    'typeform.com',
                    'forms.gle',
                    'google.com/forms',
                    'jotform.com',
                    'wufoo.com',
                    'surveymonkey.com',
                    'formstack.com',
                    'cognito-forms.com',
                    'paperform.co',
                    'tally.so',
                    'airtable.com/embed',
                    'notion.so/embed'
                ];

                // 检查 iframe src 是否包含已知的表单服务
                if (iframeIndicators.some(indicator => src.includes(indicator))) {
                    console.log(`🚫 检测到已知表单服务 iframe: ${src}`);
                    return true;
                }

                // 检查 iframe 的尺寸和位置，判断是否为主要内容
                const rect = iframe.getBoundingClientRect();
                const isLargeIframe = rect.width > 400 && rect.height > 300;
                const isVisibleIframe = rect.top < window.innerHeight && rect.bottom > 0;

                if (isLargeIframe && isVisibleIframe) {
                    console.log(`🚫 检测到大型可见 iframe，可能为表单: ${rect.width}x${rect.height}`);
                    return true;
                }
            }
        }

        return false;
    }

    /**
     * 提取页面 HTML 内容供 AI 分析
     * @returns {string} 处理后的 HTML 内容
     */
    extractPageHtml() {
        try {
            // 获取页面的完整 HTML
            let htmlContent = document.documentElement.outerHTML;

            // 清理和压缩 HTML
            htmlContent = this.cleanHtmlForAI(htmlContent);

            // 如果内容过长，进行分段处理
            const maxLength = 50000; // 50KB 限制
            if (htmlContent.length > maxLength) {
                console.log(`⚠️ HTML 内容过长 (${htmlContent.length} 字符)，进行分段处理...`);
                htmlContent = this.extractRelevantHtmlSections(htmlContent, maxLength);
            }

            return htmlContent;

        } catch (error) {
            console.error('提取页面 HTML 失败:', error);
            return '';
        }
    }

    /**
     * 清理 HTML 内容，移除不必要的部分
     * @param {string} html - 原始 HTML
     * @returns {string} 清理后的 HTML
     */
    cleanHtmlForAI(html) {
        // 移除脚本标签
        html = html.replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '');

        // 移除样式标签
        html = html.replace(/<style\b[^<]*(?:(?!<\/style>)<[^<]*)*<\/style>/gi, '');

        // 移除注释
        html = html.replace(/<!--[\s\S]*?-->/g, '');

        // 压缩空白字符
        html = html.replace(/\s+/g, ' ');

        // 移除一些不必要的属性
        html = html.replace(/\s(data-[^=]*="[^"]*")/g, '');
        html = html.replace(/\s(style="[^"]*")/g, '');

        return html.trim();
    }

    /**
     * 提取相关的 HTML 片段
     * @param {string} html - 完整 HTML
     * @param {number} maxLength - 最大长度
     * @returns {string} 相关的 HTML 片段
     */
    extractRelevantHtmlSections(html, maxLength) {
        const sections = [];
        let currentLength = 0;

        // 优先提取表单相关的部分
        const formMatches = html.match(/<form[\s\S]*?<\/form>/gi) || [];
        for (const formHtml of formMatches) {
            if (currentLength + formHtml.length <= maxLength) {
                sections.push(formHtml);
                currentLength += formHtml.length;
            }
        }

        // 如果还有空间，提取包含输入字段的部分
        if (currentLength < maxLength * 0.8) {
            const inputMatches = html.match(/<[^>]*(?:input|textarea|select|button)[^>]*>/gi) || [];
            for (const inputHtml of inputMatches) {
                if (currentLength + inputHtml.length <= maxLength) {
                    // 提取输入字段及其周围的上下文
                    const contextMatch = html.match(new RegExp(`.{0,200}${inputHtml.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}.{0,200}`, 'i'));
                    if (contextMatch && currentLength + contextMatch[0].length <= maxLength) {
                        sections.push(contextMatch[0]);
                        currentLength += contextMatch[0].length;
                    }
                }
            }
        }

        const result = sections.join('\n\n');
        console.log(`📄 提取了 ${sections.length} 个相关 HTML 片段，总长度: ${result.length} 字符`);

        return result;
    }
}

// 导出表单检测器实例
export const formDetector = new FormDetector();
