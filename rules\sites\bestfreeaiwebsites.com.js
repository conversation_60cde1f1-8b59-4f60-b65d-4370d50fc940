// BestFreeAIWebsites.com 网站规则配置
// 网站: https://bestfreeaiwebsites.com/submit-tool/
// 最后更新: 2025-07-24

export const SITE_RULE = {
  domain: 'bestfreeaiwebsites.com',
  siteName: 'Best Free AI Websites',
  priority: 1,
  lastUpdated: '2025-07-24',

  fieldMappings: {
    fullName: {
      selectors: [
        'input[name="your-name"]',
        'input[placeholder="Your Name*"]',
        '.wpcf7-text:first-of-type'
      ],
      method: 'value',
      validation: 'required',
      notes: '提交者姓名'
    },

    contactEmail: {
      selectors: [
        'input[name="your-email"]',
        'input[type="email"]',
        'input[placeholder="Your Email*"]'
      ],
      method: 'value',
      validation: 'required|email',
      notes: '联系邮箱'
    },

    siteName: {
      selectors: [
        'input[name="your-tool-name"]',
        'input[placeholder="Your Tool Name*"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '工具名称'
    },

    siteUrl: {
      selectors: [
        'input[name="your-tool-url"]',
        'input[type="url"]',
        'input[placeholder="Your Tool URL*"]'
      ],
      method: 'value',
      validation: 'required|url',
      notes: '工具URL'
    },

    detailedIntro: {
      selectors: [
        'textarea[name="tool-description"]',
        'textarea[placeholder="Tool Description (optional)"]'
      ],
      method: 'value',
      validation: 'optional',
      notes: '工具详细描述'
    },

    siteDescription: {
      selectors: [
        'textarea[name="tool-short-description"]',
        'textarea[placeholder="Tool Short Description (optional)"]'
      ],
      method: 'value',
      validation: 'optional',
      notes: '工具简短描述'
    }
  },

  submitConfig: {
    submitButton: 'input[type="submit"], .wpcf7-submit',
    submitMethod: 'click',
    successIndicators: ['.wpcf7-mail-sent-ok'],
    errorIndicators: ['.wpcf7-validation-errors']
  },

  specialHandling: {
    requiresLogin: false,
    hasCaptcha: false,
    hasFileUpload: false,
    customScript: 'handleBestFreeAIWebsitesSubmission',
    formValidation: {
      requiredFields: ['fullName', 'contactEmail', 'siteName', 'siteUrl'],
      emailValidation: true,
      urlValidation: true
    },
    notes: [
      '使用Contact Form 7表单',
      '有付费提交选项($150)',
      '有社交媒体推广选项',
      '默认选择付费提交',
      '描述字段为可选',
      '使用Elementor构建页面'
    ]
  }
};

export function handleBestFreeAIWebsitesSubmission(data, rule) {
  const processedData = { ...data };

  if (processedData.siteUrl && !processedData.siteUrl.startsWith('http')) {
    processedData.siteUrl = 'https://' + processedData.siteUrl;
  }

  // 自动选择付费提交选项($150)
  const paidOption = document.querySelector('input[name="tool-submissions"][value*="$150"]');
  if (paidOption) {
    paidOption.checked = true;
  }

  return processedData;
}

export async function customFillElement(element, value, config) {
  if (element.tagName === 'INPUT' || element.tagName === 'TEXTAREA') {
    element.value = value;
    element.dispatchEvent(new Event('input', { bubbles: true }));
    element.dispatchEvent(new Event('change', { bubbles: true }));
    return true;
  }

  return false;
}