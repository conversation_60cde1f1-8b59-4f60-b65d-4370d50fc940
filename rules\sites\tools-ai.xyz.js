// Tools-AI.xyz 网站规则配置
// 网站: https://tools-ai.xyz/submit
// 最后更新: 2025-07-24

export const SITE_RULE = {
  // 基本信息
  domain: 'tools-ai.xyz',
  siteName: 'Tools-AI.xyz',
  priority: 1,
  lastUpdated: '2025-07-24',

  // 字段映射规则
  fieldMappings: {
    // AI应用标题 -> Submit your AI application
    siteName: {
      selectors: [
        'input[name="title"]',
        'input[placeholder="Enter title"]',
        'label:contains("Submit your AI application") + input'
      ],
      method: 'value',
      validation: 'required',
      notes: 'AI应用标题'
    },

    // 网站URL -> Website URL
    siteUrl: {
      selectors: [
        'input[name="url"]',
        'input[placeholder="Enter URL"]',
        'label:contains("Website URL") + input'
      ],
      method: 'value',
      validation: 'required|url',
      notes: '网站URL地址'
    },

    // 图片URL -> Submit.image_url
    logoFile: {
      selectors: [
        'input[name="image_url"]',
        'input[placeholder="Enter image URL"]',
        'input[readonly]'
      ],
      method: 'value',
      validation: 'optional|url',
      notes: '产品图片URL'
    },

    // 缩略图URL -> Submit.thumbnail_url
    screenshotUrl: {
      selectors: [
        'input[name="thumbnail_url"]',
        'input[placeholder="Enter thumbnail URL"]'
      ],
      method: 'value',
      validation: 'optional|url',
      notes: '缩略图URL'
    },

    // 网站数据 -> Submit.website_data
    websiteData: {
      selectors: [
        'input[name="website_data"]',
        'input[placeholder="Enter website data"]'
      ],
      method: 'value',
      validation: 'optional',
      notes: '网站数据信息'
    },

    // 分类名称 -> Submit.category_name
    category: {
      selectors: [
        'input[name="category_name"]',
        'input[placeholder="Enter category name"]'
      ],
      method: 'value',
      validation: 'optional',
      notes: '产品分类名称'
    },

    // 简短描述 -> Submit.content
    siteDescription: {
      selectors: [
        'textarea[name="content"]',
        'textarea[placeholder="Short description"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '产品简短描述'
    },

    // 详细描述 -> Submit.detail
    detailedIntro: {
      selectors: [
        'textarea[name="detail"]',
        'textarea[placeholder="Detail description"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '产品详细描述'
    }
  },
  // 提交流程配置
  submitConfig: {
    submitButton: 'button[type="submit"], .bg-white',
    submitMethod: 'manual', // 手动提交
    waitAfterFill: 0,
    waitAfterSubmit: 0,
    successIndicators: ['.success-message'],
    errorIndicators: ['.error-message']
  },

  // 特殊处理
  specialHandling: {
    requiresLogin: false,
    hasCaptcha: false,
    hasFileUpload: true,
    customScript: 'handleToolsAiSubmission',
    formValidation: {
      requiredFields: ['siteName', 'siteUrl', 'siteDescription', 'detailedIntro'],
      emailValidation: false,
      urlValidation: true
    },
    notes: [
      'Tools-AI.xyz AI工具目录提交平台',
      '深色主题界面',
      '支持图片和缩略图上传',
      '需要在网站首页添加反向链接',
      '反向链接格式：<a href="http://tools-ai.xyz" title="Tools-ai.xyz Tools Directory">AI Tools Directory</a>',
      '表单字段较多，包含详细的产品信息',
      '手动提交模式'
    ]
  }
};

// 自定义处理函数
export function handleToolsAiSubmission(data) {
  console.log('Processing Tools-AI form submission...');

  const processedData = { ...data };

  // 确保URL格式正确
  if (processedData.siteUrl && !processedData.siteUrl.startsWith('http')) {
    processedData.siteUrl = 'https://' + processedData.siteUrl;
  }

  // 处理图片URL
  if (processedData.logoFile && !processedData.logoFile.startsWith('http')) {
    processedData.logoFile = 'https://' + processedData.logoFile;
  }

  // 处理缩略图URL
  if (processedData.screenshotUrl && !processedData.screenshotUrl.startsWith('http')) {
    processedData.screenshotUrl = 'https://' + processedData.screenshotUrl;
  }

  return processedData;
}

// 自定义元素填写函数
export async function customFillElement(element, value, config) {
  console.log(`Tools-AI自定义填写: ${element.tagName}, 方法: ${config.method}`);

  switch (config.method) {
    case 'value':
      // 标准输入框和文本域处理
      element.focus();
      await new Promise(resolve => setTimeout(resolve, 200));

      element.value = value;
      element.dispatchEvent(new Event('input', { bubbles: true }));
      element.dispatchEvent(new Event('change', { bubbles: true }));
      element.dispatchEvent(new Event('blur', { bubbles: true }));

      console.log(`✓ 填写字段: ${element.name || element.placeholder} = "${value}"`);
      return true;

    default:
      element.value = value;
      element.dispatchEvent(new Event('input', { bubbles: true }));
      return true;
  }

  return false;
}