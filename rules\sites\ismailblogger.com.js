// IsmailBlogger.com 网站规则配置
// 网站: https://ismailblogger.com/submit-tools/
// 最后更新: 2025-07-24

export const SITE_RULE = {
  domain: 'ismailblogger.com',
  siteName: 'IsmailBlogger',
  priority: 1,
  lastUpdated: '2025-07-24',

  fieldMappings: {
    siteName: {
      selectors: [
        'textbox[aria-label="Tool Name *"]',
        'input[placeholder*="Tool Name"]',
        'form input:nth-of-type(1)'
      ],
      method: 'value',
      validation: 'required',
      notes: '工具名称'
    },

    siteUrl: {
      selectors: [
        'input[name="names[last_name]"]',
        'input[placeholder="https://copy.ai"]',
        'input[placeholder*="https://"]',
        '#ff_18_names_last_name_'
      ],
      method: 'value',
      validation: 'required|url',
      notes: '工具网站链接'
    },

    tagline: {
      selectors: [
        'textbox[aria-label="Tool\'s short description"]',
        'input[placeholder*="short description"]',
        'form input:nth-of-type(3)'
      ],
      method: 'value',
      validation: 'required',
      notes: '工具简短描述'
    },

    contactEmail: {
      selectors: [
        'textbox[aria-label="Email *"]',
        'input[type="email"]',
        'form input:nth-of-type(4)'
      ],
      method: 'value',
      validation: 'required|email',
      notes: '联系邮箱'
    },

    category: {
      selectors: [
        'combobox[aria-label="Select categories (max 3) *"]',
        'select:nth-of-type(1)',
        'form select:first-of-type'
      ],
      method: 'select',
      validation: 'required',
      defaultValue: 'code assistant',
      notes: '工具分类'
    },

    features: {
      selectors: [
        'combobox[aria-label="Select features"]',
        'select:nth-of-type(2)',
        'form select:nth-of-type(2)'
      ],
      method: 'select',
      validation: 'optional',
      notes: '工具特性'
    },

    pricing: {
      selectors: [
        'combobox[aria-label*="Pricing"]',
        'select:nth-of-type(3)',
        'form select:last-of-type'
      ],
      method: 'select',
      validation: 'required',
      defaultValue: 'Freemium',
      notes: '定价模式'
    },

    priceAmount: {
      selectors: [
        'textbox[aria-label="Starting Price (Optional)"]',
        'input[placeholder*="Starting Price"]',
        'form input:nth-of-type(5)'
      ],
      method: 'value',
      validation: 'optional',
      notes: '起始价格'
    },

    siteDescription: {
      selectors: [
        'textbox[aria-label="Tool Description *"]',
        'textarea[placeholder*="Tool Description"]',
        'form textarea'
      ],
      method: 'value',
      validation: 'required',
      notes: '工具详细描述'
    }
  },

  submitConfig: {
    submitButton: 'button:contains("Submit Form")',
    submitMethod: 'click',
    successIndicators: ['.success-message'],
    errorIndicators: ['.error-message']
  },

  specialHandling: {
    requiresLogin: false,
    hasCaptcha: false,
    hasFileUpload: false,
    customScript: 'handleIsmailBloggerSubmission',
    formValidation: {
      requiredFields: ['siteName', 'siteUrl', 'tagline', 'contactEmail', 'category', 'pricing', 'siteDescription'],
      emailValidation: true,
      urlValidation: true
    },
    notes: [
      '有单选按钮选择是否免费',
      '分类最多选择3个',
      '价格字段可选',
      '包含多个下拉选择'
    ]
  }
};

export function handleIsmailBloggerSubmission(data, rule) {
  const processedData = { ...data };

  if (processedData.siteUrl && !processedData.siteUrl.startsWith('http')) {
    processedData.siteUrl = 'https://' + processedData.siteUrl;
  }

  if (processedData.pricing) {
    const pricingMap = {
      'Free': 'Free',
      'Freemium': 'Freemium',
      'Free Trial': 'Free Trial',
      'Paid': 'Paid',
      'Contact for Pricing': 'Contact for Pricing'
    };
    processedData.pricing = pricingMap[processedData.pricing] || 'Freemium';
  }

  return processedData;
}

export async function customFillElement(element, value, config) {
  if (element.tagName === 'SELECT') {
    const options = element.querySelectorAll('option');
    const option = Array.from(options).find(opt =>
      opt.value === value || opt.textContent.trim().toLowerCase() === value.toLowerCase()
    );
    if (option) {
      element.value = option.value;
      element.dispatchEvent(new Event('change', { bubbles: true }));
      return true;
    }
  }

  if (element.tagName === 'INPUT' || element.tagName === 'TEXTAREA') {
    element.value = value;
    element.dispatchEvent(new Event('input', { bubbles: true }));
    return true;
  }

  return false;
}