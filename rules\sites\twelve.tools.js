// twelve.tools 网站规则配置
// 网站: https://twelve.tools/submit-your-tool
// 表单技术: HTML Form with File Upload
// 最后更新: 2025-07-08

export const SITE_RULE = {
  // 基本信息
  domain: 'twelve.tools',
  siteName: 'Twelve Tools',
  priority: 1,
  lastUpdated: '2025-07-08',
  
  // 字段映射规则
  fieldMappings: {
    // 工具URL -> My tool's URL
    siteUrl: {
      selectors: [
        'input[name="rURL"]',
        'input[placeholder="My tool\'s URL"]',
        'input[maxlength="100"]'
      ],
      method: 'value',
      validation: 'required|url',
      maxLength: 100,
      notes: '工具URL，使用website-info.js中的siteUrl字段，最大100字符'
    },
    
    // 工具名称 -> My tool's name (24 chars max)
    siteName: {
      selectors: [
        'input[name="rName"]',
        'input[placeholder*="My tool\'s name"]',
        'input[maxlength="24"]'
      ],
      method: 'value',
      validation: 'required',
      maxLength: 24,
      notes: '工具名称，使用website-info.js中的siteName字段，最大24字符'
    },
    
    // 工具描述 -> My tool's description (92 chars max)
    siteDescription: {
      selectors: [
        'input[name="rDesc"]',
        'input[placeholder*="My tool\'s description"]',
        'input[maxlength="92"]'
      ],
      method: 'value',
      validation: 'required',
      maxLength: 92,
      notes: '工具描述，使用website-info.js中的siteDescription字段，最大92字符'
    },
    
    // 主分类 -> Select a category
    category1: {
      selectors: [
        'select[name="rCateg1"]'
      ],
      method: 'select',
      validation: 'required',
      defaultValue: '75', // Productivity
      notes: '主分类，默认选择Productivity (值为75)'
    },

    // 副分类 -> Select another category
    category2: {
      selectors: [
        'select[name="rCateg2"]'
      ],
      method: 'select',
      validation: 'optional',
      defaultValue: '115', // Web3
      notes: '副分类，默认选择Web3 (值为115)'
    },
    
    // 邮箱 -> My e-mail
    contactEmail: {
      selectors: [
        'input[name="email"]',
        'input[placeholder="My e-mail"]',
        'input[type="email"]'
      ],
      method: 'value',
      validation: 'required|email',
      notes: '邮箱地址，使用website-info.js中的contactEmail字段'
    },
    
    // 同意条款 -> Backlink Agreement
    agreeBacklink: {
      selectors: [
        'input[name="agreeBL"]',
        'input[id="agreeBL"]',
        'input[type="checkbox"]'
      ],
      method: 'checkbox',
      validation: 'required',
      defaultValue: true,
      notes: '同意添加反向链接，必须勾选'
    },
    
    // 文件上传 -> Logo Upload
    logoUpload: {
      selectors: [
        'input[name="rFileUpload"]',
        'input[id="imageUpload"]',
        'input[type="file"]'
      ],
      method: 'file-upload',
      validation: 'required',
      acceptedFormats: '.png, .jpg, .jpeg, .webp, .jfif',
      minSize: '128x128',
      notes: '工具Logo上传，最小128x128像素，支持png/jpg/jpeg/webp/jfif格式'
    }
  },
  
  // 自定义填写函数
  customFillElement: async function(element, value, config) {
    console.log(`Twelve Tools自定义填写: ${element.name || element.placeholder}, 方法: ${config.method}`);
    
    switch (config.method) {
      case 'value':
        // 标准输入框处理
        element.focus();
        await new Promise(resolve => setTimeout(resolve, 100));
        
        // 清空现有内容
        element.value = '';
        await new Promise(resolve => setTimeout(resolve, 50));
        
        // 处理字符限制
        let finalValue = value;
        if (config.maxLength && finalValue.length > config.maxLength) {
          finalValue = finalValue.substring(0, config.maxLength);
          console.log(`⚠️ 内容被截断到${config.maxLength}字符: ${finalValue}`);
        }
        
        // 设置新值
        element.value = finalValue;
        
        // 触发事件
        element.dispatchEvent(new Event('input', { bubbles: true }));
        element.dispatchEvent(new Event('change', { bubbles: true }));
        element.dispatchEvent(new Event('blur', { bubbles: true }));
        
        console.log(`✓ 填写字段: ${element.name} = "${finalValue}"`);
        break;
        
      case 'select':
        // 下拉选择处理
        element.focus();
        await new Promise(resolve => setTimeout(resolve, 100));
        
        const targetValue = config.defaultValue;
        
        // 查找匹配的选项
        const option = Array.from(element.options).find(opt => 
          opt.value === targetValue
        );
        
        if (option) {
          element.value = option.value;
          element.dispatchEvent(new Event('change', { bubbles: true }));
          console.log(`✓ 选择分类: ${option.text} (值: ${option.value})`);
        } else {
          console.log(`⚠️ 未找到分类选项值: ${targetValue}`);
        }
        break;
        
      case 'checkbox':
        // 复选框处理
        if (config.defaultValue) {
          element.checked = true;
          element.dispatchEvent(new Event('change', { bubbles: true }));
          console.log(`✓ 勾选复选框: ${element.name}`);
        }
        break;
        
      case 'file-upload':
        // 文件上传处理
        console.log('⚠️ 文件上传字段需要手动处理');
        console.log(`请手动上传工具Logo文件 (${config.acceptedFormats})`);
        console.log(`最小尺寸要求: ${config.minSize}像素`);
        break;
        
      default:
        element.value = value;
        element.dispatchEvent(new Event('input', { bubbles: true }));
        element.dispatchEvent(new Event('change', { bubbles: true }));
    }
  },
  
  // 提交流程配置
  submitConfig: {
    submitButton: [
      'input[name="btSubmit"]',
      'input[value="Submit Tool"]',
      'input[type="submit"]',
      '.submit'
    ],
    submitMethod: 'click',
    waitAfterFill: 3000,
    waitAfterSubmit: 5000,
    successIndicators: [
      'text:contains("submitted")',
      'text:contains("queue")',
      'text:contains("review")'
    ],
    errorIndicators: [
      'text:contains("error")',
      'text:contains("required")',
      'text:contains("invalid")'
    ]
  },
  
  // 特殊处理
  specialHandling: {
    requiresLogin: false,
    hasCaptcha: false,
    hasFileUpload: true, // 有文件上传
    requiresBacklink: true, // 需要反向链接
    hasCharacterLimits: true, // 有字符限制
    isToolDirectory: true, // 工具目录
    
    // 表单验证规则
    formValidation: {
      requiredFields: ['siteUrl', 'siteName', 'siteDescription', 'category1', 'contactEmail', 'agreeBacklink', 'logoUpload'],
      optionalFields: ['category2'],
      characterLimits: {
        siteUrl: 100,
        siteName: 24,
        siteDescription: 92
      },
      fileUploadFields: ['logoUpload'],
      backlinkRequired: true
    },
    
    // 特殊注意事项
    notes: [
      '这是Twelve Tools工具目录的提交表单',
      '表单包含8个字段：7个必填，1个可选',
      '有严格的字符限制：URL 100字符，名称24字符，描述92字符',
      '需要上传工具Logo (最小128x128像素)',
      '必须同意在网站添加反向链接',
      '每天最多发布12个新工具 (每2小时1个)',
      '会自动获取网站检查反向链接',
      '支持多种图片格式：png, jpg, jpeg, webp, jfif',
      '主分类默认选择Productivity，副分类默认选择Web3',
      '由Sam @ Ramen.Tools制作'
    ]
  }
};

// 自定义处理函数
export function handleTwelveToolsSubmission(data, _rule) {
  console.log('Processing Twelve Tools form submission...');

  const processedData = { ...data };

  // 确保URL格式正确
  if (processedData.siteUrl && !processedData.siteUrl.startsWith('http')) {
    processedData.siteUrl = 'https://' + processedData.siteUrl;
  }

  // 处理字符限制
  if (processedData.siteUrl && processedData.siteUrl.length > 100) {
    processedData.siteUrl = processedData.siteUrl.substring(0, 100);
  }

  if (processedData.siteName && processedData.siteName.length > 24) {
    processedData.siteName = processedData.siteName.substring(0, 24);
  }

  if (processedData.siteDescription && processedData.siteDescription.length > 92) {
    processedData.siteDescription = processedData.siteDescription.substring(0, 92);
  }

  // 设置默认值
  processedData.category1 = '75'; // Productivity
  processedData.category2 = '115'; // Web3
  processedData.agreeBacklink = true;

  return processedData;
}

// 字符限制检查
export function checkCharacterLimits() {
  console.log('检查字符限制...');

  const fields = [
    { name: 'rURL', limit: 100, label: 'URL' },
    { name: 'rName', limit: 24, label: '工具名称' },
    { name: 'rDesc', limit: 92, label: '工具描述' }
  ];

  fields.forEach(field => {
    const element = document.querySelector(`input[name="${field.name}"]`);
    if (element && element.value) {
      const length = element.value.length;
      console.log(`${field.label}: ${length}/${field.limit} 字符`);
      if (length > field.limit) {
        console.log(`⚠️ ${field.label}超过${field.limit}字符限制`);
      }
    }
  });
}

// 文件上传提醒
export function showFileUploadReminder() {
  console.log('📁 文件上传提醒:');
  console.log('');
  console.log('Logo要求:');
  console.log('- 最小尺寸: 128px x 128px');
  console.log('- 推荐尺寸: 正方形Logo');
  console.log('- 支持格式: PNG, JPG, JPEG, WebP, JFIF');
  console.log('- 文件大小: 建议小于2MB');
  console.log('');
  console.log('请手动点击上传区域选择Logo文件！');
}

// 反向链接提醒
export function showBacklinkReminder() {
  console.log('🔗 反向链接要求:');
  console.log('');
  console.log('必须在您的网站添加以下链接之一:');
  console.log('1. 简单链接: <a href="https://twelve.tools">Featured on Twelve Tools</a>');
  console.log('2. 徽章链接: 选择提供的12种徽章之一');
  console.log('3. 位置要求: 首页或页脚');
  console.log('4. 检查机制: 系统会自动检查反向链接');
  console.log('');
  console.log('⚠️ 没有反向链接的工具不会被发布！');
}

// 分类选择帮助
export function showCategoryHelp() {
  console.log('📂 分类选择帮助:');
  console.log('');
  console.log('热门分类:');
  console.log('- Productivity (75) - 推荐主分类');
  console.log('- Web3 (115) - 推荐副分类');
  console.log('- Artificial Intelligence (2)');
  console.log('- Design (30)');
  console.log('- Developer Tools (32)');
  console.log('- Marketing (56)');
  console.log('- No Code (64)');
  console.log('- SaaS Boilerplates (84)');
  console.log('');
  console.log('可以选择1-2个最相关的分类！');
}

// 提交队列信息
export function showSubmissionQueue() {
  console.log('⏰ 提交队列信息:');
  console.log('');
  console.log('发布规则:');
  console.log('- 每天最多发布12个新工具');
  console.log('- 每2小时发布1个工具');
  console.log('- 当前队列: 0个工具等待中');
  console.log('- 审核时间: 通常很快');
  console.log('');
  console.log('提交后会进入发布队列等待审核！');
}

// 表单验证
export function validateTwelveToolsForm() {
  console.log('验证Twelve Tools表单...');

  const requiredFields = [
    { name: 'rURL', label: '工具URL' },
    { name: 'rName', label: '工具名称' },
    { name: 'rDesc', label: '工具描述' },
    { name: 'rCateg1', label: '主分类' },
    { name: 'email', label: '邮箱' },
    { name: 'agreeBL', label: '反向链接协议' }
  ];

  let isValid = true;

  requiredFields.forEach(field => {
    const element = document.querySelector(`input[name="${field.name}"], select[name="${field.name}"]`);
    if (!element || (element.type === 'checkbox' ? !element.checked : !element.value.trim())) {
      console.log(`⚠️ 必填字段为空: ${field.label}`);
      isValid = false;
    }
  });

  // 检查文件上传
  const fileInput = document.querySelector('input[type="file"]');
  if (!fileInput || !fileInput.files.length) {
    console.log('⚠️ 需要上传Logo文件');
    isValid = false;
  }

  if (isValid) {
    console.log('✓ 表单验证通过');
  }

  return isValid;
}
