// the-ai-mentor.com 网站规则配置
// 网站: https://the-ai-mentor.com/submit-ai-tools/
// 表单技术: 简单表单
// 最后更新: 2025-07-07

export const SITE_RULE = {
  // 基本信息
  domain: 'the-ai-mentor.com',
  siteName: 'The AI Mentor',
  priority: 1,
  lastUpdated: '2025-07-07',
  
  // 字段映射规则
  fieldMappings: {
    // 提交者姓名 -> Name
    fullName: {
      selectors: [
        'input[name="name-1"]',
        '#forminator-field-name-1_686c6b0b2b711',
        'input[id*="forminator-field-name-1"]',
        'input[placeholder*="<PERSON>"]',
        'input[aria-required="true"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '提交者姓名，使用website-info.js中的fullName字段'
    },
    
    // 邮箱地址 -> Email Address
    contactEmail: {
      selectors: [
        'input[name="email-1"]',
        'input[type="email"]',
        '#forminator-field-email-1_686c6ab09a6db',
        'input[id*="forminator-field-email-1"]',
        '.forminator-email--field'
      ],
      method: 'value',
      validation: 'required|email',
      notes: '联系邮箱，使用website-info.js中的contactEmail字段'
    },
    
    // AI工具名称 -> Name from the AI tool
    siteName: {
      selectors: [
        'input[name="text-1"]',
        '#forminator-field-text-1_686c6b0b2b711',
        'input[id*="forminator-field-text-1"]',
        'input[placeholder*="text placeholder"]',
        'input[data-required="1"]'
      ],
      method: 'value',
      validation: 'required',
      notes: 'AI工具名称，使用website-info.js中的siteName字段'
    },
    
    // AI工具链接 -> Link from AI tool
    siteUrl: {
      selectors: [
        'input[name*="link"]',
        'input[name*="url"]',
        'input[placeholder*="Link from AI tool"]',
        'input[type="url"]',
        '#tool-link',
        '.tool-link-field'
      ],
      method: 'value',
      validation: 'required|url',
      notes: 'AI工具链接，使用website-info.js中的siteUrl字段'
    }
  },
  
  // 自定义填写函数
  customFillElement: async function(element, value, config) {
    console.log(`The AI Mentor自定义填写: ${element.name || element.placeholder}, 方法: ${config.method}`);
    
    switch (config.method) {
      case 'value':
        // 标准输入框处理
        element.focus();
        await new Promise(resolve => setTimeout(resolve, 100));
        
        // 清空现有内容
        element.value = '';
        await new Promise(resolve => setTimeout(resolve, 50));
        
        // 设置新值
        element.value = value;
        
        // 触发事件
        element.dispatchEvent(new Event('input', { bubbles: true }));
        element.dispatchEvent(new Event('change', { bubbles: true }));
        element.dispatchEvent(new Event('blur', { bubbles: true }));
        
        console.log(`✓ 填写字段: ${element.placeholder || element.name} = "${value}"`);
        break;
        
      default:
        element.value = value;
        element.dispatchEvent(new Event('input', { bubbles: true }));
        element.dispatchEvent(new Event('change', { bubbles: true }));
    }
  },
  
  // 提交流程配置
  submitConfig: {
    submitButton: [
      'button[type="submit"]',
      'input[type="submit"]',
      'button:contains("Send Message")',
      '.submit-button',
      '.send-button'
    ],
    submitMethod: 'click',
    waitAfterFill: 1000,
    waitAfterSubmit: 3000,
    successIndicators: [
      '.success-message',
      '.thank-you',
      '.confirmation',
      '[class*="success"]',
      'text:contains("sent")',
      'text:contains("submitted")'
    ],
    errorIndicators: [
      '.error-message',
      '.validation-error',
      '[class*="error"]'
    ]
  },
  
  // 特殊处理
  specialHandling: {
    requiresLogin: false,
    hasCaptcha: false,
    hasFileUpload: false,
    isSimpleForm: true, // 简单的4字段表单
    
    // 表单验证规则
    formValidation: {
      requiredFields: ['fullName', 'contactEmail', 'siteName', 'siteUrl'],
      emailValidation: true,
      urlValidation: true
    },
    
    // 特殊注意事项
    notes: [
      '这是WordPress网站，使用Forminator插件',
      '表单有4个字段，所有字段都是必填的',
      '提交者姓名使用name-1字段',
      'AI工具名称使用text-1字段',
      '字段ID格式：forminator-field-{type}-{number}_686c6b0b2b711',
      '没有分类选择或其他复杂字段',
      '没有文件上传功能',
      '表单设计简洁，填写快速'
    ]
  }
};

// 自定义处理函数
export function handleAIMentorSubmission(data, _rule) {
  console.log('Processing The AI Mentor form submission...');

  const processedData = { ...data };

  // 确保URL格式正确
  if (processedData.siteUrl && !processedData.siteUrl.startsWith('http')) {
    processedData.siteUrl = 'https://' + processedData.siteUrl;
  }

  return processedData;
}
