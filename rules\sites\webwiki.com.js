// webwiki.com 网站规则配置
// 网站: https://www.webwiki.com/info/add-website.html
// 表单技术: HTML Form with Multiple Sections
// 最后更新: 2025-07-08

export const SITE_RULE = {
  // 基本信息
  domain: 'webwiki.com',
  siteName: 'Webwiki',
  priority: 1,
  lastUpdated: '2025-07-08',
  
  // 字段映射规则
  fieldMappings: {
    // 网站数据部分 - Website data
    
    // URL/域名 -> URL / domain
    siteUrl: {
      selectors: [
        'input[name="website_url"]',
        'input[placeholder*="http://www.example.com"]',
        'input.form-control[size="60"]',
        'input[onchange*="check_url"]'
      ],
      method: 'value',
      validation: 'required|url',
      notes: '网站URL，使用website-info.js中的siteUrl字段'
    },
    
    // 分类 -> Category
    category: {
      selectors: [
        'select[name="category"]',
        'select[name="cat"]',
        'select:has(option:contains("Computer and Technology"))'
      ],
      method: 'select',
      validation: 'required',
      defaultValue: 'Computer and Technology',
      notes: '分类，默认选择Computer and Technology'
    },
    
    // 标题 -> Title
    siteName: {
      selectors: [
        'input[name="title"]',
        'input[name="name"]',
        'input[placeholder*="title"]',
        'input[type="text"]:nth-of-type(2)'
      ],
      method: 'value',
      validation: 'optional',
      notes: '网站标题，使用website-info.js中的siteName字段'
    },
    
    // 描述 -> Description
    siteDescription: {
      selectors: [
        'textarea[name="description"]',
        'textarea[name="desc"]',
        'textarea[placeholder*="description"]',
        'textarea:first-of-type'
      ],
      method: 'value',
      validation: 'optional',
      notes: '网站描述，使用website-info.js中的siteDescription字段'
    },
    
    // 联系信息部分 - Contact Information
    
    // 邮箱 -> E-mail
    contactEmail: {
      selectors: [
        'input[name="email"]',
        'input[type="email"]',
        'input[placeholder*="email"]',
        'input[name="contact_email"]'
      ],
      method: 'value',
      validation: 'required|email',
      notes: '联系邮箱，使用website-info.js中的contactEmail字段'
    },
    
    // 网站所有者数据部分 - Website owner data
    
    // 运营者 -> Operator
    operator: {
      selectors: [
        'input[name="name"]',
        'input.form-control[size="40"]',
        'input[type="text"][name="name"]'
      ],
      method: 'value',
      validation: 'optional',
      defaultValue: 'Marcus Thompson',
      notes: '运营者，使用独特的美国真实姓名'
    },
    
    // 地址 -> Address
    address: {
      selectors: [
        'input[name="adresse"]',
        'input.form-control[size="40"]',
        'input[type="text"][name="adresse"]'
      ],
      method: 'value',
      validation: 'optional',
      defaultValue: '2847 Maple Avenue',
      notes: '地址，使用美国真实地址'
    },
    
    // 邮编 -> ZIP
    zipCode: {
      selectors: [
        'input[name="plz"]',
        'input.form-control[size="10"]',
        'input[type="text"][name="plz"]'
      ],
      method: 'value',
      validation: 'optional',
      defaultValue: '94102',
      notes: '邮编，使用美国真实邮编'
    },

    // 城市 -> City
    city: {
      selectors: [
        'input[name="ort"]',
        'input.form-control[size="24"]',
        'input[type="text"][name="ort"]'
      ],
      method: 'value',
      validation: 'optional',
      defaultValue: 'San Francisco',
      notes: '城市，使用美国真实城市'
    },
    
    // 国家 -> Country
    country: {
      selectors: [
        'select[name="country"]',
        'select:has(option:contains("United States"))',
        'select[name="country_id"]'
      ],
      method: 'select',
      validation: 'optional',
      defaultValue: 'United States',
      notes: '国家，默认选择United States'
    },
    
    // 电话 -> Phone
    phone: {
      selectors: [
        'input[name="telefon"]',
        'input.form-control[size="30"]',
        'input[type="text"][name="telefon"]'
      ],
      method: 'value',
      validation: 'optional',
      notes: '电话，使用website-info.js中的phone字段'
    },
    
    // 传真 -> Fax
    fax: {
      selectors: [
        'input[name="fax"]',
        'input[placeholder*="fax"]',
        'input[name="fax_number"]'
      ],
      method: 'value',
      validation: 'optional',
      defaultValue: '******-892-7365',
      notes: '传真，使用真实复杂的美国传真号码'
    },
    
    // 网站 -> Website (if different from above)
    alternativeWebsite: {
      selectors: [
        'input[name="website"]',
        'input[name="alt_website"]',
        'input[placeholder*="different from above"]',
        'input[type="url"]:last-of-type'
      ],
      method: 'value',
      validation: 'optional',
      notes: '备用网站，如果与上面不同，可留空'
    },
    
    // 确认复选框 -> Certification checkbox
    certification: {
      selectors: [
        'input[type="checkbox"][name*="certify"]',
        'input[type="checkbox"][name*="confirm"]',
        'input[type="checkbox"]:last-of-type',
        'input[type="checkbox"][required]'
      ],
      method: 'checkbox',
      validation: 'required',
      defaultValue: true,
      notes: '确认数据准确性的复选框'
    }
  },
  
  // 自定义填写函数
  customFillElement: async function(element, value, config) {
    console.log(`Webwiki自定义填写: ${element.name || element.type}, 方法: ${config.method}`);
    
    switch (config.method) {
      case 'value':
        // 标准输入框和文本域处理
        element.focus();
        await new Promise(resolve => setTimeout(resolve, 100));
        
        // 清空现有内容
        element.value = '';
        await new Promise(resolve => setTimeout(resolve, 50));
        
        // 设置新值
        element.value = value;
        
        // 触发事件
        element.dispatchEvent(new Event('input', { bubbles: true }));
        element.dispatchEvent(new Event('change', { bubbles: true }));
        element.dispatchEvent(new Event('blur', { bubbles: true }));
        
        console.log(`✓ 填写字段: ${element.name} = "${value.substring(0, 50)}..."`);
        break;
        
      case 'select':
        // 下拉选择框处理
        console.log(`处理下拉选择，字段: ${element.name}, 目标值: ${config.defaultValue}`);
        
        // 尝试按值选择
        if (element.querySelector(`option[value="${config.defaultValue}"]`)) {
          element.value = config.defaultValue;
        } else {
          // 尝试按文本内容选择
          const options = Array.from(element.options);
          const targetOption = options.find(option => 
            option.textContent.includes(config.defaultValue)
          );
          if (targetOption) {
            element.value = targetOption.value;
          }
        }
        
        // 触发change事件
        element.dispatchEvent(new Event('change', { bubbles: true }));
        
        console.log(`✓ 选择${element.name}: ${config.defaultValue}`);
        break;
        
      case 'checkbox':
        // 复选框处理
        element.checked = config.defaultValue;
        element.dispatchEvent(new Event('change', { bubbles: true }));
        console.log(`✓ 勾选确认复选框`);
        break;
        
      default:
        element.value = value;
        element.dispatchEvent(new Event('input', { bubbles: true }));
        element.dispatchEvent(new Event('change', { bubbles: true }));
    }
  },
  
  // 提交流程配置
  submitConfig: {
    submitButton: [
      'button[type="submit"]',
      'input[type="submit"]',
      'button:contains("Add your website")',
      'input[value*="Add"]'
    ],
    submitMethod: 'click',
    waitAfterFill: 3000,
    waitAfterSubmit: 5000,
    successIndicators: [
      'text:contains("submitted")',
      'text:contains("added")',
      'text:contains("success")',
      'text:contains("thank you")'
    ],
    errorIndicators: [
      'text:contains("error")',
      'text:contains("required")',
      'text:contains("invalid")',
      'text:contains("failed")'
    ]
  },
  
  // 特殊处理
  specialHandling: {
    requiresLogin: false,
    hasCaptcha: false, // 无验证码
    hasFileUpload: false,
    isMultiSectionForm: true, // 多部分表单
    isWebDirectory: true, // 网站目录
    hasOptionalFields: true, // 有可选字段
    hasCountrySelection: true, // 有国家选择
    hasContactInfo: true, // 有联系信息
    hasOwnerData: true, // 有所有者数据
    requiresCertification: true, // 需要确认
    
    // 表单验证规则
    formValidation: {
      requiredFields: ['siteUrl', 'category', 'contactEmail', 'certification'],
      optionalFields: ['siteName', 'siteDescription', 'operator', 'address', 'zipCity', 'country', 'phone', 'fax', 'alternativeWebsite'],
      emailValidation: true,
      urlValidation: true,
      checkboxValidation: true,
      selectFields: ['category', 'country']
    },
    
    // 特殊注意事项
    notes: [
      '这是Webwiki的网站添加表单',
      '表单包含3个部分：网站数据、联系信息、所有者数据',
      '搜索引擎和网站评论平台',
      '无验证码保护，提交便捷',
      '分类默认选择Computer and Technology',
      '国家默认选择United States',
      '包含大量可选字段',
      '需要确认数据准确性',
      '支持多国语言版本',
      '免费添加网站',
      '提供SEO优化建议',
      '包含网站评级功能'
    ]
  }
};

// 自定义处理函数
export function handleWebwikiSubmission(data, _rule) {
  console.log('Processing Webwiki form submission...');

  const processedData = { ...data };

  // 确保URL格式正确
  if (processedData.siteUrl && !processedData.siteUrl.startsWith('http')) {
    processedData.siteUrl = 'https://' + processedData.siteUrl;
  }

  // 设置硬编码值
  processedData.category = 'Computer and Technology';
  processedData.country = 'United States';
  processedData.operator = 'Marcus Thompson';
  processedData.address = '2847 Maple Avenue';
  processedData.zipCode = '94102';
  processedData.city = 'San Francisco';
  processedData.fax = '******-892-7365';
  processedData.certification = true;

  return processedData;
}

// Webwiki信息提醒
export function showWebwikiInfo() {
  console.log('🔍 Webwiki 信息:');
  console.log('');
  console.log('平台特色:');
  console.log('- 搜索引擎和网站评论平台');
  console.log('- 免费添加网站');
  console.log('- 支持多国语言版本');
  console.log('- 提供网站评级功能');
  console.log('- SEO优化建议');
  console.log('');
  console.log('表单结构:');
  console.log('1. 网站数据 (Website data)');
  console.log('   - URL/域名 (必填)');
  console.log('   - 分类 (必填)');
  console.log('   - 标题 (可选)');
  console.log('   - 描述 (可选)');
  console.log('');
  console.log('2. 联系信息 (Contact Information)');
  console.log('   - 邮箱 (必填，不公开)');
  console.log('');
  console.log('3. 网站所有者数据 (Website owner data)');
  console.log('   - 运营者 (可选，公开)');
  console.log('   - 地址 (可选，公开)');
  console.log('   - 邮编/城市 (可选，公开)');
  console.log('   - 国家 (可选，公开)');
  console.log('   - 电话 (可选，公开)');
  console.log('   - 传真 (可选，公开)');
  console.log('   - 备用网站 (可选，公开)');
  console.log('');
  console.log('默认设置:');
  console.log('- 分类: Computer and Technology ✅');
  console.log('- 国家: United States ✅');
  console.log('- 运营者: AI Tools Company ✅');
  console.log('- 地址: 123 Tech Street ✅');
  console.log('- 城市: 10001 New York ✅');
  console.log('');
  console.log('Webwiki - 国际化的网站搜索和评论平台！');
}

// 多部分表单说明
export function showMultiSectionFormStructure() {
  console.log('📋 多部分表单结构说明:');
  console.log('');
  console.log('表单分为3个主要部分:');
  console.log('');
  console.log('🌐 第一部分：网站数据');
  console.log('- 核心网站信息');
  console.log('- URL和分类必填');
  console.log('- 标题和描述可选');
  console.log('- 如果不填写，使用网站meta数据');
  console.log('');
  console.log('📧 第二部分：联系信息');
  console.log('- 仅包含邮箱地址');
  console.log('- 必填但不公开');
  console.log('- 用于平台通知');
  console.log('');
  console.log('👤 第三部分：网站所有者数据');
  console.log('- 完全可选');
  console.log('- 所有信息都会公开');
  console.log('- 作为网站联系方式展示');
  console.log('- 包含完整的商业信息');
  console.log('');
  console.log('隐私说明:');
  console.log('- 邮箱地址不会公开');
  console.log('- 所有者数据会作为联系方式公开');
  console.log('- 可以选择性填写公开信息');
}

// 分类选择说明
export function showCategoryOptions() {
  console.log('🗂️ 分类选择说明:');
  console.log('');
  console.log('可选分类包括:');
  console.log('- Art and culture');
  console.log('- Blogs');
  console.log('- Computer and Technology ✅ 默认选择');
  console.log('- Economy and Business');
  console.log('- Education and Career');
  console.log('- Finances');
  console.log('- Fun and Games');
  console.log('- Healthcare');
  console.log('- Local');
  console.log('- News');
  console.log('- Personal websites');
  console.log('- Science and Research');
  console.log('- Shopping');
  console.log('- Social Networks and Internet');
  console.log('- Sports');
  console.log('- Travel and Tourism');
  console.log('');
  console.log('Computer and Technology选择原因:');
  console.log('- 适合AI工具网站');
  console.log('- 涵盖技术类网站');
  console.log('- 目标用户群体明确');
  console.log('- 搜索量较高');
}

// 国际化特点
export function showInternationalizationFeatures() {
  console.log('🌍 国际化特点:');
  console.log('');
  console.log('支持的国家/地区版本:');
  console.log('- 🇺🇸 United States (webwiki.com)');
  console.log('- 🇬🇧 United Kingdom (webwiki.co.uk)');
  console.log('- 🇩🇪 Germany (webwiki.de)');
  console.log('- 🇪🇸 Spain (webwiki.es)');
  console.log('- 🇫🇷 France (webwiki.fr)');
  console.log('- 🇵🇹 Portugal (webwiki.pt)');
  console.log('- 🇮🇹 Italy (webwiki.it)');
  console.log('- 🇳🇱 Netherlands (webwiki.nl)');
  console.log('- 🇨🇭 Switzerland (webwiki.ch)');
  console.log('- 🇦🇹 Austria (webwiki.at)');
  console.log('');
  console.log('国际化优势:');
  console.log('- 多语言支持');
  console.log('- 本地化搜索结果');
  console.log('- 地区性SEO优化');
  console.log('- 文化适应性');
  console.log('');
  console.log('选择建议:');
  console.log('- 根据目标市场选择版本');
  console.log('- 美国版本覆盖面最广');
  console.log('- 欧洲版本适合欧洲市场');
  console.log('- 可以多版本同时提交');
}

// 硬编码字段说明
export function showHardcodedFieldsExplanation() {
  console.log('🔧 硬编码字段说明:');
  console.log('');
  console.log('由于website-info.js中没有的字段，使用硬编码:');
  console.log('');
  console.log('📍 地址信息:');
  console.log('- 运营者: "AI Tools Company"');
  console.log('- 地址: "123 Tech Street"');
  console.log('- 邮编/城市: "10001 New York"');
  console.log('- 国家: "United States"');
  console.log('');
  console.log('📞 联系方式:');
  console.log('- 电话: "+1-555-123-4567"');
  console.log('- 传真: "+1-555-123-4568"');
  console.log('');
  console.log('✅ 确认选项:');
  console.log('- 数据准确性确认: true');
  console.log('');
  console.log('硬编码原因:');
  console.log('- 这些字段在website-info.js中不存在');
  console.log('- 使用通用的美国商业信息');
  console.log('- 符合平台要求的格式');
  console.log('- 可以根据需要修改');
}

// 表单验证
export function validateWebwikiForm() {
  console.log('验证Webwiki表单...');

  const requiredFields = [
    { selector: 'input[name="url"]', label: '网站URL' },
    { selector: 'select[name="category"]', label: '分类' },
    { selector: 'input[name="email"]', label: '联系邮箱' },
    { selector: 'input[type="checkbox"][required]', label: '确认复选框' }
  ];

  let isValid = true;

  requiredFields.forEach(field => {
    const element = document.querySelector(field.selector);
    if (!element) {
      console.log(`⚠️ 未找到字段: ${field.label}`);
      isValid = false;
    } else if (field.selector.includes('checkbox')) {
      if (!element.checked) {
        console.log(`⚠️ 必须勾选: ${field.label}`);
        isValid = false;
      }
    } else if (!element.value || !element.value.trim()) {
      console.log(`⚠️ 必填字段为空: ${field.label}`);
      isValid = false;
    }
  });

  // 检查URL格式
  const urlField = document.querySelector('input[name="url"]');
  if (urlField && urlField.value && !urlField.value.match(/^https?:\/\//)) {
    console.log('⚠️ URL格式可能不正确，建议包含http://或https://');
  }

  // 检查邮箱格式
  const emailField = document.querySelector('input[name="email"]');
  if (emailField && emailField.value && !emailField.value.includes('@')) {
    console.log('⚠️ 邮箱格式可能不正确');
  }

  if (isValid) {
    console.log('✓ 表单验证通过');
  }

  return isValid;
}

// SEO和营销价值
export function showSEOMarketingValue() {
  console.log('📈 SEO和营销价值:');
  console.log('');
  console.log('Webwiki提供的价值:');
  console.log('');
  console.log('🔍 SEO优化:');
  console.log('- 增加搜索引擎收录');
  console.log('- 提高Google、Yahoo、Bing排名');
  console.log('- 获得高质量外链');
  console.log('- 提升域名权重');
  console.log('');
  console.log('👥 流量增长:');
  console.log('- 增加网站访问者');
  console.log('- 提高品牌曝光度');
  console.log('- 获得目标用户');
  console.log('- 扩大市场覆盖');
  console.log('');
  console.log('⭐ 信誉建设:');
  console.log('- 用户评级和评论');
  console.log('- 建立客户信任');
  console.log('- 展示网站评分');
  console.log('- 获得社会证明');
  console.log('');
  console.log('🎯 推荐营销:');
  console.log('- 主动推荐营销');
  console.log('- Webwiki按钮和小工具');
  console.log('- 展示平均评分');
  console.log('- 增强客户信心');
}
