// AI Site Submitter - SubmissionWebDirectory 规则配置
// 自动生成于: 2025/7/15 18:39:55
// 域名: www.submissionwebdirectory.com

export const SITE_RULE = {
  "domain": "www.submissionwebdirectory.com",
  "siteName": "SubmissionWebDirectory",
  "lastUpdated": "2025-07-15T10:39:55.837Z",
  "fieldMappings": {
    "siteTitle": {
      "selectors": [
        "[name='TITLE']",
        "input[name='TITLE']"
      ],
      "type": "input",
      "fillMethod": "value",
      "required": false,
      "validation": "required"
    },
    "siteUrl": {
      "selectors": [
        "[name='URL']",
        "input[name='URL']"
      ],
      "type": "input",
      "fillMethod": "value",
      "required": false,
      "validation": "required"
    },
    "siteDescription": {
      "selectors": [
        "[name='DESCRIPTION']",
        "textarea[name='DESCRIPTION']"
      ],
      "type": "input",
      "fillMethod": "value",
      "required": false,
      "validation": "required"
    },
    "keywords": {
      "selectors": [
        "[name='META_KEYWORDS']",
        "input[name='META_KEYWORDS']"
      ],
      "type": "input",
      "fillMethod": "value",
      "required": false,
      "validation": "optional"
    },
    "detailedIntro": {
      "selectors": [
        "[name='META_DESCRIPTION']",
        "textarea[name='META_DESCRIPTION']"
      ],
      "type": "input",
      "fillMethod": "value",
      "required": false,
      "validation": "optional"
    },
    "fullName": {
      "selectors": [
        "[name='OWNER_NAME']",
        "input[name='OWNER_NAME']"
      ],
      "type": "input",
      "fillMethod": "value",
      "required": false,
      "validation": "optional"
    },
    "contactEmail": {
      "selectors": [
        "[name='OWNER_EMAIL']",
        "input[name='OWNER_EMAIL']"
      ],
      "type": "input",
      "fillMethod": "value",
      "required": false,
      "validation": "optional"
    },
    "agreeTerms": {
      "selectors": [
        "#AGREERULES",
        "input[name='AGREERULES']"
      ],
      "type": "input",
      "fillMethod": "value",
      "defaultValue": true,
      "required": false,
      "validation": "required"
    }
  },
  "formInfo": {
    "description": "网站目录提交表单，用于向SubmissionWebDirectory提交网站信息",
    "submitSelector": "[name='submit']",
    "totalFields": 12,
    "notes": [
      "表单包含付费选项(LINK_TYPE)，但默认选择Regular links",
      "DESCRIPTION_limit和META_DESCRIPTION_limit为字符计数器，无需填写"
    ]
  },
  "metadata": {
    "generatedBy": "AI",
    "generatedAt": "2025-07-15T10:39:55.837Z",
    "version": "3.0.0",
    "aiModel": "moonshotai/Kimi-K2-Instruct"
  }
};

// 自定义处理函数 (可选)
export function handleWwwSubmissionwebdirectoryComSubmission(data, rule) {
  console.log('Processing SubmissionWebDirectory form submission...');
  
  const processedData = { ...data };
  
  // 在这里添加特殊处理逻辑
  // 例如：URL格式化、字段验证、默认值设置等
  
  return processedData;
}

// 自定义元素填写函数 (可选)
export async function customFillElement(element, value, config) {
  console.log('🔧 SubmissionWebDirectory 自定义填写函数被调用:', element, value);
  
  // 在这里添加特殊的元素填写逻辑
  // 例如：处理特殊的UI组件、异步操作等
  
  return false; // 返回 false 使用默认填写方法
}