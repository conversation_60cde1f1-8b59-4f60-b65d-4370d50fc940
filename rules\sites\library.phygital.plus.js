// library.phygital.plus 网站规则配置
// 网站: https://library.phygital.plus/tool-submission
// 表单技术: Material-UI (MUI) Form
// 最后更新: 2025-07-08

export const SITE_RULE = {
  // 基本信息
  domain: 'library.phygital.plus',
  siteName: 'Phygital Library',
  priority: 1,
  lastUpdated: '2025-07-08',
  
  // 字段映射规则
  fieldMappings: {
    // 工具链接 -> Link to the tool
    siteUrl: {
      selectors: [
        'input[id*="form3-Tryout"]',
        'input[name="b30cbe6d"]',
        'input[placeholder="Link to the tool"]',
        'input[type="text"].MuiInputBase-input'
      ],
      method: 'value',
      validation: 'required|url',
      notes: '工具链接，使用website-info.js中的siteUrl字段'
    },
    
    // 工具描述 -> Describe the tool
    detailedIntro: {
      selectors: [
        'textarea[id*="form3-Description"]',
        'textarea[name="9cf52798"]',
        'textarea[placeholder*="Describe the tool"]',
        'textarea.MuiInputBase-inputMultiline:first-of-type'
      ],
      method: 'value',
      validation: 'required',
      notes: '工具描述，使用website-info.js中的detailedIntro字段'
    },
    
    // 提交者信息 -> Submitted by
    contactEmail: {
      selectors: [
        'textarea[id*="form3-:Submittedby"]',
        'textarea[name="27b91be5"]',
        'textarea[placeholder*="Share your social media handle"]',
        'textarea.MuiInputBase-inputMultiline:nth-of-type(2)'
      ],
      method: 'value',
      validation: 'required',
      defaultValue: '<NAME_EMAIL> for any questions or collaboration opportunities. We are currently building our presence on international social media platforms.',
      notes: '提交者信息，使用website-info.js中的contactEmail字段，英文说明暂未创建海外社交平台'
    }
  },
  
  // 自定义填写函数
  customFillElement: async function(element, value, config) {
    console.log(`Phygital Library自定义填写: ${element.id || element.name}, 方法: ${config.method}`);
    
    switch (config.method) {
      case 'value':
        // Material-UI输入框和文本域处理
        element.focus();
        await new Promise(resolve => setTimeout(resolve, 100));
        
        // 清空现有内容
        element.value = '';
        await new Promise(resolve => setTimeout(resolve, 50));
        
        // 处理特殊字段
        let finalValue = value;
        if (config.defaultValue && (!value || value.trim() === '')) {
          finalValue = config.defaultValue;
        }
        
        // 设置新值
        element.value = finalValue;
        
        // 触发Material-UI事件
        const inputEvent = new Event('input', { bubbles: true });
        const changeEvent = new Event('change', { bubbles: true });
        
        // Material-UI特殊处理
        Object.defineProperty(inputEvent, 'target', {
          writable: false,
          value: element
        });
        Object.defineProperty(changeEvent, 'target', {
          writable: false,
          value: element
        });
        
        element.dispatchEvent(inputEvent);
        element.dispatchEvent(changeEvent);
        element.dispatchEvent(new Event('blur', { bubbles: true }));
        
        // 额外的Material-UI事件
        element.dispatchEvent(new KeyboardEvent('keyup', { bubbles: true }));
        
        console.log(`✓ 填写字段: ${element.placeholder} = "${finalValue.substring(0, 50)}..."`);
        break;
        
      default:
        element.value = value;
        element.dispatchEvent(new Event('input', { bubbles: true }));
        element.dispatchEvent(new Event('change', { bubbles: true }));
    }
  },
  
  // 提交流程配置
  submitConfig: {
    submitButton: [
      'button[type="submit"]',
      'button:contains("Send")',
      'button.MuiButton-contained',
      '.MuiButton-containedPrimary'
    ],
    submitMethod: 'click',
    waitAfterFill: 2000,
    waitAfterSubmit: 5000,
    successIndicators: [
      'text:contains("submitted")',
      'text:contains("success")',
      'text:contains("thank you")',
      'text:contains("received")'
    ],
    errorIndicators: [
      'text:contains("error")',
      'text:contains("required")',
      'text:contains("invalid")'
    ]
  },
  
  // 特殊处理
  specialHandling: {
    requiresLogin: false,
    hasCaptcha: false,
    hasFileUpload: false,
    isMaterialUI: true, // Material-UI表单
    isCommunitySubmission: true, // 社区提交
    hasHiddenFields: true, // 有隐藏字段
    
    // 表单验证规则
    formValidation: {
      requiredFields: ['siteUrl', 'detailedIntro', 'contactEmail'],
      optionalFields: [],
      emailValidation: false,
      urlValidation: true,
      materialUIValidation: true
    },
    
    // 特殊注意事项
    notes: [
      '这是Phygital Library的AI工具提交表单',
      '表单包含3个字段，全部必填',
      '使用Material-UI (MUI)组件库',
      '专注于AI工具和Colab笔记本分享',
      '需要提供工具链接和详细描述',
      '需要提供提交者的社交媒体或联系方式',
      '有隐藏字段"Submitted by the community"',
      '表单有动态高度调整',
      '支持多行文本输入',
      '社区驱动的工具分享平台'
    ]
  }
};

// 自定义处理函数
export function handlePhygitalLibrarySubmission(data, _rule) {
  console.log('Processing Phygital Library form submission...');

  const processedData = { ...data };

  // 确保URL格式正确
  if (processedData.siteUrl && !processedData.siteUrl.startsWith('http')) {
    processedData.siteUrl = 'https://' + processedData.siteUrl;
  }

  // 设置默认联系信息
  if (!processedData.contactEmail) {
    processedData.contactEmail = 'Submitted by Hard Usernames team. Contact <NAME_EMAIL> for any questions or collaboration opportunities. We are currently building our presence on international social media platforms.';
  }

  return processedData;
}

// Material-UI表单检测
export function detectMaterialUIForm() {
  console.log('检测Material-UI表单...');
  
  // 检查MUI组件
  const muiElements = document.querySelectorAll('.MuiFormControl-root, .MuiTextField-root, .MuiButton-root');
  if (muiElements.length > 0) {
    console.log(`✓ 检测到 ${muiElements.length} 个Material-UI组件`);
    return true;
  }
  
  return false;
}

// 表单字段检测
export function detectFormFields() {
  console.log('检测表单字段...');
  
  const fields = [
    { placeholder: 'Link to the tool', type: 'input' },
    { placeholder: 'Describe the tool', type: 'textarea' },
    { placeholder: 'Share your social media handle', type: 'textarea' }
  ];
  
  fields.forEach(field => {
    const element = document.querySelector(`${field.type}[placeholder*="${field.placeholder.split(' ')[0]}"]`);
    if (element) {
      console.log(`✓ 找到字段: ${field.placeholder}`);
    } else {
      console.log(`⚠️ 未找到字段: ${field.placeholder}`);
    }
  });
}

// Phygital Library信息提醒
export function showPhygitalLibraryInfo() {
  console.log('📚 Phygital Library 信息:');
  console.log('');
  console.log('平台特色:');
  console.log('- AI工具和Colab笔记本分享平台');
  console.log('- 社区驱动的工具收录');
  console.log('- 专注于实用的AI工具');
  console.log('- 支持各种AI应用场景');
  console.log('');
  console.log('提交要求:');
  console.log('- 工具链接（必填）');
  console.log('- 详细描述工具功能和用途');
  console.log('- 提交者联系方式或社交媒体');
  console.log('- 所有字段都是必填的');
  console.log('');
  console.log('Phygital Library - 连接物理与数字世界的AI工具库！');
}

// 表单验证
export function validatePhygitalLibraryForm() {
  console.log('验证Phygital Library表单...');
  
  const requiredFields = [
    { selector: 'input[placeholder="Link to the tool"]', label: '工具链接' },
    { selector: 'textarea[placeholder*="Describe the tool"]', label: '工具描述' },
    { selector: 'textarea[placeholder*="Share your social media handle"]', label: '联系邮箱' }
  ];
  
  let isValid = true;
  
  requiredFields.forEach(field => {
    const element = document.querySelector(field.selector);
    if (!element || !element.value.trim()) {
      console.log(`⚠️ 必填字段为空: ${field.label}`);
      isValid = false;
    }
  });
  
  if (isValid) {
    console.log('✓ 表单验证通过');
  }
  
  return isValid;
}

// 联系信息生成器
export function generateContactInfo(socialMedia = {}) {
  console.log('生成联系信息...');
  
  const { twitter, email, linkedin, website } = socialMedia;
  
  let contactInfo = 'Submitted by AI Tools Community';
  
  if (email) contactInfo += ` - Email: ${email}`;
  if (twitter) contactInfo += ` | Twitter: ${twitter}`;
  if (linkedin) contactInfo += ` | LinkedIn: ${linkedin}`;
  if (website) contactInfo += ` | Website: ${website}`;
  
  console.log(`生成的联系信息: ${contactInfo}`);
  return contactInfo;
}
