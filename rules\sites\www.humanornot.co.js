// AI Site Submitter - Human or Not 规则配置
// 自动生成于: 2025/7/15 11:04:55
// 域名: www.humanornot.co

export const SITE_RULE = {
  "domain": "www.humanornot.co",
  "siteName": "Human or Not",
  "lastUpdated": "2025-07-15T03:04:55.490Z",
  "fieldMappings": {
    "contactEmail": {
      "selectors": [
        "#Email-2",
        "input[name='Email-2']",
        "input[type='email']"
      ],
      "type": "input",
      "fillMethod": "value",
      "required": false,
      "validation": "email"
    },
    "siteUrl": {
      "selectors": [
        "#URL-2",
        "input[name='URL-2']",
        "input[placeholder*='humanornot.co']"
      ],
      "type": "input",
      "fillMethod": "value",
      "required": false,
      "validation": "url"
    }
  },
  "formInfo": {
    "description": "AI工具免费提交表单",
    "submitSelector": "#wf-form-Tool-submit input[type='submit']",
    "totalFields": 2,
    "notes": [
      "表单仅包含邮箱和URL两个必填字段",
      "提交后48小时内完成审核",
      "仅接受包含AI功能的工具"
    ]
  },
  "metadata": {
    "generatedBy": "AI",
    "generatedAt": "2025-07-15T03:04:55.490Z",
    "version": "3.0.0",
    "aiModel": "moonshotai/Kimi-K2-Instruct"
  }
};

// 自定义处理函数 (可选)
export function handleWwwHumanornotCoSubmission(data, rule) {
  console.log('Processing Human or Not form submission...');
  
  const processedData = { ...data };
  
  // 在这里添加特殊处理逻辑
  // 例如：URL格式化、字段验证、默认值设置等
  
  return processedData;
}

// 自定义元素填写函数 (可选)
export async function customFillElement(element, value, config) {
  console.log('🔧 Human or Not 自定义填写函数被调用:', element, value);
  
  // 在这里添加特殊的元素填写逻辑
  // 例如：处理特殊的UI组件、异步操作等
  
  return false; // 返回 false 使用默认填写方法
}