// HeyAiWorld.com 网站规则配置
// 网站: https://heyaiworld.com/submit
// 最后更新: 2025-07-09

export const SITE_RULE = {
  // 基本信息
  domain: 'heyaiworld.com',
  siteName: 'HeyAiWorld',
  priority: 1,
  lastUpdated: '2025-07-09',
  
  // 字段映射规则
  fieldMappings: {
    // 工具名称 -> Tool Name
    siteName: {
      selectors: [
        'input[name="name"]',
        '#\\:r0\\:-form-item',
        'input[placeholder*="name of the AI tool"]'
      ],
      method: 'value',
      validation: 'required',
      notes: 'AI工具名称'
    },
    
    // 网站URL -> Website URL
    siteUrl: {
      selectors: [
        'input[name="website"]',
        '#\\:r1\\:-form-item',
        'input[placeholder="https://example.com"]'
      ],
      method: 'value',
      validation: 'required|url',
      notes: '网站URL地址'
    },
    
    // 描述 -> Description
    siteDescription: {
      selectors: [
        'textarea[name="description"]',
        '#\\:r2\\:-form-item',
        'textarea[placeholder*="Describe the AI tool"]'
      ],
      method: 'value',
      validation: 'optional',
      notes: '工具描述（可选）'
    }
  },
  
  // 提交流程配置
  submitConfig: {
    submitButton: 'button[type="submit"], button:contains("Submit Tool")',
    submitMethod: 'click',
    waitAfterFill: 1000, // 填写后等待1秒
    waitAfterSubmit: 3000, // 提交后等待3秒
    successIndicators: [
      '.success-message',
      '.alert-success',
      '.notification-success',
      '[class*="success"]'
    ],
    errorIndicators: [
      '.error-message',
      '.alert-error',
      '.text-destructive',
      '[class*="error"]'
    ]
  },
  
  // 特殊处理
  specialHandling: {
    requiresLogin: false,
    hasCaptcha: false,
    hasFileUpload: false,
    
    // 表单验证规则
    formValidation: {
      requiredFields: ['siteName', 'siteUrl'],
      emailValidation: false,
      urlValidation: true
    },
    
    // 特殊注意事项
    notes: [
      '极简的AI工具提交表单',
      '表单包含3个字段：工具名称、网站URL、描述',
      '必填字段：工具名称、网站URL',
      '可选字段：描述',
      '使用现代UI组件库（可能是shadcn/ui）',
      '响应式设计，支持移动端和桌面端',
      '简洁的卡片式布局'
    ]
  }
};

// 自定义处理函数
export function handleHeyAiWorldSubmission(data, rule) {
  console.log('Processing HeyAiWorld.com submission...');
  
  // 特殊处理逻辑
  const processedData = { ...data };
  
  // 确保URL格式正确
  if (processedData.siteUrl && !processedData.siteUrl.startsWith('http')) {
    processedData.siteUrl = 'https://' + processedData.siteUrl;
  }
  
  // 如果没有描述，设置默认值
  if (!processedData.siteDescription) {
    processedData.siteDescription = 'An innovative AI tool that helps users accomplish their tasks more efficiently with advanced artificial intelligence capabilities.';
  }
  
  return processedData;
}

// 自定义元素填写函数，专门处理现代UI组件
export async function customFillElement(element, value, config) {
  console.log('🔧 HeyAiWorld自定义填写函数被调用:', element, value);
  
  // 处理带有特殊ID格式的输入框（:r0:, :r1:, :r2:等）
  if (element.id && element.id.includes(':r') && element.id.includes(':-form-item')) {
    try {
      element.focus();
      element.value = value;
      element.dispatchEvent(new Event('input', { bubbles: true }));
      element.dispatchEvent(new Event('change', { bubbles: true }));
      element.dispatchEvent(new Event('blur', { bubbles: true }));
      console.log('✅ 使用特殊ID格式输入框填写:', value);
      return true;
    } catch (error) {
      console.warn('特殊ID格式输入框填写失败:', error);
    }
  }
  
  // 处理现代UI组件的输入框
  if (element.classList.contains('border-input') && element.classList.contains('bg-transparent')) {
    try {
      element.focus();
      element.value = value;
      element.dispatchEvent(new Event('input', { bubbles: true }));
      element.dispatchEvent(new Event('change', { bubbles: true }));
      element.dispatchEvent(new Event('blur', { bubbles: true }));
      console.log('✅ 使用现代UI组件输入框填写:', value);
      return true;
    } catch (error) {
      console.warn('现代UI组件输入框填写失败:', error);
    }
  }
  
  // 处理textarea
  if (element.tagName === 'TEXTAREA' && element.name === 'description') {
    try {
      element.focus();
      element.value = value;
      element.dispatchEvent(new Event('input', { bubbles: true }));
      element.dispatchEvent(new Event('change', { bubbles: true }));
      element.dispatchEvent(new Event('blur', { bubbles: true }));
      console.log('✅ 使用textarea填写描述:', value);
      return true;
    } catch (error) {
      console.warn('textarea填写失败:', error);
    }
  }
  
  // 默认处理
  return false;
}
