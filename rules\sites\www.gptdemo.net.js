// GptDemo.Net 网站规则配置
// 网站: https://www.gptdemo.net/gpt/add-tool
// 最后更新: 2025-07-24

export const SITE_RULE = {
  // 基本信息
  domain: 'www.gptdemo.net',
  siteName: 'GptDemo.Net',
  priority: 1,
  lastUpdated: '2025-07-24',

  // 字段映射规则
  fieldMappings: {
    // 工具链接 -> URL (对应website-info.js的siteUrl)
    siteUrl: {
      selectors: [
        'input[name="url"]',
        'input[placeholder*="URL"]',
        'form input:first-of-type',
        'textbox:first-of-type'
      ],
      method: 'value',
      validation: 'required|url',
      notes: '工具链接，对应website-info.js的siteUrl字段'
    },

    // 工具名称 -> Title (对应website-info.js的siteName)
    siteName: {
      selectors: [
        'input[name="title"]',
        'input[placeholder*="Title"]',
        'form input:nth-of-type(2)',
        'textbox:nth-of-type(2)'
      ],
      method: 'value',
      validation: 'required',
      notes: '工具名称，对应website-info.js的siteName字段'
    },

    // 工具描述 -> Description
    siteDescription: {
      selectors: [
        'input[name="description"]',
        'textarea[name="description"]',
        'input[placeholder*="Description"]',
        'form input:nth-of-type(3)',
        'textbox:nth-of-type(3)'
      ],
      method: 'value',
      validation: 'required',
      notes: '工具描述字段'
    },

    // 分类选择 -> Category
    category: {
      selectors: [
        'select[aria-label="Category"]',
        'combobox[aria-label="Category"]',
        'select',
        'form select'
      ],
      method: 'select',
      validation: 'required',
      defaultValue: 'Code Assistant',
      notes: '工具分类选择'
    }
  },

  // 提交流程配置
  submitConfig: {
    submitButton: 'button:contains("Submit"), input[type="submit"]',
    submitMethod: 'click',
    successIndicators: [
      '.success-message',
      '.alert-success',
      '.notification-success'
    ],
    errorIndicators: [
      '.error-message',
      '.alert-error',
      '.text-danger'
    ]
  },

  // 特殊处理
  specialHandling: {
    requiresLogin: false, // 不需要登录
    hasCaptcha: false,
    hasFileUpload: false,

    // 自定义处理脚本
    customScript: 'handleGptDemoSubmission',

    // 表单验证规则
    formValidation: {
      requiredFields: ['siteUrl', 'siteName', 'siteDescription', 'category'],
      emailValidation: false,
      urlValidation: true
    },

    // 特殊注意事项
    notes: [
      '不需要登录即可提交',
      '所有字段都是必填的',
      '分类选项非常丰富，有200+个选项',
      '默认选择"Code Assistant"分类',
      '表单结构简单，使用标准HTML元素',
      'URL字段需要完整的链接格式'
    ]
  }
};

// 自定义处理函数
export function handleGptDemoSubmission(data, rule) {
  console.log('Processing GptDemo.Net submission...');

  const processedData = { ...data };

  // 确保URL格式正确
  if (processedData.siteUrl && !processedData.siteUrl.startsWith('http')) {
    processedData.siteUrl = 'https://' + processedData.siteUrl;
  }

  // 分类映射 - 根据工具类型智能选择分类
  if (processedData.category) {
    const categoryMap = {
      'AI工具': 'Code Assistant',
      '代码助手': 'Code Assistant',
      '写作助手': 'General Writing',
      '图像生成': 'Image Generator',
      '聊天机器人': 'Chatbots',
      '搜索引擎': 'Search Engine',
      '生产力工具': 'productivity'
    };

    processedData.category = categoryMap[processedData.category] || 'Code Assistant';
  }

  // 如果没有分类，根据描述智能推断
  if (!processedData.category && processedData.siteDescription) {
    const description = processedData.siteDescription.toLowerCase();

    if (description.includes('code') || description.includes('programming')) {
      processedData.category = 'Code Assistant';
    } else if (description.includes('write') || description.includes('content')) {
      processedData.category = 'General Writing';
    } else if (description.includes('image') || description.includes('photo')) {
      processedData.category = 'Image Generator';
    } else if (description.includes('chat') || description.includes('conversation')) {
      processedData.category = 'Chatbots';
    } else {
      processedData.category = 'Code Assistant'; // 默认分类
    }
  }

  console.log('GptDemo.Net 数据处理完成:', processedData);
  return processedData;
}

// 自定义元素填写函数
export async function customFillElement(element, value, config) {
  console.log('🔧 GptDemo.Net自定义填写函数被调用:', element, value);

  // 处理分类选择下拉框
  if (element.tagName === 'SELECT' || element.getAttribute('role') === 'combobox') {
    try {
      console.log('🎯 处理分类选择');

      // 查找匹配的选项
      const options = element.querySelectorAll('option');
      let selectedOption = null;

      // 1. 精确匹配
      selectedOption = Array.from(options).find(option =>
        option.value === value || option.textContent.trim() === value
      );

      // 2. 模糊匹配
      if (!selectedOption) {
        selectedOption = Array.from(options).find(option =>
          option.textContent.toLowerCase().includes(value.toLowerCase()) ||
          value.toLowerCase().includes(option.textContent.toLowerCase())
        );
      }

      // 3. 智能匹配常见分类
      if (!selectedOption) {
        const categoryMappings = {
          'code': 'Code Assistant',
          'ai': 'Code Assistant',
          'writing': 'General Writing',
          'image': 'Image Generator',
          'chat': 'Chatbots',
          'search': 'Search Engine'
        };

        const lowerValue = value.toLowerCase();
        for (const [key, category] of Object.entries(categoryMappings)) {
          if (lowerValue.includes(key)) {
            selectedOption = Array.from(options).find(option =>
              option.textContent.includes(category)
            );
            break;
          }
        }
      }

      // 4. 默认选择第一个相关选项
      if (!selectedOption) {
        selectedOption = Array.from(options).find(option =>
          option.textContent.includes('Code Assistant')
        ) || options[1]; // 跳过第一个空选项
      }

      if (selectedOption) {
        element.value = selectedOption.value;
        element.dispatchEvent(new Event('change', { bubbles: true }));
        console.log('✅ 选择了分类:', selectedOption.textContent);
        return true;
      }

    } catch (error) {
      console.warn('分类选择失败:', error);
    }
  }

  // 处理普通输入框和文本域
  if (element.tagName === 'INPUT' || element.tagName === 'TEXTAREA') {
    try {
      element.value = value;
      element.dispatchEvent(new Event('input', { bubbles: true }));
      element.dispatchEvent(new Event('change', { bubbles: true }));

      console.log('✅ 填写字段:', value);
      return true;
    } catch (error) {
      console.warn('字段填写失败:', error);
    }
  }

  // 默认处理
  return false;
};