// osalt.com 网站规则配置
// 网站: https://osalt.com/suggest
// 表单技术: Simple HTML Form
// 最后更新: 2025-07-08

export const SITE_RULE = {
  // 基本信息
  domain: 'osalt.com',
  siteName: 'OSalt',
  priority: 1,
  lastUpdated: '2025-07-08',
  
  // 字段映射规则
  fieldMappings: {
    // 您的姓名 -> Your name (optional)
    fullName: {
      selectors: [
        'input[name="name"]',
        'input[placeholder*="Your name"]',
        'form input[type="text"]:first-of-type',
        'input:first-of-type'
      ],
      method: 'value',
      validation: 'optional',
      notes: '您的姓名，使用website-info.js中的fullName字段，可选字段'
    },
    
    // 应用名称 -> Application name
    siteName: {
      selectors: [
        'input[name="application"]',
        'input[name="app_name"]',
        'input[placeholder*="Application name"]',
        'form input[type="text"]:nth-of-type(2)'
      ],
      method: 'value',
      validation: 'required',
      notes: '应用名称，使用website-info.js中的siteName字段'
    },
    
    // 主页URL -> Homepage - URL for suggested application
    siteUrl: {
      selectors: [
        'input[name="app_homepage"]',
        'input[name="homepage"]',
        'input[name="url"]',
        'input[size="55"]'
      ],
      method: 'value',
      validation: 'required|url',
      notes: '主页URL，使用website-info.js中的siteUrl字段'
    },
    
    // 邮箱 -> Your e-mail
    contactEmail: {
      selectors: [
        'input[name="email"]',
        'input[type="email"]',
        'input[placeholder*="e-mail"]',
        'form input[type="text"]:nth-of-type(4)'
      ],
      method: 'value',
      validation: 'required|email',
      notes: '邮箱地址，使用website-info.js中的contactEmail字段'
    },
    
    // 评论 -> Comments
    siteDescription: {
      selectors: [
        'textarea[name="comments"]',
        'textarea[placeholder*="Comments"]',
        'form textarea',
        'textarea:first-of-type'
      ],
      method: 'value',
      validation: 'optional',
      notes: '评论，使用website-info.js中的siteDescription字段'
    }
  },
  
  // 自定义填写函数
  customFillElement: async function(element, value, config) {
    console.log(`OSalt自定义填写: ${element.name || element.placeholder}, 方法: ${config.method}`);
    
    switch (config.method) {
      case 'value':
        // 标准输入框和文本域处理
        element.focus();
        await new Promise(resolve => setTimeout(resolve, 100));
        
        // 清空现有内容
        element.value = '';
        await new Promise(resolve => setTimeout(resolve, 50));
        
        // 处理特殊字段
        let finalValue = value;
        if (element.name === 'comments' || element.tagName.toLowerCase() === 'textarea') {
          // 评论字段添加开源软件相关内容
          finalValue = `${value}. This is an excellent open source alternative that provides similar functionality to commercial software solutions. It offers great value for users looking for free and open source options.`;
        }
        
        // 设置新值
        element.value = finalValue;
        
        // 触发事件
        element.dispatchEvent(new Event('input', { bubbles: true }));
        element.dispatchEvent(new Event('change', { bubbles: true }));
        element.dispatchEvent(new Event('blur', { bubbles: true }));
        
        console.log(`✓ 填写字段: ${element.name} = "${finalValue.substring(0, 50)}..."`);
        break;
        
      default:
        element.value = value;
        element.dispatchEvent(new Event('input', { bubbles: true }));
        element.dispatchEvent(new Event('change', { bubbles: true }));
    }
  },
  
  // 提交流程配置
  submitConfig: {
    submitButton: [
      'input[type="submit"]',
      'button[type="submit"]',
      'input[value*="Submit"]',
      'button:contains("Submit")'
    ],
    submitMethod: 'click',
    waitAfterFill: 1500,
    waitAfterSubmit: 3000,
    successIndicators: [
      'text:contains("submitted")',
      'text:contains("thank you")',
      'text:contains("received")',
      'text:contains("added")'
    ],
    errorIndicators: [
      'text:contains("error")',
      'text:contains("required")',
      'text:contains("invalid")'
    ]
  },
  
  // 特殊处理
  specialHandling: {
    requiresLogin: false,
    hasCaptcha: false,
    hasFileUpload: false,
    isSimpleForm: true, // 简单表单
    isOpenSourceDirectory: true, // 开源软件目录
    isSuggestionForm: true, // 建议表单
    
    // 表单验证规则
    formValidation: {
      requiredFields: ['siteName', 'siteUrl', 'contactEmail'],
      optionalFields: ['fullName', 'siteDescription'],
      emailValidation: true,
      urlValidation: true
    },
    
    // 特殊注意事项
    notes: [
      '这是OSalt开源软件替代品网站的建议表单',
      '表单包含5个字段：3个必填，2个可选',
      '专注于开源软件和商业软件替代品',
      '姓名字段是可选的',
      '评论字段会自动添加开源软件相关描述',
      '网站成立于2006年，历史悠久',
      '由Airflake ApS发布',
      '有RSS订阅和Twitter关注',
      '提供软件目录和分类浏览',
      '鼓励用户推广和分享网站'
    ]
  }
};

// 自定义处理函数
export function handleOSaltSubmission(data, _rule) {
  console.log('Processing OSalt form submission...');

  const processedData = { ...data };

  // 确保URL格式正确
  if (processedData.siteUrl && !processedData.siteUrl.startsWith('http')) {
    processedData.siteUrl = 'https://' + processedData.siteUrl;
  }

  // 增强评论内容
  if (processedData.siteDescription) {
    processedData.siteDescription = `${processedData.siteDescription}. This is an excellent open source alternative that provides similar functionality to commercial software solutions. It offers great value for users looking for free and open source options.`;
  }

  return processedData;
}

// 开源软件建议检测
export function detectOpenSourceSuggestion() {
  console.log('检测开源软件建议表单...');
  
  // 检查页面内容
  const pageText = document.body.textContent.toLowerCase();
  const openSourceKeywords = ['open source', 'alternative', 'commercial', 'suggest'];
  
  let foundKeywords = 0;
  openSourceKeywords.forEach(keyword => {
    if (pageText.includes(keyword)) {
      foundKeywords++;
    }
  });
  
  if (foundKeywords >= 3) {
    console.log('✓ 确认为开源软件建议表单');
    return true;
  }
  
  return false;
}

// 表单字段检测
export function detectFormFields() {
  console.log('检测表单字段...');
  
  const expectedFields = [
    'name (optional)',
    'application name',
    'homepage',
    'e-mail',
    'comments'
  ];
  
  expectedFields.forEach(field => {
    const element = document.querySelector(`input[name*="${field.split(' ')[0]}"], textarea[name*="${field.split(' ')[0]}"]`);
    if (element) {
      console.log(`✓ 找到字段: ${field}`);
    } else {
      console.log(`⚠️ 未找到字段: ${field}`);
    }
  });
}

// 开源软件信息提醒
export function showOpenSourceInfo() {
  console.log('📖 OSalt 开源软件信息:');
  console.log('');
  console.log('关于OSalt:');
  console.log('- 成立于2006年的开源软件替代品网站');
  console.log('- 帮助用户找到商业软件的开源替代品');
  console.log('- 提供详细的软件分类和目录');
  console.log('- 支持用户建议和评论');
  console.log('');
  console.log('建议要求:');
  console.log('- 可以建议开源软件或商业软件');
  console.log('- 如果建议开源软件，请提及对应的商业软件');
  console.log('- 请先检查是否已经收录');
  console.log('- 如已收录，可以添加评论说明喜欢的原因');
  console.log('');
  console.log('OSalt致力于推广开源软件生态！');
}

// 软件分类信息
export function showSoftwareCategories() {
  console.log('📂 OSalt 软件分类:');
  console.log('');
  console.log('主要分类:');
  console.log('- Business (商业软件)');
  console.log('- Communications (通讯软件)');
  console.log('- Databases (数据库)');
  console.log('- Development (开发工具)');
  console.log('- Education & Science (教育科学)');
  console.log('- Games (游戏)');
  console.log('- Graphic Applications (图形应用)');
  console.log('- Internet & Networking (网络工具)');
  console.log('- Multimedia & Audio (多媒体音频)');
  console.log('- Security & Privacy (安全隐私)');
  console.log('- System Utilities (系统工具)');
  console.log('- Web Development (Web开发)');
  console.log('');
  console.log('涵盖了软件开发的各个领域！');
}
