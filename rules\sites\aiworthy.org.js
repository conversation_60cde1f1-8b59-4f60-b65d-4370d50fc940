// aiworthy.org 网站规则配置
// 网站: https://aiworthy.org/submit-tool/
// 表单技术: WordPress Contact Form
// 最后更新: 2025-07-08

export const SITE_RULE = {
  // 基本信息
  domain: 'aiworthy.org',
  siteName: 'AI Worthy',
  priority: 1,
  lastUpdated: '2025-07-08',
  
  // 字段映射规则
  fieldMappings: {
    // 您的姓名 -> Your name
    fullName: {
      selectors: [
        'input[name="your-name"]',
        'input[placeholder*="Your name"]',
        'input[type="text"]:first-of-type',
        'form input[type="text"]:nth-of-type(1)'
      ],
      method: 'value',
      validation: 'required',
      notes: '您的姓名，使用website-info.js中的fullName字段'
    },
    
    // 您的邮箱 -> Your email
    contactEmail: {
      selectors: [
        'input[name="your-email"]',
        'input[type="email"]',
        'input[placeholder*="Your email"]',
        'form input[type="email"]:first-of-type'
      ],
      method: 'value',
      validation: 'required|email',
      notes: '您的邮箱，使用website-info.js中的contactEmail字段'
    },
    
    // 工具名称 -> Tool Name
    siteName: {
      selectors: [
        'input[name="tool-name"]',
        'input[placeholder*="Tool Name"]',
        'input[type="text"]:nth-of-type(2)',
        'form input[type="text"]:nth-of-type(3)'
      ],
      method: 'value',
      validation: 'required',
      notes: '工具名称，使用website-info.js中的siteName字段'
    },
    
    // 工具URL -> Tool URL
    siteUrl: {
      selectors: [
        'input[name="tool-url"]',
        'input[placeholder*="Tool URL"]',
        'input[type="url"]',
        'input[type="text"]:nth-of-type(3)'
      ],
      method: 'value',
      validation: 'required|url',
      notes: '工具URL，使用website-info.js中的siteUrl字段'
    },
    
    // 工具描述 -> Tool Description (optional)
    siteDescription: {
      selectors: [
        'textarea[name="tool-description"]',
        'textarea[placeholder*="Tool Description"]',
        'form textarea:first-of-type',
        'textarea'
      ],
      method: 'value',
      validation: 'optional',
      notes: '工具描述，使用website-info.js中的siteDescription字段，可选字段'
    }
  },
  
  // 自定义填写函数
  customFillElement: async function(element, value, config) {
    console.log(`AI Worthy自定义填写: ${element.name || element.placeholder}, 方法: ${config.method}`);
    
    switch (config.method) {
      case 'value':
        // 标准输入框和文本域处理
        element.focus();
        await new Promise(resolve => setTimeout(resolve, 100));
        
        // 清空现有内容
        element.value = '';
        await new Promise(resolve => setTimeout(resolve, 50));
        
        // 设置新值
        element.value = value;
        
        // 触发事件
        element.dispatchEvent(new Event('input', { bubbles: true }));
        element.dispatchEvent(new Event('change', { bubbles: true }));
        element.dispatchEvent(new Event('blur', { bubbles: true }));
        
        console.log(`✓ 填写字段: ${element.name || element.placeholder} = "${value.substring(0, 50)}..."`);
        break;
        
      default:
        element.value = value;
        element.dispatchEvent(new Event('input', { bubbles: true }));
        element.dispatchEvent(new Event('change', { bubbles: true }));
    }
  },
  
  // 提交流程配置
  submitConfig: {
    submitButton: [
      'input[type="submit"]',
      'button[type="submit"]',
      'input[value*="Submit"]',
      'button:contains("Submit")'
    ],
    submitMethod: 'click',
    waitAfterFill: 1500,
    waitAfterSubmit: 3000,
    successIndicators: [
      'text:contains("submitted")',
      'text:contains("thank you")',
      'text:contains("received")',
      'text:contains("success")'
    ],
    errorIndicators: [
      'text:contains("error")',
      'text:contains("required")',
      'text:contains("invalid")'
    ]
  },
  
  // 特殊处理
  specialHandling: {
    requiresLogin: false,
    hasCaptcha: false,
    hasFileUpload: false,
    isWordPressForm: true, // WordPress表单
    isReviewRequest: true, // 评测请求
    hasBacklog: true, // 有积压请求
    
    // 表单验证规则
    formValidation: {
      requiredFields: ['fullName', 'contactEmail', 'siteName', 'siteUrl'],
      optionalFields: ['siteDescription'],
      emailValidation: true,
      urlValidation: true
    },
    
    // 特殊注意事项
    notes: [
      '这是AI Worthy的工具评测请求表单',
      '表单包含5个字段：4个必填，1个可选',
      '专注于AI工具的深度评测和推荐',
      '有大量的评测请求积压',
      '提供详细的AI工具洞察和分析',
      '按分类组织AI工具（营销、写作、视频等）',
      '使用WordPress Contact Form',
      '支持工具比较功能',
      '有购物车和愿望清单功能',
      '提供顶级AI工具排行榜'
    ]
  }
};

// 自定义处理函数
export function handleAIWorthySubmission(data, _rule) {
  console.log('Processing AI Worthy form submission...');

  const processedData = { ...data };

  // 确保URL格式正确
  if (processedData.siteUrl && !processedData.siteUrl.startsWith('http')) {
    processedData.siteUrl = 'https://' + processedData.siteUrl;
  }

  return processedData;
}

// WordPress表单检测
export function detectWordPressForm() {
  console.log('检测WordPress表单...');
  
  // 检查WordPress特征
  const wpElements = document.querySelectorAll('[class*="wp-"], [id*="wp-"]');
  const contactForm = document.querySelector('form');
  
  if (wpElements.length > 0 || contactForm) {
    console.log('✓ 检测到WordPress表单');
    return true;
  }
  
  return false;
}

// AI工具分类检测
export function detectAIToolCategories() {
  console.log('检测AI工具分类...');
  
  const categories = [
    'Marketing', 'Writing', 'Video', 'Voice', 
    'Meeting', 'Note Taking', 'Business'
  ];
  
  categories.forEach(category => {
    const element = document.querySelector(`a:contains("${category}")`);
    if (element) {
      console.log(`✓ 找到分类: ${category}`);
    }
  });
}

// AI Worthy信息提醒
export function showAIWorthyInfo() {
  console.log('🏆 AI Worthy 信息:');
  console.log('');
  console.log('平台特色:');
  console.log('- 专业的AI工具评测和推荐平台');
  console.log('- 提供深度的工具洞察和分析');
  console.log('- 按分类组织AI工具');
  console.log('- 顶级AI工具排行榜');
  console.log('');
  console.log('评测分类:');
  console.log('- Marketing (营销)');
  console.log('- Writing (写作)');
  console.log('- Video (视频)');
  console.log('- Voice (语音)');
  console.log('- Meeting (会议)');
  console.log('- Note Taking (笔记)');
  console.log('');
  console.log('提交说明:');
  console.log('- 有大量评测请求积压');
  console.log('- 团队会尽快处理新工具');
  console.log('- 专注于值得评测的优质AI工具');
  console.log('- 工具描述是可选的');
  console.log('');
  console.log('AI Worthy - 发现值得信赖的AI工具！');
}

// 表单验证
export function validateAIWorthyForm() {
  console.log('验证AI Worthy表单...');
  
  const requiredFields = [
    { selector: 'input[type="text"]:first-of-type', label: '姓名' },
    { selector: 'input[type="email"]', label: '邮箱' },
    { selector: 'input[type="text"]:nth-of-type(2)', label: '工具名称' },
    { selector: 'input[type="url"], input[type="text"]:nth-of-type(3)', label: '工具URL' }
  ];
  
  let isValid = true;
  
  requiredFields.forEach(field => {
    const element = document.querySelector(field.selector);
    if (!element || !element.value.trim()) {
      console.log(`⚠️ 必填字段为空: ${field.label}`);
      isValid = false;
    }
  });
  
  if (isValid) {
    console.log('✓ 表单验证通过');
  }
  
  return isValid;
}

// 评测请求提醒
export function showReviewRequestReminder() {
  console.log('📝 评测请求提醒:');
  console.log('');
  console.log('提交要求:');
  console.log('- 提供完整的个人信息');
  console.log('- 确保工具URL有效可访问');
  console.log('- 工具应该值得深度评测');
  console.log('- 描述字段可选但建议填写');
  console.log('');
  console.log('处理时间:');
  console.log('- 当前有大量请求积压');
  console.log('- 团队会按顺序处理');
  console.log('- 优质工具会优先考虑');
  console.log('- 请耐心等待评测结果');
  console.log('');
  console.log('AI Worthy致力于为用户推荐最优质的AI工具！');
}
