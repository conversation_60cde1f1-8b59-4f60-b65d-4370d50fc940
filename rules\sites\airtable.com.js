// airtable.com 网站规则配置
// 网站: https://airtable.com/appcN6nvv5n1GpABK/pagzZyGl6fEI2RWDq/form
// 表单技术: Airtable Form with Dynamic IDs
// 最后更新: 2025-07-08

export const SITE_RULE = {
  // 基本信息
  domain: 'airtable.com',
  siteName: 'Airtable Form',
  priority: 1,
  lastUpdated: '2025-07-08',
  
  // 字段映射规则
  fieldMappings: {
    // 名称 -> Name
    siteName: {
      selectors: [
        'textarea[id*="83d94ce8408075749a312d76ac423e0a"]',
        'label[for*="83d94ce8408075749a312d76ac423e0a"] ~ div textarea',
        'textarea[aria-required="true"]:first-of-type',
        'div[data-tutorial-selector-id="pageCellLabelPairName"] textarea'
      ],
      method: 'value',
      validation: 'required',
      notes: '名称，使用website-info.js中的siteName字段'
    },
    
    // 描述 -> Description
    siteDescription: {
      selectors: [
        'textarea[id*="243d11878728148b23d55e5a94064ef5"]',
        'label[for*="243d11878728148b23d55e5a94064ef5"] ~ div textarea',
        'textarea[aria-required="true"]:nth-of-type(2)',
        'div[data-tutorial-selector-id="pageCellLabelPairDescription"] textarea'
      ],
      method: 'value',
      validation: 'required',
      notes: '描述，使用website-info.js中的siteDescription字段'
    },
    
    // 网站 -> Website
    siteUrl: {
      selectors: [
        'input[id*="aa4299663a51bd8cc2af6319568df014"]',
        'label[for*="aa4299663a51bd8cc2af6319568df014"] ~ div input',
        'input[type="text"][aria-required="true"]:first-of-type',
        'div[data-tutorial-selector-id="pageCellLabelPairWebsite"] input'
      ],
      method: 'value',
      validation: 'required|url',
      notes: '网站URL，使用website-info.js中的siteUrl字段'
    },
    
    // 仓库 -> Repository
    repositoryUrl: {
      selectors: [
        'input[id*="d4164414de5d5b003cf832f9d5dfb406"]',
        'label[for*="d4164414de5d5b003cf832f9d5dfb406"] ~ div input',
        'input[type="text"][aria-required="true"]:nth-of-type(2)',
        'div[data-tutorial-selector-id="pageCellLabelPairRepository"] input'
      ],
      method: 'value',
      validation: 'required|url',
      defaultValue: 'https://github.com/coderforge/bytevault',
      notes: '代码仓库URL，默认GitHub仓库格式'
    },
    
    // 分类 -> Category
    category: {
      selectors: [
        'button[id*="id_3734fb78a9422cfa68ec06c63309869f"]',
        'button[aria-label*="Add category to Category field"]',
        'div[data-tutorial-selector-id="pageCellLabelPairCategory"] button',
        'button:contains("Add category")',
        'div[data-columntype="text"]:contains("Design")'
      ],
      method: 'airtable-select',
      validation: 'required',
      defaultValue: 'Design',
      notes: '分类，点击Add category按钮选择，默认Design'
    },
    
    // 替代品 -> Alternative
    alternative: {
      selectors: [
        'button[id*="id_425fab0892e08e1e5d4370b62d7f9c1c"]',
        'button[aria-label*="Add record to Alternative field"]',
        'div[data-tutorial-selector-id="pageCellLabelPairAlternative"] button',
        'button:contains("Add record")'
      ],
      method: 'airtable-select',
      validation: 'optional',
      notes: '替代品，点击Add record按钮选择，可选字段'
    },
    
    // 草稿状态 -> Is Draft
    isDraft: {
      selectors: [
        'div[role="checkbox"][aria-labelledby*="label-********************************"]',
        'div[data-tutorial-selector-id="pageCellLabelPairIsDraft"] div[role="checkbox"]',
        'div[role="checkbox"][aria-checked="true"]'
      ],
      method: 'airtable-checkbox',
      validation: 'readonly',
      defaultValue: true,
      notes: '草稿状态，只读字段，默认为true'
    }
  },
  
  // 自定义填写函数
  customFillElement: async function(element, value, config) {
    console.log(`Airtable自定义填写: ${element.id || element.className}, 方法: ${config.method}`);
    
    switch (config.method) {
      case 'value':
        // 标准输入框和文本域处理
        element.focus();
        await new Promise(resolve => setTimeout(resolve, 100));
        
        // 清空现有内容
        element.value = '';
        await new Promise(resolve => setTimeout(resolve, 50));
        
        // 处理特殊字段
        let finalValue = value;
        if (config.defaultValue && (!value || value.trim() === '')) {
          finalValue = config.defaultValue;
        }
        
        // 设置新值
        element.value = finalValue;
        
        // 触发Airtable事件
        element.dispatchEvent(new Event('input', { bubbles: true }));
        element.dispatchEvent(new Event('change', { bubbles: true }));
        element.dispatchEvent(new Event('blur', { bubbles: true }));
        
        // 额外的Airtable事件
        element.dispatchEvent(new KeyboardEvent('keyup', { bubbles: true }));
        
        console.log(`✓ 填写字段: ${finalValue.substring(0, 50)}...`);
        break;
        
      case 'airtable-select':
        // Airtable选择字段处理
        console.log('⚠️ Airtable选择字段需要手动处理');
        console.log(`请手动点击按钮并选择: ${config.defaultValue || '相应选项'}`);
        
        // 尝试点击按钮
        element.click();
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        console.log(`✓ 已点击选择按钮，请手动选择选项`);
        break;
        
      case 'airtable-checkbox':
        // Airtable复选框处理（只读）
        console.log('ℹ️ 这是只读复选框字段');
        console.log(`当前状态: ${element.getAttribute('aria-checked')}`);
        break;
        
      default:
        element.value = value;
        element.dispatchEvent(new Event('input', { bubbles: true }));
        element.dispatchEvent(new Event('change', { bubbles: true }));
    }
  },
  
  // 提交流程配置
  submitConfig: {
    submitButton: [
      'button[type="submit"]',
      'button:contains("Submit")',
      'button:contains("Save")',
      '.submit-button'
    ],
    submitMethod: 'click',
    waitAfterFill: 3000,
    waitAfterSubmit: 5000,
    successIndicators: [
      'text:contains("submitted")',
      'text:contains("saved")',
      'text:contains("success")'
    ],
    errorIndicators: [
      'text:contains("error")',
      'text:contains("required")',
      'text:contains("invalid")'
    ]
  },
  
  // 特殊处理
  specialHandling: {
    requiresLogin: false,
    hasCaptcha: false,
    hasFileUpload: false,
    isAirtableForm: true, // Airtable表单
    hasDynamicIds: true, // 动态ID
    hasSelectFields: true, // 有选择字段
    hasReadonlyFields: true, // 有只读字段
    
    // 表单验证规则
    formValidation: {
      requiredFields: ['siteName', 'siteDescription', 'siteUrl', 'repositoryUrl', 'category'],
      optionalFields: ['alternative'],
      readonlyFields: ['isDraft'],
      emailValidation: false,
      urlValidation: true,
      airtableFields: true
    },
    
    // 特殊注意事项
    notes: [
      '这是Airtable表单，使用动态生成的ID',
      '表单包含7个字段：5个必填，1个可选，1个只读',
      '字段ID是动态生成的长字符串',
      '分类和替代品字段需要点击按钮选择',
      '草稿状态是只读字段，默认为true',
      '需要提供代码仓库URL',
      '使用Airtable的自定义样式和组件',
      '表单有复杂的CSS类名结构',
      '支持实时数据验证',
      '专注于开源项目收录'
    ]
  }
};

// 自定义处理函数
export function handleAirtableSubmission(data, _rule) {
  console.log('Processing Airtable form submission...');

  const processedData = { ...data };

  // 确保URL格式正确
  if (processedData.siteUrl && !processedData.siteUrl.startsWith('http')) {
    processedData.siteUrl = 'https://' + processedData.siteUrl;
  }

  // 设置默认仓库URL
  if (!processedData.repositoryUrl) {
    processedData.repositoryUrl = 'https://github.com/username/repository';
  }

  // 确保仓库URL格式正确
  if (processedData.repositoryUrl && !processedData.repositoryUrl.startsWith('http')) {
    processedData.repositoryUrl = 'https://' + processedData.repositoryUrl;
  }

  // 设置默认值
  processedData.category = 'AI Tools';
  processedData.isDraft = true;

  return processedData;
}

// Airtable表单检测
export function detectAirtableForm() {
  console.log('检测Airtable表单...');

  // 检查Airtable特征
  const airtableElements = document.querySelectorAll('[data-testid*="page-element"]');
  const cellEditors = document.querySelectorAll('[data-testid="cell-editor"]');

  if (airtableElements.length > 0 && cellEditors.length > 0) {
    console.log(`✓ 检测到Airtable表单，${airtableElements.length}个页面元素，${cellEditors.length}个单元格编辑器`);
    return true;
  }

  return false;
}

// 动态ID字段检测
export function detectDynamicIdFields() {
  console.log('检测动态ID字段...');

  const fields = [
    { label: 'Name', selector: 'textarea[aria-required="true"]' },
    { label: 'Description', selector: 'textarea[aria-required="true"]' },
    { label: 'Website', selector: 'input[aria-required="true"]' },
    { label: 'Repository', selector: 'input[aria-required="true"]' },
    { label: 'Category', selector: 'button[aria-label*="Add category"]' },
    { label: 'Alternative', selector: 'button[aria-label*="Add record"]' },
    { label: 'Is Draft', selector: 'div[role="checkbox"]' }
  ];

  fields.forEach(field => {
    const elements = document.querySelectorAll(field.selector);
    console.log(`${field.label}: 找到 ${elements.length} 个匹配元素`);
  });
}

// Airtable选择字段处理
export async function handleAirtableSelectField(buttonElement, optionValue) {
  console.log(`处理Airtable选择字段: ${optionValue}`);

  // 点击按钮打开选择器
  buttonElement.click();
  await new Promise(resolve => setTimeout(resolve, 1000));

  // 查找选项（这里需要根据实际的选项结构调整）
  const option = document.querySelector(`[data-value="${optionValue}"], [title="${optionValue}"]`);
  if (option) {
    option.click();
    console.log(`✓ 选择了选项: ${optionValue}`);
    return true;
  } else {
    console.log(`⚠️ 未找到选项: ${optionValue}`);
    return false;
  }
}

// 表单验证
export function validateAirtableForm() {
  console.log('验证Airtable表单...');

  const requiredFields = [
    { selector: 'textarea[aria-required="true"]', label: '名称和描述' },
    { selector: 'input[aria-required="true"]', label: '网站和仓库' }
  ];

  let isValid = true;

  requiredFields.forEach(field => {
    const elements = document.querySelectorAll(field.selector);
    elements.forEach((element, index) => {
      if (!element.value.trim()) {
        console.log(`⚠️ 必填字段为空: ${field.label} ${index + 1}`);
        isValid = false;
      }
    });
  });

  if (isValid) {
    console.log('✓ 表单验证通过');
  }

  return isValid;
}

// Airtable表单信息提醒
export function showAirtableFormInfo() {
  console.log('📋 Airtable表单信息:');
  console.log('');
  console.log('表单特点:');
  console.log('- 使用Airtable的动态表单系统');
  console.log('- 字段ID是动态生成的');
  console.log('- 支持多种字段类型');
  console.log('- 实时数据验证');
  console.log('');
  console.log('字段说明:');
  console.log('- Name: 项目名称（必填）');
  console.log('- Description: 项目描述（必填）');
  console.log('- Website: 项目网站（必填）');
  console.log('- Repository: 代码仓库（必填）');
  console.log('- Category: 项目分类（必填，需手动选择）');
  console.log('- Alternative: 替代品（可选，需手动选择）');
  console.log('- Is Draft: 草稿状态（只读，默认true）');
  console.log('');
  console.log('Airtable - 强大的数据库表单系统！');
}

// 仓库URL生成器
export function generateRepositoryUrl(projectName) {
  console.log(`生成仓库URL: ${projectName}`);

  // 将项目名称转换为GitHub仓库格式
  const repoName = projectName.toLowerCase()
    .replace(/[^a-z0-9\s-]/g, '')
    .replace(/\s+/g, '-')
    .replace(/-+/g, '-')
    .trim();

  const repoUrl = `https://github.com/username/${repoName}`;
  console.log(`生成的仓库URL: ${repoUrl}`);

  return repoUrl;
}

// 分类选择提醒
export function showCategorySelectionReminder() {
  console.log('📂 分类选择提醒:');
  console.log('');
  console.log('常见分类:');
  console.log('- AI Tools (AI工具)');
  console.log('- Web Development (Web开发)');
  console.log('- Mobile Apps (移动应用)');
  console.log('- Developer Tools (开发工具)');
  console.log('- Design Tools (设计工具)');
  console.log('- Productivity (生产力工具)');
  console.log('');
  console.log('请点击"Add category"按钮手动选择分类！');
}
