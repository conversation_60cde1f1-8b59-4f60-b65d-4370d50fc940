// 247WebDirectory.com 网站规则配置
// 网站: https://www.247webdirectory.com/submit.aspx
// 最后更新: 2025-07-26

export const SITE_RULE = {
  // 基本信息
  domain: 'www.247webdirectory.com',
  siteName: '247 Web Directory',
  priority: 1,
  lastUpdated: '2025-07-26',

  // 字段映射规则
  fieldMappings: {
    // 网站标题 -> Website Title
    siteName: {
      selectors: [
        'input[name="site_title"]',
        '#site_title',
        'input[placeholder="Enter Website Title"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '网站标题，提及公司/网站名称'
    },

    // 网站URL -> Website URL
    siteUrl: {
      selectors: [
        'input[name="site_url"]',
        '#site_url',
        'input[placeholder="Enter Website URL"]'
      ],
      method: 'value',
      validation: 'required|url',
      notes: '网站URL地址'
    },

    // 联系邮箱 -> E-mail ID
    contactEmail: {
      selectors: [
        'input[name="email_add"]',
        '#email_add',
        'input[type="email"]',
        'input[placeholder="Enter email"]'
      ],
      method: 'value',
      validation: 'required|email',
      notes: '联系邮箱，用于发送确认邮件，不会公开显示'
    },

    // 网站描述 -> Site Description
    siteDescription: {
      selectors: [
        'textarea[name="site_desc"]',
        '#site_desc',
        'textarea[rows="5"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '网站描述，不要使用全大写或全小写，避免推广性语言'
    },

    // 验证码 -> Type the code from the below box
    captcha: {
      selectors: [
        'input[name="txtInputCaptcha"]',
        '#txtInputCaptcha',
        'input[placeholder="Enter Below Text"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '验证码，输入下方显示的文本'
    },

    // 付费选项 -> Expedite Review / Free
    pricing: {
      selectors: [
        'input[name="paid"]'
      ],
      method: 'radio',
      validation: 'required',
      options: ['Y', 'N'],
      defaultValue: 'N',
      notes: '审核选项：Y=快速审核($39.99终身)，N=免费(无时间保证)'
    }
  },

  // 提交流程配置
  submitConfig: {
    submitButton: 'button[onclick="validation()"], .btn-sm',
    submitMethod: 'manual',
    waitAfterFill: 0,
    waitAfterSubmit: 0,
    successIndicators: ['.success-message'],
    errorIndicators: ['.error-message']
  },

  // 特殊处理
  specialHandling: {
    requiresLogin: false,
    hasCaptcha: true,
    hasFileUpload: false,
    customScript: 'handle247WebDirectorySubmission',
    formValidation: {
      requiredFields: ['siteName', 'siteUrl', 'contactEmail', 'siteDescription', 'captcha', 'pricing'],
      emailValidation: true,
      urlValidation: true
    },
    notes: [
      '247 Web Directory 网站目录',
      '提供两种审核选项：',
      '- 快速审核：$39.99终身，2个工作日内审核，支持PayPal，未通过全额退款',
      '- 免费审核：无时间保证',
      '包含验证码验证',
      '邮箱仅用于确认，不会公开显示或分享',
      '描述要求：不使用全大写/小写，避免推广性语言，保留编辑权',
      '现代化Bootstrap界面',
      '使用ASP.NET技术',
      '手动提交模式'
    ]
  }
};

// 自定义处理函数
export function handle247WebDirectorySubmission(data, rule) {
  console.log('Processing 247 Web Directory form submission...');
  
  const processedData = { ...data };
  
  // 确保URL格式正确
  if (processedData.siteUrl && !processedData.siteUrl.startsWith('http')) {
    processedData.siteUrl = 'https://' + processedData.siteUrl;
  }
  
  // 设置默认为免费审核
  if (!processedData.pricing) {
    processedData.pricing = 'N';
  }
  
  // 处理描述格式
  if (processedData.siteDescription) {
    // 确保描述不是全大写或全小写
    const desc = processedData.siteDescription.trim();
    if (desc === desc.toUpperCase() || desc === desc.toLowerCase()) {
      // 转换为标题格式
      processedData.siteDescription = desc.toLowerCase().replace(/\b\w/g, l => l.toUpperCase());
    }
  }
  
  return processedData;
}
