// AI Site Submitter - 网站名称 规则配置
// 自动生成于: 2025/7/15 10:57:01
// 域名: whattheai.tech

export const SITE_RULE = {
  "domain": "whattheai.tech",
  "siteName": "Whattheai",
  "lastUpdated": "2025-07-15T02:57:01.747Z",
  "fieldMappings": {
    "siteName": {
      "selectors": [
        "form .w-full.px-4.py-2.rounded-lg.border.border-gray-300:nth-of-type(1)"
      ],
      "type": "input",
      "fillMethod": "value",
      "required": false,
      "validation": "required"
    },
    "siteDescription": {
      "selectors": [
        "form .w-full.px-4.py-2.rounded-lg.border.border-gray-300:nth-of-type(2)"
      ],
      "type": "input",
      "fillMethod": "value",
      "required": false,
      "validation": "required"
    },
    "detailedIntro": {
      "selectors": [
        "form textarea.w-full.px-4.py-2.rounded-lg.border.border-gray-300"
      ],
      "type": "input",
      "fillMethod": "value",
      "required": false,
      "validation": "required"
    },
    "companyName": {
      "selectors": [
        "form .w-full.px-4.py-2.rounded-lg.border.border-gray-300:nth-of-type(3)"
      ],
      "type": "input",
      "fillMethod": "value",
      "required": false
    },
    "thumbnailUrl": {
      "selectors": [
        "#logo-upload"
      ],
      "type": "input",
      "fillMethod": "value",
      "required": false,
      "validation": "required"
    },
    "siteUrl": {
      "selectors": [
        "form .w-full.px-4.py-2.rounded-lg.border.border-gray-300[type='url']:nth-of-type(1)"
      ],
      "type": "input",
      "fillMethod": "value",
      "required": false,
      "validation": "url"
    },
    "apiAvailable": {
      "selectors": [
        "form .w-full.px-4.py-2.rounded-lg.border.border-gray-300[type='url']:nth-of-type(2)"
      ],
      "type": "input",
      "fillMethod": "value",
      "required": false,
      "validation": "url"
    },
    "socialLinks": {
      "selectors": [
        "form .w-full.px-4.py-2.rounded-lg.border.border-gray-300[type='url']:nth-of-type(3)"
      ],
      "type": "input",
      "fillMethod": "value",
      "required": false,
      "validation": "url"
    },
    "category": {
      "selectors": [
        "[id^='category-']"
      ],
      "type": "input",
      "fillMethod": "value",
      "required": false
    },
    "submitterRole": {
      "selectors": [
        "#is_associated"
      ],
      "type": "input",
      "fillMethod": "value",
      "required": false
    }
  },
  "formInfo": {
    "description": "Submit AI tool form",
    "submitSelector": ".inline-flex.items-center.px-6.py-3.border.border-transparent.text-base.font-medium.rounded-full.shadow-sm.text-white.bg-lime-600",
    "totalFields": 20,
    "notes": [
      "Uses GET method",
      "Logo upload required",
      "Multiple category checkboxes"
    ]
  },
  "metadata": {
    "generatedBy": "AI",
    "generatedAt": "2025-07-15T02:57:01.747Z",
    "version": "3.0.0",
    "aiModel": "moonshotai/Kimi-K2-Instruct"
  }
};

// 自定义处理函数 (可选)
export function handleWhattheaiTechSubmission(data, rule) {
  console.log('Processing 网站名称 form submission...');
  
  const processedData = { ...data };
  
  // 在这里添加特殊处理逻辑
  // 例如：URL格式化、字段验证、默认值设置等
  
  return processedData;
}

// 自定义元素填写函数 (可选)
export async function customFillElement(element, value, config) {
  console.log('🔧 网站名称 自定义填写函数被调用:', element, value);
  
  // 在这里添加特殊的元素填写逻辑
  // 例如：处理特殊的UI组件、异步操作等
  
  return false; // 返回 false 使用默认填写方法
}