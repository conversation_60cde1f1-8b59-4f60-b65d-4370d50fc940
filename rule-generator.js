// AI Site Submitter v3.0.0 - 规则生成器模块
// 基于AI分析结果生成和保存规则文件，支持自动下载
// 最后更新: 2025-07-15

/**
 * 规则生成器类
 */
class RuleGenerator {
    constructor() {
        this.storageKey = 'ai_generated_rules';
    }

    /**
     * 生成并保存规则
     * @param {Object} aiRule - AI 生成的规则
     * @param {string} domain - 网站域名
     * @returns {Promise<Object>} 保存结果
     */
    async generateAndSaveRule(aiRule, domain) {
        try {
            console.log('📝 开始生成规则文件...');
            
            // 验证规则
            const validatedRule = this.validateAndEnhanceRule(aiRule, domain);
            
            // 生成规则文件内容
            const ruleFileContent = this.generateRuleFileContent(validatedRule);
            
            // 保存到本地存储
            await this.saveRuleToStorage(domain, validatedRule, ruleFileContent);
            
            console.log('✅ 规则生成并保存成功');
            
            // 生成文件下载
            this.downloadRuleFile(domain, ruleFileContent);

            return {
                success: true,
                domain: domain,
                fieldsCount: Object.keys(validatedRule.fieldMappings).length,
                ruleContent: ruleFileContent,
                fileName: `${domain}.js`
            };
            
        } catch (error) {
            console.error('❌ 规则生成失败:', error);
            throw new Error(`规则生成失败: ${error.message}`);
        }
    }

    /**
     * 验证和增强规则
     * @param {Object} rule - 原始规则
     * @param {string} domain - 网站域名
     * @returns {Object} 验证后的规则
     */
    validateAndEnhanceRule(rule, domain) {
        const enhancedRule = {
            domain: domain,
            siteName: rule.siteName || this.generateSiteName(domain),
            lastUpdated: new Date().toISOString(),
            fieldMappings: {},
            formInfo: rule.formInfo || {},
            metadata: {
                generatedBy: 'AI',
                generatedAt: new Date().toISOString(),
                version: '3.0.0',
                aiModel: 'moonshotai/Kimi-K2-Instruct'
            }
        };

        // 验证和处理字段映射
        if (rule.fieldMappings && typeof rule.fieldMappings === 'object') {
            for (const [fieldName, fieldConfig] of Object.entries(rule.fieldMappings)) {
                const validatedConfig = this.validateFieldConfig(fieldConfig);
                if (validatedConfig) {
                    enhancedRule.fieldMappings[fieldName] = validatedConfig;
                }
            }
        }

        // 确保至少有一个字段映射
        if (Object.keys(enhancedRule.fieldMappings).length === 0) {
            throw new Error('规则中没有有效的字段映射');
        }

        return enhancedRule;
    }

    /**
     * 验证字段配置
     * @param {Object} config - 字段配置
     * @returns {Object|null} 验证后的配置
     */
    validateFieldConfig(config) {
        if (!config || typeof config !== 'object') {
            return null;
        }

        // 必须有选择器
        if (!config.selectors || !Array.isArray(config.selectors) || config.selectors.length === 0) {
            return null;
        }

        return {
            selectors: config.selectors.filter(selector => selector && typeof selector === 'string'),
            type: config.type || 'input',
            fillMethod: config.fillMethod || 'value',
            defaultValue: config.defaultValue || undefined,
            required: config.required || false,
            validation: config.validation || undefined
        };
    }

    /**
     * 生成网站名称
     * @param {string} domain - 域名
     * @returns {string} 网站名称
     */
    generateSiteName(domain) {
        return domain
            .replace(/^www\./, '')
            .split('.')
            .map(part => part.charAt(0).toUpperCase() + part.slice(1))
            .join(' ');
    }

    /**
     * 生成规则文件内容
     * @param {Object} rule - 规则对象
     * @returns {string} 规则文件内容
     */
    generateRuleFileContent(rule) {
        const template = `// AI Site Submitter - ${rule.siteName} 规则配置
// 自动生成于: ${new Date().toLocaleString()}
// 域名: ${rule.domain}

export const SITE_RULE = ${JSON.stringify(rule, null, 2)};

// 自定义处理函数 (可选)
export function handle${this.toCamelCase(rule.domain)}Submission(data, rule) {
  console.log('Processing ${rule.siteName} form submission...');
  
  const processedData = { ...data };
  
  // 在这里添加特殊处理逻辑
  // 例如：URL格式化、字段验证、默认值设置等
  
  return processedData;
}

// 自定义元素填写函数 (可选)
export async function customFillElement(element, value, config) {
  console.log('🔧 ${rule.siteName} 自定义填写函数被调用:', element, value);
  
  // 在这里添加特殊的元素填写逻辑
  // 例如：处理特殊的UI组件、异步操作等
  
  return false; // 返回 false 使用默认填写方法
}`;

        return template;
    }

    /**
     * 转换为驼峰命名
     * @param {string} str - 输入字符串
     * @returns {string} 驼峰命名字符串
     */
    toCamelCase(str) {
        return str
            .replace(/[^a-zA-Z0-9]/g, ' ')
            .split(' ')
            .map((word, index) => {
                if (index === 0) {
                    return word.charAt(0).toUpperCase() + word.slice(1).toLowerCase();
                }
                return word.charAt(0).toUpperCase() + word.slice(1).toLowerCase();
            })
            .join('');
    }

    /**
     * 保存规则到本地存储
     * @param {string} domain - 域名
     * @param {Object} rule - 规则对象
     * @param {string} ruleContent - 规则文件内容
     * @returns {Promise<void>}
     */
    async saveRuleToStorage(domain, rule, ruleContent) {
        try {
            // 获取现有规则
            const existingRules = await this.getStoredRules();
            
            // 添加新规则
            existingRules[domain] = {
                rule: rule,
                content: ruleContent,
                createdAt: new Date().toISOString(),
                lastUsed: null
            };
            
            // 保存到 Chrome 存储
            await chrome.storage.local.set({
                [this.storageKey]: existingRules
            });
            
            console.log(`规则已保存到本地存储: ${domain}`);
            
        } catch (error) {
            console.error('保存规则到存储失败:', error);
            throw new Error(`保存规则失败: ${error.message}`);
        }
    }

    /**
     * 获取存储的规则
     * @returns {Promise<Object>} 存储的规则
     */
    async getStoredRules() {
        try {
            const result = await chrome.storage.local.get([this.storageKey]);
            return result[this.storageKey] || {};
        } catch (error) {
            console.error('获取存储规则失败:', error);
            return {};
        }
    }

    /**
     * 检查域名是否有存储的规则
     * @param {string} domain - 域名
     * @returns {Promise<Object|null>} 规则对象或null
     */
    async getStoredRule(domain) {
        try {
            const rules = await this.getStoredRules();
            const ruleData = rules[domain];
            
            if (ruleData) {
                // 更新最后使用时间
                ruleData.lastUsed = new Date().toISOString();
                await this.saveRuleToStorage(domain, ruleData.rule, ruleData.content);
                
                return ruleData.rule;
            }
            
            return null;
        } catch (error) {
            console.error('获取存储规则失败:', error);
            return null;
        }
    }

    /**
     * 删除存储的规则
     * @param {string} domain - 域名
     * @returns {Promise<boolean>} 删除结果
     */
    async deleteStoredRule(domain) {
        try {
            const rules = await this.getStoredRules();
            
            if (rules[domain]) {
                delete rules[domain];
                await chrome.storage.local.set({
                    [this.storageKey]: rules
                });
                console.log(`已删除规则: ${domain}`);
                return true;
            }
            
            return false;
        } catch (error) {
            console.error('删除规则失败:', error);
            return false;
        }
    }

    /**
     * 获取所有存储规则的统计信息
     * @returns {Promise<Object>} 统计信息
     */
    async getRulesStats() {
        try {
            const rules = await this.getStoredRules();
            const domains = Object.keys(rules);
            
            return {
                totalRules: domains.length,
                domains: domains,
                lastGenerated: domains.length > 0 ? 
                    Math.max(...domains.map(d => new Date(rules[d].createdAt).getTime())) : null
            };
        } catch (error) {
            console.error('获取规则统计失败:', error);
            return { totalRules: 0, domains: [], lastGenerated: null };
        }
    }

    /**
     * 清理过期规则
     * @param {number} maxAge - 最大保存天数
     * @returns {Promise<number>} 清理的规则数量
     */
    async cleanupOldRules(maxAge = 30) {
        try {
            const rules = await this.getStoredRules();
            const cutoffDate = new Date(Date.now() - maxAge * 24 * 60 * 60 * 1000);
            let cleanedCount = 0;
            
            for (const [domain, ruleData] of Object.entries(rules)) {
                const createdDate = new Date(ruleData.createdAt);
                const lastUsedDate = ruleData.lastUsed ? new Date(ruleData.lastUsed) : createdDate;
                
                if (lastUsedDate < cutoffDate) {
                    delete rules[domain];
                    cleanedCount++;
                }
            }
            
            if (cleanedCount > 0) {
                await chrome.storage.local.set({
                    [this.storageKey]: rules
                });
                console.log(`清理了 ${cleanedCount} 个过期规则`);
            }
            
            return cleanedCount;
        } catch (error) {
            console.error('清理规则失败:', error);
            return 0;
        }
    }

    /**
     * 下载规则文件
     * @param {string} domain - 域名
     * @param {string} content - 文件内容
     */
    downloadRuleFile(domain, content) {
        try {
            // 创建 Blob 对象
            const blob = new Blob([content], { type: 'text/javascript' });
            const url = URL.createObjectURL(blob);

            // 创建下载链接
            const a = document.createElement('a');
            a.href = url;
            a.download = `${domain}.js`;
            a.style.display = 'none';

            // 触发下载
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);

            // 清理 URL 对象
            URL.revokeObjectURL(url);

            console.log(`📥 规则文件已下载: ${domain}.js`);

        } catch (error) {
            console.error('下载规则文件失败:', error);
        }
    }

    /**
     * 获取规则文件保存路径提示
     * @param {string} domain - 域名
     * @returns {string} 保存路径提示
     */
    getRuleSavePath(domain) {
        return `请将下载的 ${domain}.js 文件保存到：\nD:\\code\\ai-site-submitter-v2\\rules\\sites\\${domain}.js`;
    }
}

// 导出规则生成器实例
export const ruleGenerator = new RuleGenerator();
