// pickai.tools 网站规则配置
// 网站: https://www.pickai.tools/submit
// 表单技术: Modern React Components with Tailwind CSS
// 最后更新: 2025-07-07

export const SITE_RULE = {
  // 基本信息
  domain: 'pickai.tools',
  siteName: 'Pickai',
  priority: 1,
  lastUpdated: '2025-07-07',
  
  // 字段映射规则
  fieldMappings: {
    // 网站名称 -> Website Name
    siteName: {
      selectors: [
        'input[name="website"]',
        'input[placeholder="Handpicked AI"]',
        'input[id*="form-item"]:first-of-type',
        'input[aria-describedby*="form-item-description"]:first-of-type'
      ],
      method: 'value',
      validation: 'required',
      notes: '网站名称，使用website-info.js中的siteName字段'
    },
    
    // 网站URL -> Website URL
    siteUrl: {
      selectors: [
        'input[name="url"]',
        'input[placeholder="https://www.pickai.tools"]',
        'input[id*="form-item"]:last-of-type',
        'input[aria-describedby*="form-item-description"]:last-of-type'
      ],
      method: 'value',
      validation: 'required|url',
      notes: '网站URL，使用website-info.js中的siteUrl字段'
    }
  },
  
  // 自定义填写函数
  customFillElement: async function(element, value, config) {
    console.log(`Pick AI Tools自定义填写: ${element.name || element.placeholder}, 方法: ${config.method}`);
    
    switch (config.method) {
      case 'value':
        // 标准输入框处理
        element.focus();
        await new Promise(resolve => setTimeout(resolve, 100));
        
        // 清空现有内容
        element.value = '';
        await new Promise(resolve => setTimeout(resolve, 50));
        
        // 设置新值
        element.value = value;
        
        // 触发现代React事件
        element.dispatchEvent(new Event('input', { bubbles: true }));
        element.dispatchEvent(new Event('change', { bubbles: true }));
        element.dispatchEvent(new Event('blur', { bubbles: true }));
        
        // 触发React合成事件
        const reactEvent = new Event('input', { bubbles: true });
        Object.defineProperty(reactEvent, 'target', {
          writable: false,
          value: element
        });
        element.dispatchEvent(reactEvent);
        
        console.log(`✓ 填写字段: ${element.name} = "${value}"`);
        break;
        
      default:
        element.value = value;
        element.dispatchEvent(new Event('input', { bubbles: true }));
        element.dispatchEvent(new Event('change', { bubbles: true }));
    }
  },
  
  // 提交流程配置
  submitConfig: {
    submitButton: [
      'button[type="submit"]',
      'button:contains("Submit")',
      '.flex-center[type="submit"]',
      'button.bg-white'
    ],
    submitMethod: 'click',
    waitAfterFill: 2000,
    waitAfterSubmit: 5000,
    
    // 提交前检查
    preSubmitChecks: [
      {
        type: 'backlink',
        requirement: 'Add backlink to homepage',
        description: '需要在网站首页添加反向链接',
        linkCode: '<a href="https://www.pickai.tools" title="Pick AI Tools Directory">Pick AI Tools Directory</a>'
      }
    ],
    
    successIndicators: [
      '.success-message',
      '.thank-you',
      '[class*="success"]',
      'text:contains("submitted")',
      'text:contains("successfully")'
    ],
    errorIndicators: [
      '.error-message',
      '[class*="error"]',
      'text:contains("error")',
      'text:contains("failed")'
    ]
  },
  
  // 特殊处理
  specialHandling: {
    requiresLogin: false,
    hasCaptcha: false,
    hasFileUpload: false,
    isModernReact: true, // 使用现代React组件
    hasTailwindCSS: true, // 使用Tailwind CSS
    requiresBacklink: true, // 需要反向链接
    isFreeSubmission: true, // 免费提交（需要反向链接）
    
    // 表单验证规则
    formValidation: {
      requiredFields: ['siteName', 'siteUrl'],
      emailValidation: false,
      urlValidation: true,
      backlinkValidation: true
    },
    
    // 反向链接要求
    backlinkRequirement: {
      required: true,
      linkText: 'Pick AI Tools Directory',
      linkUrl: 'https://www.pickai.tools',
      linkTitle: 'Pick AI Tools Directory',
      placement: 'homepage',
      htmlCode: '<a href="https://www.pickai.tools" title="Pick AI Tools Directory">Pick AI Tools Directory</a>',
      note: '需要在网站首页添加反向链接才能免费提交'
    },
    
    // 特殊注意事项
    notes: [
      '这是现代React网站，使用Tailwind CSS',
      '表单只有2个字段，都是必填',
      '免费提交，但需要在首页添加反向链接',
      '反向链接代码：<a href="https://www.pickai.tools" title="Pick AI Tools Directory">Pick AI Tools Directory</a>',
      '字段ID包含随机字符串',
      '使用现代化的UI设计和动画效果',
      '表单背景为深色主题',
      '提交按钮为白色背景黑色文字',
      '专注于AI工具目录服务'
    ]
  }
};

// 自定义处理函数
export function handlePickAIToolsSubmission(data, _rule) {
  console.log('Processing Pick AI Tools form submission...');

  const processedData = { ...data };

  // 确保URL格式正确
  if (processedData.siteUrl && !processedData.siteUrl.startsWith('http')) {
    processedData.siteUrl = 'https://' + processedData.siteUrl;
  }

  return processedData;
}

// 反向链接检查函数
export async function checkBacklinkRequirement(siteUrl) {
  console.log('检查反向链接要求...');
  
  if (!siteUrl) {
    console.log('⚠️ 无法检查反向链接：网站URL为空');
    return false;
  }
  
  try {
    // 这里可以添加实际的反向链接检查逻辑
    // 由于跨域限制，实际检查需要后端支持
    console.log(`需要在 ${siteUrl} 首页添加反向链接:`);
    console.log('链接代码: <a href="https://www.pickai.tools" title="Pick AI Tools Directory">Pick AI Tools Directory</a>');
    console.log('链接文本: Pick AI Tools Directory');
    console.log('链接URL: https://www.pickai.tools');
    
    return true;
  } catch (error) {
    console.error('反向链接检查出错:', error);
    return false;
  }
}

// 反向链接提醒函数
export function showBacklinkReminder() {
  console.log('📋 反向链接要求提醒:');
  console.log('');
  console.log('为了免费提交您的网站，请在您的网站首页添加以下链接:');
  console.log('');
  console.log('HTML代码:');
  console.log('<a href="https://www.pickai.tools" title="Pick AI Tools Directory">Pick AI Tools Directory</a>');
  console.log('');
  console.log('链接要求:');
  console.log('- 链接文本: Pick AI Tools Directory');
  console.log('- 链接URL: https://www.pickai.tools');
  console.log('- 链接标题: Pick AI Tools Directory');
  console.log('- 放置位置: 网站首页');
  console.log('');
  console.log('添加链接后即可免费提交您的AI工具！');
}

// 表单预提交处理
export async function preSubmitHandler(data) {
  console.log('执行提交前检查...');
  
  // 显示反向链接提醒
  showBacklinkReminder();
  
  // 检查反向链接要求
  const backlinkCheck = await checkBacklinkRequirement(data.siteUrl);
  
  if (!backlinkCheck) {
    console.log('⚠️ 请确保已在网站首页添加反向链接');
  }
  
  return true; // 允许继续提交
}
