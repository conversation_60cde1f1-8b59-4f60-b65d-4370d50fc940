// SeeWhatNewAI.com 网站规则配置
// 网站: https://www.seewhatnewai.com/zh/submit
// 最后更新: 2025-07-24

export const SITE_RULE = {
  // 基本信息
  domain: 'www.seewhatnewai.com',
  siteName: 'SeeWhatNewAI',
  priority: 1,
  lastUpdated: '2025-07-24',

  // 字段映射规则
  fieldMappings: {
    // 网站名称 -> 网站名称
    siteName: {
      selectors: [
        'input[name="websiteName"]',
        'input[placeholder="SeeWhatNewAI Directory"]',
        'label:contains("网站名称") + input'
      ],
      method: 'value',
      validation: 'required',
      notes: '网站名称'
    },

    // 网站网址 -> 网站网址
    siteUrl: {
      selectors: [
        'input[name="websiteUrl"]',
        'input[placeholder="https://www.seewhatnewai.com/"]',
        'label:contains("网站网址") + input'
      ],
      method: 'value',
      validation: 'required|url',
      notes: '网站URL地址'
    },

    // 选择套餐 -> 付费/免费选项
    pricing: {
      selectors: [
        'button[value="free"]',
        'button[value="one-time"]',
        'input[value="free"]',
        'input[value="one-time"]'
      ],
      method: 'radio',
      validation: 'required',
      notes: '提交套餐选择：免费或付费'
    }
  },

  // 提交流程配置
  submitConfig: {
    submitButton: 'button[type="submit"], .bg-primary',
    submitMethod: 'manual', // 手动提交
    waitAfterFill: 0,
    waitAfterSubmit: 0,
    successIndicators: ['.success-message'],
    errorIndicators: ['.error-message']
  },

  // 特殊处理
  specialHandling: {
    requiresLogin: false,
    hasCaptcha: false,
    hasFileUpload: false,
    customScript: 'handleSeeWhatNewAiSubmission',
    formValidation: {
      requiredFields: ['siteName', 'siteUrl', 'pricing'],
      emailValidation: false,
      urlValidation: true
    },
    notes: [
      'SeeWhatNewAI AI工具目录提交平台',
      '中文界面',
      '提供付费和免费两种提交方式',
      '付费提交：2美元，24小时审核，无需反向链接',
      '免费提交：需要添加反向链接，约4周审核',
      '反向链接格式：<a href="https://www.seewhatnewai.com" target="_blank">[反向链接描述]</a>',
      '手动提交模式'
    ]
  }
};

// 自定义处理函数
export function handleSeeWhatNewAiSubmission(data) {
  console.log('Processing SeeWhatNewAI form submission...');

  const processedData = { ...data };

  // 确保URL格式正确
  if (processedData.siteUrl && !processedData.siteUrl.startsWith('http')) {
    processedData.siteUrl = 'https://' + processedData.siteUrl;
  }

  return processedData;
}

// 自定义元素填写函数
export async function customFillElement(element, value, config) {
  console.log(`SeeWhatNewAI自定义填写: ${element.tagName}, 方法: ${config.method}`);

  switch (config.method) {
    case 'value':
      // 标准输入框处理
      element.focus();
      await new Promise(resolve => setTimeout(resolve, 200));

      element.value = value;
      element.dispatchEvent(new Event('input', { bubbles: true }));
      element.dispatchEvent(new Event('change', { bubbles: true }));
      element.dispatchEvent(new Event('blur', { bubbles: true }));

      console.log(`✓ 填写字段: ${element.name} = "${value}"`);
      return true;

    case 'radio':
      // 单选按钮处理 - 默认选择免费选项
      const freeRadio = document.querySelector('button[value="free"]') ||
                       document.querySelector('input[value="free"]');

      if (freeRadio) {
        freeRadio.click();
        console.log(`✓ 选择套餐: 免费提交`);
        return true;
      }
      break;

    default:
      element.value = value;
      element.dispatchEvent(new Event('input', { bubbles: true }));
      return true;
  }

  return false;
}