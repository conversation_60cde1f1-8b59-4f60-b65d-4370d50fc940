// Business-Software.com 网站规则配置
// 网站: https://www.business-software.com/add-your-product/
// 最后更新: 2025-07-24

export const SITE_RULE = {
  domain: 'www.business-software.com',
  siteName: 'Business Software',
  priority: 1,
  lastUpdated: '2025-07-24',

  fieldMappings: {
    fullName: {
      selectors: [
        '#fscf_name3',
        'input[name="full_name"]',
        'label:contains("Name:") + div input'
      ],
      method: 'value',
      validation: 'required',
      notes: '联系人姓名'
    },

    contactEmail: {
      selectors: [
        '#fscf_email3',
        'input[name="email"]',
        'label:contains("Email:") + div input'
      ],
      method: 'value',
      validation: 'required|email',
      notes: '联系邮箱'
    },

    companyName: {
      selectors: [
        '#fscf_field3_4',
        'input[name="new-field"]',
        'label:contains("Company Name:") + div input'
      ],
      method: 'value',
      validation: 'required',
      notes: '公司名称'
    },

    siteName: {
      selectors: [
        '#fscf_field3_5',
        'input[name="product-name"]',
        'label:contains("Product Name:") + div input'
      ],
      method: 'value',
      validation: 'required',
      notes: '产品名称'
    },

    phone: {
      selectors: [
        '#fscf_field3_6',
        'input[name="phone"]',
        'label:contains("Phone:") + div input'
      ],
      method: 'value',
      validation: 'optional',
      notes: '联系电话'
    },

    submitterRole: {
      selectors: [
        '#fscf_field3_7',
        'input[name="job-title"]',
        'label:contains("Job Title:") + div input'
      ],
      method: 'value',
      validation: 'optional',
      notes: '提交者角色，在公司中的职位'
    },

    siteDescription: {
      selectors: [
        '#fscf_field3_3',
        'textarea[name="message"]',
        'label:contains("Message:") + div textarea'
      ],
      method: 'value',
      validation: 'optional',
      notes: '产品描述信息'
    },

    captcha: {
      selectors: [
        '#fscf_captcha_code3',
        'input[name="captcha_code"]',
        'label:contains("CAPTCHA Code:") + div input'
      ],
      method: 'value',
      validation: 'required',
      notes: '验证码，需要手动输入'
    }
  },

  submitConfig: {
    submitButton: '#fscf_submit3, input[type="submit"]',
    submitMethod: 'click',
    successIndicators: ['.success-message'],
    errorIndicators: ['.error-message']
  },

  specialHandling: {
    requiresLogin: false,
    hasCaptcha: true,
    hasFileUpload: false,
    customScript: 'handleBusinessSoftwareSubmission',
    formValidation: {
      requiredFields: ['fullName', 'contactEmail', 'companyName', 'siteName', 'captcha'],
      emailValidation: true,
      urlValidation: false
    },
    notes: [
      '使用Fast Secure Contact Form插件',
      '有CAPTCHA验证码',
      '面向400万+技术买家',
      '免费产品提交平台',
      '需要手动输入验证码',
      '2个工作日内联系'
    ]
  }
};

export function handleBusinessSoftwareSubmission(data, rule) {
  const processedData = { ...data };

  // 提醒用户需要手动输入验证码
  if (processedData.captcha === undefined) {
    console.warn('需要手动输入CAPTCHA验证码');
  }

  return processedData;
}

export async function customFillElement(element, value, config) {
  // 验证码字段需要特殊处理
  if (element.name === 'captcha_code') {
    console.warn('CAPTCHA验证码需要手动输入，无法自动填写');
    return false;
  }

  if (element.tagName === 'INPUT' || element.tagName === 'TEXTAREA') {
    element.value = value;
    element.dispatchEvent(new Event('input', { bubbles: true }));
    element.dispatchEvent(new Event('change', { bubbles: true }));
    return true;
  }

  return false;
}