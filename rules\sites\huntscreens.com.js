// HuntScreens.com 网站规则配置
// 网站: https://huntscreens.com/en/submit
// 最后更新: 2025-07-24

export const SITE_RULE = {
  // 基本信息
  domain: 'huntscreens.com',
  siteName: 'Hunt Screens',
  priority: 1,
  lastUpdated: '2025-07-24',

  // 字段映射规则
  fieldMappings: {
    // 产品URL -> Product URL
    siteUrl: {
      selectors: [
        '#url',
        'input[type="url"]',
        'input[placeholder="https://example.com"]'
      ],
      method: 'value',
      validation: 'required|url',
      notes: '产品URL地址'
    },

    // 高质量创新产品 -> High-quality & innovative products
    highQuality: {
      selectors: [
        '#highQuality',
        'button[id="highQuality"]'
      ],
      method: 'checkbox',
      validation: 'required',
      notes: '高质量和创新产品确认'
    },

    // 功能完整可用 -> Fully functional and ready to use
    functional: {
      selectors: [
        '#functional',
        'button[id="functional"]'
      ],
      method: 'checkbox',
      validation: 'required',
      notes: '功能完整且可用确认'
    },

    // 独特价值主张 -> Unique value proposition
    unique: {
      selectors: [
        '#unique',
        'button[id="unique"]'
      ],
      method: 'checkbox',
      validation: 'required',
      notes: '独特价值主张确认'
    },

    // 良好设计用户体验 -> Good design and user experience
    goodDesign: {
      selectors: [
        '#goodDesign',
        'button[id="goodDesign"]'
      ],
      method: 'checkbox',
      validation: 'required',
      notes: '良好设计和用户体验确认'
    },

    // 全球用户可访问 -> Accessible to global audience
    globalAccess: {
      selectors: [
        '#globalAccess',
        'button[id="globalAccess"]'
      ],
      method: 'checkbox',
      validation: 'required',
      notes: '全球用户可访问确认'
    }
  },
  // 提交流程配置
  submitConfig: {
    submitButton: 'button[type="submit"], .bg-primary',
    submitMethod: 'manual', // 手动提交
    waitAfterFill: 0,
    waitAfterSubmit: 0,
    successIndicators: ['.success-message'],
    errorIndicators: ['.error-message']
  },

  // 特殊处理
  specialHandling: {
    requiresLogin: false,
    hasCaptcha: false,
    hasFileUpload: false,
    customScript: 'handleHuntScreensSubmission',
    formValidation: {
      requiredFields: ['siteUrl', 'highQuality', 'functional', 'unique', 'goodDesign', 'globalAccess'],
      emailValidation: false,
      urlValidation: true
    },
    notes: [
      'Hunt Screens 产品展示平台',
      '专注于高质量产品的策展',
      '简化的提交流程，只需URL和确认标准',
      '所有标准必须满足才能提交',
      '48小时内审核',
      '通过邮件通知审核结果',
      '现代化的Tailwind CSS界面',
      '手动提交模式'
    ]
  }
};

// 自定义处理函数
export function handleHuntScreensSubmission(data) {
  console.log('Processing Hunt Screens form submission...');

  const processedData = { ...data };

  // 确保URL格式正确
  if (processedData.siteUrl && !processedData.siteUrl.startsWith('http')) {
    processedData.siteUrl = 'https://' + processedData.siteUrl;
  }

  return processedData;
}

// 自定义元素填写函数
export async function customFillElement(element, value, config) {
  console.log(`Hunt Screens自定义填写: ${element.tagName}, 方法: ${config.method}`);

  switch (config.method) {
    case 'value':
      // 标准输入框处理
      element.focus();
      await new Promise(resolve => setTimeout(resolve, 200));

      element.value = value;
      element.dispatchEvent(new Event('input', { bubbles: true }));
      element.dispatchEvent(new Event('change', { bubbles: true }));
      element.dispatchEvent(new Event('blur', { bubbles: true }));

      console.log(`✓ 填写字段: ${element.id} = "${value}"`);
      return true;

    case 'checkbox':
      // 复选框处理 - 这里是特殊的按钮式复选框
      if (element.tagName === 'BUTTON' && element.getAttribute('role') === 'checkbox') {
        // 点击按钮来切换状态
        element.click();

        // 等待状态更新
        await new Promise(resolve => setTimeout(resolve, 100));

        console.log(`✓ 复选框设置: ${element.id} = checked`);
        return true;
      }
      break;

    default:
      element.value = value;
      element.dispatchEvent(new Event('input', { bubbles: true }));
      return true;
  }

  return false;
}