// GPTBot.io 网站规则配置
// 网站: https://gptbot.io/submit-ai-tool
// 最后更新: 2025-07-24

export const SITE_RULE = {
  domain: 'gptbot.io',
  siteName: 'GPTBot',
  priority: 1,
  lastUpdated: '2025-07-24',

  fieldMappings: {
    siteName: {
      selectors: [
        '#websiteName',
        'input[placeholder="Tool Name"]',
        'label:contains("Website Name") + input'
      ],
      method: 'value',
      validation: 'required',
      notes: '工具名称'
    },

    siteUrl: {
      selectors: [
        '#websiteUrl',
        'input[type="url"]',
        'input[placeholder="https://example.com"]'
      ],
      method: 'value',
      validation: 'required|url',
      notes: '网站URL'
    },

    contactEmail: {
      selectors: [
        '#email',
        'input[type="email"]',
        'input[placeholder="<EMAIL>"]'
      ],
      method: 'value',
      validation: 'required|email',
      notes: '联系邮箱'
    },

    siteDescription: {
      selectors: [
        '#description',
        'textarea[placeholder*="short description"]',
        'label:contains("Tool Description") + textarea'
      ],
      method: 'value',
      validation: 'required',
      notes: '工具描述'
    },

    pricing: {
      selectors: [
        'input[type="radio"][value="Free"]',
        'button[role="radio"][value="Free"]',
        '#Free'
      ],
      method: 'radio',
      validation: 'required',
      defaultValue: 'Free',
      notes: '定价类型，默认选择Free'
    },

    detailedIntro: {
      selectors: [
        '#additionalInfo',
        'textarea[placeholder*="Additional Information"]',
        'label:contains("Additional Information") + textarea'
      ],
      method: 'value',
      validation: 'optional',
      notes: '附加信息，可选'
    }
  },

  submitConfig: {
    submitButton: 'button[type="submit"], .bg-brand',
    submitMethod: 'click',
    successIndicators: ['.success-message'],
    errorIndicators: ['.error-message']
  },

  specialHandling: {
    requiresLogin: false,
    hasCaptcha: false,
    hasFileUpload: false,
    customScript: 'handleGPTBotSubmission',
    formValidation: {
      requiredFields: ['siteName', 'siteUrl', 'contactEmail', 'siteDescription', 'pricing'],
      emailValidation: true,
      urlValidation: true
    },
    notes: [
      '有严格的审核标准',
      '不接受NSFW工具',
      '需要原创描述',
      '要求专业域名',
      '需要隐私政策和服务条款',
      '使用现代化UI设计'
    ]
  }
};

export function handleGPTBotSubmission(data, rule) {
  const processedData = { ...data };

  if (processedData.siteUrl && !processedData.siteUrl.startsWith('http')) {
    processedData.siteUrl = 'https://' + processedData.siteUrl;
  }

  // 自动选择Free定价选项
  const freeRadio = document.querySelector('button[role="radio"][value="Free"]');
  if (freeRadio) {
    freeRadio.click();
  }

  return processedData;
}

export async function customFillElement(element, value, config) {
  // 处理自定义单选按钮
  if (element.getAttribute('role') === 'radio') {
    element.click();
    return true;
  }

  if (element.tagName === 'INPUT' || element.tagName === 'TEXTAREA') {
    element.value = value;
    element.dispatchEvent(new Event('input', { bubbles: true }));
    element.dispatchEvent(new Event('change', { bubbles: true }));
    return true;
  }

  return false;
}