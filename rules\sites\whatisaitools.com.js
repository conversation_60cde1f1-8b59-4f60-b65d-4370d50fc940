// WhatIsAiTools.com 网站规则配置
// 网站: https://whatisaitools.com/submit
// 最后更新: 2025-07-24

export const SITE_RULE = {
  // 基本信息
  domain: 'whatisaitools.com',
  siteName: 'What Is AI Tools',
  priority: 1,
  lastUpdated: '2025-07-24',
  
  // 字段映射规则
  fieldMappings: {
    // 网站名称 -> Website Name
    siteName: {
      selectors: [
        'textarea[placeholder="What Is Ai Tools"]',
        'textarea[placeholder*="What Is Ai Tools"]',
        'textarea:first-of-type'
      ],
      method: 'value',
      validation: 'required',
      notes: '网站名称'
    },

    // 网站URL -> Website URL
    siteUrl: {
      selectors: [
        'textarea[placeholder="https://whatisaitools.com/"]',
        'textarea[placeholder*="https://"]',
        'textarea:nth-of-type(2)'
      ],
      method: 'value',
      validation: 'required|url',
      notes: '网站URL地址'
    }
  },

  // 提交流程配置
  submitConfig: {
    submitButton: 'button.button-bg, button[class*="bg-indigo-600"]',
    submitMethod: 'manual', // 手动提交
    waitAfterFill: 0,
    waitAfterSubmit: 0,
    successIndicators: ['.success-message'],
    errorIndicators: ['.error-message']
  },

  // 特殊处理
  specialHandling: {
    requiresLogin: false,
    hasCaptcha: false,
    hasFileUpload: false,
    customScript: 'handleWhatIsAiToolsSubmission',
    formValidation: {
      requiredFields: ['siteName', 'siteUrl'],
      emailValidation: false,
      urlValidation: true
    },
    notes: [
      'What Is AI Tools 网站提交平台',
      '极简表单，只有网站名称和URL两个字段',
      '使用textarea而非input元素',
      'Tailwind CSS样式',
      '需要在网站首页添加反向链接才能成功提交',
      '手动提交模式'
    ]
  }
};

// 自定义处理函数
export function handleWhatIsAiToolsSubmission(data) {
  console.log('Processing What Is AI Tools form submission...');
  
  const processedData = { ...data };

  // 确保URL格式正确
  if (processedData.siteUrl && !processedData.siteUrl.startsWith('http')) {
    processedData.siteUrl = 'https://' + processedData.siteUrl;
  }

  return processedData;
}

// 自定义元素填写函数
export async function customFillElement(element, value, config) {
  console.log(`WhatIsAiTools自定义填写: ${element.tagName}, 方法: ${config.method}`);

  switch (config.method) {
    case 'value':
      // textarea元素处理
      element.focus();
      await new Promise(resolve => setTimeout(resolve, 200));

      element.value = value;
      element.dispatchEvent(new Event('input', { bubbles: true }));
      element.dispatchEvent(new Event('change', { bubbles: true }));
      element.dispatchEvent(new Event('blur', { bubbles: true }));

      console.log(`✓ 填写字段: ${element.placeholder} = "${value}"`);
      return true;

    default:
      element.value = value;
      element.dispatchEvent(new Event('input', { bubbles: true }));
      return true;
  }

  return false;
}
