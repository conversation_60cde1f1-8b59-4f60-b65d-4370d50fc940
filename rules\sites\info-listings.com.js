// info-listings.com 网站规则配置
// 网站: https://info-listings.com/submit.php
// 表单技术: PHP Form with CAPTCHA
// 最后更新: 2025-07-08

export const SITE_RULE = {
  // 基本信息
  domain: 'info-listings.com',
  siteName: 'Info Listings',
  priority: 1,
  lastUpdated: '2025-07-08',
  
  // 字段映射规则
  fieldMappings: {
    // 定价选项 -> Pricing
    linkType: {
      selectors: [
        'input[name="LINK_TYPE"][value="normal"]',
        'input[type="radio"][value="normal"]',
        'input[value="normal"]'
      ],
      method: 'radio',
      validation: 'required',
      defaultValue: 'normal',
      availableOptions: ['featured', 'normal'],
      notes: '定价选项，默认选择normal (Regular links，免费，3-6个月审核)'
    },
    
    // 标题 -> Title
    siteName: {
      selectors: [
        'input[name="TITLE"]',
        'input.text:first-of-type',
        'input[maxlength="100"]',
        'input[size="40"]:first-of-type'
      ],
      method: 'value',
      validation: 'required',
      notes: '网站标题，使用website-info.js中的siteName字段'
    },
    
    // URL -> URL
    siteUrl: {
      selectors: [
        'input[name="URL"]',
        'input[maxlength="255"]',
        'input.text:nth-of-type(2)',
        'input[size="40"]:nth-of-type(2)'
      ],
      method: 'value',
      validation: 'required|url',
      notes: '网站URL，使用website-info.js中的siteUrl字段，必填'
    },
    
    // 描述 -> Description
    detailedIntro: {
      selectors: [
        'textarea[name="DESCRIPTION"]',
        'textarea.text:first-of-type',
        'textarea[rows="3"]',
        'textarea[cols="37"]'
      ],
      method: 'value',
      validation: 'optional',
      maxLength: 500,
      notes: '网站详细描述，使用website-info.js中的detailedIntro字段，限制500字符'
    },
    
    // 您的姓名 -> Your Name
    fullName: {
      selectors: [
        'input[name="OWNER_NAME"]',
        'input[maxlength="50"]',
        'input.text:nth-of-type(3)',
        'input[size="40"]:nth-of-type(3)'
      ],
      method: 'value',
      validation: 'required',
      notes: '您的姓名，使用website-info.js中的fullName字段'
    },
    
    // 您的邮箱 -> Your Email
    contactEmail: {
      selectors: [
        'input[name="OWNER_EMAIL"]',
        'input.text:nth-of-type(4)',
        'input[size="40"]:nth-of-type(4)',
        'input[maxlength="255"]:nth-of-type(2)'
      ],
      method: 'value',
      validation: 'required|email',
      notes: '您的邮箱，使用website-info.js中的contactEmail字段'
    }
  },
  
  // 自定义填写函数
  customFillElement: async function(element, value, config) {
    console.log(`Info Listings自定义填写: ${element.name || element.type}, 方法: ${config.method}`);
    
    switch (config.method) {
      case 'value':
        // 标准输入框和文本域处理
        element.focus();
        await new Promise(resolve => setTimeout(resolve, 100));
        
        // 清空现有内容
        element.value = '';
        await new Promise(resolve => setTimeout(resolve, 50));
        
        // 处理字符限制
        let finalValue = value;
        if (config.maxLength && finalValue.length > config.maxLength) {
          finalValue = finalValue.substring(0, config.maxLength);
          console.log(`⚠️ 内容被截断到${config.maxLength}字符: ${finalValue}`);
        }
        
        // 设置新值
        element.value = finalValue;
        
        // 触发事件
        element.dispatchEvent(new Event('input', { bubbles: true }));
        element.dispatchEvent(new Event('change', { bubbles: true }));
        element.dispatchEvent(new Event('blur', { bubbles: true }));
        
        console.log(`✓ 填写字段: ${element.name} = "${finalValue.substring(0, 50)}..."`);
        break;
        
      case 'radio':
        // 单选按钮处理
        console.log(`处理定价选项，目标值: ${config.defaultValue}`);
        
        // 查找所有同名单选按钮
        const radioButtons = document.querySelectorAll('input[name="LINK_TYPE"]');
        
        // 先取消所有选择
        radioButtons.forEach(rb => {
          rb.checked = false;
        });
        
        // 选择目标选项
        const targetRadio = Array.from(radioButtons).find(rb => 
          rb.value === config.defaultValue
        );
        
        if (targetRadio) {
          targetRadio.checked = true;
          targetRadio.dispatchEvent(new Event('change', { bubbles: true }));
          console.log(`✓ 选择定价选项: Regular links (${config.defaultValue})`);
        } else {
          console.log(`⚠️ 未找到定价选项: ${config.defaultValue}`);
        }
        break;
        
      default:
        element.value = value;
        element.dispatchEvent(new Event('input', { bubbles: true }));
        element.dispatchEvent(new Event('change', { bubbles: true }));
    }
  },
  
  // 提交流程配置
  submitConfig: {
    submitButton: [
      'button[type="submit"]',
      'input[type="submit"]',
      'button:contains("Submit")',
      'input[value*="Submit"]'
    ],
    submitMethod: 'click',
    waitAfterFill: 2000,
    waitAfterSubmit: 5000,
    successIndicators: [
      'text:contains("submitted")',
      'text:contains("thank you")',
      'text:contains("success")',
      'text:contains("approved")'
    ],
    errorIndicators: [
      'text:contains("error")',
      'text:contains("required")',
      'text:contains("invalid")',
      'text:contains("captcha")'
    ]
  },
  
  // 特殊处理
  specialHandling: {
    requiresLogin: false,
    hasCaptcha: true, // 可能有验证码
    hasFileUpload: false,
    isPHPForm: true, // PHP表单
    isInfoDirectory: true, // 信息目录
    hasLifetimeOption: true, // 有终身付费选项
    hasLongReviewTime: true, // 审核时间较长
    isSimplifiedForm: true, // 简化表单
    
    // 表单验证规则
    formValidation: {
      requiredFields: ['linkType', 'siteName', 'siteUrl', 'fullName', 'contactEmail'],
      optionalFields: ['detailedIntro'],
      emailValidation: true,
      urlValidation: true,
      characterLimits: {
        detailedIntro: 500
      },
      radioGroups: ['linkType']
    },
    
    // 特殊注意事项
    notes: [
      '这是Info Listings的网站提交表单',
      '表单包含6个字段：5个必填，1个可选',
      '信息列表目录网站',
      '可能有验证码保护，需要手动处理',
      '默认选择Regular links（免费，3-6个月审核）',
      '有付费选项：Featured links $29.99终身（最贵的终身选项）',
      '简化表单，没有META字段',
      '描述限制500字符',
      '使用实际字段名：LINK_TYPE, TITLE, URL, DESCRIPTION, OWNER_NAME, OWNER_EMAIL',
      '审核时间较长（3-6个月）',
      '专注于信息和列表类网站收录',
      '终身付费选项，一次性费用'
    ]
  }
};

// 自定义处理函数
export function handleInfoListingsSubmission(data, _rule) {
  console.log('Processing Info Listings form submission...');

  const processedData = { ...data };

  // 确保URL格式正确
  if (processedData.siteUrl && !processedData.siteUrl.startsWith('http')) {
    processedData.siteUrl = 'https://' + processedData.siteUrl;
  }

  // 处理字符限制
  if (processedData.detailedIntro && processedData.detailedIntro.length > 500) {
    processedData.detailedIntro = processedData.detailedIntro.substring(0, 500);
  }

  // 设置默认值
  processedData.linkType = 'normal'; // Regular links

  return processedData;
}

// Info Listings信息提醒
export function showInfoListingsInfo() {
  console.log('📋 Info Listings 信息:');
  console.log('');
  console.log('平台特色:');
  console.log('- 信息列表目录网站');
  console.log('- 简化的表单结构');
  console.log('- 终身付费选项');
  console.log('- 专注于信息类网站');
  console.log('');
  console.log('提交选项 (2种):');
  console.log('1. Featured links - $29.99终身');
  console.log('   - 一次性付费');
  console.log('   - 终身有效');
  console.log('   - 最贵的终身选项');
  console.log('');
  console.log('2. Regular links - 免费 ✅ 默认选择');
  console.log('   - 3-6个月审核');
  console.log('   - 审核时间较长');
  console.log('   - 完全免费');
  console.log('');
  console.log('表单特点:');
  console.log('- 简化表单，只有6个字段');
  console.log('- 没有META字段');
  console.log('- 描述限制500字符');
  console.log('- URL字段必填');
  console.log('');
  console.log('Info Listings - 专业的信息列表目录！');
}

// 终身付费选项分析
export function showLifetimeOptionAnalysis() {
  console.log('♾️ 终身付费选项分析:');
  console.log('');
  console.log('Info Listings终身选项:');
  console.log('- 价格: $29.99');
  console.log('- 有效期: 终身');
  console.log('- 性质: 一次性付费');
  console.log('');
  console.log('与年费对比:');
  console.log('- Australia Web: $12.95/年');
  console.log('- Free PR Web: $9.99/年');
  console.log('- Free Internet: $6.97/年');
  console.log('- All States USA: $6.79');
  console.log('- Info Listings: $29.99终身 ✅');
  console.log('');
  console.log('投资回报分析:');
  console.log('- 2-3年后开始盈利');
  console.log('- 长期使用更划算');
  console.log('- 适合长期运营的网站');
  console.log('- 一次投资，终身受益');
}

// 审核时间对比
export function showReviewTimeAnalysis() {
  console.log('⏰ 审核时间分析:');
  console.log('');
  console.log('免费选项审核时间排序:');
  console.log('1. Free Internet: 2-3个月');
  console.log('2. Australia Web: 3-4个月');
  console.log('3. Free PR Web: 3-4个月');
  console.log('4. Info Listings: 3-6个月 ⚠️ 最长');
  console.log('5. All States USA: 8-10周');
  console.log('');
  console.log('Info Listings特点:');
  console.log('- 审核时间最长（3-6个月）');
  console.log('- 变动范围较大');
  console.log('- 需要更多耐心等待');
  console.log('- 适合不急于上线的网站');
  console.log('');
  console.log('建议:');
  console.log('- 如果急需上线，考虑其他目录');
  console.log('- 如果不急，可以等待免费审核');
  console.log('- 考虑终身付费选项');
}

// 简化表单优势
export function showSimplifiedFormAdvantages() {
  console.log('✨ 简化表单优势:');
  console.log('');
  console.log('Info Listings表单特点:');
  console.log('- 只有6个字段');
  console.log('- 没有META字段');
  console.log('- 没有分类选择');
  console.log('- 填写速度快');
  console.log('');
  console.log('与其他目录对比:');
  console.log('- Australia Web: 7个字段');
  console.log('- All States USA: 8个字段');
  console.log('- Free PR Web: 8个字段');
  console.log('- Free Internet: 8个字段');
  console.log('- Info Listings: 6个字段 ✅ 最简单');
  console.log('');
  console.log('优势:');
  console.log('- 填写时间最短');
  console.log('- 降低用户流失率');
  console.log('- 提高完成率');
  console.log('- 用户体验更好');
}

// 表单验证
export function validateInfoListingsForm() {
  console.log('验证Info Listings表单...');

  const requiredFields = [
    { selector: 'input[name="TITLE"]', label: '网站标题' },
    { selector: 'input[name="URL"]', label: '网站URL' },
    { selector: 'input[name="OWNER_NAME"]', label: '您的姓名' },
    { selector: 'input[name="OWNER_EMAIL"]', label: '您的邮箱' }
  ];

  let isValid = true;

  requiredFields.forEach(field => {
    const element = document.querySelector(field.selector);
    if (!element || !element.value.trim()) {
      console.log(`⚠️ 必填字段为空: ${field.label}`);
      isValid = false;
    }
  });

  // 检查定价选项
  const radioButtons = document.querySelectorAll('input[name="LINK_TYPE"]:checked');
  if (radioButtons.length === 0) {
    console.log('⚠️ 请选择定价选项');
    isValid = false;
  }

  // 检查URL格式
  const urlField = document.querySelector('input[name="URL"]');
  if (urlField && urlField.value && !urlField.value.match(/^https?:\/\//)) {
    console.log('⚠️ URL格式可能不正确，建议包含http://或https://');
  }

  // 检查字符限制
  const description = document.querySelector('textarea[name="DESCRIPTION"]');
  if (description && description.value.length > 500) {
    console.log('⚠️ 描述超过500字符限制');
  }

  if (isValid) {
    console.log('✓ 表单验证通过');
  }

  return isValid;
}

// 信息类网站类型
export function showInfoWebsiteTypes() {
  console.log('📊 适合Info Listings的网站类型:');
  console.log('');
  console.log('信息门户:');
  console.log('- 行业信息网站');
  console.log('- 新闻资讯平台');
  console.log('- 数据统计网站');
  console.log('- 研究报告平台');
  console.log('');
  console.log('列表服务:');
  console.log('- 企业名录');
  console.log('- 产品目录');
  console.log('- 服务列表');
  console.log('- 资源汇总');
  console.log('');
  console.log('参考资料:');
  console.log('- 百科全书');
  console.log('- 技术文档');
  console.log('- 操作指南');
  console.log('- 常见问题');
  console.log('');
  console.log('数据库:');
  console.log('- 联系人数据库');
  console.log('- 产品数据库');
  console.log('- 价格比较');
  console.log('- 评价系统');
}

// 目录网站投资建议
export function showDirectoryInvestmentAdvice() {
  console.log('💡 目录网站投资建议:');
  console.log('');
  console.log('短期需求 (1年内):');
  console.log('- 选择年费选项');
  console.log('- All States USA: $6.79');
  console.log('- Free Internet: $6.97');
  console.log('- 成本较低');
  console.log('');
  console.log('长期规划 (3年以上):');
  console.log('- 考虑终身选项');
  console.log('- Info Listings: $29.99终身');
  console.log('- 长期更划算');
  console.log('- 一次性投资');
  console.log('');
  console.log('免费策略:');
  console.log('- 选择免费选项');
  console.log('- 耐心等待审核');
  console.log('- 适合预算有限');
  console.log('- 时间成本较高');
  console.log('');
  console.log('混合策略:');
  console.log('- 多个目录同时提交');
  console.log('- 分散投资风险');
  console.log('- 提高收录概率');
  console.log('- 最大化曝光度');
}
