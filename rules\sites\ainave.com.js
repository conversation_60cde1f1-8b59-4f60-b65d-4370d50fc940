// AiNave.com 网站规则配置
// 网站: https://www.ainave.com/submit/tools
// 最后更新: 2025-07-09

export const SITE_RULE = {
  // 基本信息
  domain: 'ainave.com',
  siteName: 'AiNave',
  priority: 1,
  lastUpdated: '2025-07-09',
  
  // 字段映射规则
  fieldMappings: {
    // 标题 -> Title
    siteName: {
      selectors: [
        'input[name="title"]',
        'input[placeholder="Enter the title of the tool"]',
        'input[aria-label="Title"]',
        'input[data-slot="input"][name="title"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '工具标题'
    },
    
    // 标语 -> Tagline
    siteDescription: {
      selectors: [
        'input[name="description"]',
        'input[placeholder="Enter the description of the tool"]',
        'input[aria-label="Tagline"]',
        'input[data-slot="input"][name="description"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '工具描述标语'
    },
    
    // 网站URL -> URL
    siteUrl: {
      selectors: [
        'input[name="url"]',
        'input[placeholder="https://website.com/"]',
        'input[aria-label="URL"]',
        'input[data-slot="input"][name="url"]'
      ],
      method: 'value',
      validation: 'required|url',
      notes: '网站URL地址'
    },
    
    // 内容 -> Content (富文本编辑器)
    detailedIntro: {
      selectors: [
        '.tiptap.ProseMirror[contenteditable="true"]',
        'div[contenteditable="true"][class*="tiptap"]',
        'div[data-placeholder="Enter the content of the tool"]',
        '.ProseMirror[contenteditable="true"]'
      ],
      method: 'contenteditable',
      validation: 'required',
      notes: '详细内容，使用富文本编辑器'
    }
  },
  
  // 提交流程配置
  submitConfig: {
    submitButton: 'button[type="submit"], .submit-btn, button:contains("Submit")',
    submitMethod: 'click',
    waitAfterFill: 2000, // 填写后等待2秒
    waitAfterSubmit: 3000, // 提交后等待3秒
    successIndicators: [
      '.success-message',
      '.alert-success',
      '.notification-success',
      '[class*="success"]'
    ],
    errorIndicators: [
      '.error-message',
      '.alert-error',
      '.alert-danger',
      '[class*="error"]'
    ]
  },
  
  // 特殊处理
  specialHandling: {
    requiresLogin: false,
    hasCaptcha: false,
    hasFileUpload: false,
    
    // 表单验证规则
    formValidation: {
      requiredFields: ['siteName', 'siteDescription', 'siteUrl', 'detailedIntro'],
      emailValidation: false,
      urlValidation: true
    },
    
    // 特殊注意事项
    notes: [
      '表单包含4个字段：标题、标语、URL、内容',
      '所有字段都是必填的',
      '使用React组件和复杂的UI库',
      '富文本编辑器使用TipTap/ProseMirror',
      '字段有动态ID和复杂的CSS类',
      '内容编辑器有字符限制（0/10,000）'
    ]
  }
};

// 自定义处理函数
export function handleAiNaveSubmission(data, rule) {
  console.log('Processing AiNave.com submission...');
  
  // 特殊处理逻辑
  const processedData = { ...data };
  
  // 确保URL格式正确
  if (processedData.siteUrl && !processedData.siteUrl.startsWith('http')) {
    processedData.siteUrl = 'https://' + processedData.siteUrl;
  }
  
  // 处理详细内容，确保不超过10,000字符限制
  if (processedData.detailedIntro && processedData.detailedIntro.length > 10000) {
    processedData.detailedIntro = processedData.detailedIntro.substring(0, 9997) + '...';
  }
  
  return processedData;
}

// 自定义元素填写函数，专门处理富文本编辑器
export async function customFillElement(element, value, config) {
  console.log('🔧 AiNave自定义填写函数被调用:', element, value);
  
  // 处理富文本编辑器 (TipTap/ProseMirror)
  if (element.classList.contains('tiptap') || element.classList.contains('ProseMirror')) {
    try {
      // 方法1: 直接设置innerHTML
      element.innerHTML = `<p>${value}</p>`;
      
      // 触发输入事件
      element.dispatchEvent(new Event('input', { bubbles: true }));
      element.dispatchEvent(new Event('change', { bubbles: true }));
      
      // 如果有编辑器实例，尝试使用API
      if (element.__editor || element.editor) {
        const editor = element.__editor || element.editor;
        if (editor.commands) {
          editor.commands.setContent(value);
        }
      }
      
      console.log('✅ 使用富文本编辑器填写:', value);
      return true;
    } catch (error) {
      console.warn('富文本编辑器填写失败:', error);
    }
  }
  
  // 处理React组件的input字段
  if (element.hasAttribute('data-slot') && element.getAttribute('data-slot') === 'input') {
    try {
      // React组件需要特殊处理
      const nativeInputValueSetter = Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype, 'value').set;
      nativeInputValueSetter.call(element, value);
      
      // 触发React事件
      element.dispatchEvent(new Event('input', { bubbles: true }));
      element.dispatchEvent(new Event('change', { bubbles: true }));
      
      console.log('✅ 使用React组件填写:', value);
      return true;
    } catch (error) {
      console.warn('React组件填写失败:', error);
    }
  }
  
  // 默认处理
  return false;
}
