// DropYourAI.com 网站规则配置
// 网站: https://www.dropyourai.com/submit-tool
// 最后更新: 2025-07-06

export const SITE_RULE = {
  // 基本信息
  domain: 'dropyourai.com',
  siteName: 'DropYourAI',
  priority: 1,
  lastUpdated: '2025-07-06',
  
  // 字段映射规则
  fieldMappings: {
    // 项目名称 -> Project Name
    siteName: {
      selectors: [
        'input[placeholder*="Project name here"]',
        'input[aria-label*="Project Name"]',
        'input[data-testid*="project-name"]',
        'input[name*="project"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '工具项目名称'
    },
    
    // 邮箱 -> E-mail
    contactEmail: {
      selectors: [
        'input[placeholder*="Your e-mail"]',
        'input[type="email"]',
        'input[aria-label*="E-mail"]',
        'input[name*="email"]'
      ],
      method: 'value',
      validation: 'required|email',
      notes: '联系邮箱地址'
    },
    
    // 电话 -> Phone (可选)
    phone: {
      selectors: [
        'input[placeholder*="Your phone number"]',
        'input[type="tel"]',
        'input[aria-label*="Phone"]',
        'input[name*="phone"]'
      ],
      method: 'value',
      validation: 'optional',
      notes: '联系电话号码'
    },
    
    // 网站URL -> Website
    siteUrl: {
      selectors: [
        'input[placeholder*="Your website\'s full URL"]',
        'input[placeholder*="website"]',
        'input[type="url"]',
        'input[aria-label*="Website"]'
      ],
      method: 'value',
      validation: 'required|url',
      notes: '工具网站完整URL'
    },
    
    // 定价类型 -> Pricing type (单选按钮)
    pricing: {
      selectors: [
        'input[name*="comp-lkoezacg"][value="Free"]',
        'input[type="radio"][value="Free"]',
        '[data-testid="radioGroup"] input[value="Free"]'
      ],
      method: 'radio',
      validation: 'required',
      options: ['Free', 'Free Trial', 'Paid'],
      defaultValue: 'Free',
      notes: '定价类型单选按钮，默认选择Free'
    },

    // 价格 -> Pricing
    priceAmount: {
      selectors: [
        'input[name="price"]',
        'input[placeholder*="Price"]',
        '#input_comp-lk5wjxgk',
        'input[type="number"][step="0.01"]'
      ],
      method: 'value',
      validation: 'required',
      defaultValue: '0.01',
      notes: '具体价格金额'
    },

    // 货币 -> Currency
    currency: {
      selectors: [
        'select[aria-label*="Currency"]',
        '#collection_comp-lkgn3a7y',
        'select[id*="comp-lkgn3a7y"]'
      ],
      method: 'select',
      validation: 'required',
      options: ['$', '€', '£', '₺'],
      defaultValue: '$',
      notes: '货币选择，默认选择USD'
    },

    // 定价周期 -> Pricing term
    pricingTerm: {
      selectors: [
        'select[aria-label*="Pricing term"]',
        '#collection_comp-lkgn50bs',
        'select[id*="comp-lkgn50bs"]'
      ],
      method: 'select',
      validation: 'required',
      options: ['day', 'week', 'month', 'year', 'one time off'],
      defaultValue: 'month',
      notes: '定价周期，默认选择monthly'
    },
    
    // 简短描述 -> Short Description
    siteDescription: {
      selectors: [
        'input[placeholder*="Describe in 40 characters"]',
        'textarea[placeholder*="40 characters"]',
        'input[aria-label*="Short Description"]',
        'input[maxlength="40"]'
      ],
      method: 'value',
      validation: 'required',
      maxLength: 40,
      notes: '40字符以内的简短描述'
    },
    
    // 详细介绍 -> Long Description
    detailedIntro: {
      selectors: [
        'textarea[placeholder*="In 1000-1500 characters"]',
        'textarea[placeholder*="1000"]',
        'textarea[aria-label*="Long Description"]',
        'textarea[maxlength*="1500"]'
      ],
      method: 'value',
      validation: 'required',
      minLength: 1000,
      maxLength: 1500,
      notes: '1000-1500字符的详细介绍'
    },
    
    // YouTube视频URL -> Youtube Video URL (可选)
    videoUrl: {
      selectors: [
        '#input_comp-lk5wj7iv',
        'input[name="url"][placeholder*="Youtube link"]',
        'input[placeholder*="e.g. Youtube link"]',
        'input[type="url"][placeholder*="Youtube"]'
      ],
      method: 'value',
      validation: 'optional|url',
      notes: 'YouTube视频链接'
    },

    // Twitter链接 -> Twitter (可选)
    twitterUrl: {
      selectors: [
        '#input_comp-lkgmxpld',
        'input[name="url"][placeholder*="Full URL of Twitter account"]',
        'input[placeholder*="Twitter account"]',
        'input[type="url"][placeholder*="Twitter"]'
      ],
      method: 'value',
      validation: 'optional|url',
      notes: 'Twitter账号完整URL'
    },

    // LinkedIn链接 -> Linkedin (可选)
    linkedinUrl: {
      selectors: [
        '#input_comp-lk5x4hgc4',
        'input[name="url"][placeholder*="Full URL of Linkedin account"]',
        'input[placeholder*="Linkedin account"]',
        'input[type="url"][placeholder*="Linkedin"]'
      ],
      method: 'value',
      validation: 'optional|url',
      notes: 'LinkedIn账号完整URL'
    },

    // AI分类 -> AI Category
    category: {
      selectors: [
        'select[aria-label*="AI Category"]',
        'select[id*="collection_comp"]',
        'select.wixui-dropdown__input',
        'select[data-testid="select-trigger"]'
      ],
      method: 'select',
      validation: 'required',
      options: ['Audio', 'Business', 'Coding', 'Design', 'Finance', 'Image', 'Life', 'Marketing', 'Productivity', 'Video', 'Writing'],
      defaultValue: 'Design',
      notes: 'AI工具分类，默认选择Design'
    }
  },
  
  // 自定义填写函数
  customFillElement: async function(element, value, config) {
    console.log(`DropYourAI自定义填写: ${element.placeholder || element.id}, 方法: ${config.method}`);
    
    switch (config.method) {
      case 'value':
        // Wix表单特殊处理
        element.focus();
        await new Promise(resolve => setTimeout(resolve, 200));
        
        element.value = value;
        element.dispatchEvent(new Event('input', { bubbles: true }));
        element.dispatchEvent(new Event('change', { bubbles: true }));
        element.dispatchEvent(new Event('blur', { bubbles: true }));
        
        // Wix可能需要额外的事件
        if (window.wixDevelopersAnalytics) {
          element.dispatchEvent(new Event('wixInput', { bubbles: true }));
        }
        break;
        
      case 'radio':
        // Wix单选按钮处理
        if (element.type === 'radio') {
          const targetValue = config.defaultValue || value;
          if (element.value === targetValue) {
            // 先取消选中同组的其他单选按钮
            const sameGroupRadios = document.querySelectorAll(`input[name="${element.name}"]`);
            sameGroupRadios.forEach(radio => {
              radio.checked = false;
            });

            // 选中目标单选按钮
            element.checked = true;
            element.dispatchEvent(new Event('change', { bubbles: true }));
            element.dispatchEvent(new Event('click', { bubbles: true }));

            console.log(`✓ 选中单选按钮: ${element.value}`);
          }
        }
        break;

      case 'select':
        // Wix下拉选择处理
        if (element.tagName.toLowerCase() === 'select') {
          const targetValue = config.defaultValue || value;
          const option = Array.from(element.options).find(opt =>
            opt.value === targetValue || opt.text === targetValue || opt.text.includes(targetValue)
          );
          if (option) {
            element.value = option.value;
            element.dispatchEvent(new Event('change', { bubbles: true }));
            console.log(`✓ 选择下拉选项: ${option.text || option.value}`);
          }
        } else {
          // 可能是自定义下拉组件
          element.click();
          await new Promise(resolve => setTimeout(resolve, 300));

          const targetValue = config.defaultValue || value;
          const optionElement = document.querySelector(`[data-value="${targetValue}"], [aria-label="${targetValue}"]`);
          if (optionElement) {
            optionElement.click();
          }
        }
        break;
        
      default:
        element.value = value;
        element.dispatchEvent(new Event('input', { bubbles: true }));
        element.dispatchEvent(new Event('change', { bubbles: true }));
    }
  },
  
  // 提交流程配置
  submitConfig: {
    submitButton: 'button[aria-label*="Continue"], button[data-testid*="submit"], input[type="submit"]',
    submitMethod: 'click',
    waitAfterFill: 3000, // Wix表单需要更长等待时间
    waitAfterSubmit: 5000,
    successIndicators: [
      '[data-testid*="success"]',
      '.success-message',
      '[aria-label*="success"]'
    ],
    errorIndicators: [
      '[data-testid*="error"]',
      '.error-message',
      '[aria-label*="error"]'
    ]
  },
  
  // 特殊处理
  specialHandling: {
    requiresLogin: false,
    hasCaptcha: false,
    hasFileUpload: true, // 有图片上传功能
    
    // 表单验证规则
    formValidation: {
      requiredFields: ['siteName', 'contactEmail', 'siteUrl', 'pricing', 'priceAmount', 'currency', 'pricingTerm', 'siteDescription', 'detailedIntro', 'category'],
      emailValidation: true,
      urlValidation: true
    },
    
    // 特殊注意事项
    notes: [
      '这是Wix网站，表单是动态生成的',
      '有文件上传功能（图片），需要手动处理',
      '简短描述限制40字符',
      '详细介绍要求1000-1500字符',
      '定价类型使用单选按钮，默认选择Free',
      '价格默认设置为0.01',
      '货币默认选择USD ($)',
      '定价周期默认选择monthly',
      'AI分类默认选择Design',
      '可能需要额外的Wix特定事件触发'
    ]
  }
};

// 自定义处理函数
export function handleDropYourAISubmission(data, rule) {
  console.log('Processing DropYourAI.com submission...');
  
  const processedData = { ...data };
  
  // 确保URL格式正确
  ['siteUrl', 'videoUrl', 'twitterUrl', 'linkedinUrl'].forEach(field => {
    if (processedData[field] && !processedData[field].startsWith('http')) {
      processedData[field] = 'https://' + processedData[field];
    }
  });
  
  // 处理简短描述长度限制
  if (processedData.siteDescription && processedData.siteDescription.length > 40) {
    processedData.siteDescription = processedData.siteDescription.substring(0, 37) + '...';
  }
  
  // 处理详细介绍长度要求
  if (processedData.detailedIntro) {
    if (processedData.detailedIntro.length < 1000) {
      // 如果太短，可以重复或扩展内容
      processedData.detailedIntro = processedData.detailedIntro + ' ' + processedData.detailedIntro;
    }
    if (processedData.detailedIntro.length > 1500) {
      processedData.detailedIntro = processedData.detailedIntro.substring(0, 1497) + '...';
    }
  }
  
  // 设置默认定价相关字段
  if (!processedData.pricing) {
    processedData.pricing = 'Free';
  }

  if (!processedData.priceAmount) {
    processedData.priceAmount = '0.01';
  }

  if (!processedData.currency) {
    processedData.currency = '$';
  }

  if (!processedData.pricingTerm) {
    processedData.pricingTerm = 'month';
  }
  
  return processedData;
}
