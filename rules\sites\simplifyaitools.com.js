// SimplifyAITools.com 网站规则配置
// 网站: https://simplifyaitools.com/submit-tool/
// 表单技术: WordPress + WPForms
// 最后更新: 2025-07-07

export const SITE_RULE = {
  // 基本信息
  domain: 'simplifyaitools.com',
  siteName: 'SimplifyAITools',
  priority: 1,
  lastUpdated: '2025-07-07',
  
  // 字段映射规则
  fieldMappings: {
    // 提交者姓名 -> Name
    fullName: {
      selectors: [
        'input[name*="name"]:not([name*="tool"])',
        'input[placeholder*="Name"]',
        'input[aria-label*="Name"]',
        '#wpforms-field_1',
        '.wpforms-field-name input'
      ],
      method: 'value',
      validation: 'required',
      notes: '提交者姓名，使用website-info.js中的fullName字段'
    },
    
    // 联系电话 -> Phone
    phone: {
      selectors: [
        'input[name*="phone"]',
        'input[type="tel"]',
        'input[placeholder*="Phone"]',
        'input[aria-label*="Phone"]',
        '#wpforms-field_2'
      ],
      method: 'value',
      validation: 'required',
      notes: '联系电话，使用website-info.js中的phone字段'
    },
    
    // 联系邮箱 -> Email
    contactEmail: {
      selectors: [
        'input[name="wpforms[fields][57]"]',
        '#wpforms-5116-field_57',
        'input[type="email"]',
        'input[name*="email"]',
        'input[placeholder*="Email"]'
      ],
      method: 'value',
      validation: 'required|email',
      notes: '联系邮箱，使用website-info.js中的contactEmail字段'
    },
    
    // 工具名称 -> Tool Name/Title
    siteName: {
      selectors: [
        '#wpforms-5116-field_56',
        'input[name="wpforms[fields][56]"]',
        'input[id*="field_56"]',
        'input[name*="tool"][name*="name"]',
        'input[placeholder*="Tool Name"]',
        'input[placeholder*="Tool Title"]',
        '.wpforms-field-name input',
        'input[type="text"]:not([name*="email"]):not([name*="phone"])'
      ],
      method: 'value',
      validation: 'required',
      notes: '工具名称，使用website-info.js中的siteName字段'
    },
    

    
    // 工具详细描述 -> Detailed Description (富文本编辑器)
    detailedIntro: {
      selectors: [
        'textarea[name*="description"]',
        'textarea[name*="detail"]',
        '#wpforms-field_8',
        '.wpforms-field-textarea textarea',
        // 富文本编辑器可能的选择器
        '.wp-editor-area',
        'iframe[title*="Rich Text Area"]'
      ],
      method: 'richtext',
      validation: 'required',
      notes: '工具详细描述，使用website-info.js中的detailedIntro字段，支持富文本'
    },
    
    // 定价模式 -> Pricing Model (单选按钮)
    pricing: {
      selectors: [
        'input[name*="pricing"][value="Free"]',
        'input[name*="pricing"][value="Paid"]',
        'input[name*="pricing"][value="Freemium"]',
        'input[name*="pricing"][value="Premium"]',
        '#wpforms-field_9'
      ],
      method: 'radio',
      validation: 'required',
      defaultValue: 'Free',
      notes: '定价模式，根据website-info.js中的pricing字段选择对应单选按钮'
    },
    
    // 价格 -> Price
    priceAmount: {
      selectors: [
        'input[name*="price"]:not([name*="pricing"])',
        'input[placeholder*="Price"]',
        '#wpforms-field_10'
      ],
      method: 'value',
      validation: 'optional',
      notes: '具体价格，使用website-info.js中的priceAmount字段'
    },
    
    // 官方网站 -> Official Tool Website
    siteUrl: {
      selectors: [
        'input[name*="website"]',
        'input[name*="url"]:not([name*="image"])',
        'input[type="url"]',
        'input[placeholder*="Website"]',
        '#wpforms-field_11'
      ],
      method: 'value',
      validation: 'required|url',
      notes: '工具官方网站URL，使用website-info.js中的siteUrl字段'
    },

    // 推广选项 -> Are you interested in promoting your tool on SimplifyAITools?
    promotionOptions: {
      selectors: [
        'input[name="wpforms[fields][59][]"][value="No, I am not interested in additional promotion at this time"]',
        '#wpforms-5116-field_59_6',
        'input[value*="not interested in additional promotion"]'
      ],
      method: 'checkbox',
      validation: 'required',
      defaultValue: 'No, I am not interested in additional promotion at this time',
      notes: '推广选项，默认选择不感兴趣'
    },

    // 首页广告位 -> Are you interested in buying space on homepage?
    homepageAd: {
      selectors: [
        'input[name="wpforms[fields][53]"][value="No"]',
        '#wpforms-5116-field_53_2',
        'input[value="No"]'
      ],
      method: 'radio',
      validation: 'optional',
      defaultValue: 'No',
      notes: '首页广告位询问，默认选择No'
    }
  },
  
  // 自定义填写函数
  customFillElement: async function(element, value, config) {
    console.log(`SimplifyAITools自定义填写: ${element.name || element.id}, 方法: ${config.method}`);
    
    switch (config.method) {
      case 'value':
        // 标准输入框处理
        element.focus();
        await new Promise(resolve => setTimeout(resolve, 100));

        // 清空现有内容
        element.value = '';
        await new Promise(resolve => setTimeout(resolve, 50));

        // 设置新值
        element.value = value;

        // 触发多种事件确保兼容性
        element.dispatchEvent(new Event('input', { bubbles: true }));
        element.dispatchEvent(new Event('change', { bubbles: true }));
        element.dispatchEvent(new Event('keyup', { bubbles: true }));
        element.dispatchEvent(new Event('blur', { bubbles: true }));

        // 验证值是否设置成功
        if (element.value === value) {
          console.log(`✓ 填写字段: ${element.name || element.id} = "${value}"`);
        } else {
          console.warn(`⚠ 字段值设置可能失败: ${element.name || element.id}`);
        }
        break;
        

      case 'select':
        // 标准下拉选择框处理
        if (element.tagName.toLowerCase() === 'select') {
          // 分类映射表
          const categoryMapping = {
            'AI Tools': 'Other',
            'Developer Tools': 'Code & Developer Tools',
            'Content Creation': 'Writing & Content',
            'Image Generation': 'Image Generation',
            'Video Generation': 'Video Generation',
            'Audio Tools': 'Audio & Speech',
            'Chatbot': 'Chatbots & LLMs',
            'Marketing': 'Marketing & Sales',
            'Design': 'Design & Media',
            'Productivity': 'Productivity & Business',
            'Education': 'Education & Learning',
            'Finance': 'Finance & Analytics',
            'Creative': 'Creativity & Art',
            'Lifestyle': 'Lifestyle & Personal Growth',
            'Research': 'Search & Research',
            'Automation': 'Automation & Agents',
            'Healthcare': 'Healthcare & Wellness',
            'Legal': 'Legal & Compliance',
            'HR': 'HR & Recruiting',
            'Support': 'Customer Support'
          };

          // 尝试映射分类
          let targetValue = categoryMapping[value] || value;

          // 查找匹配的选项
          const option = Array.from(element.options).find(opt =>
            opt.value === targetValue ||
            opt.text === targetValue ||
            opt.text.toLowerCase().includes(targetValue.toLowerCase()) ||
            targetValue.toLowerCase().includes(opt.text.toLowerCase())
          );

          if (option) {
            element.value = option.value;
            element.dispatchEvent(new Event('change', { bubbles: true }));
            console.log(`✓ 选择分类: ${option.text}`);
          } else {
            // 使用默认值
            const defaultOption = Array.from(element.options).find(opt =>
              opt.value === config.defaultValue || opt.text === config.defaultValue
            );
            if (defaultOption) {
              element.value = defaultOption.value;
              element.dispatchEvent(new Event('change', { bubbles: true }));
              console.log(`✓ 使用默认分类: ${defaultOption.text}`);
            }
          }
        }
        break;
        
      case 'radio':
        // 单选按钮处理
        const pricingMapping = {
          'Free': 'Free',
          'Paid': 'Paid',
          'Freemium': 'Freemium',
          'Premium': 'Premium',
          'Subscription': 'Premium'
        };
        
        const targetPricing = pricingMapping[value] || config.defaultValue;
        const radioButton = document.querySelector(`input[name*="pricing"][value="${targetPricing}"]`);
        
        if (radioButton) {
          radioButton.checked = true;
          radioButton.dispatchEvent(new Event('change', { bubbles: true }));
          console.log(`✓ 选择定价模式: ${targetPricing}`);
        }
        break;

      case 'checkbox':
        // 复选框处理 - 推广选项
        if (element.type === 'checkbox') {
          // 对于推广选项，只勾选"不感兴趣"选项
          if (element.value === 'No, I am not interested in additional promotion at this time') {
            element.checked = true;
            element.dispatchEvent(new Event('change', { bubbles: true }));
            console.log(`✓ 勾选推广选项: ${element.value}`);
          }
        }
        break;

      case 'richtext':
        // 富文本编辑器处理
        if (element.tagName.toLowerCase() === 'iframe') {
          // TinyMCE或类似的富文本编辑器
          try {
            const doc = element.contentDocument || element.contentWindow.document;
            const body = doc.body;
            if (body) {
              body.innerHTML = value;
              // 触发编辑器更新事件
              element.dispatchEvent(new Event('input', { bubbles: true }));
              console.log(`✓ 填写富文本: ${value.substring(0, 50)}...`);
            }
          } catch (error) {
            console.warn('富文本编辑器填写失败，尝试textarea方式');
            // 回退到普通textarea处理
            element.value = value;
            element.dispatchEvent(new Event('input', { bubbles: true }));
          }
        } else {
          // 普通textarea
          element.value = value;
          element.dispatchEvent(new Event('input', { bubbles: true }));
          element.dispatchEvent(new Event('change', { bubbles: true }));
          console.log(`✓ 填写描述: ${value.substring(0, 50)}...`);
        }
        break;
        
      default:
        element.value = value;
        element.dispatchEvent(new Event('input', { bubbles: true }));
        element.dispatchEvent(new Event('change', { bubbles: true }));
    }
  },

  // 提交流程配置
  submitConfig: {
    submitButton: [
      'button[type="submit"]',
      'input[type="submit"]',
      '.wpforms-submit',
      '#wpforms-submit',
      'button:contains("Share My Tool")',
      'button:contains("Submit")'
    ],
    submitMethod: 'click',
    waitAfterFill: 2000,
    waitAfterSubmit: 5000,
    successIndicators: [
      '.wpforms-confirmation-container',
      '.success-message',
      '.thank-you',
      '[class*="success"]'
    ],
    errorIndicators: [
      '.wpforms-error',
      '.error-message',
      '.validation-error',
      '[class*="error"]'
    ]
  },

  // 特殊处理
  specialHandling: {
    requiresLogin: false,
    hasCaptcha: false,
    hasFileUpload: true, // 有工具图片上传功能
    hasRichTextEditor: true, // 有富文本编辑器
    hasPromotionOptions: true, // 有推广选项复选框

    // 表单验证规则
    formValidation: {
      requiredFields: ['fullName', 'phone', 'contactEmail', 'siteName', 'detailedIntro', 'pricing', 'siteUrl', 'promotionOptions'],
      emailValidation: true,
      urlValidation: true,
      phoneValidation: true
    },

    // 特殊注意事项
    notes: [
      '这是WordPress网站，使用WPForms插件',
      '有文件上传功能（工具图片），需要手动处理',
      '包含富文本编辑器用于详细描述',
      '分类字段使用Choices.js插件，需要手动选择',
      '推广选项是必填复选框组，默认选择"不感兴趣"',
      '首页广告位询问使用单选按钮，默认选择No',
      '定价模式使用单选按钮：Paid/Free/Freemium/Premium',
      '实际字段ID使用wpforms-5116-field_XX格式',
      '表单字段较多，填写时间可能较长',
      '提交后需要编辑团队审核'
    ]
  }
};

// 自定义处理函数
export function handleSimplifyAIToolsSubmission(data, _rule) {
  console.log('Processing SimplifyAITools form submission...');

  const processedData = { ...data };

  // 确保URL格式正确
  if (processedData.siteUrl && !processedData.siteUrl.startsWith('http')) {
    processedData.siteUrl = 'https://' + processedData.siteUrl;
  }



  // 处理定价模式映射
  if (processedData.pricing) {
    const pricingMapping = {
      'Free': 'Free',
      'Paid': 'Paid',
      'Freemium': 'Freemium',
      'Premium': 'Premium',
      'Subscription': 'Premium'
    };

    processedData.pricing = pricingMapping[processedData.pricing] || 'Free';
  }

  // 处理价格格式
  if (processedData.priceAmount) {
    // 移除货币符号，只保留数字
    processedData.priceAmount = processedData.priceAmount.replace(/[^\d.]/g, '');
  }

  // 处理电话号码格式
  if (processedData.phone) {
    // 确保电话号码格式正确
    if (!processedData.phone.startsWith('+')) {
      processedData.phone = '+1-' + processedData.phone.replace(/[^\d]/g, '');
    }
  }



  return processedData;
}
