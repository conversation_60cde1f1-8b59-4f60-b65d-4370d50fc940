// tooljunction.io 网站规则配置
// 网站: https://www.tooljunction.io/submit-tool
// 表单技术: Modern React Components with Tailwind CSS
// 最后更新: 2025-07-08

export const SITE_RULE = {
  // 基本信息
  domain: 'tooljunction.io',
  siteName: 'Tool Junction',
  priority: 1,
  lastUpdated: '2025-07-08',
  
  // 字段映射规则
  fieldMappings: {
    // 第一部分：工具信息
    // 工具名称 -> Tool Name
    siteName: {
      selectors: [
        'input[id="tool-name"]',
        'input[placeholder*="AI Assistant Pro"]'
      ],
      method: 'value',
      validation: 'required',
      section: 1,
      notes: '工具名称，使用website-info.js中的siteName字段'
    },
    
    // 公司名称 -> Company Name
    companyName: {
      selectors: [
        'input[id="company-name"]',
        'input[placeholder*="Your Company"]'
      ],
      method: 'value',
      validation: 'required',
      section: 1,
      notes: '公司名称，使用website-info.js中的companyName字段'
    },
    
    // 网站URL -> Website URL
    siteUrl: {
      selectors: [
        'input[id="website"]',
        'input[type="url"]',
        'input[placeholder="https://yourtool.com"]'
      ],
      method: 'value',
      validation: 'required|url',
      section: 1,
      notes: '网站URL，使用website-info.js中的siteUrl字段'
    },
    
    // 标语 -> Tagline (Short Description)
    siteDescription: {
      selectors: [
        'input[id="tagline"]',
        'input[placeholder*="One sentence"]'
      ],
      method: 'value',
      validation: 'required',
      section: 1,
      notes: '工具标语，使用website-info.js中的siteDescription字段'
    },
    
    // 完整描述 -> Full Description
    detailedIntro: {
      selectors: [
        'textarea[id="description"]',
        'textarea[placeholder*="detailed description"]'
      ],
      method: 'value',
      validation: 'required',
      section: 1,
      notes: '完整描述，使用website-info.js中的detailedIntro字段'
    },
    
    // 主要分类 -> Primary Category
    category: {
      selectors: [
        'button[role="combobox"][aria-controls*="Rjhkfelb"]',
        'select[aria-hidden="true"]:first-of-type'
      ],
      method: 'combobox',
      validation: 'required',
      section: 1,
      defaultValue: 'productivity',
      notes: '主要分类，默认选择productivity'
    },
    
    // 定价模式 -> Pricing Model
    pricing: {
      selectors: [
        'button[role="combobox"][aria-controls*="Rlhkfelb"]',
        'select[aria-hidden="true"]:nth-of-type(2)'
      ],
      method: 'combobox',
      validation: 'required',
      section: 1,
      defaultValue: 'free',
      notes: '定价模式，默认选择free'
    },
    
    // Logo上传 -> Logo Upload
    logoUpload: {
      selectors: [
        'input[id="logo"]',
        'input[type="file"][accept="image/*"]'
      ],
      method: 'file-upload',
      validation: 'required',
      section: 1,
      notes: 'Logo上传，必填字段，推荐300x300px以上'
    },
    
    // 第二部分：联系信息
    // 联系人姓名 -> Contact Name
    fullName: {
      selectors: [
        'input[id="contact-name"]',
        'input[placeholder="Your name"]'
      ],
      method: 'value',
      validation: 'required',
      section: 2,
      notes: '联系人姓名，使用website-info.js中的fullName字段'
    },
    
    // 邮箱地址 -> Email Address
    contactEmail: {
      selectors: [
        'input[id="contact-email"]',
        'input[type="email"]',
        'input[placeholder*="<EMAIL>"]'
      ],
      method: 'value',
      validation: 'required|email',
      section: 2,
      notes: '邮箱地址，使用website-info.js中的contactEmail字段'
    },
    
    // 电话号码 -> Phone Number (Optional)
    phone: {
      selectors: [
        'input[id="contact-phone"]',
        'input[type="tel"]',
        'input[placeholder*="*************"]'
      ],
      method: 'value',
      validation: 'optional',
      section: 2,
      notes: '电话号码，可选字段'
    },
    
    // 第三部分：附加详情
    // 独特功能 -> What makes your tool unique?
    uniqueSellingPoints: {
      selectors: [
        'textarea[id="unique-features"]',
        'textarea[placeholder*="sets your tool apart"]'
      ],
      method: 'value',
      validation: 'optional',
      section: 3,
      notes: '独特功能描述，使用website-info.js中的uniqueSellingPoints字段'
    },
    
    // 成立年份 -> Year Founded
    foundedYear: {
      selectors: [
        'input[id="founded-year"]',
        'input[type="number"]',
        'input[placeholder*="2023"]'
      ],
      method: 'value',
      validation: 'optional',
      section: 3,
      defaultValue: '2025',
      notes: '成立年份，使用默认值2025'
    },
    
    // 团队规模 -> Team Size
    teamSize: {
      selectors: [
        'button[role="combobox"][aria-controls*="Rarkfelb"]',
        'select[aria-hidden="true"]:last-of-type'
      ],
      method: 'combobox',
      validation: 'optional',
      section: 3,
      defaultValue: '1-10',
      notes: '团队规模，默认选择1-10 employees'
    }
  },

  // 自定义填写函数
  customFillElement: async function(element, value, config) {
    console.log(`Tool Junction自定义填写: ${element.id || element.placeholder}, 方法: ${config.method}`);

    switch (config.method) {
      case 'value':
        // 标准输入框和文本域处理
        element.focus();
        await new Promise(resolve => setTimeout(resolve, 100));

        // 清空现有内容
        element.value = '';
        await new Promise(resolve => setTimeout(resolve, 50));

        // 处理特殊字段
        let finalValue = value;
        if (element.id === 'founded-year') {
          finalValue = config.defaultValue;
        }

        // 设置新值
        element.value = finalValue;

        // 触发事件
        element.dispatchEvent(new Event('input', { bubbles: true }));
        element.dispatchEvent(new Event('change', { bubbles: true }));
        element.dispatchEvent(new Event('blur', { bubbles: true }));

        console.log(`✓ 填写字段: ${element.id} = "${finalValue.substring(0, 50)}..."`);
        break;

      case 'combobox':
        // Combobox组件处理
        try {
          // 点击combobox按钮打开选项
          element.click();
          console.log('点击打开Combobox组件');

          // 等待选项加载
          await new Promise(resolve => setTimeout(resolve, 800));

          let targetValue = config.defaultValue;

          // 分类映射
          if (element.getAttribute('aria-controls')?.includes('Rjhkfelb')) {
            const categoryMapping = {
              'AI Tools': 'productivity',
              'Text Generation': 'text-generation',
              'Image Generation': 'image-generation',
              'Video Creation': 'video-creation',
              'Code Assistant': 'code-assistant',
              'Chatbots': 'chatbots',
              'Analytics': 'analytics',
              'Marketing': 'marketing',
              'Productivity': 'productivity'
            };
            targetValue = categoryMapping[value] || config.defaultValue;
          }

          // 定价映射
          if (element.getAttribute('aria-controls')?.includes('Rlhkfelb')) {
            const pricingMapping = {
              'Free': 'free',
              'Freemium': 'freemium',
              'Subscription': 'subscription',
              'One-time': 'one-time',
              'Usage-based': 'usage-based'
            };
            targetValue = pricingMapping[value] || config.defaultValue;
          }

          // 团队规模映射
          if (element.getAttribute('aria-controls')?.includes('Rarkfelb')) {
            targetValue = config.defaultValue;
          }

          // 查找匹配的选项
          const options = document.querySelectorAll('[role="option"], .radix-select-item');
          let targetOption = null;

          for (const option of options) {
            const text = (option.textContent || '').trim();
            const optionValue = option.getAttribute('data-value') || '';
            if (optionValue === targetValue || text.toLowerCase().includes(targetValue.toLowerCase())) {
              targetOption = option;
              break;
            }
          }

          if (targetOption) {
            targetOption.click();
            console.log(`✓ 选择选项: ${targetOption.textContent}`);
          } else {
            // 尝试通过隐藏的select元素
            const hiddenSelect = element.parentElement.querySelector('select[aria-hidden="true"]');
            if (hiddenSelect) {
              const option = Array.from(hiddenSelect.options).find(opt =>
                opt.value === targetValue || opt.text.toLowerCase().includes(targetValue.toLowerCase())
              );
              if (option) {
                hiddenSelect.value = option.value;
                hiddenSelect.dispatchEvent(new Event('change', { bubbles: true }));
                console.log(`✓ 通过隐藏select选择: ${option.text}`);
              }
            }
          }
        } catch (error) {
          console.error('Combobox处理出错:', error);
        }
        break;

      case 'file-upload':
        // 文件上传处理
        console.log('⚠️ 文件上传字段需要手动处理');
        console.log('请手动上传Logo文件（推荐300x300px以上的方形图片）');
        break;

      default:
        element.value = value;
        element.dispatchEvent(new Event('input', { bubbles: true }));
        element.dispatchEvent(new Event('change', { bubbles: true }));
    }
  },

  // 提交流程配置
  submitConfig: {
    submitButton: [
      'button[type="submit"]',
      'button:contains("Submit Tool for Review")',
      '.bg-teal-600[type="submit"]'
    ],
    submitMethod: 'click',
    waitAfterFill: 3000, // 等待文件上传
    waitAfterSubmit: 5000,

    // 提交前检查
    preSubmitChecks: [
      {
        type: 'checkbox',
        selector: 'input[type="checkbox"][required]',
        action: 'check',
        description: '勾选服务条款和隐私政策同意框'
      },
      {
        type: 'file-upload',
        selector: 'input[type="file"]',
        action: 'verify',
        description: '确认Logo文件已上传'
      }
    ],

    successIndicators: [
      '.success-message',
      '.thank-you',
      '[class*="success"]',
      'text:contains("submitted")',
      'text:contains("review")'
    ],
    errorIndicators: [
      '.error-message',
      '[class*="error"]',
      'text:contains("error")',
      'text:contains("required")'
    ]
  },

  // 特殊处理
  specialHandling: {
    requiresLogin: false,
    hasCaptcha: false,
    hasFileUpload: true, // 有文件上传字段
    isModernReact: true, // 使用现代React组件
    hasTailwindCSS: true, // 使用Tailwind CSS
    hasMultiStep: true, // 多步骤表单（3个部分）
    hasTermsAgreement: true, // 有服务条款同意

    // 表单验证规则
    formValidation: {
      requiredFields: ['siteName', 'companyName', 'siteUrl', 'siteDescription', 'detailedIntro', 'category', 'pricing', 'logoUpload', 'fullName', 'contactEmail'],
      emailValidation: true,
      urlValidation: true,
      fileUploadFields: ['logoUpload'],
      fileUploadLimits: {
        logoUpload: { accept: 'image/*', minSize: '300x300px' }
      }
    },

    // 特殊注意事项
    notes: [
      '这是现代React网站，使用Tailwind CSS',
      '表单分为3个部分：工具信息、联系信息、附加详情',
      '表单包含13个字段，10个必填，3个可选',
      '需要上传Logo文件（推荐300x300px以上方形图片）',
      '有服务条款和隐私政策同意框，必须勾选',
      '提交后1-2个工作日内审核',
      '使用Combobox组件处理下拉选择',
      '字段ID使用标准命名',
      '支持拖拽上传Logo文件',
      '表单有完整的验证和错误提示'
    ]
  }
};

// 自定义处理函数
export function handleToolJunctionSubmission(data, _rule) {
  console.log('Processing Tool Junction form submission...');

  const processedData = { ...data };

  // 确保URL格式正确
  if (processedData.siteUrl && !processedData.siteUrl.startsWith('http')) {
    processedData.siteUrl = 'https://' + processedData.siteUrl;
  }

  // 处理分类映射
  if (processedData.category) {
    const categoryMapping = {
      'AI Tools': 'productivity',
      'Text Generation': 'text-generation',
      'Image Generation': 'image-generation',
      'Video Creation': 'video-creation',
      'Code Assistant': 'code-assistant',
      'Chatbots': 'chatbots',
      'Analytics': 'analytics',
      'Marketing': 'marketing',
      'Productivity': 'productivity'
    };
    processedData.category = categoryMapping[processedData.category] || 'productivity';
  }

  // 处理定价映射
  if (processedData.pricing) {
    const pricingMapping = {
      'Free': 'free',
      'Freemium': 'freemium',
      'Subscription': 'subscription',
      'One-time': 'one-time',
      'Usage-based': 'usage-based'
    };
    processedData.pricing = pricingMapping[processedData.pricing] || 'free';
  }

  // 设置默认值
  processedData.foundedYear = '2025';
  processedData.teamSize = '1-10';

  // 文件上传提醒
  console.log('⚠️ 请注意：需要手动上传Logo文件');

  return processedData;
}

// 服务条款同意框处理函数
export async function handleTermsAgreement() {
  console.log('处理服务条款同意框...');

  const termsCheckbox = document.querySelector('input[type="checkbox"][required]');
  if (termsCheckbox && !termsCheckbox.checked) {
    termsCheckbox.click();
    console.log('✓ 已勾选服务条款和隐私政策同意框');
    await new Promise(resolve => setTimeout(resolve, 500));
  }
}

// 多步骤表单处理函数
export async function handleMultiStepForm(rule, data) {
  console.log('处理多步骤表单...');

  const sections = [
    { name: '工具信息', fields: ['siteName', 'companyName', 'siteUrl', 'siteDescription', 'detailedIntro', 'category', 'pricing', 'logoUpload'] },
    { name: '联系信息', fields: ['fullName', 'contactEmail', 'phone'] },
    { name: '附加详情', fields: ['uniqueSellingPoints', 'foundedYear', 'teamSize'] }
  ];

  for (let i = 0; i < sections.length; i++) {
    const section = sections[i];
    console.log(`填写第${i + 1}部分: ${section.name}`);

    for (const fieldName of section.fields) {
      if (rule.fieldMappings[fieldName] && data[fieldName]) {
        const fieldConfig = rule.fieldMappings[fieldName];
        const element = document.querySelector(fieldConfig.selectors[0]);

        if (element) {
          await rule.customFillElement(element, data[fieldName], fieldConfig);
          await new Promise(resolve => setTimeout(resolve, 200));
        }
      }
    }

    // 每个部分之间稍作停顿
    if (i < sections.length - 1) {
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
  }

  console.log('✓ 多步骤表单填写完成');
}
