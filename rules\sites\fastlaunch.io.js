// FastLaunch 网站规则配置
// 网站: https://fastlaunch.io/projects/submit
// 最后更新: 2025-08-01

export const SITE_RULE = {
  // 基本信息
  domain: 'fastlaunch.io',
  siteName: 'FastLaunch',
  priority: 1,
  lastUpdated: '2025-08-01',
  
  // 字段映射规则
  fieldMappings: {
    // 项目名称 -> Project Name
    siteName: {
      selectors: [
        'input[name="name"]',
        'input#name',
        'input[placeholder*="Your Cool Project"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '项目名称，必填字段'
    },

    // 网站URL -> Website URL
    siteUrl: {
      selectors: [
        'input[name="websiteUrl"]',
        'input#websiteUrl',
        'input[placeholder*="https://yourcoolproject.app"]'
      ],
      method: 'value',
      validation: 'required|url',
      notes: '网站URL地址，必填字段'
    },

    // 简短描述 -> Short Description
    siteDescription: {
      selectors: [
        'textarea[name="description"]',
        'textarea#description',
        'textarea[placeholder*="Describe your project in a sentence or two"]'
      ],
      method: 'textarea',
      validation: 'required',
      notes: '项目简短描述，必填字段'
    },

    // 详细描述 -> Detailed Description
    detailedIntro: {
      selectors: [
        'textarea[name="detailedDescription"]',
        'textarea#detailedDescription',
        'textarea[placeholder*="Detail your project features, benefits, and technology"]'
      ],
      method: 'textarea',
      validation: 'required',
      notes: '项目详细描述，必填字段'
    }
  },
  
  // 提交流程配置
  submitConfig: {
    submitButton: 'button:contains("Next"), button[type="submit"]',
    submitMethod: 'click',
    waitAfterFill: 2000,
    waitAfterSubmit: 5000,
    successIndicators: [
      '.success-message',
      '.thank-you',
      '.submission-success'
    ],
    errorIndicators: [
      '.error-message',
      '.validation-error',
      '.form-error'
    ]
  },
  
  // 特殊处理
  specialHandling: {
    requiresLogin: false,
    hasCaptcha: false,
    hasFileUpload: true, // 有Logo和Cover Image上传
    isMultiStep: true, // 多步骤表单
    
    // 表单验证规则
    formValidation: {
      requiredFields: ['siteName', 'siteUrl', 'siteDescription', 'detailedIntro'],
      optionalFields: [],
      emailValidation: false,
      urlValidation: true,
      fileUploadValidation: true
    },
    
    // 特殊注意事项
    notes: [
      '多步骤表单，当前为第一步',
      'Logo上传必填 (1:1方形，最大1MB)',
      'Cover Image上传可选 (6:1宽图片)',
      '有进度条显示当前步骤',
      '点击Next按钮进入下一步'
    ]
  }
};

// 自定义处理函数
export function handleFastLaunchSubmission(data, rule) {
  console.log('Processing FastLaunch submission...');
  
  const processedData = { ...data };
  
  // 确保URL格式正确
  if (processedData.siteUrl && !processedData.siteUrl.startsWith('http')) {
    processedData.siteUrl = 'https://' + processedData.siteUrl;
  }
  
  return processedData;
}

// 自定义元素填写函数
export async function customFillElement(element, value, config) {
  console.log(`FastLaunch自定义填写: ${element.tagName}, 方法: ${config.method}`);

  switch (config.method) {
    case 'textarea':
      // 文本域处理
      element.focus();
      await new Promise(resolve => setTimeout(resolve, 200));

      element.value = value;
      element.dispatchEvent(new Event('input', { bubbles: true }));
      element.dispatchEvent(new Event('change', { bubbles: true }));

      console.log(`✓ 填写文本域: "${value}"`);
      return true;

    case 'value':
      // 标准输入框处理
      element.focus();
      await new Promise(resolve => setTimeout(resolve, 200));

      element.value = value;
      element.dispatchEvent(new Event('input', { bubbles: true }));
      element.dispatchEvent(new Event('change', { bubbles: true }));
      element.dispatchEvent(new Event('blur', { bubbles: true }));

      console.log(`✓ 填写字段: ${element.name || element.id} = "${value}"`);
      return true;

    default:
      element.value = value;
      element.dispatchEvent(new Event('input', { bubbles: true }));
      return true;
  }

  return false;
}

// 特殊处理：多步骤表单导航
export async function handleMultiStepForm() {
  console.log('处理多步骤表单...');
  
  // 等待表单填写完成后点击Next按钮
  const nextButton = document.querySelector('button:contains("Next")');
  if (nextButton && !nextButton.disabled) {
    await new Promise(resolve => setTimeout(resolve, 1000));
    nextButton.click();
    console.log('✓ 已点击Next按钮进入下一步');
    return true;
  }
  
  return false;
}
