// toolscout.ai 网站规则配置
// 网站: https://toolscout.ai/submit
// 表单技术: Bubble.io Platform with Select2
// 最后更新: 2025-07-07

export const SITE_RULE = {
  // 基本信息
  domain: 'toolscout.ai',
  siteName: 'ToolScout AI',
  priority: 1,
  lastUpdated: '2025-07-07',
  
  // 字段映射规则
  fieldMappings: {
    // 工具名称 -> Tool Name
    siteName: {
      selectors: [
        'input.bubble-element.Input.cmaSaFaW',
        'input[maxlength="150"]:first-of-type',
        'input[class*="cmaSaFaW"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '工具名称，使用website-info.js中的siteName字段，最大150字符'
    },
    
    // 工具URL -> Tool URL
    siteUrl: {
      selectors: [
        'input.bubble-element.Input.cmaSaFaY',
        'input[placeholder="https://"]',
        'input[class*="cmaSaFaY"]'
      ],
      method: 'value',
      validation: 'required|url',
      notes: '工具URL，使用website-info.js中的siteUrl字段，最大150字符'
    },
    
    // 描述 -> Description
    siteDescription: {
      selectors: [
        'textarea.bubble-element.MultiLineInput',
        'textarea[placeholder="What Can It Do?"]',
        'textarea[maxlength="800"]'
      ],
      method: 'value',
      validation: 'required',
      notes: '工具描述，使用website-info.js中的siteDescription字段，最大800字符'
    },
    
    // 标签 -> Tags
    keywords: {
      selectors: [
        'input.select2-search__field[placeholder*="Writing"]',
        'input[class*="a1751942682525x6933"]',
        '.select2-search__field:first-of-type'
      ],
      method: 'select2-tags',
      validation: 'required',
      defaultValue: '#design',
      notes: '工具标签，使用Select2组件，默认选择#design'
    },

    // 定价 -> Pricing
    pricing: {
      selectors: [
        'input.select2-search__field[placeholder*="Open Source"]',
        'input[class*="a1751942682230x6925"]',
        '.select2-search__field:last-of-type'
      ],
      method: 'select2-single',
      validation: 'required',
      defaultValue: 'Free',
      notes: '定价模式，使用Select2组件，默认选择Free'
    },
    
    // 开发者邮箱 -> Developer Email
    contactEmail: {
      selectors: [
        'input.bubble-element.Input.cmaSaGaX',
        'input[placeholder="<EMAIL>"]',
        'input[class*="cmaSaGaX"]'
      ],
      method: 'value',
      validation: 'required|email',
      notes: '开发者邮箱，使用website-info.js中的contactEmail字段，最大150字符'
    }
  },
  
  // 自定义填写函数
  customFillElement: async function(element, value, config) {
    console.log(`ToolScout AI自定义填写: ${element.className || element.placeholder}, 方法: ${config.method}`);
    
    switch (config.method) {
      case 'value':
        // 标准输入框和文本域处理
        element.focus();
        await new Promise(resolve => setTimeout(resolve, 100));
        
        // 清空现有内容
        element.value = '';
        await new Promise(resolve => setTimeout(resolve, 50));
        
        // 处理字符限制
        let finalValue = value;
        if (element.maxLength) {
          const maxLength = parseInt(element.maxLength);
          if (finalValue.length > maxLength) {
            finalValue = finalValue.substring(0, maxLength);
          }
        }
        
        // 设置新值
        element.value = finalValue;
        
        // 触发Bubble.io事件
        element.dispatchEvent(new Event('input', { bubbles: true }));
        element.dispatchEvent(new Event('change', { bubbles: true }));
        element.dispatchEvent(new Event('blur', { bubbles: true }));
        
        console.log(`✓ 填写字段: ${element.placeholder || element.className} = "${finalValue.substring(0, 50)}..."`);
        break;
        
      case 'select2-tags':
        // Select2多选标签处理
        try {
          // 点击Select2容器激活
          const container = element.closest('.select2-container') || element.parentElement;
          if (container) {
            container.click();
            console.log('点击激活Select2标签组件');

            // 等待组件激活
            await new Promise(resolve => setTimeout(resolve, 500));

            // 使用默认值或传入的值
            const targetTag = config.defaultValue || value || '#design';

            // 在搜索框中输入标签
            element.focus();
            element.value = targetTag;
            element.dispatchEvent(new Event('input', { bubbles: true }));

            // 等待选项出现
            await new Promise(resolve => setTimeout(resolve, 300));

            // 尝试选择匹配的选项
            const options = document.querySelectorAll('.select2-results__option, [role="option"]');
            let optionSelected = false;

            for (const option of options) {
              const optionText = option.textContent || '';
              if (optionText.toLowerCase().includes(targetTag.toLowerCase()) ||
                  optionText.toLowerCase().includes('design')) {
                option.click();
                optionSelected = true;
                console.log(`✓ 选择标签: ${targetTag}`);
                break;
              }
            }

            // 如果没有匹配选项，按Enter创建新标签
            if (!optionSelected) {
              element.dispatchEvent(new KeyboardEvent('keydown', { key: 'Enter', bubbles: true }));
              console.log(`✓ 创建新标签: ${targetTag}`);
            }

            await new Promise(resolve => setTimeout(resolve, 200));
          }
        } catch (error) {
          console.error('Select2标签处理出错:', error);
        }
        break;
        
      case 'select2-single':
        // Select2单选处理
        try {
          // 点击Select2容器打开选项
          const container = element.closest('.select2-container') || element.parentElement;
          if (container) {
            container.click();
            console.log('点击打开Select2定价组件');
            
            // 等待选项加载
            await new Promise(resolve => setTimeout(resolve, 500));
            
            // 定价映射
            const pricingMapping = {
              'Free': ['Free', 'free', 'Open Source', 'open source'],
              'Freemium': ['Freemium', 'freemium'],
              'Paid': ['Paid', 'paid', 'Premium'],
              'Subscription': ['Subscription', 'subscription']
            };
            
            let targetValue = config.defaultValue;
            for (const [key, values] of Object.entries(pricingMapping)) {
              if (values.some(v => v.toLowerCase() === value.toLowerCase())) {
                targetValue = key;
                break;
              }
            }
            
            // 查找匹配的选项
            const options = document.querySelectorAll('.select2-results__option, [role="option"]');
            let targetOption = null;
            
            for (const option of options) {
              const text = option.textContent || '';
              if (text.toLowerCase().includes(targetValue.toLowerCase()) ||
                  pricingMapping[targetValue]?.some(v => text.toLowerCase().includes(v.toLowerCase()))) {
                targetOption = option;
                break;
              }
            }
            
            if (targetOption) {
              targetOption.click();
              console.log(`✓ 选择定价: ${targetOption.textContent}`);
            } else {
              // 如果没找到，输入文本
              element.focus();
              element.value = targetValue;
              element.dispatchEvent(new Event('input', { bubbles: true }));
              element.dispatchEvent(new KeyboardEvent('keydown', { key: 'Enter', bubbles: true }));
              console.log(`✓ 输入定价: ${targetValue}`);
            }
          }
        } catch (error) {
          console.error('Select2定价处理出错:', error);
        }
        break;
        
      default:
        element.value = value;
        element.dispatchEvent(new Event('input', { bubbles: true }));
        element.dispatchEvent(new Event('change', { bubbles: true }));
    }
  },
  
  // 提交流程配置
  submitConfig: {
    submitButton: [
      'button[type="submit"]',
      '.bubble-element.Button',
      'button:contains("Submit")',
      '[class*="submit"]'
    ],
    submitMethod: 'click',
    waitAfterFill: 2000,
    waitAfterSubmit: 5000,
    successIndicators: [
      '.success-message',
      '.bubble-element.Text:contains("success")',
      '[class*="success"]'
    ],
    errorIndicators: [
      '.error-message',
      '.bubble-element.Text:contains("error")',
      '[class*="error"]'
    ]
  },
  
  // 特殊处理
  specialHandling: {
    requiresLogin: false,
    hasCaptcha: false,
    hasFileUpload: false,
    isBubbleApp: true, // 使用Bubble.io平台
    hasSelect2: true, // 使用Select2组件
    
    // 表单验证规则
    formValidation: {
      requiredFields: ['siteName', 'siteUrl', 'siteDescription', 'keywords', 'pricing', 'contactEmail'],
      emailValidation: true,
      urlValidation: true,
      characterLimits: {
        siteName: 150,
        siteUrl: 150,
        siteDescription: 800,
        contactEmail: 150
      }
    },
    
    // 特殊注意事项
    notes: [
      '这是Bubble.io平台构建的网站',
      '表单包含6个字段，全部必填',
      '使用Select2组件处理标签和定价选择',
      '字段有严格的字符长度限制',
      '工具名称和URL限制150字符',
      '描述限制800字符',
      '标签支持多选，建议选择3个以内',
      '定价支持Free, Freemium, Paid, Subscription等',
      'CSS类名包含随机字符串',
      '需要特殊的Bubble.io事件处理'
    ]
  }
};

// 自定义处理函数
export function handleToolScoutSubmission(data, _rule) {
  console.log('Processing ToolScout AI form submission...');

  const processedData = { ...data };

  // 确保URL格式正确
  if (processedData.siteUrl && !processedData.siteUrl.startsWith('http')) {
    processedData.siteUrl = 'https://' + processedData.siteUrl;
  }

  // 处理字符限制
  if (processedData.siteName && processedData.siteName.length > 150) {
    processedData.siteName = processedData.siteName.substring(0, 150);
  }

  if (processedData.siteDescription && processedData.siteDescription.length > 800) {
    processedData.siteDescription = processedData.siteDescription.substring(0, 800);
  }

  // 设置默认值
  processedData.keywords = '#design';
  processedData.pricing = 'Free';

  return processedData;
}
