// Tools4miRs.org 网站规则配置
// 网站: https://tools4mirs.org/software/submit/
// 最后更新: 2025-07-24

export const SITE_RULE = {
  domain: 'tools4mirs.org',
  siteName: 'Tools4miRs',
  priority: 1,
  lastUpdated: '2025-07-24',

  fieldMappings: {
    siteName: {
      selectors: [
        '#id_name',
        'input[name="name"]',
        'input[maxlength="100"]:first-of-type'
      ],
      method: 'value',
      validation: 'required',
      notes: '工具名称'
    },

    category: {
      selectors: [
        '#id_category',
        'select[name="category"]',
        'select[multiple="multiple"]'
      ],
      method: 'select',
      validation: 'required',
      defaultValue: '5',
      notes: '分类选择，默认选择Other Tools'
    },

    siteDescription: {
      selectors: [
        '#id_description',
        'textarea[name="description"]',
        'textarea[rows="10"]:first-of-type'
      ],
      method: 'value',
      validation: 'required',
      notes: '工具描述'
    },

    siteUrl: {
      selectors: [
        '#id_link',
        'input[name="link"]',
        'input[type="url"]:first-of-type'
      ],
      method: 'value',
      validation: 'required|url',
      notes: '工具主页URL'
    },

    online: {
      selectors: [
        '#id_online',
        'select[name="online"]'
      ],
      method: 'select',
      validation: 'required',
      defaultValue: 'True',
      notes: '是否在线工具'
    },

    local: {
      selectors: [
        '#id_local',
        'select[name="local"]'
      ],
      method: 'select',
      validation: 'required',
      defaultValue: 'False',
      notes: '是否本地工具'
    },

    installLevel: {
      selectors: [
        '#id_easy_install_usage',
        'select[name="easy_install_usage"]'
      ],
      method: 'select',
      validation: 'required',
      defaultValue: 'easy',
      notes: '安装/使用难度'
    },

    referenceUrl: {
      selectors: [
        '#id_literature',
        'input[name="literature"]',
        'input[type="url"]:nth-of-type(2)'
      ],
      method: 'value',
      validation: 'required|url',
      notes: '参考文献URL'
    }
  },

  submitConfig: {
    submitButton: 'button[type="submit"], .btn.btn-primary',
    submitMethod: 'click',
    successIndicators: ['.success-message'],
    errorIndicators: ['.error-message']
  },

  specialHandling: {
    requiresLogin: false,
    hasCaptcha: false,
    hasFileUpload: false,
    customScript: 'handleTools4miRsSubmission',
    formValidation: {
      requiredFields: ['siteName', 'category', 'siteDescription', 'siteUrl', 'online', 'local', 'installLevel', 'referenceUrl'],
      emailValidation: false,
      urlValidation: true
    },
    notes: [
      '专业的miRNA工具提交表单',
      '有多个必填的下拉选择',
      '需要参考文献URL',
      '有复杂的生物信息学字段',
      '使用Django框架',
      '有CSRF保护'
    ]
  }
};

export function handleTools4miRsSubmission(data, rule) {
  const processedData = { ...data };

  if (processedData.siteUrl && !processedData.siteUrl.startsWith('http')) {
    processedData.siteUrl = 'https://' + processedData.siteUrl;
  }

  if (processedData.referenceUrl && !processedData.referenceUrl.startsWith('http')) {
    processedData.referenceUrl = 'https://' + processedData.referenceUrl;
  }

  return processedData;
}

export async function customFillElement(element, value, config) {
  if (element.tagName === 'SELECT') {
    if (element.hasAttribute('multiple')) {
      // 处理多选下拉框
      const options = element.querySelectorAll('option');
      const option = Array.from(options).find(opt => opt.value === '5' || opt.textContent.includes('Other Tools'));
      if (option) {
        option.selected = true;
        element.dispatchEvent(new Event('change', { bubbles: true }));
        return true;
      }
    } else {
      // 处理单选下拉框
      const options = element.querySelectorAll('option');
      let option;

      if (element.name === 'online') {
        option = Array.from(options).find(opt => opt.value === 'True');
      } else if (element.name === 'local') {
        option = Array.from(options).find(opt => opt.value === 'False');
      } else if (element.name === 'easy_install_usage') {
        option = Array.from(options).find(opt => opt.value === 'easy');
      } else {
        option = Array.from(options).find(opt => opt.value === value);
      }

      if (option) {
        element.value = option.value;
        element.dispatchEvent(new Event('change', { bubbles: true }));
        return true;
      }
    }
  }

  if (element.tagName === 'INPUT' || element.tagName === 'TEXTAREA') {
    element.value = value;
    element.dispatchEvent(new Event('input', { bubbles: true }));
    return true;
  }

  return false;
}